import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/services.dart';

/// محسن الأداء للتطبيق
class PerformanceOptimizer {
  static bool _isInitialized = false;
  static final Map<String, Timer> _debounceTimers = {};
  static final Map<String, DateTime> _lastExecutionTimes = {};
  static final Map<String, dynamic> _performanceMetrics = {};

  /// تهيئة محسن الأداء
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // تحسين إعدادات النظام
      await _optimizeSystemSettings();

      // تهيئة مراقبة الأداء
      _initializePerformanceMonitoring();

      // تحسين إعدادات الذاكرة
      _optimizeMemorySettings();

      _isInitialized = true;
      debugPrint('✅ تم تهيئة محسن الأداء بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة محسن الأداء: $e');
    }
  }

  /// تحسين إعدادات النظام
  static Future<void> _optimizeSystemSettings() async {
    try {
      // تحسين إعدادات الرسوم المتحركة
      if (!kIsWeb && Platform.isAndroid) {
        await SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
      }

      // تحسين إعدادات الشاشة
      await SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
        DeviceOrientation.portraitDown,
      ]);
    } catch (e) {
      debugPrint('⚠️ تحذير في تحسين إعدادات النظام: $e');
    }
  }

  /// تهيئة مراقبة الأداء
  static void _initializePerformanceMonitoring() {
    // مراقبة معدل الإطارات
    SchedulerBinding.instance.addPersistentFrameCallback((timeStamp) {
      _trackFrameRate();
    });

    // مراقبة استخدام الذاكرة كل دقيقة
    Timer.periodic(const Duration(minutes: 1), (timer) {
      _trackMemoryUsage();
    });
  }

  /// تحسين إعدادات الذاكرة
  static void _optimizeMemorySettings() {
    // تنظيف الذاكرة المؤقتة كل 5 دقائق
    Timer.periodic(const Duration(minutes: 5), (timer) {
      _cleanupMemory();
    });
  }

  /// تتبع معدل الإطارات
  static void _trackFrameRate() {
    final now = DateTime.now();
    final lastFrameTime = _performanceMetrics['last_frame_time'] as DateTime?;

    if (lastFrameTime != null) {
      final frameDuration = now.difference(lastFrameTime).inMilliseconds;
      final fps = 1000 / frameDuration;

      _performanceMetrics['current_fps'] = fps;
      _performanceMetrics['frame_times'] =
          (_performanceMetrics['frame_times'] as List<double>? ?? [])
            ..add(frameDuration.toDouble())
            ..take(100).toList(); // الاحتفاظ بآخر 100 إطار
    }

    _performanceMetrics['last_frame_time'] = now;
  }

  /// تتبع استخدام الذاكرة
  static void _trackMemoryUsage() {
    try {
      // قياس استخدام الذاكرة (تقريبي)
      final runtimeType = Object().runtimeType;
      _performanceMetrics['memory_usage'] = {
        'timestamp': DateTime.now(),
        'runtime_type': runtimeType.toString(),
      };
    } catch (e) {
      debugPrint('⚠️ خطأ في تتبع استخدام الذاكرة: $e');
    }
  }

  /// تنظيف الذاكرة
  static void _cleanupMemory() {
    try {
      // تنظيف المؤقتات المنتهية الصلاحية
      _cleanupExpiredTimers();

      // تنظيف البيانات القديمة
      _cleanupOldMetrics();

      debugPrint('🧹 تم تنظيف الذاكرة');
    } catch (e) {
      debugPrint('❌ خطأ في تنظيف الذاكرة: $e');
    }
  }

  /// تنظيف المؤقتات المنتهية الصلاحية
  static void _cleanupExpiredTimers() {
    final expiredKeys = <String>[];
    final now = DateTime.now();

    for (final entry in _lastExecutionTimes.entries) {
      if (now.difference(entry.value).inMinutes > 10) {
        expiredKeys.add(entry.key);
      }
    }

    for (final key in expiredKeys) {
      _debounceTimers[key]?.cancel();
      _debounceTimers.remove(key);
      _lastExecutionTimes.remove(key);
    }
  }

  /// تنظيف البيانات القديمة
  static void _cleanupOldMetrics() {
    final frameTimes = _performanceMetrics['frame_times'] as List<double>?;
    if (frameTimes != null && frameTimes.length > 100) {
      _performanceMetrics['frame_times'] = frameTimes.take(100).toList();
    }
  }

  /// تنفيذ عملية مع Debouncing
  static void debounce(
    String key,
    VoidCallback callback, {
    Duration delay = const Duration(milliseconds: 300),
  }) {
    _debounceTimers[key]?.cancel();
    _debounceTimers[key] = Timer(delay, () {
      callback();
      _lastExecutionTimes[key] = DateTime.now();
    });
  }

  /// تنفيذ عملية مع Throttling
  static void throttle(
    String key,
    VoidCallback callback, {
    Duration interval = const Duration(milliseconds: 100),
  }) {
    final lastExecution = _lastExecutionTimes[key];
    final now = DateTime.now();

    if (lastExecution == null || now.difference(lastExecution) >= interval) {
      callback();
      _lastExecutionTimes[key] = now;
    }
  }

  /// الحصول على إحصائيات الأداء
  static Map<String, dynamic> getPerformanceStats() {
    final frameTimes = _performanceMetrics['frame_times'] as List<double>?;
    double avgFrameTime = 0;
    double avgFps = 0;

    if (frameTimes != null && frameTimes.isNotEmpty) {
      avgFrameTime = frameTimes.reduce((a, b) => a + b) / frameTimes.length;
      avgFps = 1000 / avgFrameTime;
    }

    return {
      'is_initialized': _isInitialized,
      'current_fps': _performanceMetrics['current_fps'] ?? 0,
      'average_fps': avgFps,
      'average_frame_time_ms': avgFrameTime,
      'active_timers': _debounceTimers.length,
      'memory_usage': _performanceMetrics['memory_usage'],
      'last_cleanup': _performanceMetrics['last_cleanup'],
    };
  }

  /// تحسين أداء القوائم
  static Widget optimizeListView({
    required Widget child,
    bool addRepaintBoundary = true,
    bool addAutomaticKeepAlive = false,
  }) {
    Widget optimizedChild = child;

    if (addRepaintBoundary) {
      optimizedChild = RepaintBoundary(child: optimizedChild);
    }

    return optimizedChild;
  }

  /// تحسين أداء الصور
  static Widget optimizeImage({
    required Widget imageWidget,
    bool addRepaintBoundary = true,
    bool enableMemoryCache = true,
  }) {
    Widget optimizedImage = imageWidget;

    if (addRepaintBoundary) {
      optimizedImage = RepaintBoundary(child: optimizedImage);
    }

    return optimizedImage;
  }

  /// تحسين أداء الرسوم المتحركة
  static AnimationController optimizeAnimationController({
    required TickerProvider vsync,
    Duration duration = const Duration(milliseconds: 300),
    String? debugLabel,
  }) {
    return AnimationController(
      duration: duration,
      vsync: vsync,
      debugLabel: debugLabel,
    );
  }

  /// إعادة تعيين الإحصائيات
  static void resetStats() {
    _performanceMetrics.clear();
    _debounceTimers.clear();
    _lastExecutionTimes.clear();
    debugPrint('🔄 تم إعادة تعيين إحصائيات الأداء');
  }

  /// تنظيف الموارد
  static void dispose() {
    for (final timer in _debounceTimers.values) {
      timer.cancel();
    }
    _debounceTimers.clear();
    _lastExecutionTimes.clear();
    _performanceMetrics.clear();
    _isInitialized = false;
    debugPrint('🗑️ تم تنظيف موارد محسن الأداء');
  }
}
