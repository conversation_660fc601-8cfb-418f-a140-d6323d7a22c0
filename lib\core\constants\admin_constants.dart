import 'package:motorcycle_parts_shop/core/services/auth_supabase_service.dart';

// خدمة ثوابت المسؤولين - تحصل على البيانات من قاعدة البيانات
class AdminConstants {
  static final AuthSupabaseService _supabaseService = AuthSupabaseService();
  static String? _cachedMainAdminEmail;
  static List<String>? _cachedMainAdminPermissions;
  static DateTime? _lastFetchTime;
  static const Duration _cacheDuration = Duration(hours: 1);

  /// جلب البريد الإلكتروني للمسؤول الرئيسي من قاعدة البيانات
  static Future<String> getMainAdminEmail() async {
    // التحقق من وجود بيانات مخزنة مؤقتاً وصالحة
    if (_cachedMainAdminEmail != null &&
        _lastFetchTime != null &&
        DateTime.now().difference(_lastFetchTime!) < _cacheDuration) {
      return _cachedMainAdminEmail!;
    }

    try {
      final response =
          await _supabaseService.client
              .from('admin_settings')
              .select('main_admin_email')
              .eq('setting_key', 'main_admin')
              .single();

      final email = response['main_admin_email'] as String;

      // تخزين البيانات مؤقتاً
      _cachedMainAdminEmail = email;
      _lastFetchTime = DateTime.now();

      return email;
    } catch (e) {
      // في حالة فشل جلب البيانات، إرجاع بريد افتراضي
      return '<EMAIL>';
    }
  }

  /// جلب صلاحيات المسؤول الرئيسي من قاعدة البيانات
  static Future<List<String>> getMainAdminPermissions() async {
    // التحقق من وجود بيانات مخزنة مؤقتاً وصالحة
    if (_cachedMainAdminPermissions != null &&
        _lastFetchTime != null &&
        DateTime.now().difference(_lastFetchTime!) < _cacheDuration) {
      return _cachedMainAdminPermissions!;
    }

    try {
      final response = await _supabaseService.client
          .from('admin_permissions')
          .select('permission_name')
          .eq('is_active', true)
          .order('permission_name');

      final permissions =
          (response as List)
              .map((item) => item['permission_name'] as String)
              .toList();

      // تخزين البيانات مؤقتاً
      _cachedMainAdminPermissions = permissions;
      _lastFetchTime = DateTime.now();

      return permissions;
    } catch (e) {
      // في حالة فشل جلب البيانات، إرجاع قائمة افتراضية
      return [
        'customer',
        'admin',
        'products',
        'orders',
        'customers',
        'admins',
        'statistics',
        'marketing',
        'settings',
        'reports',
        'notifications',
        'all',
      ];
    }
  }

  /// التحقق مما إذا كان البريد الإلكتروني هو بريد المسؤول الرئيسي
  static Future<bool> isMainAdmin(String email) async {
    final mainAdminEmail = await getMainAdminEmail();
    return email.toLowerCase() == mainAdminEmail.toLowerCase();
  }

  /// مسح البيانات المخزنة مؤقتاً
  static void clearCache() {
    _cachedMainAdminEmail = null;
    _cachedMainAdminPermissions = null;
    _lastFetchTime = null;
  }
}
