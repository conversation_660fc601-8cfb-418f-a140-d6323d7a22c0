import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// خدمة إدارة البيانات المخزنة مؤقتًا
/// توفر هذه الخدمة آليات لإدارة البيانات المخزنة مؤقتًا
/// لضمان سلامة البيانات المخزنة مؤقتًا
class DataIntegrityService extends ChangeNotifier {
  static final DataIntegrityService _instance =
      DataIntegrityService._internal();
  late SharedPreferences _prefs;

  // مفاتيح التخزين المؤقت
  static const String _integrityLogKey = 'data_integrity_log';

  // تطبيق نمط Singleton لإنشاء كائن واحد فقط من هذه الخدمة
  factory DataIntegrityService() {
    return _instance;
  }

  DataIntegrityService._internal();

  /// تهيئة خدمة إدارة البيانات
  Future<void> initialize() async {
    try {
      _prefs = await SharedPreferences.getInstance();
      debugPrint('تم تهيئة خدمة إدارة البيانات بنجاح');
      notifyListeners();
    } catch (e) {
      debugPrint('خطأ أثناء تهيئة خدمة إدارة البيانات: $e');
      rethrow;
    }
  }

  /// تخزين البيانات
  Future<void> storeData(
    String key,
    Map<String, dynamic> data,
  ) async {
    try {
      final storageData = {
        'data': data,
        'timestamp': DateTime.now().toIso8601String(),
      };

      await _prefs.setString(key, jsonEncode(storageData));
      debugPrint('تم تخزين البيانات: $key');
    } catch (e) {
      debugPrint('خطأ أثناء تخزين البيانات: $e');
      rethrow;
    }
  }

  /// استرجاع البيانات
  Future<Map<String, dynamic>?> retrieveData(
    String key,
  ) async {
    try {
      final String? jsonData = _prefs.getString(key);
      if (jsonData == null) return null;

      final Map<String, dynamic> storageData = jsonDecode(jsonData);
      final data = storageData['data'] as Map<String, dynamic>;
      
      return data;
    } catch (e) {
      debugPrint('خطأ أثناء استرجاع البيانات: $e');
      await _logDataError(
        key,
        'خطأ في استرجاع البيانات: ${e.toString()}',
      );
      return null;
    }
  }

  /// تسجيل خطأ في البيانات
  Future<void> _logDataError(String key, String reason) async {
    try {
      final String? logJson = _prefs.getString(_integrityLogKey);
      List<Map<String, dynamic>> log = [];

      if (logJson != null) {
        final List<dynamic> decodedLog = jsonDecode(logJson);
        log = decodedLog.cast<Map<String, dynamic>>().toList();
      }

      // إضافة سجل جديد
      log.add({
        'key': key,
        'reason': reason,
        'timestamp': DateTime.now().toIso8601String(),
      });

      // الاحتفاظ بآخر 50 سجل فقط
      if (log.length > 50) {
        log = log.sublist(log.length - 50);
      }

      await _prefs.setString(_integrityLogKey, jsonEncode(log));
    } catch (e) {
      debugPrint('خطأ أثناء تسجيل خطأ في البيانات: $e');
    }
  }

  /// الحصول على سجل أخطاء البيانات
  Future<List<Map<String, dynamic>>> getDataErrorLog() async {
    try {
      final String? logJson = _prefs.getString(_integrityLogKey);
      if (logJson == null) return [];

      final List<dynamic> decodedLog = jsonDecode(logJson);
      return decodedLog.cast<Map<String, dynamic>>().toList();
    } catch (e) {
      debugPrint('خطأ أثناء الحصول على سجل أخطاء البيانات: $e');
      return [];
    }
  }

  /// مسح سجل أخطاء البيانات
  Future<void> clearDataErrorLog() async {
    try {
      await _prefs.remove(_integrityLogKey);
      debugPrint('تم مسح سجل أخطاء البيانات');
    } catch (e) {
      debugPrint('خطأ أثناء مسح سجل أخطاء البيانات: $e');
    }
  }

  /// التخلص من الموارد عند إنهاء الخدمة
  @override
  void dispose() {
    super.dispose();
  }
}
