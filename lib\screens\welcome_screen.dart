import 'dart:async';
import 'dart:math' as math;
import 'package:confetti/confetti.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:lottie/lottie.dart';
import 'package:motorcycle_parts_shop/core/services/auth_supabase_service.dart';
import 'package:motorcycle_parts_shop/core/theme/app_theme.dart';
import 'package:motorcycle_parts_shop/core/theme/gradients.dart';
import 'package:motorcycle_parts_shop/core/utils/responsive_helper.dart';
import 'package:motorcycle_parts_shop/core/widgets/shimmer_loading.dart';
import 'package:motorcycle_parts_shop/screens/auth/login_screen.dart';
import 'package:motorcycle_parts_shop/screens/onboarding/onboarding_screen.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

const _kAnimationDuration = Duration(milliseconds: 2000);
const _kImageHeight = 280.0;
const _kButtonHeight = 60.0;
const _kParticleCount = 50;

class WelcomeScreen extends StatefulWidget {
  const WelcomeScreen({super.key});

  @override
  State<WelcomeScreen> createState() => _WelcomeScreenState();
}

class _WelcomeScreenState extends State<WelcomeScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late AnimationController _rotationController;
  late AnimationController _pulseController;
  late AnimationController _particleController;
  late ConfettiController _confettiController;

  late Animation<double> _fadeInAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _rotationAnimation;
  late Animation<double> _pulseAnimation;
  late Animation<double> _particleAnimation;

  bool _hasSeenOnboarding = false;
  bool _isLoading = true;
  bool _showParticles = false;
  Timer? _particleTimer;

  @override
  void initState() {
    super.initState();
    _initAnimations();
    _checkInitialStateFast();
  }

  void _initAnimations() {
    // المتحكم الرئيسي للرسوم المتحركة
    _animationController = AnimationController(
      vsync: this,
      duration: _kAnimationDuration,
    );

    // متحكم الدوران
    _rotationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 20),
    );

    // متحكم النبض
    _pulseController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    // متحكم الجسيمات
    _particleController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 3),
    );

    // متحكم الكونفيتي
    _confettiController = ConfettiController(
      duration: const Duration(seconds: 2),
    );

    // الرسوم المتحركة الأساسية
    _fadeInAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutCubic),
    );

    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.elasticOut),
    );

    // رسوم متحركة متقدمة
    _rotationAnimation = Tween<double>(begin: 0.0, end: 2 * math.pi).animate(
      CurvedAnimation(parent: _rotationController, curve: Curves.linear),
    );

    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.1).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    _particleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _particleController, curve: Curves.easeOut),
    );

    // بدء الرسوم المتحركة
    _animationController.forward();
    _rotationController.repeat();
    _pulseController.repeat(reverse: true);

    // بدء الجسيمات بعد تأخير
    Timer(const Duration(milliseconds: 500), () {
      if (mounted) {
        setState(() {
          _showParticles = true;
        });
        _particleController.repeat();
      }
    });
  }

  /// التحقق السريع من الحالة الأولية
  Future<void> _checkInitialStateFast() async {
    try {
      // تحديث الحالة فوراً لإظهار الواجهة
      setState(() {
        _isLoading = false;
      });

      // التحقق من SharedPreferences بسرعة
      final prefs = await SharedPreferences.getInstance();
      final hasSeenOnboarding = prefs.getBool('onboarding_complete') ?? false;

      if (!mounted) return;

      setState(() {
        _hasSeenOnboarding = hasSeenOnboarding;
      });

      // التحقق من المصادقة في الخلفية
      _checkAuthenticationInBackground();

      // إذا لم يشاهد المستخدم onboarding، انتقل إليه
      if (!_hasSeenOnboarding) {
        Future.delayed(const Duration(milliseconds: 1000), () {
          if (mounted) {
            _navigateToOnboarding();
          }
        });
      }
    } catch (e) {
      debugPrint('خطأ في التحقق السريع: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
          _hasSeenOnboarding = false;
        });

        // في حالة الخطأ، انتقل إلى onboarding
        if (!_hasSeenOnboarding) {
          _navigateToOnboarding();
        }
      }
    }
  }

  /// التحقق من المصادقة في الخلفية
  void _checkAuthenticationInBackground() {
    Future.delayed(const Duration(milliseconds: 100), () async {
      try {
        final authService = Provider.of<AuthSupabaseService>(
          context,
          listen: false,
        );

        // تهيئة خدمة المصادقة بدون انتظار
        if (!authService.isInitialized) {
          authService.initialize().catchError((e) {
            debugPrint('خطأ في تهيئة خدمة المصادقة: $e');
          });
        }

        // التحقق من حالة المصادقة
        if (authService.isAuthenticated && mounted) {
          _navigateToHome();
        }
      } catch (e) {
        debugPrint('خطأ في التحقق من المصادقة: $e');
      }
    });
  }

  /// الانتقال إلى شاشة تسجيل الدخول
  void _navigateToLogin() {
    HapticFeedback.lightImpact();
    Navigator.of(context).pushReplacement(
      PageRouteBuilder(
        transitionDuration: const Duration(milliseconds: 500),
        pageBuilder: (_, __, ___) => const LoginScreen(),
        transitionsBuilder:
            (_, a, __, c) => FadeTransition(opacity: a, child: c),
      ),
    );
  }

  /// الانتقال إلى شاشة التعريف
  void _navigateToOnboarding() {
    Navigator.of(context).pushReplacement(
      PageRouteBuilder(
        transitionDuration: const Duration(milliseconds: 500),
        pageBuilder: (_, __, ___) => const OnboardingScreen(),
        transitionsBuilder:
            (_, a, __, c) => FadeTransition(opacity: a, child: c),
      ),
    );
  }

  /// الانتقال إلى الشاشة الرئيسية
  void _navigateToHome() {
    Navigator.of(
      context,
    ).pushReplacementNamed('/home'); // افتراض وجود مسار للشاشة الرئيسية
  }

  @override
  void dispose() {
    _animationController.dispose();
    _rotationController.dispose();
    _pulseController.dispose();
    _particleController.dispose();
    _confettiController.dispose();
    _particleTimer?.cancel();
    super.dispose();
  }

  /// بناء صورة الترحيب المتحركة
  Widget _buildWelcomeImage() {
    return ResponsiveBuilder(
      builder: (context, constraints) {
        final imageHeight =
            ResponsiveHelper.isMobile(context) ? 220.0 : _kImageHeight;
        final padding = ResponsiveHelper.getPadding(context);

        return FadeTransition(
          opacity: _fadeInAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: ScaleTransition(
              scale: _scaleAnimation,
              child: Stack(
                alignment: Alignment.center,
                children: [
                  // خلفية دائرية متحركة
                  AnimatedBuilder(
                    animation: _rotationAnimation,
                    builder: (context, child) {
                      return Transform.rotate(
                        angle: _rotationAnimation.value,
                        child: Container(
                          width: imageHeight + 40,
                          height: imageHeight + 40,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            gradient: RadialGradient(
                              colors: [
                                Colors.white.withOpacity(0.1),
                                Colors.white.withOpacity(0.05),
                                Colors.transparent,
                              ],
                            ),
                          ),
                        ),
                      );
                    },
                  ),

                  // الصورة الرئيسية مع تأثير النبض
                  AnimatedBuilder(
                    animation: _pulseAnimation,
                    builder: (context, child) {
                      return Transform.scale(
                        scale: _pulseAnimation.value,
                        child: Container(
                          padding: EdgeInsets.symmetric(horizontal: padding),
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            boxShadow: [
                              BoxShadow(
                                color: Colors.white.withOpacity(0.3),
                                blurRadius: 20,
                                spreadRadius: 5,
                              ),
                            ],
                          ),
                          child: ClipOval(
                            child: Container(
                              width: imageHeight,
                              height: imageHeight,
                              decoration: BoxDecoration(
                                gradient: RadialGradient(
                                  colors: [
                                    Colors.white.withOpacity(0.2),
                                    Colors.white.withOpacity(0.1),
                                  ],
                                ),
                              ),
                              child: _buildImageContent(imageHeight),
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// بناء محتوى الصورة
  Widget _buildImageContent(double imageHeight) {
    return Stack(
      alignment: Alignment.center,
      children: [
        // محاولة عرض Lottie أولاً
        Lottie.asset(
          'assets/animations/welcome.json',
          height: imageHeight * 0.8,
          fit: BoxFit.contain,
          repeat: true,
          animate: true,
          errorBuilder: (context, error, stackTrace) {
            // في حالة عدم وجود ملف Lottie، استخدم الصورة العادية
            return Image.asset(
              'assets/images/welcome_image.png',
              height: imageHeight * 0.8,
              fit: BoxFit.contain,
              filterQuality: FilterQuality.medium,
              semanticLabel: 'صورة ترحيبية لمتجر قطع غيار الدراجات النارية',
              errorBuilder: (context, error, stackTrace) => _buildImageError(),
            );
          },
        ),
      ],
    );
  }

  /// بناء خطأ الصورة
  Widget _buildImageError() {
    return Container(
      height: _kImageHeight,
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(24), // Consistent border radius
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 48, color: Colors.grey[400]),
          const SizedBox(height: 8),
          Text('تعذر تحميل الصورة', style: TextStyle(color: Colors.grey[600])),
        ],
      ),
    );
  }

  /// بناء عنوان الترحيب المتحرك
  Widget _buildWelcomeTitle() {
    return ResponsiveBuilder(
      builder: (context, constraints) {
        final fontSize = ResponsiveHelper.getFontSize(
          context,
          ResponsiveHelper.isMobile(context) ? 32 : 36,
        );

        return FadeTransition(
          opacity: _fadeInAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: AnimatedBuilder(
              animation: _pulseAnimation,
              builder: (context, child) {
                return Transform.scale(
                  scale: 1.0 + (_pulseAnimation.value - 1.0) * 0.02,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 20,
                      vertical: 10,
                    ),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(20),
                      color: Colors.white.withOpacity(0.1),
                      border: Border.all(
                        color: Colors.white.withOpacity(0.2),
                        width: 1,
                      ),
                    ),
                    child: Text(
                      _hasSeenOnboarding
                          ? '🏍️ مرحباً بعودتك! 🔧'
                          : '🏍️ مرحباً بك في متجرنا! 🔧',
                      textAlign: TextAlign.center,
                      style: Theme.of(
                        context,
                      ).textTheme.displayMedium!.copyWith(
                        fontWeight: FontWeight.bold,
                        fontSize: fontSize,
                        color: Colors.white,
                        shadows: [
                          Shadow(
                            blurRadius: 10,
                            color: Colors.black.withOpacity(0.3),
                            offset: const Offset(2, 2),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        );
      },
    );
  }

  /// بناء نص الترحيب المحسن
  Widget _buildWelcomeSubtitle() {
    return ResponsiveBuilder(
      builder: (context, constraints) {
        final padding = ResponsiveHelper.getPadding(context);
        final fontSize = ResponsiveHelper.getFontSize(context, 18);

        return FadeTransition(
          opacity: _fadeInAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: padding),
              child: Column(
                children: [
                  Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(15),
                      color: Colors.white.withOpacity(0.1),
                      border: Border.all(
                        color: Colors.white.withOpacity(0.2),
                        width: 1,
                      ),
                    ),
                    child: Text(
                      _hasSeenOnboarding
                          ? 'نحن سعداء برؤيتك مرة أخرى! استمتع بتجربة تسوق محسنة مع أحدث قطع الغيار.'
                          : 'أفضل مكان للعثور على جميع قطع غيار دراجتك النارية بجودة عالية وأسعار لا تقبل المنافسة.',
                      textAlign: TextAlign.center,
                      style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                        color: Colors.white.withOpacity(0.9),
                        fontSize: fontSize,
                        height: 1.6,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  const SizedBox(height: 20),
                  _buildFeaturesList(),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// بناء قائمة المميزات
  Widget _buildFeaturesList() {
    final features = [
      {'icon': '🏍️', 'text': 'قطع غيار أصلية'},
      {'icon': '⚡', 'text': 'توصيل سريع'},
      {'icon': '💎', 'text': 'جودة عالية'},
      {'icon': '🤖', 'text': 'ذكاء اصطناعي'},
    ];

    return Wrap(
      alignment: WrapAlignment.center,
      spacing: 12,
      runSpacing: 12,
      children:
          features.map((feature) {
            return AnimatedBuilder(
              animation: _pulseAnimation,
              builder: (context, child) {
                return Transform.scale(
                  scale: 1.0 + (_pulseAnimation.value - 1.0) * 0.03,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: Colors.white.withOpacity(0.3),
                        width: 1,
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          feature['icon']!,
                          style: const TextStyle(fontSize: 16),
                        ),
                        const SizedBox(width: 6),
                        Text(
                          feature['text']!,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            );
          }).toList(),
    );
  }

  /// بناء أزرار البدء المحسنة
  Widget _buildStartButton() {
    return ResponsiveBuilder(
      builder: (context, constraints) {
        final padding = ResponsiveHelper.getPadding(context);
        final buttonHeight =
            ResponsiveHelper.isMobile(context) ? 60.0 : _kButtonHeight;

        return FadeTransition(
          opacity: _fadeInAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: ScaleTransition(
              scale: _scaleAnimation,
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: padding),
                child: Column(
                  children: [
                    // الزر الرئيسي
                    AnimatedBuilder(
                      animation: _pulseAnimation,
                      builder: (context, child) {
                        return Transform.scale(
                          scale: 1.0 + (_pulseAnimation.value - 1.0) * 0.05,
                          child: Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(30),
                              gradient: LinearGradient(
                                colors: [
                                  AppTheme.accentColor,
                                  AppTheme.accentColor.withOpacity(0.8),
                                ],
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                              ),
                              boxShadow: [
                                BoxShadow(
                                  color: AppTheme.accentColor.withOpacity(0.4),
                                  blurRadius: 20,
                                  spreadRadius: 2,
                                  offset: const Offset(0, 8),
                                ),
                              ],
                            ),
                            child: ElevatedButton(
                              onPressed: () {
                                HapticFeedback.heavyImpact();
                                _confettiController.play();
                                _navigateToLogin();
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.transparent,
                                foregroundColor: Colors.white,
                                elevation: 0,
                                minimumSize: Size(
                                  double.infinity,
                                  buttonHeight,
                                ),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(30),
                                ),
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    _hasSeenOnboarding
                                        ? '🚀 متابعة التسوق'
                                        : '🚀 ابدأ رحلتك معنا',
                                    style: const TextStyle(
                                      fontSize: 20,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  const SizedBox(width: 12),
                                  Container(
                                    padding: const EdgeInsets.all(8),
                                    decoration: BoxDecoration(
                                      color: Colors.white.withOpacity(0.2),
                                      shape: BoxShape.circle,
                                    ),
                                    child: const Icon(
                                      Icons.arrow_forward_ios,
                                      size: 18,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        );
                      },
                    ),

                    const SizedBox(height: 16),

                    // زر ثانوي للتعريف (يظهر فقط إذا لم يشاهد المستخدم onboarding)
                    if (!_hasSeenOnboarding)
                      TextButton(
                        onPressed: () {
                          HapticFeedback.lightImpact();
                          _navigateToOnboarding();
                        },
                        style: TextButton.styleFrom(
                          foregroundColor: Colors.white.withOpacity(0.8),
                          padding: const EdgeInsets.symmetric(
                            horizontal: 24,
                            vertical: 12,
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.info_outline,
                              size: 18,
                              color: Colors.white.withOpacity(0.8),
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'تعرف على المزيد',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                                color: Colors.white.withOpacity(0.8),
                              ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// بناء الجسيمات المتحركة
  Widget _buildAnimatedParticles() {
    if (!_showParticles) return const SizedBox.shrink();

    return AnimatedBuilder(
      animation: _particleAnimation,
      builder: (context, child) {
        return Stack(
          children: List.generate(_kParticleCount, (index) {
            final random = math.Random(index);
            final x = random.nextDouble() * MediaQuery.of(context).size.width;
            final y = random.nextDouble() * MediaQuery.of(context).size.height;
            final size = random.nextDouble() * 4 + 2;
            final opacity = random.nextDouble() * 0.5 + 0.1;

            return Positioned(
              left: x,
              top: y + (_particleAnimation.value * 100),
              child: Container(
                width: size,
                height: size,
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(opacity),
                  shape: BoxShape.circle,
                ),
              ),
            );
          }),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: SystemUiOverlayStyle.light.copyWith(
        statusBarColor: Colors.transparent,
        systemNavigationBarColor: AppTheme.primaryColor,
        statusBarIconBrightness: Brightness.light,
        systemNavigationBarIconBrightness: Brightness.light,
      ),
      child: Scaffold(
        body: Stack(
          children: [
            // الخلفية المتدرجة المتحركة
            Container(
              decoration: BoxDecoration(gradient: AppGradients.welcomeGradient),
            ),

            // خلفية متحركة إضافية
            AnimatedBuilder(
              animation: _rotationAnimation,
              builder: (context, child) {
                return Transform.rotate(
                  angle: _rotationAnimation.value * 0.1,
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: RadialGradient(
                        center: Alignment.topRight,
                        radius: 1.5,
                        colors: [
                          Colors.white.withOpacity(0.1),
                          Colors.transparent,
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),

            // الجسيمات المتحركة
            _buildAnimatedParticles(),

            // الكونفيتي
            Align(
              alignment: Alignment.topCenter,
              child: ConfettiWidget(
                confettiController: _confettiController,
                blastDirectionality: BlastDirectionality.explosive,
                shouldLoop: false,
                colors: const [
                  Colors.blue,
                  Colors.yellow,
                  Colors.red,
                  Colors.green,
                  Colors.purple,
                  Colors.orange,
                ],
              ),
            ),

            // المحتوى الرئيسي
            SafeArea(
              child: Center(
                child:
                    _isLoading
                        ? Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const CircularProgressIndicator(
                              color: Colors.white,
                              strokeWidth: 3,
                            ),
                            const SizedBox(height: 20),
                            Text(
                              'جاري التحضير...',
                              style: TextStyle(
                                color: Colors.white.withOpacity(0.8),
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        )
                        : SingleChildScrollView(
                          padding: const EdgeInsets.symmetric(vertical: 40.0),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.stretch,
                            children: [
                              _buildWelcomeImage(),
                              const SizedBox(height: 40),
                              _buildWelcomeTitle(),
                              const SizedBox(height: 24),
                              _buildWelcomeSubtitle(),
                              const SizedBox(height: 48),
                              _buildStartButton(),
                            ],
                          ),
                        ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
