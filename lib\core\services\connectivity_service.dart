import 'dart:async';
import 'dart:io';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// خدمة مراقبة الاتصال بالإنترنت وقاعدة البيانات
///
/// توفر هذه الخدمة وظائف لمراقبة حالة الاتصال بالإنترنت وقياس جودته
/// وتوفر أيضًا وظائف للتحقق من إمكانية الوصول إلى خدمات محددة مثل قاعدة البيانات
///
/// استخدامات الخدمة:
/// - مراقبة حالة الاتصال بالإنترنت
/// - قياس جودة الاتصال (زمن الاستجابة)
/// - التحقق من إمكانية الوصول إلى خدمات محددة
/// - تسجيل تغييرات الاتصال

/// نوع الاتصال بالشبكة
enum NetworkType { wifi, mobile, ethernet, bluetooth, vpn, none, unknown }

/// جودة الاتصال بالشبكة
enum ConnectionQuality {
  excellent, // < 50ms
  good, // 50-150ms
  fair, // 150-300ms
  poor, // > 300ms
  unknown,
}

/// خدمة مراقبة الاتصال بالإنترنت
///
/// توفر هذه الخدمة وظائف متقدمة لمراقبة حالة الاتصال بالإنترنت وقياس جودته
/// وتوفر أيضًا وظائف للتحقق من إمكانية الوصول إلى خدمات محددة مثل قاعدة البيانات
///
/// تم تحسين الخدمة لتوفير:
/// - مراقبة دقيقة لحالة الاتصال
/// - قياس جودة الاتصال بشكل دوري
/// - تخزين سجل الاتصال محليًا
/// - التكامل مع خدمات أخرى مثل خدمة تفاعلات المستخدم
class ConnectivityService extends StateNotifier<bool> {
  static final ConnectivityService _instance = ConnectivityService._internal();
  factory ConnectivityService() => _instance;
  ConnectivityService._internal() : super(false);

  final _connectivity = Connectivity();
  final _controller = StreamController<bool>.broadcast();
  Stream<bool> get onConnectivityChanged => _controller.stream;

  NetworkType _networkType = NetworkType.unknown;
  ConnectionQuality _connectionQuality = ConnectionQuality.unknown;

  NetworkType get networkType => _networkType;
  ConnectionQuality get connectionQuality => _connectionQuality;

  Future<void> initialize() async {
    // Listen to connectivity changes
    _connectivity.onConnectivityChanged.listen((
      List<ConnectivityResult> results,
    ) {
      final isConnected =
          results.isNotEmpty && results.first != ConnectivityResult.none;
      state = isConnected;
      _updateNetworkType(results.first);
      _controller.add(isConnected);
    });

    // Initial connectivity check
    final results = await _connectivity.checkConnectivity();
    final isConnected =
        results.isNotEmpty && results.first != ConnectivityResult.none;
    state = isConnected;
    _updateNetworkType(results.first);
    _controller.add(isConnected);
  }

  void _updateNetworkType(ConnectivityResult result) {
    switch (result) {
      case ConnectivityResult.wifi:
        _networkType = NetworkType.wifi;
        _connectionQuality = ConnectionQuality.excellent;
        break;
      case ConnectivityResult.mobile:
        _networkType = NetworkType.mobile;
        _connectionQuality = ConnectionQuality.good;
        break;
      case ConnectivityResult.ethernet:
        _networkType = NetworkType.ethernet;
        _connectionQuality = ConnectionQuality.excellent;
        break;
      case ConnectivityResult.bluetooth:
        _networkType = NetworkType.bluetooth;
        _connectionQuality = ConnectionQuality.fair;
        break;
      case ConnectivityResult.vpn:
        _networkType = NetworkType.vpn;
        _connectionQuality = ConnectionQuality.good;
        break;
      case ConnectivityResult.none:
        _networkType = NetworkType.none;
        _connectionQuality = ConnectionQuality.poor;
        break;
      default:
        _networkType = NetworkType.unknown;
        _connectionQuality = ConnectionQuality.unknown;
    }
  }

  Future<bool> checkConnectivity() async {
    final results = await _connectivity.checkConnectivity();
    return results.isNotEmpty && results.first != ConnectivityResult.none;
  }

  Future<bool> canReachService(String url) async {
    try {
      final result = await InternetAddress.lookup(url);
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } on SocketException catch (_) {
      return false;
    }
  }

  @override
  void dispose() {
    _controller.close();
    super.dispose();
  }
}

final connectivityServiceProvider =
    StateNotifierProvider<ConnectivityService, bool>((ref) {
      return ConnectivityService();
    });
