import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:motorcycle_parts_shop/core/services/ai_services.dart';
import 'package:motorcycle_parts_shop/core/theme/app_theme.dart';
import 'package:motorcycle_parts_shop/core/widgets/custom_app_bar.dart';
import 'package:motorcycle_parts_shop/core/widgets/loading_indicator.dart';
import 'package:motorcycle_parts_shop/models/category_model.dart';
import 'package:motorcycle_parts_shop/models/product_model.dart';
import 'package:provider/provider.dart';

class AddEditProductScreen extends StatefulWidget {
  final ProductModel? product;
  final bool isEditing;

  const AddEditProductScreen({super.key, this.product, this.isEditing = false});

  @override
  State<AddEditProductScreen> createState() => _AddEditProductScreenState();
}

class _AddEditProductScreenState extends State<AddEditProductScreen>
    with SingleTickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  final TextEditingController _priceController = TextEditingController();
  final TextEditingController _discountPriceController =
      TextEditingController();
  final TextEditingController _quantityController = TextEditingController();
  final TextEditingController _skuController = TextEditingController();
  final TextEditingController _brandController = TextEditingController();

  List<CategoryModel> _categories = [];
  String? _selectedCategoryId;
  bool _isLoading = false;
  bool _isOnSale = false;
  bool _isFeatured = false;
  List<String> _imageUrls = [];
  List<File> _pickedImages = [];

  late AuthSupabaseService _supabaseService;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  final ImagePicker _picker = ImagePicker();

  @override
  void initState() {
    super.initState();
    _supabaseService = Provider.of<AuthSupabaseService>(context, listen: false);
    _loadCategories();

    if (widget.isEditing && widget.product != null) {
      _initializeFormWithProductData();
    }

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.15),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutCubic),
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    _discountPriceController.dispose();
    _quantityController.dispose();
    _skuController.dispose();
    _brandController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadCategories() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final categories = await _supabaseService.getCategories();
      if (mounted) {
        setState(() {
          _categories = categories;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        _showErrorSnackBar('حدث خطأ أثناء تحميل الفئات: $e');
      }
    }
  }

  void _initializeFormWithProductData() {
    final product = widget.product!;
    _nameController.text = product.name;
    _descriptionController.text = product.description;
    _priceController.text = product.price.toString();
    _discountPriceController.text = product.discountPrice?.toString() ?? '';
    _quantityController.text = product.quantity.toString();
    _skuController.text = product.sku;
    _brandController.text = product.brand;
    _selectedCategoryId = product.categoryId;
    _isOnSale = product.isOnSale;
    _isFeatured = product.isFeatured;
    _imageUrls = List<String>.from(product.images);
  }

  void _showErrorSnackBar(String message) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: Theme.of(
            context,
          ).textTheme.bodyLarge!.copyWith(color: AppTheme.textLightColor),
        ),
        backgroundColor: AppTheme.errorColor,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: AppTheme.borderRadiusMedium,
        ),
        duration: const Duration(seconds: 4),
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: Theme.of(
            context,
          ).textTheme.bodyLarge!.copyWith(color: AppTheme.textLightColor),
        ),
        backgroundColor: AppTheme.successColor,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: AppTheme.borderRadiusMedium,
        ),
        duration: const Duration(seconds: 3),
      ),
    );
  }

  Future<void> _pickImages() async {
    try {
      final List<XFile> pickedFiles = await _picker.pickMultiImage(
        imageQuality: 70,
      );
      if (pickedFiles.isNotEmpty && mounted) {
        setState(() {
          _pickedImages.addAll(pickedFiles.map((file) => File(file.path)));
        });
      }
    } catch (e) {
      _showErrorSnackBar('حدث خطأ أثناء اختيار الصور: $e');
    }
  }

  void _removeImage(int index, bool isPickedImage) {
    setState(() {
      if (isPickedImage) {
        _pickedImages.removeAt(index);
      } else {
        _imageUrls.removeAt(index);
      }
    });
  }

  Future<void> _saveProduct() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedCategoryId == null) {
      _showErrorSnackBar('الرجاء اختيار فئة للمنتج');
      return;
    }

    if (_imageUrls.isEmpty && _pickedImages.isEmpty) {
      _showErrorSnackBar('الرجاء إضافة صورة واحدة على الأقل للمنتج');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      List<String> finalImageUrls = List.from(_imageUrls);

      // Upload new images
      for (File imageFile in _pickedImages) {
        // استخدام CloudinaryService لرفع الصور
        final aiServices = AIServicesManager();
        final imageUrl = await aiServices.cloudinaryService.uploadImage(
          imageFile: imageFile,
          folder: 'products',
        );
        if (imageUrl.isNotEmpty) {
          finalImageUrls.add(imageUrl);
        } else {
          _showErrorSnackBar('فشل تحميل إحدى الصور');
          setState(() => _isLoading = false);
          return;
        }
      }

      final name = _nameController.text.trim();
      final description = _descriptionController.text.trim();
      final price = double.parse(_priceController.text.trim());
      final discountPriceText = _discountPriceController.text.trim();
      final discountPrice =
          discountPriceText.isNotEmpty ? double.parse(discountPriceText) : null;
      final quantity = int.parse(_quantityController.text.trim());
      final sku = _skuController.text.trim();
      final brand = _brandController.text.trim();

      // Check SKU uniqueness
      final skuQuery = _supabaseService.client
          .from('products')
          .select('id')
          .eq('sku', sku);
      final existingSku =
          widget.isEditing && widget.product != null
              ? await skuQuery.neq('id', widget.product!.id).maybeSingle()
              : await skuQuery.maybeSingle();

      if (existingSku != null) {
        _showErrorSnackBar('رمز المنتج (SKU) موجود مسبقاً');
        setState(() => _isLoading = false);
        return;
      }

      final productData = {
        'name': name,
        'description': description,
        'price': price,
        'discount_price': discountPrice,
        'quantity': quantity,
        'sku': sku,
        'brand': brand,

        'category_id': _selectedCategoryId,
        'is_on_sale': _isOnSale,
        'is_featured': _isFeatured,
        'images': finalImageUrls,
        'updated_at': DateTime.now().toIso8601String(),
      };

      bool success = false;
      if (widget.isEditing && widget.product != null) {
        await _supabaseService.client
            .from('products')
            .update(productData)
            .eq('id', widget.product!.id);

        if (widget.product!.quantity != quantity) {
          await _supabaseService.client.from('inventory_movements').insert({
            'product_id': widget.product!.id,
            'quantity_change': quantity - widget.product!.quantity,
            'reason': 'admin_adjustment',
            'notes': 'تعديل كمية المنتج من شاشة تعديل المنتج',
            'created_at': DateTime.now().toIso8601String(),
            'created_by': _supabaseService.currentUser?.id,
          });
        }
        success = true;
        _showSuccessSnackBar('تم تعديل المنتج بنجاح');
      } else {
        productData['created_at'] = DateTime.now().toIso8601String();
        final response =
            await _supabaseService.client
                .from('products')
                .insert(productData)
                .select();
        if (response.isNotEmpty) {
          final newProductId = response[0]['id'];
          await _supabaseService.client.from('inventory_movements').insert({
            'product_id': newProductId,
            'quantity_change': quantity,
            'reason': 'initial_stock',
            'notes': 'إضافة مخزون أولي للمنتج',
            'created_at': DateTime.now().toIso8601String(),
            'created_by': _supabaseService.currentUser?.id,
          });
          success = true;
          _showSuccessSnackBar('تم إضافة المنتج بنجاح');
          _resetForm();
        }
      }

      if (success && mounted) {
        Navigator.pop(context);
      } else {
        _showErrorSnackBar('فشل حفظ المنتج');
      }
    } catch (e) {
      _showErrorSnackBar('حدث خطأ أثناء حفظ المنتج: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _resetForm() {
    _formKey.currentState?.reset();
    _nameController.clear();
    _descriptionController.clear();
    _priceController.clear();
    _discountPriceController.clear();
    _quantityController.clear();
    _skuController.clear();
    _brandController.clear();
    setState(() {
      _selectedCategoryId = null;
      _isOnSale = false;
      _isFeatured = false;
      _imageUrls = [];
      _pickedImages = [];
    });
  }

  Widget _buildTextFormField({
    required TextEditingController controller,
    required String labelText,
    String? hintText,
    IconData? icon,
    TextInputType keyboardType = TextInputType.text,
    int maxLines = 1,
    String? Function(String?)? validator,
    String? prefixText,
  }) {
    return TextFormField(
      controller: controller,
      decoration: InputDecoration(
        labelText: labelText,
        hintText: hintText,
        prefixIcon:
            icon != null
                ? Icon(icon, color: AppTheme.primaryColor, size: 22)
                : null,
        prefixText: prefixText,
        border: OutlineInputBorder(
          borderRadius: AppTheme.borderRadiusMedium,
          borderSide: BorderSide(color: AppTheme.dividerColor),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: AppTheme.borderRadiusMedium,
          borderSide: BorderSide(color: AppTheme.dividerColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: AppTheme.borderRadiusMedium,
          borderSide: const BorderSide(color: AppTheme.accentColor, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: AppTheme.borderRadiusMedium,
          borderSide: const BorderSide(color: AppTheme.errorColor, width: 1.5),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: AppTheme.borderRadiusMedium,
          borderSide: const BorderSide(color: AppTheme.errorColor, width: 2),
        ),
        filled: true,
        fillColor: AppTheme.lightGreyColor.withOpacity(0.7),
        contentPadding: const EdgeInsets.symmetric(
          vertical: 16,
          horizontal: 12,
        ),
      ),
      keyboardType: keyboardType,
      maxLines: maxLines,
      validator: validator,
      style: Theme.of(context).textTheme.bodyLarge,
      textInputAction: TextInputAction.next,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: widget.isEditing ? 'تعديل منتج' : 'إضافة منتج جديد',
        showBackButton: true,
      ),
      body:
          _isLoading && _categories.isEmpty
              ? const Center(child: LoadingIndicator())
              : SlideTransition(
                position: _slideAnimation,
                child: FadeTransition(
                  opacity: _fadeAnimation,
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(20.0),
                    child: Form(
                      key: _formKey,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          Text(
                            widget.isEditing
                                ? 'تعديل تفاصيل المنتج'
                                : 'إضافة منتج جديد',
                            style: Theme.of(context).textTheme.headlineMedium!
                                .copyWith(color: AppTheme.primaryColor),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 24),

                          // اسم المنتج
                          _buildTextFormField(
                            controller: _nameController,
                            labelText: 'اسم المنتج *',
                            hintText: 'مثال: خوذة دراجة نارية',
                            icon: Icons.label_important_outline_rounded,
                            validator: (value) {
                              if (value == null || value.trim().isEmpty) {
                                return 'الرجاء إدخال اسم المنتج';
                              }
                              return null;
                            },
                          ),
                          const SizedBox(height: 16),

                          // وصف المنتج
                          _buildTextFormField(
                            controller: _descriptionController,
                            labelText: 'وصف المنتج *',
                            hintText: 'مثال: خوذة متينة بتصميم عصري',
                            icon: Icons.description_outlined,
                            maxLines: 3,
                            validator: (value) {
                              if (value == null || value.trim().isEmpty) {
                                return 'الرجاء إدخال وصف المنتج';
                              }
                              return null;
                            },
                          ),
                          const SizedBox(height: 16),

                          // السعر والخصم
                          Row(
                            children: [
                              Expanded(
                                child: _buildTextFormField(
                                  controller: _priceController,
                                  labelText: 'السعر *',
                                  hintText: 'مثال: 150.00',
                                  icon: Icons.monetization_on_outlined,
                                  keyboardType: TextInputType.number,
                                  prefixText: 'ر.س ',
                                  validator: (value) {
                                    if (value == null || value.trim().isEmpty) {
                                      return 'الرجاء إدخال السعر';
                                    }
                                    if (double.tryParse(value) == null) {
                                      return 'الرجاء إدخال سعر صحيح';
                                    }
                                    return null;
                                  },
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: _buildTextFormField(
                                  controller: _discountPriceController,
                                  labelText: 'سعر الخصم',
                                  hintText: 'مثال: 120.00',
                                  icon: Icons.discount_outlined,
                                  keyboardType: TextInputType.number,
                                  prefixText: 'ر.س ',
                                  validator: (value) {
                                    if (value != null &&
                                        value.trim().isNotEmpty) {
                                      final discount = double.tryParse(value);
                                      if (discount == null) {
                                        return 'الرجاء إدخال سعر صحيح';
                                      }
                                      final price =
                                          double.tryParse(
                                            _priceController.text,
                                          ) ??
                                          0;
                                      if (discount >= price) {
                                        return 'يجب أن يكون سعر الخصم أقل من السعر الأصلي';
                                      }
                                    }
                                    return null;
                                  },
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),

                          // الكمية والرمز
                          Row(
                            children: [
                              Expanded(
                                child: _buildTextFormField(
                                  controller: _quantityController,
                                  labelText: 'الكمية *',
                                  hintText: 'مثال: 50',
                                  icon: Icons.inventory_2_outlined,
                                  keyboardType: TextInputType.number,
                                  validator: (value) {
                                    if (value == null || value.trim().isEmpty) {
                                      return 'الرجاء إدخال الكمية';
                                    }
                                    if (int.tryParse(value) == null) {
                                      return 'الرجاء إدخال كمية صحيحة';
                                    }
                                    return null;
                                  },
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: _buildTextFormField(
                                  controller: _skuController,
                                  labelText: 'رمز المنتج (SKU) *',
                                  hintText: 'مثال: HELM-001',
                                  icon: Icons.qr_code_2_outlined,
                                  validator: (value) {
                                    if (value == null || value.trim().isEmpty) {
                                      return 'الرجاء إدخال رمز المنتج';
                                    }
                                    return null;
                                  },
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),

                          // العلامة التجارية
                          _buildTextFormField(
                            controller: _brandController,
                            labelText: 'العلامة التجارية *',
                            hintText: 'مثال: Yamaha',
                            icon: Icons.branding_watermark_outlined,
                            validator: (value) {
                              if (value == null || value.trim().isEmpty) {
                                return 'الرجاء إدخال العلامة التجارية';
                              }
                              return null;
                            },
                          ),
                          const SizedBox(height: 16),

                          // الفئة
                          DropdownButtonFormField<String>(
                            decoration: InputDecoration(
                              labelText: 'الفئة *',
                              prefixIcon: const Icon(
                                Icons.category_outlined,
                                color: AppTheme.primaryColor,
                                size: 22,
                              ),
                              border: OutlineInputBorder(
                                borderRadius: AppTheme.borderRadiusMedium,
                                borderSide: BorderSide(
                                  color: AppTheme.dividerColor,
                                ),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: AppTheme.borderRadiusMedium,
                                borderSide: BorderSide(
                                  color: AppTheme.dividerColor,
                                ),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: AppTheme.borderRadiusMedium,
                                borderSide: const BorderSide(
                                  color: AppTheme.accentColor,
                                  width: 2,
                                ),
                              ),
                              errorBorder: OutlineInputBorder(
                                borderRadius: AppTheme.borderRadiusMedium,
                                borderSide: const BorderSide(
                                  color: AppTheme.errorColor,
                                  width: 1.5,
                                ),
                              ),
                              focusedErrorBorder: OutlineInputBorder(
                                borderRadius: AppTheme.borderRadiusMedium,
                                borderSide: const BorderSide(
                                  color: AppTheme.errorColor,
                                  width: 2,
                                ),
                              ),
                              filled: true,
                              fillColor: AppTheme.lightGreyColor.withOpacity(
                                0.7,
                              ),
                            ),
                            value: _selectedCategoryId,
                            items:
                                _categories.map((category) {
                                  return DropdownMenuItem<String>(
                                    value: category.id,
                                    child: Text(
                                      category.name,
                                      style:
                                          Theme.of(context).textTheme.bodyLarge,
                                    ),
                                  );
                                }).toList(),
                            onChanged: (value) {
                              setState(() {
                                _selectedCategoryId = value;
                              });
                            },
                            validator: (value) {
                              if (value == null) {
                                return 'الرجاء اختيار فئة';
                              }
                              return null;
                            },
                          ),
                          const SizedBox(height: 24),

                          // خيارات إضافية
                          Row(
                            children: [
                              Expanded(
                                child: SwitchListTile.adaptive(
                                  title: Text(
                                    'عرض خاص',
                                    style: Theme.of(context)
                                        .textTheme
                                        .bodyLarge!
                                        .copyWith(fontWeight: FontWeight.w500),
                                  ),
                                  value: _isOnSale,
                                  onChanged: (value) {
                                    setState(() => _isOnSale = value);
                                  },
                                  activeColor: AppTheme.accentColor,
                                  contentPadding: const EdgeInsets.symmetric(
                                    horizontal: 4,
                                  ),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: AppTheme.borderRadiusMedium,
                                  ),
                                  tileColor: AppTheme.lightGreyColor
                                      .withOpacity(0.5),
                                ),
                              ),
                              Expanded(
                                child: SwitchListTile.adaptive(
                                  title: Text(
                                    'منتج مميز',
                                    style: Theme.of(context)
                                        .textTheme
                                        .bodyLarge!
                                        .copyWith(fontWeight: FontWeight.w500),
                                  ),
                                  value: _isFeatured,
                                  onChanged: (value) {
                                    setState(() => _isFeatured = value);
                                  },
                                  activeColor: AppTheme.accentColor,
                                  contentPadding: const EdgeInsets.symmetric(
                                    horizontal: 4,
                                  ),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: AppTheme.borderRadiusMedium,
                                  ),
                                  tileColor: AppTheme.lightGreyColor
                                      .withOpacity(0.5),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 24),

                          // صور المنتج
                          Text(
                            'صور المنتج *',
                            style: Theme.of(context).textTheme.titleLarge!
                                .copyWith(color: AppTheme.textPrimaryColor),
                          ),
                          const SizedBox(height: 8),
                          ElevatedButton.icon(
                            onPressed: _isLoading ? null : _pickImages,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppTheme.primaryColor,
                              foregroundColor: AppTheme.textLightColor,
                              padding: const EdgeInsets.symmetric(
                                vertical: 12,
                                horizontal: 16,
                              ),
                              shape: RoundedRectangleBorder(
                                borderRadius: AppTheme.borderRadiusMedium,
                              ),
                            ),
                            icon: const Icon(
                              Icons.add_a_photo_outlined,
                              size: 22,
                            ),
                            label: const Text('إضافة صور'),
                          ),
                          const SizedBox(height: 16),

                          // عرض الصور الموجودة والمختارة
                          if (_imageUrls.isNotEmpty || _pickedImages.isNotEmpty)
                            SizedBox(
                              height: 120,
                              child: ListView.builder(
                                scrollDirection: Axis.horizontal,
                                shrinkWrap: true,
                                itemCount:
                                    _imageUrls.length + _pickedImages.length,
                                itemBuilder: (context, index) {
                                  final isPickedImage =
                                      index >= _imageUrls.length;
                                  final imageIndex =
                                      isPickedImage
                                          ? index - _imageUrls.length
                                          : index;
                                  final image =
                                      isPickedImage
                                          ? _pickedImages[imageIndex]
                                          : _imageUrls[imageIndex];

                                  return Stack(
                                    children: [
                                      Container(
                                        margin: const EdgeInsets.only(right: 8),
                                        width: 100,
                                        height: 100,
                                        decoration: BoxDecoration(
                                          border: Border.all(
                                            color: AppTheme.textSecondaryColor,
                                          ),
                                          borderRadius:
                                              AppTheme.borderRadiusMedium,
                                        ),
                                        child: ClipRRect(
                                          borderRadius:
                                              AppTheme.borderRadiusMedium,
                                          child:
                                              isPickedImage
                                                  ? Image.file(
                                                    image as File,
                                                    fit: BoxFit.cover,
                                                    errorBuilder: (
                                                      context,
                                                      error,
                                                      stackTrace,
                                                    ) {
                                                      return const Center(
                                                        child: Icon(
                                                          Icons.error,
                                                        ),
                                                      );
                                                    },
                                                  )
                                                  : Image.network(
                                                    image as String,
                                                    fit: BoxFit.cover,
                                                    errorBuilder: (
                                                      context,
                                                      error,
                                                      stackTrace,
                                                    ) {
                                                      return const Center(
                                                        child: Icon(
                                                          Icons.error,
                                                        ),
                                                      );
                                                    },
                                                  ),
                                        ),
                                      ),
                                      Positioned(
                                        top: 0,
                                        right: 8,
                                        child: GestureDetector(
                                          onTap:
                                              () => _removeImage(
                                                index,
                                                isPickedImage,
                                              ),
                                          child: Container(
                                            padding: const EdgeInsets.all(2),
                                            decoration: const BoxDecoration(
                                              color: AppTheme.errorColor,
                                              shape: BoxShape.circle,
                                            ),
                                            child: const Icon(
                                              Icons.close,
                                              color: AppTheme.textLightColor,
                                              size: 16,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  );
                                },
                              ),
                            ),
                          const SizedBox(height: 32),

                          // أزرار الحفظ والإلغاء
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _isLoading ? null : _saveProduct,
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: AppTheme.primaryColor,
                                    padding: const EdgeInsets.symmetric(
                                      vertical: 16,
                                    ),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: AppTheme.borderRadiusMedium,
                                    ),
                                    elevation: _isLoading ? 0 : 4,
                                  ),
                                  icon:
                                      _isLoading
                                          ? const SizedBox(
                                            width: 24,
                                            height: 24,
                                            child: CircularProgressIndicator(
                                              color: AppTheme.textLightColor,
                                              strokeWidth: 3,
                                            ),
                                          )
                                          : const Icon(
                                            Icons.save_alt_rounded,
                                            color: AppTheme.textLightColor,
                                            size: 26,
                                          ),
                                  label: Text(
                                    _isLoading
                                        ? 'جاري الحفظ...'
                                        : widget.isEditing
                                        ? 'حفظ التعديلات'
                                        : 'إضافة المنتج',
                                    style: Theme.of(
                                      context,
                                    ).textTheme.labelLarge!.copyWith(
                                      color: AppTheme.textLightColor,
                                    ),
                                  ),
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: OutlinedButton(
                                  onPressed:
                                      _isLoading
                                          ? null
                                          : () {
                                            if (widget.isEditing) {
                                              Navigator.pop(context);
                                            } else {
                                              _resetForm();
                                            }
                                          },
                                  style: OutlinedButton.styleFrom(
                                    padding: const EdgeInsets.symmetric(
                                      vertical: 16,
                                    ),
                                    side: BorderSide(
                                      color: AppTheme.textSecondaryColor,
                                    ),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: AppTheme.borderRadiusMedium,
                                    ),
                                  ),
                                  child: Text(
                                    widget.isEditing ? 'إلغاء' : 'مسح',
                                    style: Theme.of(
                                      context,
                                    ).textTheme.labelLarge!.copyWith(
                                      color: AppTheme.textSecondaryColor,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
    );
  }
}
