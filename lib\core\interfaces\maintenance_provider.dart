
/// واجهة موحدة لمقدمي الصيانة
abstract class MaintenanceProvider {
  /// تنفيذ عملية الصيانة
  void performMaintenance();
  
  /// التحقق من صحة النظام
  bool get isHealthy;
  
  /// الحصول على حالة الصيانة
  MaintenanceStatus get maintenanceStatus;
  
  /// جدولة صيانة دورية
  void schedulePeriodicMaintenance(Duration interval) {
    // يمكن تنفيذها في الكلاسات المشتقة حسب الحاجة
  }
  
  /// تنظيف الموارد
  void cleanup() {
    performMaintenance();
  }
  
  /// فحص سريع للنظام
  bool quickHealthCheck() {
    try {
      return isHealthy;
    } catch (e) {
      return false;
    }
  }
}

/// حالات الصيانة المختلفة
enum MaintenanceStatus {
  healthy,
  needsMaintenance,
  critical,
  unknown,
}
