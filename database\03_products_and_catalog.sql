-- ===================================================================
-- جداول المنتجات والكتالوج
-- متجر قطع غيار الدراجات النارية - Supabase Database
-- ===================================================================

-- ===================================================================
-- 1. جدول الشركات المصنعة
-- ===================================================================
CREATE TABLE IF NOT EXISTS companies (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL UNIQUE,
    logo TEXT,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    products_count INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_companies_name ON companies(name);
CREATE INDEX IF NOT EXISTS idx_companies_is_active ON companies(is_active);

-- ===================================================================
-- 2. جدول الفئات
-- ===================================================================
CREATE TABLE IF NOT EXISTS categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    image_url TEXT,
    icon_data INTEGER,
    parent_id UUID REFERENCES categories(id) ON DELETE SET NULL,
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    products_count INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_categories_name ON categories(name);
CREATE INDEX IF NOT EXISTS idx_categories_parent_id ON categories(parent_id);
CREATE INDEX IF NOT EXISTS idx_categories_is_active ON categories(is_active);
CREATE INDEX IF NOT EXISTS idx_categories_sort_order ON categories(sort_order);

-- ===================================================================
-- 3. جدول المنتجات
-- ===================================================================
CREATE TABLE IF NOT EXISTS products (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    sku VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    brand VARCHAR(100),
    price DECIMAL(12,2) NOT NULL CHECK (price >= 0),
    discount_price DECIMAL(12,2) CHECK (discount_price >= 0 AND discount_price <= price),
    original_price DECIMAL(12,2),
    category_id UUID NOT NULL REFERENCES categories(id) ON DELETE RESTRICT,
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE RESTRICT,
    image_urls JSONB DEFAULT '[]'::jsonb,
    specifications JSONB DEFAULT '{}'::jsonb,
    stock_quantity INTEGER NOT NULL DEFAULT 0 CHECK (stock_quantity >= 0),
    min_stock_level INTEGER DEFAULT 5,
    weight DECIMAL(8,3),
    dimensions JSONB, -- {length, width, height}
    average_rating DECIMAL(3,2) DEFAULT 0.00 CHECK (average_rating >= 0 AND average_rating <= 5),
    review_count INTEGER DEFAULT 0,
    view_count INTEGER DEFAULT 0,
    sales_count INTEGER DEFAULT 0,
    is_featured BOOLEAN DEFAULT false,
    is_best_selling BOOLEAN DEFAULT false,
    is_available BOOLEAN DEFAULT true,
    is_on_sale BOOLEAN DEFAULT false,
    is_new BOOLEAN DEFAULT true,
    new_until TIMESTAMPTZ DEFAULT NOW() + INTERVAL '10 days',
    meta_title VARCHAR(255),
    meta_description TEXT,
    tags TEXT[],
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- فهارس للأداء والبحث
CREATE INDEX IF NOT EXISTS idx_products_sku ON products(sku);
CREATE INDEX IF NOT EXISTS idx_products_name ON products USING gin(name gin_trgm_ops);
CREATE INDEX IF NOT EXISTS idx_products_category_id ON products(category_id);
CREATE INDEX IF NOT EXISTS idx_products_company_id ON products(company_id);
CREATE INDEX IF NOT EXISTS idx_products_price ON products(price);
CREATE INDEX IF NOT EXISTS idx_products_is_featured ON products(is_featured);
CREATE INDEX IF NOT EXISTS idx_products_is_available ON products(is_available);
CREATE INDEX IF NOT EXISTS idx_products_is_new ON products(is_new);
CREATE INDEX IF NOT EXISTS idx_products_new_until ON products(new_until);
CREATE INDEX IF NOT EXISTS idx_products_average_rating ON products(average_rating);
CREATE INDEX IF NOT EXISTS idx_products_view_count ON products(view_count);
CREATE INDEX IF NOT EXISTS idx_products_sales_count ON products(sales_count);
CREATE INDEX IF NOT EXISTS idx_products_created_at ON products(created_at);
CREATE INDEX IF NOT EXISTS idx_products_tags ON products USING gin(tags);

-- فهارس مركبة للاستعلامات المعقدة
CREATE INDEX IF NOT EXISTS idx_products_category_price ON products(category_id, price);
CREATE INDEX IF NOT EXISTS idx_products_company_rating ON products(company_id, average_rating);
CREATE INDEX IF NOT EXISTS idx_products_featured_available ON products(is_featured, is_available);
CREATE INDEX IF NOT EXISTS idx_products_new_available ON products(is_new, is_available, created_at);

-- فهرس البحث النصي المتقدم
CREATE INDEX IF NOT EXISTS idx_products_search ON products USING gin(
    to_tsvector('arabic', COALESCE(name, '') || ' ' || COALESCE(description, '') || ' ' || COALESCE(brand, ''))
);

-- ===================================================================
-- 4. جدول مشاهدات المنتجات
-- ===================================================================
CREATE TABLE IF NOT EXISTS product_views (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    user_id UUID REFERENCES profiles(id) ON DELETE SET NULL,
    session_id VARCHAR(100),
    view_duration_seconds INTEGER,
    referrer_source VARCHAR(100), -- 'search', 'category', 'recommendation', 'direct'
    device_type VARCHAR(20),
    ip_address INET,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_product_views_product_id ON product_views(product_id);
CREATE INDEX IF NOT EXISTS idx_product_views_user_id ON product_views(user_id);
CREATE INDEX IF NOT EXISTS idx_product_views_created_at ON product_views(created_at);

-- ===================================================================
-- الدوال المساعدة
-- ===================================================================

-- دالة تحديث عدد المنتجات في الفئة
CREATE OR REPLACE FUNCTION update_category_product_count()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public, extensions
AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE categories SET products_count = products_count + 1 WHERE id = NEW.category_id;
        RETURN NEW;
    END IF;

    IF TG_OP = 'UPDATE' THEN
        IF OLD.category_id <> NEW.category_id THEN
            UPDATE categories SET products_count = products_count - 1 WHERE id = OLD.category_id;
            UPDATE categories SET products_count = products_count + 1 WHERE id = NEW.category_id;
        END IF;
        RETURN NEW;
    END IF;

    IF TG_OP = 'DELETE' THEN
        UPDATE categories SET products_count = products_count - 1 WHERE id = OLD.category_id;
        RETURN OLD;
    END IF;

    RETURN NULL;
END;
$$;

-- دالة تحديث إحصائيات المنتج
CREATE OR REPLACE FUNCTION update_product_stats()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public, extensions
AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        -- تحديث عدد المراجعات ومتوسط التقييم
        IF TG_TABLE_NAME = 'reviews' THEN
            UPDATE products SET
                review_count = (SELECT COUNT(*) FROM reviews WHERE product_id = NEW.product_id AND is_approved = true),
                average_rating = (SELECT COALESCE(AVG(rating), 0) FROM reviews WHERE product_id = NEW.product_id AND is_approved = true)
            WHERE id = NEW.product_id;
        END IF;

        -- تحديث عدد المبيعات
        IF TG_TABLE_NAME = 'order_items' THEN
            UPDATE products SET
                sales_count = sales_count + NEW.quantity
            WHERE id = NEW.product_id;
        END IF;
        RETURN NEW;
    END IF;

    IF TG_OP = 'UPDATE' THEN
        -- تحديث إحصائيات المراجعات عند التحديث
        IF TG_TABLE_NAME = 'reviews' THEN
            UPDATE products SET
                review_count = (SELECT COUNT(*) FROM reviews WHERE product_id = NEW.product_id AND is_approved = true),
                average_rating = (SELECT COALESCE(AVG(rating), 0) FROM reviews WHERE product_id = NEW.product_id AND is_approved = true)
            WHERE id = NEW.product_id;
        END IF;
        RETURN NEW;
    END IF;

    IF TG_OP = 'DELETE' THEN
        -- تحديث الإحصائيات عند الحذف
        IF TG_TABLE_NAME = 'reviews' THEN
            UPDATE products SET
                review_count = (SELECT COUNT(*) FROM reviews WHERE product_id = OLD.product_id AND is_approved = true),
                average_rating = (SELECT COALESCE(AVG(rating), 0) FROM reviews WHERE product_id = OLD.product_id AND is_approved = true)
            WHERE id = OLD.product_id;
        END IF;
        RETURN OLD;
    END IF;

    RETURN NULL;
END;
$$;

-- دالة إرسال إشعارات المنتجات الجديدة
CREATE OR REPLACE FUNCTION notify_new_product()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    v_user RECORD;
    v_product_name VARCHAR;
    v_company_name VARCHAR;
    v_category_name VARCHAR;
    v_notification_count INTEGER := 0;
BEGIN
    -- التأكد من أن المنتج جديد ومتاح
    IF NEW.is_new = true AND NEW.is_available = true THEN
        -- الحصول على بيانات المنتج
        SELECT NEW.name INTO v_product_name;

        SELECT c.name INTO v_company_name
        FROM companies c WHERE c.id = NEW.company_id;

        SELECT cat.name INTO v_category_name
        FROM categories cat WHERE cat.id = NEW.category_id;

        -- التحقق من وجود الجداول المطلوبة قبل إرسال الإشعارات
        IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'profiles') AND
           EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'notification_settings') THEN

            -- إرسال إشعار لجميع المستخدمين الذين فعلوا إشعارات المنتجات الجديدة
            FOR v_user IN (
                SELECT p.id, p.name
                FROM profiles p
                INNER JOIN notification_settings ns ON p.id = ns.user_id
                WHERE p.profile_type = 'customer'
                AND ns.new_products = true
                AND ns.push_notifications = true
            ) LOOP
                -- إرسال الإشعار
                BEGIN
                    PERFORM send_notification(
                        v_user.id,
                        'new_product',
                        jsonb_build_object(
                            'product_name', v_product_name,
                            'company_name', v_company_name,
                            'category_name', v_category_name,
                            'product_id', NEW.id::text,
                            'price', NEW.price::text
                        )
                    );
                    v_notification_count := v_notification_count + 1;
                EXCEPTION
                    WHEN OTHERS THEN
                        -- تجاهل الأخطاء في إرسال الإشعارات
                        NULL;
                END;
            END LOOP;
        END IF;

        -- تسجيل في السجل
        RAISE NOTICE 'تم إرسال % إشعار للمنتج الجديد: %', v_notification_count, v_product_name;
    END IF;

    RETURN NEW;
END;
$$;

-- دالة تحديث حالة المنتجات الجديدة (إزالة علامة "جديد" بعد انتهاء المدة)
CREATE OR REPLACE FUNCTION update_new_products_status()
RETURNS TABLE (
    updated_count INTEGER,
    cleanup_enabled BOOLEAN,
    next_cleanup TIMESTAMPTZ
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    v_updated_count INTEGER := 0;
    v_cleanup_enabled BOOLEAN;
    v_cleanup_interval INTEGER;
    v_next_cleanup TIMESTAMPTZ;
BEGIN
    -- التحقق من تفعيل التنظيف التلقائي
    BEGIN
        SELECT
            CASE WHEN setting_value = 1 THEN true ELSE false END
        INTO v_cleanup_enabled
        FROM new_product_settings
        WHERE setting_name = 'auto_cleanup_enabled' AND is_active = true;
    EXCEPTION
        WHEN OTHERS THEN
            v_cleanup_enabled := NULL;
    END;

    -- إذا لم توجد الإعدادات، استخدم القيم الافتراضية
    IF v_cleanup_enabled IS NULL THEN
        v_cleanup_enabled := true;
    END IF;

    -- تنفيذ التنظيف إذا كان مفعلاً
    IF v_cleanup_enabled THEN
        -- تحديث المنتجات التي انتهت مدة كونها "جديدة"
        UPDATE products
        SET is_new = false, updated_at = NOW()
        WHERE is_new = true
        AND new_until < NOW();

        GET DIAGNOSTICS v_updated_count = ROW_COUNT;

        -- تسجيل عملية التنظيف
        BEGIN
            INSERT INTO system_logs (
                log_type, message, details, created_at
            ) VALUES (
                'auto_cleanup',
                'تم تنظيف المنتجات الجديدة تلقائياً',
                jsonb_build_object(
                    'updated_products', v_updated_count,
                    'cleanup_time', NOW()
                ),
                NOW()
            );
        EXCEPTION
            WHEN OTHERS THEN
                -- تجاهل الخطأ إذا لم يكن جدول system_logs موجود
                NULL;
        END;
    END IF;

    -- حساب موعد التنظيف التالي
    BEGIN
        SELECT setting_value INTO v_cleanup_interval
        FROM new_product_settings
        WHERE setting_name = 'cleanup_interval_hours' AND is_active = true;
    EXCEPTION
        WHEN OTHERS THEN
            v_cleanup_interval := NULL;
    END;

    IF v_cleanup_interval IS NULL THEN
        v_cleanup_interval := 24;
    END IF;

    v_next_cleanup := NOW() + (v_cleanup_interval || ' hours')::INTERVAL;

    -- إرجاع النتائج
    updated_count := v_updated_count;
    cleanup_enabled := v_cleanup_enabled;
    next_cleanup := v_next_cleanup;
    RETURN NEXT;
END;
$$;

-- ===================================================================
-- المحفزات
-- ===================================================================

-- محفزات تحديث الطوابع الزمنية
CREATE TRIGGER update_companies_updated_at
    BEFORE UPDATE ON companies
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_categories_updated_at
    BEFORE UPDATE ON categories
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_products_updated_at
    BEFORE UPDATE ON products
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();



-- محفز إرسال إشعارات المنتجات الجديدة
CREATE TRIGGER notify_new_product_trigger
    AFTER INSERT ON products
    FOR EACH ROW
    EXECUTE FUNCTION notify_new_product();

-- ===================================================================
-- دوال التنظيف التلقائي للمنتجات الجديدة
-- ===================================================================

-- دالة التنظيف التلقائي البديل (بدون pg_cron)
CREATE OR REPLACE FUNCTION auto_cleanup_new_products()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    v_last_cleanup TIMESTAMPTZ;
    v_cleanup_interval INTEGER;
    v_should_cleanup BOOLEAN := false;
BEGIN
    -- التحقق من وجود الجداول المطلوبة
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'system_logs') THEN
        RETURN NEW;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'new_product_settings') THEN
        RETURN NEW;
    END IF;

    -- الحصول على آخر عملية تنظيف
    BEGIN
        SELECT MAX(created_at) INTO v_last_cleanup
        FROM system_logs
        WHERE log_type = 'auto_cleanup';
    EXCEPTION
        WHEN OTHERS THEN
            v_last_cleanup := NULL;
    END;

    -- الحصول على فترة التنظيف (افتراضي 24 ساعة)
    v_cleanup_interval := 24;

    BEGIN
        SELECT setting_value INTO v_cleanup_interval
        FROM new_product_settings
        WHERE setting_name = 'cleanup_interval_hours' AND is_active = true;
    EXCEPTION
        WHEN OTHERS THEN
            v_cleanup_interval := 24;
    END;

    -- التحقق من الحاجة للتنظيف
    IF v_last_cleanup IS NULL OR
       v_last_cleanup < NOW() - (v_cleanup_interval || ' hours')::INTERVAL THEN
        v_should_cleanup := true;
    END IF;

    -- تنفيذ التنظيف إذا كان مطلوباً
    IF v_should_cleanup THEN
        -- تحديث المنتجات التي انتهت مدة كونها "جديدة"
        UPDATE products
        SET is_new = false, updated_at = NOW()
        WHERE is_new = true
        AND new_until < NOW();

        -- تسجيل عملية التنظيف إذا كان الجدول موجود
        BEGIN
            INSERT INTO system_logs (
                log_type, message, details, created_at
            ) VALUES (
                'auto_cleanup',
                'تم تنظيف المنتجات الجديدة تلقائياً',
                jsonb_build_object(
                    'cleanup_time', NOW(),
                    'trigger_source', 'auto_cleanup_new_products'
                ),
                NOW()
            );
        EXCEPTION
            WHEN OTHERS THEN
                -- تجاهل الخطأ إذا لم يكن الجدول موجود
                NULL;
        END;
    END IF;

    RETURN NEW;
END;
$$;

-- محفز التنظيف التلقائي (سيتم تفعيله في setup_new_products_system.sql)
-- CREATE TRIGGER auto_cleanup_trigger
--     AFTER INSERT ON products
--     FOR EACH STATEMENT
--     EXECUTE FUNCTION auto_cleanup_new_products();

-- ===================================================================
-- رسالة نجاح
-- ===================================================================

DO $$
BEGIN
    RAISE NOTICE '✅ تم إنشاء جداول المنتجات والكتالوج بنجاح!';
    RAISE NOTICE '🏢 جدول الشركات المصنعة (companies)';
    RAISE NOTICE '📂 جدول الفئات (categories)';
    RAISE NOTICE '📦 جدول المنتجات (products)';
    RAISE NOTICE '👁️ جدول مشاهدات المنتجات (product_views)';
    RAISE NOTICE '🔍 تم إنشاء فهارس البحث المتقدمة';
END $$;
