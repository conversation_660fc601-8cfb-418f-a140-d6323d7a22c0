import 'package:motorcycle_parts_shop/models/coupon_model.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class CouponService {
  final SupabaseClient _client;
  final String _tableName = 'coupons';
  final String _usageTableName = 'coupon_usage';

  CouponService(this._client);

  Future<List<CouponModel>> getActiveCoupons() async {
    try {
      final response = await _client
          .from(_tableName)
          .select()
          .eq('is_active', true)
          .gte('end_date', DateTime.now().toIso8601String())
          .order('created_at', ascending: false);

      return response.map((json) => CouponModel.fromJson(json)).toList();
    } catch (e) {
      throw Exception('فشل في جلب الكوبونات النشطة: $e');
    }
  }

  Future<CouponModel?> validateCoupon(
    String code,
    double purchaseAmount,
  ) async {
    try {
      final response =
          await _client
              .from(_tableName)
              .select()
              .eq('code', code)
              .eq('is_active', true)
              .single();

      if (response == null) return null;

      final coupon = CouponModel.fromJson(response);
      if (!coupon.isValid) return null;

      if (coupon.minPurchaseAmount != null &&
          purchaseAmount < coupon.minPurchaseAmount!) {
        return null;
      }

      return coupon;
    } catch (e) {
      throw Exception('فشل في التحقق من الكوبون: $e');
    }
  }

  Future<bool> applyCoupon(String code, String orderId, String userId) async {
    try {
      final coupon = await validateCoupon(code, 0);
      if (coupon == null) return false;

      await _client.from(_usageTableName).insert({
        'coupon_id': coupon.id,
        'user_id': userId,
        'order_id': orderId,
        'discount_amount': 0, // سيتم تحديثه لاحقاً
      });

      await _client
          .from(_tableName)
          .update({'usage_count': coupon.usageCount + 1})
          .eq('id', coupon.id);

      return true;
    } catch (e) {
      throw Exception('فشل في تطبيق الكوبون: $e');
    }
  }

  Future<List<CouponModel>> getUserCoupons(String userId) async {
    try {
      final response = await _client
          .from(_usageTableName)
          .select('coupon_id')
          .eq('user_id', userId);

      if (response.isEmpty) return [];

      final couponIds = response.map((r) => r['coupon_id'] as String).toList();
      final coupons = await _client
          .from(_tableName)
          .select()
          .inFilter('id', couponIds)
          .order('created_at', ascending: false);

      return coupons.map((json) => CouponModel.fromJson(json)).toList();
    } catch (e) {
      throw Exception('فشل في جلب كوبونات المستخدم: $e');
    }
  }

  Future<void> createCoupon(CouponModel coupon) async {
    try {
      await _client.from(_tableName).insert(coupon.toJson());
    } catch (e) {
      throw Exception('فشل في إنشاء الكوبون: $e');
    }
  }

  Future<void> updateCoupon(CouponModel coupon) async {
    try {
      await _client
          .from(_tableName)
          .update(coupon.toJson())
          .eq('id', coupon.id);
    } catch (e) {
      throw Exception('فشل في تحديث الكوبون: $e');
    }
  }

  Future<void> deleteCoupon(String id) async {
    try {
      await _client.from(_tableName).delete().eq('id', id);
    } catch (e) {
      throw Exception('فشل في حذف الكوبون: $e');
    }
  }
}
