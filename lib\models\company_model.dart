
// نموذج بيانات الشركة المصنعة لقطع غيار الدراجات النارية
class CompanyModel {
  final String id; // معرف الشركة الفريد
  final String name; // اسم الشركة
  final String? logo; // رابط شعار الشركة (اختياري)
  final String? description; // وصف الشركة (اختياري)
  final bool isActive; // حالة نشاط الشركة
  final int productsCount; // عدد المنتجات للشركة
  final DateTime createdAt; // تاريخ إضافة الشركة
  final DateTime updatedAt; // تاريخ آخر تحديث للشركة

  /// المُنشئ الرئيسي لنموذج الشركة
  CompanyModel({
    required this.id,
    required this.name,
    this.logo,
    this.description,
    required this.isActive,
    this.productsCount = 0,
    required this.createdAt,
    required this.updatedAt,
  });

  // تحويل من JSON إلى كائن CompanyModel
  factory CompanyModel.fromJson(Map<String, dynamic> json) {
    return CompanyModel(
      id: json['id']?.toString() ?? '',
      name: json['name']?.toString() ?? '',
      logo: json['logo']?.toString(),
      description: json['description']?.toString(),
      isActive: json['is_active'] ?? true,
      productsCount: json['products_count'] ?? 0,
      createdAt:
          json['created_at'] != null
              ? DateTime.parse(json['created_at'])
              : DateTime.now(),
      updatedAt:
          json['updated_at'] != null
              ? DateTime.parse(json['updated_at'])
              : DateTime.now(),
    );
  }

  // تحويل كائن CompanyModel إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'logo': logo,
      'description': description,
      'is_active': isActive,
      'products_count': productsCount,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  // إنشاء نسخة معدلة من الكائن مع إمكانية تغيير بعض القيم
  CompanyModel copyWith({
    String? id,
    String? name,
    String? logo,
    String? description,
    bool? isActive,
    int? productsCount,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CompanyModel(
      id: id ?? this.id,
      name: name ?? this.name,
      logo: logo ?? this.logo,
      description: description ?? this.description,
      isActive: isActive ?? this.isActive,
      productsCount: productsCount ?? this.productsCount,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
