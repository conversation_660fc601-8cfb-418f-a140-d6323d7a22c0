
/// نموذج نتيجة تحليل Clarifai
class ClarifaiResult {
  final bool success;
  final String? error;
  final String? modelUsed;
  final int processingTime;
  final List<ConceptResult> concepts;
  final List<RegionResult> regions;
  final List<ColorResult> colors;
  final List<TextResult> textResults;
  final Map<String, dynamic>? rawResponse;
  final DateTime timestamp;

  ClarifaiResult({
    required this.success,
    this.error,
    this.modelUsed,
    required this.processingTime,
    this.concepts = const [],
    this.regions = const [],
    this.colors = const [],
    this.textResults = const [],
    this.rawResponse,
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'error': error,
      'model_used': modelUsed,
      'processing_time': processingTime,
      'concepts': concepts.map((c) => c.toJson()).toList(),
      'regions': regions.map((r) => r.to<PERSON>son()).toList(),
      'colors': colors.map((c) => c.toJson()).toList(),
      'text_results': textResults.map((t) => t.toJson()).toList(),
      'raw_response': rawResponse,
      'timestamp': timestamp.toIso8601String(),
    };
  }
}

/// نموذج مفهوم
class ConceptResult {
  final String id;
  final String name;
  final double confidence;
  final Map<String, dynamic>? metadata;

  ConceptResult({
    required this.id,
    required this.name,
    required this.confidence,
    this.metadata,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'confidence': confidence,
      'metadata': metadata,
    };
  }
}

/// نموذج منطقة
class RegionResult {
  final BoundingBox? boundingBox;
  final List<ConceptResult> concepts;
  final Map<String, dynamic>? metadata;

  RegionResult({
    this.boundingBox,
    this.concepts = const [],
    this.metadata,
  });

  Map<String, dynamic> toJson() {
    return {
      'bounding_box': boundingBox?.toJson(),
      'concepts': concepts.map((c) => c.toJson()).toList(),
      'metadata': metadata,
    };
  }
}

/// نموذج صندوق الحدود
class BoundingBox {
  final double topRow;
  final double leftCol;
  final double bottomRow;
  final double rightCol;

  BoundingBox({
    required this.topRow,
    required this.leftCol,
    required this.bottomRow,
    required this.rightCol,
  });

  Map<String, dynamic> toJson() {
    return {
      'top_row': topRow,
      'left_col': leftCol,
      'bottom_row': bottomRow,
      'right_col': rightCol,
    };
  }
}

/// نموذج لون
class ColorResult {
  final String hex;
  final String webSafeHex;
  final String webSafeColorName;
  final double percentage;

  ColorResult({
    required this.hex,
    required this.webSafeHex,
    required this.webSafeColorName,
    required this.percentage,
  });

  Map<String, dynamic> toJson() {
    return {
      'hex': hex,
      'web_safe_hex': webSafeHex,
      'web_safe_color_name': webSafeColorName,
      'percentage': percentage,
    };
  }
}

/// نموذج نص
class TextResult {
  final String text;
  final double confidence;
  final BoundingBox? boundingBox;

  TextResult({
    required this.text,
    required this.confidence,
    this.boundingBox,
  });

  Map<String, dynamic> toJson() {
    return {
      'text': text,
      'confidence': confidence,
      'bounding_box': boundingBox?.toJson(),
    };
  }
}

/// نماذج النتائج المتخصصة
class ObjectDetectionResult {
  final List<RegionResult> objects;
  final int processingTime;
  final bool success;

  ObjectDetectionResult({
    required this.objects,
    required this.processingTime,
    required this.success,
  });

  factory ObjectDetectionResult.fromClarifaiResult(ClarifaiResult result) {
    return ObjectDetectionResult(
      objects: result.regions,
      processingTime: result.processingTime,
      success: result.success,
    );
  }
}

class TextExtractionResult {
  final List<TextResult> texts;
  final int processingTime;
  final bool success;

  TextExtractionResult({
    required this.texts,
    required this.processingTime,
    required this.success,
  });

  factory TextExtractionResult.fromClarifaiResult(ClarifaiResult result) {
    return TextExtractionResult(
      texts: result.textResults,
      processingTime: result.processingTime,
      success: result.success,
    );
  }
}

class ColorAnalysisResult {
  final List<ColorResult> colors;
  final int processingTime;
  final bool success;

  ColorAnalysisResult({
    required this.colors,
    required this.processingTime,
    required this.success,
  });

  factory ColorAnalysisResult.fromClarifaiResult(ClarifaiResult result) {
    return ColorAnalysisResult(
      colors: result.colors,
      processingTime: result.processingTime,
      success: result.success,
    );
  }
}

class ModerationResult {
  final bool isAppropriate;
  final double confidence;
  final List<String> flags;
  final int processingTime;
  final bool success;

  ModerationResult({
    required this.isAppropriate,
    required this.confidence,
    required this.flags,
    required this.processingTime,
    required this.success,
  });

  factory ModerationResult.fromClarifaiResult(ClarifaiResult result) {
    final moderationConcepts = result.concepts
        .where((c) => c.confidence > 0.5)
        .toList();
    
    return ModerationResult(
      isAppropriate: moderationConcepts.isEmpty,
      confidence: moderationConcepts.isNotEmpty 
          ? moderationConcepts.first.confidence 
          : 1.0,
      flags: moderationConcepts.map((c) => c.name).toList(),
      processingTime: result.processingTime,
      success: result.success,
    );
  }
}
