import 'dart:io';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:motorcycle_parts_shop/models/inventory_movement_model.dart';
import 'package:motorcycle_parts_shop/models/product_model.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';

/// خدمة تقارير المخزون
/// تستخدم لإنشاء وتصدير تقارير المخزون بتنسيقات مختلفة
class InventoryReportService {
  /// إنشاء تقرير بجميع المنتجات
  Future<pw.Document> generateProductsReport(
    List<ProductModel> products,
  ) async {
    final pdf = pw.Document();

    // إضافة الترويسة
    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        build:
            (pw.Context context) => [
              _buildReportHeader('تقرير المنتجات'),
              pw.SizedBox(height: 20),
              _buildProductsTable(products),
            ],
        footer: (context) => _buildReportFooter(context),
      ),
    );

    return pdf;
  }

  /// إنشاء تقرير بالمنتجات منخفضة المخزون
  Future<pw.Document> generateLowStockReport(
    List<ProductModel> products,
  ) async {
    final pdf = pw.Document();

    // إضافة الترويسة
    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        build:
            (pw.Context context) => [
              _buildReportHeader('تقرير المنتجات منخفضة المخزون'),
              pw.SizedBox(height: 20),
              _buildProductsTable(products),
            ],
        footer: (context) => _buildReportFooter(context),
      ),
    );

    return pdf;
  }

  /// إنشاء تقرير بحركة المخزون
  Future<pw.Document> generateMovementsReport(
    List<InventoryMovementModel> movements,
    List<ProductModel> products,
  ) async {
    final pdf = pw.Document();

    // إضافة الترويسة
    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        build:
            (pw.Context context) => [
              _buildReportHeader('تقرير حركة المخزون'),
              pw.SizedBox(height: 20),
              _buildMovementsTable(movements, products),
            ],
        footer: (context) => _buildReportFooter(context),
      ),
    );

    return pdf;
  }

  /// بناء ترويسة التقرير
  pw.Widget _buildReportHeader(String title) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.center,
      children: [
        pw.Text(
          'متجر قطع غيار الدراجات النارية',
          style: pw.TextStyle(fontSize: 24, fontWeight: pw.FontWeight.bold),
          textDirection: pw.TextDirection.rtl,
        ),
        pw.SizedBox(height: 10),
        pw.Text(
          title,
          style: pw.TextStyle(fontSize: 18),
          textDirection: pw.TextDirection.rtl,
        ),
        pw.SizedBox(height: 5),
        pw.Text(
          'تاريخ التقرير: ${DateFormat('yyyy/MM/dd - HH:mm').format(DateTime.now())}',
          style: pw.TextStyle(fontSize: 14),
          textDirection: pw.TextDirection.rtl,
        ),
        pw.Divider(),
      ],
    );
  }

  /// بناء جدول المنتجات
  pw.Widget _buildProductsTable(List<ProductModel> products) {
    return pw.TableHelper.fromTextArray(
      headers: [
        'الكود',
        'اسم المنتج',
        'العلامة التجارية',
        'السعر',
        'الكمية',
        'الحالة',
      ],
      headerStyle: pw.TextStyle(fontWeight: pw.FontWeight.bold),
      headerDecoration: pw.BoxDecoration(color: PdfColors.grey300),
      data:
          products
              .map(
                (product) => [
                  product.sku,
                  product.name,
                  product.brand,
                  product.price.toString(),
                  product.quantity.toString(),
                  product.isAvailable ? 'متاح' : 'غير متاح',
                ],
              )
              .toList(),
      cellAlignment: pw.Alignment.center,
      cellStyle: pw.TextStyle(fontSize: 10),
      cellHeight: 30,
      cellAlignments: {
        0: pw.Alignment.centerRight,
        1: pw.Alignment.centerRight,
        2: pw.Alignment.centerRight,
        3: pw.Alignment.center,
        4: pw.Alignment.center,
        5: pw.Alignment.centerRight,
      },
    );
  }

  /// بناء جدول حركة المخزون
  pw.Widget _buildMovementsTable(
    List<InventoryMovementModel> movements,
    List<ProductModel> products,
  ) {
    return pw.TableHelper.fromTextArray(
      headers: ['المنتج', 'التغيير', 'السبب', 'الملاحظات', 'التاريخ'],
      headerStyle: pw.TextStyle(fontWeight: pw.FontWeight.bold),
      headerDecoration: pw.BoxDecoration(color: PdfColors.grey300),
      data:
          movements.map((movement) {
            // البحث عن المنتج المرتبط بالحركة
            final product = products.firstWhere(
              (p) => p.id == movement.productId,
              orElse:
                  () => ProductModel(
                    id: 'غير معروف',
                    name: 'منتج غير معروف',
                    description: '',
                    price: 0,
                    categoryId: '',
                    companyId: '',
                    images: [],
                    specifications: {},
                    quantity: 0,
                    rating: 0,
                    reviewsCount: 0,
                    viewCount: 0,
                    isFeatured: false,
                    isBestSelling: false,
                    isNew: false,
                    createdAt: DateTime.now(),
                    updatedAt: DateTime.now(),
                    sku: '',
                    brand: '',
                    isAvailable: false,
                  ),
            );

            return [
              product.name,
              '${movement.quantityChange > 0 ? "+" : ""}${movement.quantityChange}',
              movement.reason,
              movement.notes ?? '',
              DateFormat('yyyy/MM/dd - HH:mm').format(movement.createdAt),
            ];
          }).toList(),
      cellAlignment: pw.Alignment.center,
      cellStyle: pw.TextStyle(fontSize: 10),
      cellHeight: 30,
      cellAlignments: {
        0: pw.Alignment.centerRight,
        1: pw.Alignment.center,
        2: pw.Alignment.centerRight,
        3: pw.Alignment.centerRight,
        4: pw.Alignment.center,
      },
    );
  }

  /// بناء تذييل التقرير
  pw.Widget _buildReportFooter(pw.Context context) {
    return pw.Container(
      alignment: pw.Alignment.centerRight,
      margin: const pw.EdgeInsets.only(top: 10),
      child: pw.Text(
        'صفحة ${context.pageNumber} من ${context.pagesCount}',
        style: pw.TextStyle(fontSize: 10),
        textDirection: pw.TextDirection.rtl,
      ),
    );
  }

  /// طباعة التقرير
  Future<void> printReport(pw.Document pdf) async {
    await Printing.layoutPdf(
      onLayout: (PdfPageFormat format) async => pdf.save(),
    );
  }

  /// حفظ التقرير كملف PDF
  Future<String?> saveReportAsPdf(pw.Document pdf, String fileName) async {
    try {
      final output = await getTemporaryDirectory();
      final file = File('${output.path}/$fileName.pdf');
      await file.writeAsBytes(await pdf.save());
      return file.path;
    } catch (e) {
      debugPrint('خطأ في حفظ التقرير: $e');
      return null;
    }
  }

  /// تصدير التقرير كملف CSV
  Future<String?> exportProductsAsCsv(
    List<ProductModel> products,
    String fileName,
  ) async {
    try {
      final output = await getTemporaryDirectory();
      final file = File('${output.path}/$fileName.csv');

      // إنشاء محتوى CSV
      final csvContent = StringBuffer();

      // إضافة رأس الجدول
      csvContent.writeln(
        'الكود,اسم المنتج,العلامة التجارية,السعر,الكمية,الحالة',
      );

      // إضافة بيانات المنتجات
      for (final product in products) {
        csvContent.writeln(
          '${product.sku},${product.name},${product.brand},${product.price},${product.quantity},${product.isAvailable ? "متاح" : "غير متاح"}',
        );
      }

      // كتابة المحتوى إلى الملف
      await file.writeAsString(csvContent.toString());
      return file.path;
    } catch (e) {
      debugPrint('خطأ في تصدير التقرير: $e');
      return null;
    }
  }
}
