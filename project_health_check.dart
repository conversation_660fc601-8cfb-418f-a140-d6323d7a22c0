// ===================================================================
// فحص شامل لحالة المشروع
// متجر قطع غيار الدراجات النارية
// ===================================================================

import 'dart:io';

import 'package:http/http.dart' as http;

/// فئة فحص حالة المشروع الشاملة
class ProjectHealthCheck {
  static const String _version = '1.0.0';

  /// تشغيل فحص شامل للمشروع
  static Future<Map<String, dynamic>> runFullHealthCheck() async {
    print('🔍 بدء الفحص الشامل للمشروع...');
    print('📅 التاريخ: ${DateTime.now()}');
    print('🔢 الإصدار: $_version');
    print('');

    final results = <String, dynamic>{
      'timestamp': DateTime.now().toIso8601String(),
      'version': _version,
      'overall_status': 'unknown',
      'checks': <String, dynamic>{},
    };

    try {
      // 1. فحص ملف البيئة
      results['checks']['environment'] = await _checkEnvironmentFile();

      // 2. فحص الاتصال بـ Supabase
      results['checks']['supabase'] = await _checkSupabaseConnection();

      // 3. فحص قاعدة البيانات
      results['checks']['database'] = await _checkDatabaseStructure();

      // 4. فحص Cloudinary
      results['checks']['cloudinary'] = await _checkCloudinaryConnection();

      // 5. فحص Clarifai
      results['checks']['clarifai'] = await _checkClarifaiConnection();

      // 6. فحص Google AI
      results['checks']['google_ai'] = await _checkGoogleAIConnection();

      // 7. فحص التبعيات
      results['checks']['dependencies'] = await _checkDependencies();

      // 8. فحص الملفات المطلوبة
      results['checks']['required_files'] = await _checkRequiredFiles();

      // تحديد الحالة العامة
      results['overall_status'] = _calculateOverallStatus(results['checks']);

      // طباعة التقرير
      _printHealthReport(results);
    } catch (e) {
      print('❌ خطأ في الفحص الشامل: $e');
      results['error'] = e.toString();
      results['overall_status'] = 'error';
    }

    return results;
  }

  /// فحص ملف البيئة
  static Future<Map<String, dynamic>> _checkEnvironmentFile() async {
    print('🔧 فحص ملف البيئة...');

    final result = <String, dynamic>{
      'status': 'unknown',
      'details': <String, dynamic>{},
    };

    try {
      final envFile = File('.env');

      if (!await envFile.exists()) {
        result['status'] = 'error';
        result['message'] = 'ملف .env غير موجود';
        return result;
      }

      final envContent = await envFile.readAsString();
      final requiredKeys = [
        'SUPABASE_URL',
        'SUPABASE_ANON_KEY',
        'CLOUDINARY_CLOUD_NAME',
        'CLOUDINARY_API_KEY',
        'CLARIFAI_API_KEY',
        'GOOGLE_AI_API_KEY',
      ];

      final missingKeys = <String>[];
      final presentKeys = <String>[];

      for (final key in requiredKeys) {
        final regex = RegExp('$key=(.+)');
        final match = regex.firstMatch(envContent);
        if (match != null &&
            match.group(1)!.trim().isNotEmpty &&
            !match.group(1)!.contains('your-')) {
          presentKeys.add(key);
        } else {
          missingKeys.add(key);
        }
      }

      result['details']['present_keys'] = presentKeys;
      result['details']['missing_keys'] = missingKeys;
      result['details']['total_required'] = requiredKeys.length;
      result['details']['present_count'] = presentKeys.length;

      if (missingKeys.isEmpty) {
        result['status'] = 'success';
        result['message'] = 'جميع المفاتيح موجودة';
      } else if (presentKeys.length >= requiredKeys.length * 0.7) {
        result['status'] = 'warning';
        result['message'] = 'بعض المفاتيح مفقودة: ${missingKeys.join(', ')}';
      } else {
        result['status'] = 'error';
        result['message'] = 'معظم المفاتيح مفقودة';
      }
    } catch (e) {
      result['status'] = 'error';
      result['message'] = 'خطأ في قراءة ملف البيئة: $e';
    }

    return result;
  }

  /// فحص الاتصال بـ Supabase
  static Future<Map<String, dynamic>> _checkSupabaseConnection() async {
    print('🔗 فحص الاتصال بـ Supabase...');

    final result = <String, dynamic>{
      'status': 'unknown',
      'details': <String, dynamic>{},
    };

    try {
      // محاولة الاتصال بـ Supabase
      final supabaseUrl = const String.fromEnvironment('SUPABASE_URL');
      final supabaseKey = const String.fromEnvironment('SUPABASE_ANON_KEY');

      if (supabaseUrl.isEmpty || supabaseKey.isEmpty) {
        result['status'] = 'error';
        result['message'] = 'إعدادات Supabase غير مكتملة في متغيرات البيئة';
        return result;
      }

      final response = await http
          .get(
            Uri.parse('$supabaseUrl/rest/v1/'),
            headers: {
              'apikey': supabaseKey,
              'Authorization': 'Bearer $supabaseKey',
            },
          )
          .timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        result['status'] = 'success';
        result['message'] = 'الاتصال بـ Supabase ناجح';
        result['details']['response_time'] = '< 10 ثواني';
      } else {
        result['status'] = 'error';
        result['message'] = 'فشل الاتصال بـ Supabase: ${response.statusCode}';
      }
    } catch (e) {
      result['status'] = 'error';
      result['message'] = 'خطأ في الاتصال بـ Supabase: $e';
    }

    return result;
  }

  /// فحص هيكل قاعدة البيانات
  static Future<Map<String, dynamic>> _checkDatabaseStructure() async {
    print('🗄️ فحص هيكل قاعدة البيانات...');

    final result = <String, dynamic>{
      'status': 'unknown',
      'details': <String, dynamic>{},
    };

    try {
      // قراءة إعدادات Supabase من ملف .env
      final envFile = File('.env');
      String supabaseUrl = '';
      String supabaseKey = '';

      if (await envFile.exists()) {
        final envContent = await envFile.readAsString();
        final lines = envContent.split('\n');

        for (final line in lines) {
          if (line.startsWith('SUPABASE_URL=')) {
            supabaseUrl = line.split('=')[1].trim();
          } else if (line.startsWith('SUPABASE_ANON_KEY=')) {
            supabaseKey = line.split('=')[1].trim();
          }
        }
      }

      if (supabaseUrl.isEmpty || supabaseKey.isEmpty) {
        result['status'] = 'error';
        result['message'] = 'إعدادات Supabase غير مكتملة';
        return result;
      }

      // فحص الجداول الأساسية باستخدام HTTP requests
      final requiredTables = [
        'profiles',
        'products',
        'categories',
        'orders',
        'order_items',
        'reviews',
        'notifications',
      ];

      final existingTables = <String>[];
      final missingTables = <String>[];

      for (final table in requiredTables) {
        try {
          final response = await http
              .get(
                Uri.parse('$supabaseUrl/rest/v1/$table?select=*&limit=1'),
                headers: {
                  'apikey': supabaseKey,
                  'Authorization': 'Bearer $supabaseKey',
                },
              )
              .timeout(const Duration(seconds: 5));

          if (response.statusCode == 200) {
            existingTables.add(table);
          } else {
            missingTables.add(table);
          }
        } catch (e) {
          missingTables.add(table);
        }
      }

      result['details']['existing_tables'] = existingTables;
      result['details']['missing_tables'] = missingTables;
      result['details']['total_required'] = requiredTables.length;
      result['details']['existing_count'] = existingTables.length;

      if (missingTables.isEmpty) {
        result['status'] = 'success';
        result['message'] = 'جميع الجداول موجودة';
      } else if (existingTables.length >= requiredTables.length * 0.8) {
        result['status'] = 'warning';
        result['message'] = 'بعض الجداول مفقودة: ${missingTables.join(', ')}';
      } else {
        result['status'] = 'error';
        result['message'] = 'معظم الجداول مفقودة';
      }
    } catch (e) {
      result['status'] = 'error';
      result['message'] = 'خطأ في فحص قاعدة البيانات: $e';
    }

    return result;
  }

  /// فحص الاتصال بـ Cloudinary
  static Future<Map<String, dynamic>> _checkCloudinaryConnection() async {
    print('☁️ فحص الاتصال بـ Cloudinary...');

    final result = <String, dynamic>{
      'status': 'unknown',
      'details': <String, dynamic>{},
    };

    try {
      // قراءة إعدادات Cloudinary من متغيرات البيئة
      const cloudName = String.fromEnvironment('CLOUDINARY_CLOUD_NAME');

      if (cloudName.isEmpty) {
        result['status'] = 'warning';
        result['message'] = 'إعدادات Cloudinary غير مكتملة';
        return result;
      }

      // اختبار الاتصال بـ Cloudinary
      final response = await http
          .get(
            Uri.parse(
              'https://res.cloudinary.com/$cloudName/image/upload/sample.jpg',
            ),
          )
          .timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        result['status'] = 'success';
        result['message'] = 'الاتصال بـ Cloudinary ناجح';
      } else {
        result['status'] = 'warning';
        result['message'] = 'Cloudinary متاح لكن قد تحتاج تكوين إضافي';
      }
    } catch (e) {
      result['status'] = 'warning';
      result['message'] = 'لا يمكن التحقق من Cloudinary: $e';
    }

    return result;
  }

  /// فحص الاتصال بـ Clarifai
  static Future<Map<String, dynamic>> _checkClarifaiConnection() async {
    print('🤖 فحص الاتصال بـ Clarifai...');

    final result = <String, dynamic>{
      'status': 'unknown',
      'details': <String, dynamic>{},
    };

    try {
      const apiKey = String.fromEnvironment('CLARIFAI_API_KEY');

      if (apiKey.isEmpty) {
        result['status'] = 'warning';
        result['message'] = 'مفتاح Clarifai غير موجود';
        return result;
      }

      // اختبار بسيط لـ Clarifai API
      final response = await http
          .get(
            Uri.parse('https://api.clarifai.com/v2/users/me'),
            headers: {
              'Authorization': 'Key $apiKey',
              'Content-Type': 'application/json',
            },
          )
          .timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        result['status'] = 'success';
        result['message'] = 'الاتصال بـ Clarifai ناجح';
      } else {
        result['status'] = 'error';
        result['message'] = 'فشل الاتصال بـ Clarifai: ${response.statusCode}';
      }
    } catch (e) {
      result['status'] = 'warning';
      result['message'] = 'لا يمكن التحقق من Clarifai: $e';
    }

    return result;
  }

  /// فحص Google AI
  static Future<Map<String, dynamic>> _checkGoogleAIConnection() async {
    print('🧠 فحص Google AI...');

    final result = <String, dynamic>{
      'status': 'unknown',
      'details': <String, dynamic>{},
    };

    const apiKey = String.fromEnvironment('GOOGLE_AI_API_KEY');

    if (apiKey.isEmpty) {
      result['status'] = 'warning';
      result['message'] = 'مفتاح Google AI غير موجود';
    } else {
      result['status'] = 'success';
      result['message'] = 'مفتاح Google AI موجود';
    }

    return result;
  }

  /// فحص التبعيات
  static Future<Map<String, dynamic>> _checkDependencies() async {
    print('📦 فحص التبعيات...');

    final result = <String, dynamic>{
      'status': 'success',
      'message': 'التبعيات متاحة في وقت التشغيل',
      'details': <String, dynamic>{},
    };

    return result;
  }

  /// فحص الملفات المطلوبة
  static Future<Map<String, dynamic>> _checkRequiredFiles() async {
    print('📁 فحص الملفات المطلوبة...');

    final result = <String, dynamic>{
      'status': 'unknown',
      'details': <String, dynamic>{},
    };

    final requiredFiles = [
      'pubspec.yaml',
      'lib/main.dart',
      'lib/core/services/auth_supabase_service.dart',
      'lib/core/services/product_service.dart',
      'lib/models/product_model.dart',
      'lib/models/user_model.dart',
    ];

    final existingFiles = <String>[];
    final missingFiles = <String>[];

    for (final filePath in requiredFiles) {
      final file = File(filePath);
      if (await file.exists()) {
        existingFiles.add(filePath);
      } else {
        missingFiles.add(filePath);
      }
    }

    result['details']['existing_files'] = existingFiles;
    result['details']['missing_files'] = missingFiles;
    result['details']['total_required'] = requiredFiles.length;
    result['details']['existing_count'] = existingFiles.length;

    if (missingFiles.isEmpty) {
      result['status'] = 'success';
      result['message'] = 'جميع الملفات المطلوبة موجودة';
    } else {
      result['status'] = 'warning';
      result['message'] = 'بعض الملفات مفقودة: ${missingFiles.join(', ')}';
    }

    return result;
  }

  /// حساب الحالة العامة
  static String _calculateOverallStatus(Map<String, dynamic> checks) {
    int successCount = 0;
    int warningCount = 0;
    int errorCount = 0;
    int totalCount = checks.length;

    for (final check in checks.values) {
      switch (check['status']) {
        case 'success':
          successCount++;
          break;
        case 'warning':
          warningCount++;
          break;
        case 'error':
          errorCount++;
          break;
      }
    }

    if (errorCount > 0) {
      return 'error';
    } else if (warningCount > 0) {
      return 'warning';
    } else if (successCount == totalCount) {
      return 'success';
    } else {
      return 'unknown';
    }
  }

  /// طباعة تقرير الحالة
  static void _printHealthReport(Map<String, dynamic> results) {
    print('');
    print('📊 تقرير حالة المشروع');
    print('=' * 50);

    final overallStatus = results['overall_status'];
    final statusIcon =
        overallStatus == 'success'
            ? '✅'
            : overallStatus == 'warning'
            ? '⚠️'
            : overallStatus == 'error'
            ? '❌'
            : '❓';

    print('$statusIcon الحالة العامة: $overallStatus');
    print('');

    final checks = results['checks'] as Map<String, dynamic>;

    for (final entry in checks.entries) {
      final checkName = entry.key;
      final checkResult = entry.value as Map<String, dynamic>;
      final status = checkResult['status'];
      final message = checkResult['message'] ?? 'لا توجد رسالة';

      final icon =
          status == 'success'
              ? '✅'
              : status == 'warning'
              ? '⚠️'
              : status == 'error'
              ? '❌'
              : '❓';

      print('$icon $checkName: $message');

      if (checkResult.containsKey('details')) {
        final details = checkResult['details'] as Map<String, dynamic>;
        for (final detail in details.entries) {
          print('   • ${detail.key}: ${detail.value}');
        }
      }
      print('');
    }

    print('🎯 التوصيات:');
    if (overallStatus == 'success') {
      print('   ✅ المشروع جاهز للتشغيل!');
      print('   🚀 يمكنك تشغيل: flutter run');
    } else if (overallStatus == 'warning') {
      print('   ⚠️  المشروع يعمل لكن يحتاج تحسينات');
      print('   🔧 راجع التحذيرات أعلاه وأصلحها');
    } else {
      print('   ❌ المشروع يحتاج إصلاحات قبل التشغيل');
      print('   🔧 أصلح الأخطاء المذكورة أعلاه');
    }

    print('');
    print('📅 تاريخ الفحص: ${results['timestamp']}');
    print('🔢 إصدار الفحص: ${results['version']}');
    print('=' * 50);
  }
}

/// تشغيل فحص الصحة من سطر الأوامر
void main() async {
  await ProjectHealthCheck.runFullHealthCheck();
}
