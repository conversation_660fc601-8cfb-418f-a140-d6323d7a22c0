import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:motorcycle_parts_shop/core/theme/app_theme.dart';

/// ويجت تحميل الصور بشكل lazy مع تحسينات الأداء
class LazyImageWidget extends StatefulWidget {
  final String? imageUrl;
  final double? width;
  final double? height;
  final BoxFit fit;
  final BorderRadius? borderRadius;
  final Widget? placeholder;
  final Widget? errorWidget;
  final bool enableMemoryCache;
  final bool enableDiskCache;
  final Duration fadeInDuration;
  final Duration fadeOutDuration;
  final int? memCacheWidth;
  final int? memCacheHeight;
  final bool showLoadingProgress;

  const LazyImageWidget({
    super.key,
    this.imageUrl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.borderRadius,
    this.placeholder,
    this.errorWidget,
    this.enableMemoryCache = true,
    this.enableDiskCache = true,
    this.fadeInDuration = const Duration(milliseconds: 300),
    this.fadeOutDuration = const Duration(milliseconds: 100),
    this.memCacheWidth,
    this.memCacheHeight,
    this.showLoadingProgress = true,
  });

  @override
  State<LazyImageWidget> createState() => _LazyImageWidgetState();
}

class _LazyImageWidgetState extends State<LazyImageWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: widget.fadeInDuration,
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.imageUrl == null || widget.imageUrl!.isEmpty) {
      return _buildErrorWidget();
    }

    return ClipRRect(
      borderRadius: widget.borderRadius ?? BorderRadius.zero,
      child: SizedBox(
        width: widget.width,
        height: widget.height,
        child: CachedNetworkImage(
          imageUrl: widget.imageUrl!,
          width: widget.width,
          height: widget.height,
          fit: widget.fit,
          memCacheWidth: widget.memCacheWidth,
          memCacheHeight: widget.memCacheHeight,
          fadeInDuration: widget.fadeInDuration,
          fadeOutDuration: widget.fadeOutDuration,
          useOldImageOnUrlChange: true,
          placeholder: (context, url) => _buildPlaceholder(),
          errorWidget: (context, url, error) => _buildErrorWidget(),
          imageBuilder: (context, imageProvider) {
            _animationController.forward();
            return FadeTransition(
              opacity: _fadeAnimation,
              child: Container(
                decoration: BoxDecoration(
                  image: DecorationImage(
                    image: imageProvider,
                    fit: widget.fit,
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildPlaceholder() {
    if (widget.placeholder != null) {
      return widget.placeholder!;
    }

    return Container(
      width: widget.width,
      height: widget.height,
      decoration: BoxDecoration(
        gradient: AppTheme.cardGradient,
        borderRadius: widget.borderRadius,
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          if (widget.showLoadingProgress) ...[
            const CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
            ),
            const SizedBox(height: 8),
          ],
          Icon(
            Icons.image_outlined,
            color: AppTheme.textTertiaryColor,
            size: widget.width != null && widget.width! < 100 ? 24 : 48,
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget() {
    if (widget.errorWidget != null) {
      return widget.errorWidget!;
    }

    return Container(
      width: widget.width,
      height: widget.height,
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: widget.borderRadius,
        border: Border.all(
          color: AppTheme.borderColor,
          width: 1,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.broken_image_outlined,
            color: AppTheme.textTertiaryColor,
            size: widget.width != null && widget.width! < 100 ? 24 : 48,
          ),
          if (widget.height == null || widget.height! > 60) ...[
            const SizedBox(height: 8),
            Text(
              'فشل تحميل الصورة',
              style: TextStyle(
                color: AppTheme.textTertiaryColor,
                fontSize: 12,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }
}

/// ويجت تحميل الصور مع تأثيرات متقدمة
class AdvancedLazyImageWidget extends StatefulWidget {
  final String? imageUrl;
  final double? width;
  final double? height;
  final BoxFit fit;
  final BorderRadius? borderRadius;
  final bool showShimmer;
  final bool enableHeroAnimation;
  final String? heroTag;
  final VoidCallback? onTap;
  final bool enableZoom;

  const AdvancedLazyImageWidget({
    super.key,
    this.imageUrl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.borderRadius,
    this.showShimmer = true,
    this.enableHeroAnimation = false,
    this.heroTag,
    this.onTap,
    this.enableZoom = false,
  });

  @override
  State<AdvancedLazyImageWidget> createState() => _AdvancedLazyImageWidgetState();
}

class _AdvancedLazyImageWidgetState extends State<AdvancedLazyImageWidget>
    with TickerProviderStateMixin {
  late AnimationController _shimmerController;
  late Animation<double> _shimmerAnimation;

  @override
  void initState() {
    super.initState();
    if (widget.showShimmer) {
      _shimmerController = AnimationController(
        duration: const Duration(milliseconds: 1500),
        vsync: this,
      );
      _shimmerAnimation = Tween<double>(
        begin: -1.0,
        end: 2.0,
      ).animate(CurvedAnimation(
        parent: _shimmerController,
        curve: Curves.easeInOut,
      ));
      _shimmerController.repeat();
    }
  }

  @override
  void dispose() {
    if (widget.showShimmer) {
      _shimmerController.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    Widget imageWidget = LazyImageWidget(
      imageUrl: widget.imageUrl,
      width: widget.width,
      height: widget.height,
      fit: widget.fit,
      borderRadius: widget.borderRadius,
      placeholder: widget.showShimmer ? _buildShimmerPlaceholder() : null,
    );

    if (widget.enableHeroAnimation && widget.heroTag != null) {
      imageWidget = Hero(
        tag: widget.heroTag!,
        child: imageWidget,
      );
    }

    if (widget.onTap != null) {
      imageWidget = GestureDetector(
        onTap: widget.onTap,
        child: imageWidget,
      );
    }

    if (widget.enableZoom) {
      imageWidget = InteractiveViewer(
        panEnabled: true,
        scaleEnabled: true,
        minScale: 1.0,
        maxScale: 3.0,
        child: imageWidget,
      );
    }

    return imageWidget;
  }

  Widget _buildShimmerPlaceholder() {
    return AnimatedBuilder(
      animation: _shimmerAnimation,
      builder: (context, child) {
        return Container(
          width: widget.width,
          height: widget.height,
          decoration: BoxDecoration(
            borderRadius: widget.borderRadius,
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              stops: [
                _shimmerAnimation.value - 0.3,
                _shimmerAnimation.value,
                _shimmerAnimation.value + 0.3,
              ].map((stop) => stop.clamp(0.0, 1.0)).toList(),
              colors: [
                AppTheme.surfaceColor,
                AppTheme.surfaceColor.withOpacity(0.5),
                AppTheme.surfaceColor,
              ],
            ),
          ),
        );
      },
    );
  }
}

/// مدير تحميل الصور مع تحسينات الأداء
class ImageLoadingManager {
  static final ImageLoadingManager _instance = ImageLoadingManager._internal();
  factory ImageLoadingManager() => _instance;
  ImageLoadingManager._internal();

  final Map<String, bool> _loadingImages = {};
  final Map<String, DateTime> _lastLoadTime = {};
  static const Duration _loadCooldown = Duration(milliseconds: 500);

  /// التحقق من إمكانية تحميل الصورة
  bool canLoadImage(String imageUrl) {
    if (_loadingImages[imageUrl] == true) {
      return false;
    }

    final lastLoad = _lastLoadTime[imageUrl];
    if (lastLoad != null) {
      final timeSinceLastLoad = DateTime.now().difference(lastLoad);
      if (timeSinceLastLoad < _loadCooldown) {
        return false;
      }
    }

    return true;
  }

  /// تسجيل بداية تحميل الصورة
  void startLoading(String imageUrl) {
    _loadingImages[imageUrl] = true;
    _lastLoadTime[imageUrl] = DateTime.now();
  }

  /// تسجيل انتهاء تحميل الصورة
  void finishLoading(String imageUrl) {
    _loadingImages[imageUrl] = false;
  }

  /// مسح سجل التحميل
  void clearLoadingHistory() {
    _loadingImages.clear();
    _lastLoadTime.clear();
  }

  /// الحصول على إحصائيات التحميل
  Map<String, dynamic> getLoadingStats() {
    final currentlyLoading = _loadingImages.values.where((loading) => loading).length;
    final totalImages = _loadingImages.length;

    return {
      'currently_loading': currentlyLoading,
      'total_images': totalImages,
      'loading_percentage': totalImages > 0 ? (currentlyLoading / totalImages) * 100 : 0,
    };
  }
}
