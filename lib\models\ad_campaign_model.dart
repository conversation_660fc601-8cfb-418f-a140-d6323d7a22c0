
// نموذج بيانات الحملات الإعلانية
class AdCampaignModel {
  final String id; // معرف الحملة الفريد
  final String name; // اسم الحملة
  final String? description; // وصف الحملة (اختياري)
  final double? budget; // ميزانية الحملة (اختياري)
  final DateTime startDate; // تاريخ بدء الحملة
  final DateTime? endDate; // تاريخ انتهاء الحملة (اختياري)
  final bool isActive; // حالة نشاط الحملة
  final DateTime createdAt; // تاريخ إنشاء الحملة
  final DateTime updatedAt; // تاريخ آخر تحديث للحملة

  /// المُنشئ الرئيسي لنموذج الحملة الإعلانية
  AdCampaignModel({
    required this.id,
    required this.name,
    this.description,
    this.budget,
    required this.startDate,
    this.endDate,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
  });

  // تحويل من JSON إلى كائن AdCampaignModel
  factory AdCampaignModel.fromJson(Map<String, dynamic> json) {
    return AdCampaignModel(
      id: json['id']?.toString() ?? '',
      name: json['name']?.toString() ?? '',
      description: json['description']?.toString(),
      budget:
          json['budget'] != null ? (json['budget'] as num).toDouble() : null,
      startDate: DateTime.parse(json['start_date']),
      endDate:
          json['end_date'] != null ? DateTime.parse(json['end_date']) : null,
      isActive: json['is_active'] ?? false,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  // تحويل كائن AdCampaignModel إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'budget': budget,
      'start_date': startDate.toIso8601String(),
      'end_date': endDate?.toIso8601String(),
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  // إنشاء نسخة معدلة من الكائن مع إمكانية تغيير بعض القيم
  AdCampaignModel copyWith({
    String? id,
    String? name,
    String? description,
    double? budget,
    DateTime? startDate,
    DateTime? endDate,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return AdCampaignModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      budget: budget ?? this.budget,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  bool get isExpired => endDate != null && DateTime.now().isAfter(endDate!);

  bool get isActiveNow {
    final now = DateTime.now();
    return isActive &&
        now.isAfter(startDate) &&
        (endDate == null || now.isBefore(endDate!));
  }

  double? get remainingBudget {
    // يمكن إضافة منطق حساب الميزانية المتبقية هنا
    // يتطلب معرفة المصروفات الحالية للحملة
    return budget;
  }

  bool get hasBudget => budget != null && budget! > 0;
}
