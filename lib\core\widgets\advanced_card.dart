import 'package:flutter/material.dart';

/// بطاقة متقدمة مع تأثيرات بصرية ورسوم متحركة
/// توفر هذه البطاقة مظهرًا متسقًا في جميع أنحاء التطبيق
class AdvancedCard extends StatefulWidget {
  final Widget child; // المحتوى الذي يتم عرضه داخل البطاقة
  final double? width; // عرض البطاقة (اختياري)
  final double? height; // ارتفاع البطاقة (اختياري)
  final Color? backgroundColor; // لون خلفية البطاقة
  final Color? shadowColor; // لون الظل
  final double elevation; // ارتفاع الظل
  final double borderRadius; // نصف قطر حافة البطاقة
  final EdgeInsets padding; // الحشو الداخلي للبطاقة
  final EdgeInsets margin; // الهوامش الخارجية للبطاقة
  final bool showBorder; // عرض الحدود (إذا كان `true`)
  final Color? borderColor; // لون الحدود
  final double borderWidth; // عرض الحدود
  final GestureTapCallback? onTap; // دالة يتم استدعاؤها عند النقر على البطاقة
  final bool showShadow; // عرض الظل (إذا كان `true`)
  final bool showRipple; // عرض تأثير التموج عند النقر (إذا كان `true`)
  final Duration animationDuration; // مدة الرسوم المتحركة
  final Gradient? gradient; // تدرج لوني للخلفية
  final BoxShape shape; // شكل البطاقة (مستطيل أو دائري)
  final DecorationImage? image; // صورة خلفية للبطاقة
  final bool showHoverEffect; // عرض تأثير التحويم (إذا كان `true`)

  const AdvancedCard({
    super.key,
    required this.child,
    this.width,
    this.height,
    this.backgroundColor,
    this.shadowColor,
    this.elevation = 2.0,
    this.borderRadius = 16.0,
    this.padding = const EdgeInsets.all(16.0),
    this.margin = const EdgeInsets.all(0),
    this.showBorder = false,
    this.borderColor,
    this.borderWidth = 1.0,
    this.onTap,
    this.showShadow = true,
    this.showRipple = true,
    this.animationDuration = const Duration(milliseconds: 200),
    this.gradient,
    this.shape = BoxShape.rectangle,
    this.image,
    this.showHoverEffect = true,
  });

  @override
  State<AdvancedCard> createState() => _AdvancedCardState();
}

class _AdvancedCardState extends State<AdvancedCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController; // تحكم في الرسوم المتحركة
  late Animation<double> _scaleAnimation; // تحريك تغيير الحجم
  bool _isHovered = false; // حالة التحويم

  @override
  void initState() {
    super.initState();
    // تهيئة التحكم في الرسوم المتحركة
    _animationController = AnimationController(
      vsync: this,
      duration: widget.animationDuration,
    );
    // تعريف تحريك تغيير الحجم
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.98).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _animationController.dispose(); // التخلص من التحكم في الرسوم المتحركة
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    // عند الضغط على البطاقة
    if (widget.onTap != null) {
      _animationController.forward(); // تشغيل الرسوم المتحركة
    }
  }

  void _handleTapUp(TapUpDetails details) {
    // عند رفع الضغط عن البطاقة
    if (widget.onTap != null) {
      _animationController.reverse(); // عكس الرسوم المتحركة
    }
  }

  void _handleTapCancel() {
    // عند إلغاء الضغط
    if (widget.onTap != null) {
      _animationController.reverse(); // عكس الرسوم المتحركة
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    // تحديد الألوان بناءً على الثيم والخواص
    final bgColor = widget.backgroundColor ?? theme.cardColor;
    final shadow =
        widget.shadowColor ??
        (isDarkMode
            ? Colors.black.withOpacity(0.3)
            : Colors.black.withOpacity(0.1));
    final border = widget.borderColor ?? theme.dividerColor;

    // بناء محتوى البطاقة
    Widget cardContent = Container(
      width: widget.width,
      height: widget.height,
      padding: widget.padding,
      decoration:
          widget.shape == BoxShape.rectangle
              ? BoxDecoration(
                color: bgColor,
                borderRadius: BorderRadius.circular(widget.borderRadius),
                border:
                    widget.showBorder
                        ? Border.all(color: border, width: widget.borderWidth)
                        : null,
                boxShadow:
                    widget.showShadow
                        ? [
                          BoxShadow(
                            color: shadow,
                            blurRadius: widget.elevation * 2,
                            spreadRadius: widget.elevation * 0.2,
                            offset: Offset(0, widget.elevation * 0.5),
                          ),
                        ]
                        : null,
                gradient: widget.gradient,
                image: widget.image,
              )
              : BoxDecoration(
                color: bgColor,
                shape: BoxShape.circle,
                border:
                    widget.showBorder
                        ? Border.all(color: border, width: widget.borderWidth)
                        : null,
                boxShadow:
                    widget.showShadow
                        ? [
                          BoxShadow(
                            color: shadow,
                            blurRadius: widget.elevation * 2,
                            spreadRadius: widget.elevation * 0.2,
                            offset: Offset(0, widget.elevation * 0.5),
                          ),
                        ]
                        : null,
                gradient: widget.gradient,
                image: widget.image,
              ),
      child: widget.child,
    );

    // تطبيق تأثير التحويم إذا كان مفعلًا
    if (widget.showHoverEffect) {
      cardContent = MouseRegion(
        onEnter: (_) => setState(() => _isHovered = true),
        onExit: (_) => setState(() => _isHovered = false),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 150),
          transform:
              _isHovered
                  ? (Matrix4.identity()..translate(0, -4))
                  : Matrix4.identity(),
          child: cardContent,
        ),
      );
    }

    // تطبيق رسوم متحركة عند النقر إذا تم توفير `onTap`
    if (widget.onTap != null) {
      cardContent = AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(scale: _scaleAnimation.value, child: child);
        },
        child: GestureDetector(
          onTapDown: _handleTapDown,
          onTapUp: _handleTapUp,
          onTapCancel: _handleTapCancel,
          onTap: widget.onTap,
          child: cardContent,
        ),
      );
    }

    // تطبيق تأثير التموج إذا كان مفعلًا وتم توفير `onTap`
    if (widget.showRipple && widget.onTap != null) {
      cardContent = Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: widget.onTap,
          borderRadius:
              widget.shape == BoxShape.rectangle
                  ? BorderRadius.circular(widget.borderRadius)
                  : null,
          splashColor: theme.colorScheme.primary.withOpacity(0.1),
          highlightColor: theme.colorScheme.primary.withOpacity(0.05),
          child: cardContent,
        ),
      );
    }

    // تطبيق الهوامش الخارجية
    return Container(margin: widget.margin, child: cardContent);
  }
}

/// فئة لإنشاء أنماط بطاقات شائعة
class AdvancedCards {
  /// إنشاء بطاقة قياسية
  static AdvancedCard standard({
    required Widget child,
    double? width,
    double? height,
    EdgeInsets padding = const EdgeInsets.all(16.0),
    EdgeInsets margin = const EdgeInsets.all(0),
    double borderRadius = 16.0,
    double elevation = 2.0,
    GestureTapCallback? onTap,
    Color? backgroundColor,
    bool showShadow = true,
  }) {
    return AdvancedCard(
      width: width,
      height: height,
      padding: padding,
      margin: margin,
      borderRadius: borderRadius,
      elevation: elevation,
      onTap: onTap,
      backgroundColor: backgroundColor,
      showShadow: showShadow,
      child: child,
    );
  }

  /// إنشاء بطاقة مسطحة مع حدود
  static AdvancedCard flat({
    required Widget child,
    double? width,
    double? height,
    EdgeInsets padding = const EdgeInsets.all(16.0),
    EdgeInsets margin = const EdgeInsets.all(0),
    double borderRadius = 16.0,
    Color? borderColor,
    GestureTapCallback? onTap,
    Color? backgroundColor,
  }) {
    return AdvancedCard(
      width: width,
      height: height,
      padding: padding,
      margin: margin,
      borderRadius: borderRadius,
      showBorder: true,
      borderColor: borderColor,
      onTap: onTap,
      backgroundColor: backgroundColor,
      showShadow: false,
      child: child,
    );
  }

  /// إنشاء بطاقة بتدرج لوني
  static AdvancedCard gradient({
    required Widget child,
    required Gradient gradient,
    double? width,
    double? height,
    EdgeInsets padding = const EdgeInsets.all(16.0),
    EdgeInsets margin = const EdgeInsets.all(0),
    double borderRadius = 16.0,
    GestureTapCallback? onTap,
  }) {
    return AdvancedCard(
      width: width,
      height: height,
      padding: padding,
      margin: margin,
      borderRadius: borderRadius,
      gradient: gradient,
      onTap: onTap,
      showShadow: true,
      child: child,
    );
  }

  /// إنشاء بطاقة بصورة خلفية
  static AdvancedCard image({
    required Widget child,
    required DecorationImage image,
    double? width,
    double? height,
    EdgeInsets padding = const EdgeInsets.all(16.0),
    EdgeInsets margin = const EdgeInsets.all(0),
    double borderRadius = 16.0,
    GestureTapCallback? onTap,
  }) {
    return AdvancedCard(
      width: width,
      height: height,
      padding: padding,
      margin: margin,
      borderRadius: borderRadius,
      image: image,
      onTap: onTap,
      showShadow: true,
      child: child,
    );
  }

  /// إنشاء بطاقة دائرية
  static AdvancedCard circular({
    required Widget child,
    double size = 80.0,
    EdgeInsets padding = const EdgeInsets.all(8.0),
    EdgeInsets margin = const EdgeInsets.all(0),
    GestureTapCallback? onTap,
    Color? backgroundColor,
    bool showShadow = true,
  }) {
    return AdvancedCard(
      width: size,
      height: size,
      padding: padding,
      margin: margin,
      shape: BoxShape.circle,
      onTap: onTap,
      backgroundColor: backgroundColor,
      showShadow: showShadow,
      child: child,
    );
  }
}
