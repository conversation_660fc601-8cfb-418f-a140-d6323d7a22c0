import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// خدمة جلب الإحصائيات الفعلية من قاعدة البيانات
class RealStatsService {
  final SupabaseClient _supabase = Supabase.instance.client;

  /// جلب جميع الإحصائيات الفعلية
  Future<Map<String, dynamic>> getAllRealStats() async {
    try {
      debugPrint('🔍 بدء جلب الإحصائيات الفعلية...');
      
      final results = <String, dynamic>{};
      
      // جلب إحصائيات المنتجات
      results['products'] = await _getProductsStats();
      
      // جلب إحصائيات المستخدمين
      results['users'] = await _getUsersStats();
      
      // جلب إحصائيات الطلبات
      results['orders'] = await _getOrdersStats();
      
      // جلب إحصائيات الفئات
      results['categories'] = await _getCategoriesStats();
      
      // جلب إحصائيات الشركات
      results['companies'] = await _getCompaniesStats();
      
      // جلب إحصائيات المراجعات
      results['reviews'] = await _getReviewsStats();
      
      // جلب إحصائيات قوائم الرغبات
      results['wishlists'] = await _getWishlistsStats();
      
      // جلب إحصائيات سلة التسوق
      results['cart'] = await _getCartStats();
      
      // جلب إحصائيات الإشعارات
      results['notifications'] = await _getNotificationsStats();
      
      debugPrint('✅ تم جلب جميع الإحصائيات بنجاح');
      return results;
      
    } catch (e) {
      debugPrint('❌ خطأ في جلب الإحصائيات: $e');
      return {'error': e.toString()};
    }
  }

  /// إحصائيات المنتجات الفعلية
  Future<Map<String, dynamic>> _getProductsStats() async {
    try {
      final products = await _supabase.from('products').select('*');
      
      final totalProducts = products.length;
      final availableProducts = products.where((p) => p['is_available'] == true).length;
      final featuredProducts = products.where((p) => p['is_featured'] == true).length;
      
      // حساب متوسط التقييم
      final ratings = products
          .where((p) => p['average_rating'] != null)
          .map((p) => (p['average_rating'] as num).toDouble())
          .toList();
      final avgRating = ratings.isNotEmpty 
          ? ratings.reduce((a, b) => a + b) / ratings.length 
          : 0.0;
      
      // حساب إجمالي المخزون
      final totalStock = products
          .where((p) => p['stock_quantity'] != null)
          .map((p) => (p['stock_quantity'] as num).toInt())
          .fold(0, (sum, stock) => sum + stock);
      
      final outOfStock = products.where((p) => (p['stock_quantity'] ?? 0) == 0).length;
      final lowStock = products.where((p) {
        final stock = p['stock_quantity'] ?? 0;
        return stock > 0 && stock < 10;
      }).length;

      return {
        'total_products': totalProducts,
        'available_products': availableProducts,
        'featured_products': featuredProducts,
        'average_rating': double.parse(avgRating.toStringAsFixed(2)),
        'total_stock_quantity': totalStock,
        'out_of_stock': outOfStock,
        'low_stock': lowStock,
      };
    } catch (e) {
      debugPrint('خطأ في جلب إحصائيات المنتجات: $e');
      return {'error': e.toString()};
    }
  }

  /// إحصائيات المستخدمين الفعلية
  Future<Map<String, dynamic>> _getUsersStats() async {
    try {
      final users = await _supabase.from('profiles').select('*');
      
      final totalUsers = users.length;
      final activeUsers = users.where((u) => u['is_active'] == true).length;
      final adminUsers = users.where((u) => u['profile_type'] == 'admin').length;
      final customerUsers = users.where((u) => u['profile_type'] == 'customer').length;
      
      // المستخدمين الجدد في آخر 30 يوم
      final thirtyDaysAgo = DateTime.now().subtract(const Duration(days: 30));
      final newUsers = users.where((u) {
        if (u['created_at'] == null) return false;
        final createdAt = DateTime.parse(u['created_at']);
        return createdAt.isAfter(thirtyDaysAgo);
      }).length;

      return {
        'total_users': totalUsers,
        'active_users': activeUsers,
        'admin_users': adminUsers,
        'customer_users': customerUsers,
        'new_users_last_30_days': newUsers,
      };
    } catch (e) {
      debugPrint('خطأ في جلب إحصائيات المستخدمين: $e');
      return {'error': e.toString()};
    }
  }

  /// إحصائيات الطلبات الفعلية
  Future<Map<String, dynamic>> _getOrdersStats() async {
    try {
      final orders = await _supabase.from('orders').select('*');
      
      final totalOrders = orders.length;
      final pendingOrders = orders.where((o) => o['status'] == 'pending').length;
      final completedOrders = orders.where((o) => o['status'] == 'completed').length;
      final cancelledOrders = orders.where((o) => o['status'] == 'cancelled').length;
      
      // إجمالي المبيعات
      final revenue = orders
          .where((o) => o['total_amount'] != null)
          .map((o) => (o['total_amount'] as num).toDouble())
          .fold(0.0, (sum, amount) => sum + amount);

      return {
        'total_orders': totalOrders,
        'pending_orders': pendingOrders,
        'completed_orders': completedOrders,
        'cancelled_orders': cancelledOrders,
        'total_revenue': double.parse(revenue.toStringAsFixed(2)),
        'average_order_value': totalOrders > 0 
            ? double.parse((revenue / totalOrders).toStringAsFixed(2))
            : 0.0,
      };
    } catch (e) {
      debugPrint('خطأ في جلب إحصائيات الطلبات: $e');
      return {'error': e.toString()};
    }
  }

  /// إحصائيات الفئات الفعلية
  Future<Map<String, dynamic>> _getCategoriesStats() async {
    try {
      final categories = await _supabase.from('categories').select('*');
      
      final totalCategories = categories.length;
      final activeCategories = categories.where((c) => c['is_active'] == true).length;

      return {
        'total_categories': totalCategories,
        'active_categories': activeCategories,
      };
    } catch (e) {
      debugPrint('خطأ في جلب إحصائيات الفئات: $e');
      return {'error': e.toString()};
    }
  }

  /// إحصائيات الشركات الفعلية
  Future<Map<String, dynamic>> _getCompaniesStats() async {
    try {
      final companies = await _supabase.from('companies').select('*');
      
      final totalCompanies = companies.length;
      final activeCompanies = companies.where((c) => c['is_active'] == true).length;

      return {
        'total_companies': totalCompanies,
        'active_companies': activeCompanies,
      };
    } catch (e) {
      debugPrint('خطأ في جلب إحصائيات الشركات: $e');
      return {'error': e.toString()};
    }
  }

  /// إحصائيات المراجعات الفعلية
  Future<Map<String, dynamic>> _getReviewsStats() async {
    try {
      final reviews = await _supabase.from('reviews').select('*');
      
      final totalReviews = reviews.length;
      final approvedReviews = reviews.where((r) => r['is_approved'] == true).length;

      return {
        'total_reviews': totalReviews,
        'approved_reviews': approvedReviews,
      };
    } catch (e) {
      debugPrint('خطأ في جلب إحصائيات المراجعات: $e');
      return {'error': e.toString()};
    }
  }

  /// إحصائيات قوائم الرغبات الفعلية
  Future<Map<String, dynamic>> _getWishlistsStats() async {
    try {
      final wishlists = await _supabase.from('wishlists').select('*');
      return {'total_wishlist_items': wishlists.length};
    } catch (e) {
      debugPrint('خطأ في جلب إحصائيات قوائم الرغبات: $e');
      return {'error': e.toString()};
    }
  }

  /// إحصائيات سلة التسوق الفعلية
  Future<Map<String, dynamic>> _getCartStats() async {
    try {
      final cartItems = await _supabase.from('cart_items').select('*');
      return {'total_cart_items': cartItems.length};
    } catch (e) {
      debugPrint('خطأ في جلب إحصائيات سلة التسوق: $e');
      return {'error': e.toString()};
    }
  }

  /// إحصائيات الإشعارات الفعلية
  Future<Map<String, dynamic>> _getNotificationsStats() async {
    try {
      final notifications = await _supabase.from('notifications').select('*');
      return {'total_notifications': notifications.length};
    } catch (e) {
      return {'total_notifications': 0};
    }
  }

  /// فحص صلاحيات المدير
  Future<bool> checkAdminPermissions() async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) return false;

      final response = await _supabase
          .from('profiles')
          .select('profile_type')
          .eq('id', user.id)
          .single();

      return response['profile_type'] == 'admin';
    } catch (e) {
      debugPrint('خطأ في فحص صلاحيات المدير: $e');
      return false;
    }
  }
}
