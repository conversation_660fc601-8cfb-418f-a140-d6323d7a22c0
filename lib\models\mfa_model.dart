import 'package:json_annotation/json_annotation.dart';

part 'mfa_model.g.dart';

/// نموذج عامل المصادقة متعددة العوامل
@JsonSerializable()
class MFAFactor {
  final String id;
  final String userId;
  final MFAFactorType type;
  final String friendlyName;
  final MFAFactorStatus status;
  final DateTime createdAt;
  final DateTime? lastUsedAt;
  final Map<String, dynamic>? metadata;

  const MFAFactor({
    required this.id,
    required this.userId,
    required this.type,
    required this.friendlyName,
    required this.status,
    required this.createdAt,
    this.lastUsedAt,
    this.metadata,
  });

  factory MFAFactor.fromJson(Map<String, dynamic> json) =>
      _$MFAFactorFromJson(json);

  Map<String, dynamic> toJson() => _$MFAFactorToJson(this);

  MFAFactor copyWith({
    String? id,
    String? userId,
    MFAFactorType? type,
    String? friendlyName,
    MFAFactorStatus? status,
    DateTime? createdAt,
    DateTime? lastUsedAt,
    Map<String, dynamic>? metadata,
  }) {
    return MFAFactor(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      type: type ?? this.type,
      friendlyName: friendlyName ?? this.friendlyName,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      lastUsedAt: lastUsedAt ?? this.lastUsedAt,
      metadata: metadata ?? this.metadata,
    );
  }
}

/// أنواع عوامل MFA
enum MFAFactorType {
  @JsonValue('totp')
  totp, // Time-based One-Time Password (Google Authenticator)
  
  @JsonValue('sms')
  sms, // SMS Text Message
  
  @JsonValue('email')
  email, // Email OTP
  
  @JsonValue('biometric')
  biometric, // Fingerprint/Face ID
  
  @JsonValue('backup_codes')
  backupCodes, // Backup Recovery Codes
  
  @JsonValue('hardware_key')
  hardwareKey, // Hardware Security Key
}

/// حالات عامل MFA
enum MFAFactorStatus {
  @JsonValue('active')
  active,
  
  @JsonValue('inactive')
  inactive,
  
  @JsonValue('pending')
  pending,
  
  @JsonValue('suspended')
  suspended,
}

/// نموذج إعدادات MFA للمستخدم
@JsonSerializable()
class UserMFASettings {
  final String userId;
  final bool isEnabled;
  final bool isRequired;
  final MFAFactorType? primaryFactor;
  final List<String> enabledFactors;
  final List<String> backupCodes;
  final DateTime? lastUpdated;
  final Map<String, dynamic>? preferences;

  const UserMFASettings({
    required this.userId,
    required this.isEnabled,
    required this.isRequired,
    this.primaryFactor,
    required this.enabledFactors,
    required this.backupCodes,
    this.lastUpdated,
    this.preferences,
  });

  factory UserMFASettings.fromJson(Map<String, dynamic> json) =>
      _$UserMFASettingsFromJson(json);

  Map<String, dynamic> toJson() => _$UserMFASettingsToJson(this);

  UserMFASettings copyWith({
    String? userId,
    bool? isEnabled,
    bool? isRequired,
    MFAFactorType? primaryFactor,
    List<String>? enabledFactors,
    List<String>? backupCodes,
    DateTime? lastUpdated,
    Map<String, dynamic>? preferences,
  }) {
    return UserMFASettings(
      userId: userId ?? this.userId,
      isEnabled: isEnabled ?? this.isEnabled,
      isRequired: isRequired ?? this.isRequired,
      primaryFactor: primaryFactor ?? this.primaryFactor,
      enabledFactors: enabledFactors ?? this.enabledFactors,
      backupCodes: backupCodes ?? this.backupCodes,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      preferences: preferences ?? this.preferences,
    );
  }
}

/// نموذج جلسة MFA
@JsonSerializable()
class MFASession {
  final String id;
  final String userId;
  final String challengeId;
  final MFAFactorType factorType;
  final MFASessionStatus status;
  final DateTime createdAt;
  final DateTime expiresAt;
  final int attemptsRemaining;
  final Map<String, dynamic>? metadata;

  const MFASession({
    required this.id,
    required this.userId,
    required this.challengeId,
    required this.factorType,
    required this.status,
    required this.createdAt,
    required this.expiresAt,
    required this.attemptsRemaining,
    this.metadata,
  });

  factory MFASession.fromJson(Map<String, dynamic> json) =>
      _$MFASessionFromJson(json);

  Map<String, dynamic> toJson() => _$MFASessionToJson(this);

  bool get isExpired => DateTime.now().isAfter(expiresAt);
  bool get isActive => status == MFASessionStatus.pending && !isExpired;
}

/// حالات جلسة MFA
enum MFASessionStatus {
  @JsonValue('pending')
  pending,
  
  @JsonValue('verified')
  verified,
  
  @JsonValue('failed')
  failed,
  
  @JsonValue('expired')
  expired,
}

/// نموذج رمز النسخ الاحتياطي
@JsonSerializable()
class BackupCode {
  final String code;
  final bool isUsed;
  final DateTime? usedAt;

  const BackupCode({
    required this.code,
    required this.isUsed,
    this.usedAt,
  });

  factory BackupCode.fromJson(Map<String, dynamic> json) =>
      _$BackupCodeFromJson(json);

  Map<String, dynamic> toJson() => _$BackupCodeToJson(this);
}

/// نموذج تحدي MFA
class MFAChallenge {
  final String id;
  final MFAFactorType type;
  final String? qrCode; // للـ TOTP
  final String? secret; // للـ TOTP
  final String? phoneNumber; // للـ SMS
  final String? email; // للـ Email
  final DateTime expiresAt;
  final Map<String, dynamic>? metadata;

  const MFAChallenge({
    required this.id,
    required this.type,
    this.qrCode,
    this.secret,
    this.phoneNumber,
    this.email,
    required this.expiresAt,
    this.metadata,
  });

  bool get isExpired => DateTime.now().isAfter(expiresAt);
}

/// نموذج إحصائيات MFA
@JsonSerializable()
class MFAStats {
  final String userId;
  final int totalAttempts;
  final int successfulAttempts;
  final int failedAttempts;
  final DateTime? lastSuccessfulLogin;
  final DateTime? lastFailedAttempt;
  final Map<MFAFactorType, int> factorUsage;

  const MFAStats({
    required this.userId,
    required this.totalAttempts,
    required this.successfulAttempts,
    required this.failedAttempts,
    this.lastSuccessfulLogin,
    this.lastFailedAttempt,
    required this.factorUsage,
  });

  factory MFAStats.fromJson(Map<String, dynamic> json) =>
      _$MFAStatsFromJson(json);

  Map<String, dynamic> toJson() => _$MFAStatsToJson(this);

  double get successRate {
    if (totalAttempts == 0) return 0.0;
    return successfulAttempts / totalAttempts;
  }
}

/// استثناءات MFA
class MFAException implements Exception {
  final String message;
  final String? code;
  final Map<String, dynamic>? details;

  const MFAException(this.message, {this.code, this.details});

  @override
  String toString() => 'MFAException: $message';
}

/// أنواع أخطاء MFA
enum MFAErrorType {
  invalidCode,
  expiredCode,
  factorNotFound,
  factorAlreadyExists,
  tooManyAttempts,
  biometricNotAvailable,
  biometricNotEnrolled,
  networkError,
  serverError,
  userCancelled,
}
