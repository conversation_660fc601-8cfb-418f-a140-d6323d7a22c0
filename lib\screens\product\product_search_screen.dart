import 'package:flutter/material.dart';
import 'package:motorcycle_parts_shop/core/services/auth_supabase_service.dart';
import 'package:motorcycle_parts_shop/core/utils/responsive_helper.dart';
import 'package:motorcycle_parts_shop/core/widgets/custom_app_bar.dart';
import 'package:motorcycle_parts_shop/core/widgets/empty_state_widget.dart';
import 'package:motorcycle_parts_shop/core/widgets/loading_indicator.dart';
import 'package:motorcycle_parts_shop/models/category_model.dart';
import 'package:motorcycle_parts_shop/models/product_model.dart';
import 'package:postgrest/postgrest.dart';
import 'package:provider/provider.dart';

/// شاشة البحث عن المنتجات
/// تستخدم للبحث عن المنتجات وعرض نتائج البحث
class ProductSearchScreen extends StatefulWidget {
  final String? initialQuery;
  final String? categoryId;

  const ProductSearchScreen({super.key, this.initialQuery, this.categoryId});

  @override
  ProductSearchScreenState createState() => ProductSearchScreenState();
}

class ProductSearchScreenState extends State<ProductSearchScreen> {
  final TextEditingController _searchController = TextEditingController();
  List<ProductModel> _searchResults = [];
  List<CategoryModel> _categories = [];
  String? _selectedCategoryId;
  bool _isLoading = false;
  bool _hasSearched = false;
  String? _errorMessage;

  // خيارات الترتيب
  final List<String> _sortOptions = [
    'الأحدث',
    'الأقدم',
    'السعر: من الأعلى إلى الأقل',
    'السعر: من الأقل إلى الأعلى',
    'الاسم: أ-ي',
    'الاسم: ي-أ',
  ];
  String _selectedSortOption = 'الأحدث';

  // نطاق السعر
  RangeValues _priceRange = const RangeValues(0, 10000);
  RangeValues _currentPriceRange = const RangeValues(0, 10000);

  @override
  void initState() {
    super.initState();
    _loadCategories();

    // تعيين القيم الأولية إذا تم تمريرها
    if (widget.initialQuery != null && widget.initialQuery!.isNotEmpty) {
      _searchController.text = widget.initialQuery!;
    }

    if (widget.categoryId != null) {
      _selectedCategoryId = widget.categoryId;
    }

    // إجراء البحث الأولي إذا كان هناك استعلام أو فئة
    if ((widget.initialQuery != null && widget.initialQuery!.isNotEmpty) ||
        widget.categoryId != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _searchProducts();
      });
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  /// تحميل الفئات من قاعدة البيانات
  Future<void> _loadCategories() async {
    try {
      final supabaseService = Provider.of<AuthSupabaseService>(
        context,
        listen: false,
      );
      final categories = await supabaseService.getCategories();
      setState(() {
        _categories = categories;
      });
    } catch (e) {
      debugPrint('خطأ في تحميل الفئات: $e');
    }
  }

  /// البحث عن المنتجات
  Future<void> _searchProducts() async {
    final query = _searchController.text.trim();

    // إذا كان الاستعلام فارغًا ولم يتم اختيار فئة، لا نقوم بالبحث
    if (query.isEmpty && _selectedCategoryId == null) {
      setState(() {
        _errorMessage = 'الرجاء إدخال كلمة بحث أو اختيار فئة';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
      _hasSearched = true;
    });

    try {
      final supabaseService = Provider.of<AuthSupabaseService>(
        context,
        listen: false,
      );

      // بناء استعلام Supabase
      final query = supabaseService.client.from('products').select('*');

      // بناء شروط الاستعلام
      var filterQuery = query;

      // إضافة شرط البحث بالنص إذا كان موجودًا
      if (_searchController.text.trim().isNotEmpty) {
        final searchText = _searchController.text.trim();
        filterQuery = filterQuery.or(
          'name.ilike.%$searchText%,description.ilike.%$searchText%,brand.ilike.%$searchText%,sku.ilike.%$searchText%',
        );
      }

      // إضافة شرط الفئة إذا كان موجودًا
      if (_selectedCategoryId != null) {
        filterQuery = filterQuery.eq('category_id', _selectedCategoryId!);
      }

      // إضافة شرط نطاق السعر
      filterQuery = filterQuery.gte('price', _currentPriceRange.start);
      filterQuery = filterQuery.lte('price', _currentPriceRange.end);

      // إضافة الترتيب
      PostgrestTransformBuilder<PostgrestList> sortedQuery;
      switch (_selectedSortOption) {
        case 'الأحدث':
          sortedQuery = filterQuery.order('created_at', ascending: false);
          break;
        case 'الأقدم':
          sortedQuery = filterQuery.order('created_at', ascending: true);
          break;
        case 'السعر: من الأعلى إلى الأقل':
          sortedQuery = filterQuery.order('price', ascending: false);
          break;
        case 'السعر: من الأقل إلى الأعلى':
          sortedQuery = filterQuery.order('price', ascending: true);
          break;
        case 'الاسم: أ-ي':
          sortedQuery = filterQuery.order('name', ascending: true);
          break;
        case 'الاسم: ي-أ':
          sortedQuery = filterQuery.order('name', ascending: false);
          break;
        default:
          sortedQuery = filterQuery.order('created_at', ascending: false);
      }

      // تنفيذ الاستعلام
      final response = await sortedQuery;

      // تحويل النتائج إلى كائنات ProductModel
      final List<ProductModel> results =
          (response as List).map((product) {
            return ProductModel.fromJson(product);
          }).toList();

      setState(() {
        _searchResults = results;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _searchResults = [];
        _isLoading = false;
        _errorMessage = 'حدث خطأ أثناء البحث: $e';
      });
    }
  }

  /// عرض خيارات التصفية
  void _showFilterOptions() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16.0)),
      ),
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setModalState) {
            return Container(
              padding: const EdgeInsets.all(16.0),
              height: MediaQuery.of(context).size.height * 0.7,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'خيارات التصفية',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const Divider(),

                  // الفئات
                  const Text(
                    'الفئة',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8.0),
                  Wrap(
                    spacing: 8.0,
                    children: [
                      FilterChip(
                        label: const Text('الكل'),
                        selected: _selectedCategoryId == null,
                        onSelected: (selected) {
                          setModalState(() {
                            _selectedCategoryId = null;
                          });
                        },
                      ),
                      ..._categories.map((category) {
                        return FilterChip(
                          label: Text(category.name),
                          selected: _selectedCategoryId == category.id,
                          onSelected: (selected) {
                            setModalState(() {
                              _selectedCategoryId =
                                  selected ? category.id : null;
                            });
                          },
                        );
                      }),
                    ],
                  ),
                  const SizedBox(height: 16.0),

                  // نطاق السعر
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'نطاق السعر',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      Text(
                        '${_currentPriceRange.start.toInt()} - ${_currentPriceRange.end.toInt()} ر.س',
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                  RangeSlider(
                    values: _currentPriceRange,
                    min: 0,
                    max: 10000,
                    divisions: 100,
                    labels: RangeLabels(
                      '${_currentPriceRange.start.toInt()} ر.س',
                      '${_currentPriceRange.end.toInt()} ر.س',
                    ),
                    onChanged: (values) {
                      setModalState(() {
                        _currentPriceRange = values;
                      });
                    },
                  ),
                  const SizedBox(height: 16.0),

                  // الترتيب
                  const Text(
                    'الترتيب',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8.0),
                  Wrap(
                    spacing: 8.0,
                    children:
                        _sortOptions.map((option) {
                          return FilterChip(
                            label: Text(option),
                            selected: _selectedSortOption == option,
                            onSelected: (selected) {
                              setModalState(() {
                                _selectedSortOption = option;
                              });
                            },
                          );
                        }).toList(),
                  ),
                  const Spacer(),

                  // زر تطبيق التصفية
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {
                        setState(() {
                          _priceRange = _currentPriceRange;
                        });
                        Navigator.pop(context);
                        _searchProducts();
                      },
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12.0),
                      ),
                      child: const Text('تطبيق التصفية'),
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  /// الانتقال إلى صفحة تفاصيل المنتج
  void _navigateToProductDetails(ProductModel product) {
    Navigator.pushNamed(
      context,
      '/product-details',
      arguments: {'product': product},
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(title: 'البحث عن المنتجات', showBackButton: true),
      body: Column(
        children: [
          // حقل البحث
          Padding(
            padding: EdgeInsets.all(ResponsiveHelper.getPadding(context)),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      hintText: 'ابحث عن منتج...',
                      prefixIcon: const Icon(Icons.search),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8.0),
                      ),
                    ),
                    onSubmitted: (_) => _searchProducts(),
                  ),
                ),
                const SizedBox(width: 8.0),
                IconButton(
                  icon: const Icon(Icons.filter_list),
                  onPressed: _showFilterOptions,
                  tooltip: 'خيارات التصفية',
                ),
              ],
            ),
          ),

          // عرض الفلاتر النشطة
          if (_selectedCategoryId != null ||
              _priceRange != const RangeValues(0, 10000))
            Padding(
              padding: EdgeInsets.symmetric(
                horizontal: ResponsiveHelper.getPadding(context),
              ),
              child: Wrap(
                spacing: 8.0,
                children: [
                  if (_selectedCategoryId != null)
                    Chip(
                      label: Text(
                        'الفئة: ${_categories.firstWhere((c) => c.id == _selectedCategoryId).name}',
                      ),
                      onDeleted: () {
                        setState(() {
                          _selectedCategoryId = null;
                        });
                        _searchProducts();
                      },
                    ),
                  if (_priceRange != const RangeValues(0, 10000))
                    Chip(
                      label: Text(
                        'السعر: ${_priceRange.start.toInt()} - ${_priceRange.end.toInt()} ر.س',
                      ),
                      onDeleted: () {
                        setState(() {
                          _priceRange = const RangeValues(0, 10000);
                          _currentPriceRange = const RangeValues(0, 10000);
                        });
                        _searchProducts();
                      },
                    ),
                  if (_selectedSortOption != 'الأحدث')
                    Chip(
                      label: Text('الترتيب: $_selectedSortOption'),
                      onDeleted: () {
                        setState(() {
                          _selectedSortOption = 'الأحدث';
                        });
                        _searchProducts();
                      },
                    ),
                ],
              ),
            ),

          // عرض نتائج البحث
          Expanded(
            child:
                _isLoading
                    ? const Center(child: LoadingIndicator())
                    : _errorMessage != null
                    ? Center(child: Text(_errorMessage!))
                    : !_hasSearched
                    ? const Center(
                      child: Text('ابحث عن منتج أو اختر فئة للبدء'),
                    )
                    : _searchResults.isEmpty
                    ? const EmptyStateWidget(
                      icon: Icons.search_off,
                      title: 'لا توجد نتائج',
                      message: 'لم يتم العثور على منتجات مطابقة لبحثك',
                    )
                    : ResponsiveBuilder(
                      builder: (context, constraints) {
                        final crossAxisCount = ResponsiveHelper.getGridColumns(
                          context,
                        );
                        final aspectRatio = ResponsiveHelper.getAspectRatio(
                          context,
                          mobile: 0.7,
                          tablet: 0.8,
                          desktop: 0.9,
                        );
                        final spacing = ResponsiveHelper.getPadding(
                          context,
                          mobile: 12,
                          tablet: 16,
                          desktop: 20,
                        );

                        return GridView.builder(
                          padding: EdgeInsets.all(spacing),
                          gridDelegate:
                              SliverGridDelegateWithFixedCrossAxisCount(
                                crossAxisCount: crossAxisCount,
                                childAspectRatio: aspectRatio,
                                crossAxisSpacing: spacing,
                                mainAxisSpacing: spacing,
                              ),
                          itemCount: _searchResults.length,
                          itemBuilder: (context, index) {
                            final product = _searchResults[index];
                            return _buildProductCard(product);
                          },
                        );
                      },
                    ),
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة المنتج
  Widget _buildProductCard(ProductModel product) {
    return ResponsiveBuilder(
      builder: (context, constraints) {
        final padding = ResponsiveHelper.getPadding(
          context,
          mobile: 8,
          tablet: 12,
          desktop: 16,
        );

        return Card(
          elevation: 2.0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8.0),
          ),
          child: InkWell(
            onTap: () => _navigateToProductDetails(product),
            borderRadius: BorderRadius.circular(8.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // صورة المنتج
                Expanded(
                  child: ClipRRect(
                    borderRadius: const BorderRadius.vertical(
                      top: Radius.circular(8.0),
                    ),
                    child:
                        product.images.isNotEmpty
                            ? Image.network(
                              product.images.first,
                              width: double.infinity,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                return const Center(
                                  child: Icon(Icons.image_not_supported),
                                );
                              },
                            )
                            : Container(
                              color: Colors.grey[300],
                              child: const Center(
                                child: Icon(Icons.image_not_supported),
                              ),
                            ),
                  ),
                ),

                // تفاصيل المنتج
                Padding(
                  padding: EdgeInsets.all(padding),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        product.name,
                        style: const TextStyle(fontWeight: FontWeight.bold),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4.0),
                      Text(
                        product.brand,
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 12.0,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4.0),
                      Row(
                        children: [
                          if (product.discountPrice != null) ...[
                            Text(
                              '${product.discountPrice} ر.س',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.green,
                              ),
                            ),
                            const SizedBox(width: 4.0),
                            Text(
                              '${product.price} ر.س',
                              style: const TextStyle(
                                decoration: TextDecoration.lineThrough,
                                color: Colors.grey,
                                fontSize: 12.0,
                              ),
                            ),
                          ] else
                            Text(
                              '${product.price} ر.س',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
