import 'dart:io';

/// 🧠 نماذج بيانات Clarifai الشاملة
/// 
/// مجموعة شاملة من نماذج البيانات لجميع أنواع نتائج Clarifai
/// مع دعم كامل للتحويل والمعالجة

/// 📋 طلب Clarifai
class ClarifaiRequest {
  final String id;
  final ClarifaiRequestType type;
  final String modelKey;
  final File? imageFile;
  final String? imageUrl;
  final Map<String, dynamic> options;
  final DateTime startTime;

  ClarifaiRequest({
    required this.id,
    required this.type,
    required this.modelKey,
    this.imageFile,
    this.imageUrl,
    required this.options,
    required this.startTime,
  });

  int get processingTime => DateTime.now().difference(startTime).inMilliseconds;
}

/// 🏷️ أنواع طلبات Clarifai
enum ClarifaiRequestType {
  imageAnalysis,
  objectDetection,
  textExtraction,
  colorAnalysis,
  moderation,
  faceDetection,
  customAnalysis,
}

/// 📊 نتيجة Clarifai الأساسية
class ClarifaiResult {
  final bool success;
  final String? error;
  final String modelUsed;
  final int processingTime;
  final List<ConceptResult> concepts;
  final List<RegionResult> regions;
  final List<ColorResult> colors;
  final List<TextResult> text;
  final Map<String, dynamic>? rawResponse;
  final DateTime timestamp;

  ClarifaiResult({
    required this.success,
    this.error,
    required this.modelUsed,
    required this.processingTime,
    this.concepts = const [],
    this.regions = const [],
    this.colors = const [],
    this.text = const [],
    this.rawResponse,
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'error': error,
      'model_used': modelUsed,
      'processing_time_ms': processingTime,
      'concepts': concepts.map((c) => c.toJson()).toList(),
      'regions': regions.map((r) => r.toJson()).toList(),
      'colors': colors.map((c) => c.toJson()).toList(),
      'text': text.map((t) => t.toJson()).toList(),
      'timestamp': timestamp.toIso8601String(),
    };
  }

  /// إنشاء من JSON
  factory ClarifaiResult.fromJson(Map<String, dynamic> json) {
    return ClarifaiResult(
      success: json['success'] ?? false,
      error: json['error'],
      modelUsed: json['model_used'] ?? '',
      processingTime: json['processing_time_ms'] ?? 0,
      concepts: (json['concepts'] as List?)
          ?.map((c) => ConceptResult.fromJson(c))
          .toList() ?? [],
      regions: (json['regions'] as List?)
          ?.map((r) => RegionResult.fromJson(r))
          .toList() ?? [],
      colors: (json['colors'] as List?)
          ?.map((c) => ColorResult.fromJson(c))
          .toList() ?? [],
      text: (json['text'] as List?)
          ?.map((t) => TextResult.fromJson(t))
          .toList() ?? [],
      timestamp: DateTime.tryParse(json['timestamp'] ?? '') ?? DateTime.now(),
    );
  }
}

/// 🎯 نتيجة المفهوم
class ConceptResult {
  final String id;
  final String name;
  final double confidence;
  final String appId;
  final Map<String, dynamic>? metadata;

  ConceptResult({
    required this.id,
    required this.name,
    required this.confidence,
    required this.appId,
    this.metadata,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'confidence': confidence,
      'app_id': appId,
      'metadata': metadata,
    };
  }

  factory ConceptResult.fromJson(Map<String, dynamic> json) {
    return ConceptResult(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      confidence: (json['confidence'] ?? 0.0).toDouble(),
      appId: json['app_id'] ?? '',
      metadata: json['metadata'],
    );
  }
}

/// 📍 نتيجة المنطقة
class RegionResult {
  final BoundingBox? boundingBox;
  final List<ConceptResult> concepts;
  final Map<String, dynamic>? metadata;

  RegionResult({
    this.boundingBox,
    required this.concepts,
    this.metadata,
  });

  Map<String, dynamic> toJson() {
    return {
      'bounding_box': boundingBox?.toJson(),
      'concepts': concepts.map((c) => c.toJson()).toList(),
      'metadata': metadata,
    };
  }

  factory RegionResult.fromJson(Map<String, dynamic> json) {
    return RegionResult(
      boundingBox: json['bounding_box'] != null 
          ? BoundingBox.fromJson(json['bounding_box'])
          : null,
      concepts: (json['concepts'] as List?)
          ?.map((c) => ConceptResult.fromJson(c))
          .toList() ?? [],
      metadata: json['metadata'],
    );
  }
}

/// 📦 صندوق الحدود
class BoundingBox {
  final double topRow;
  final double leftCol;
  final double bottomRow;
  final double rightCol;

  BoundingBox({
    required this.topRow,
    required this.leftCol,
    required this.bottomRow,
    required this.rightCol,
  });

  double get width => rightCol - leftCol;
  double get height => bottomRow - topRow;
  double get area => width * height;

  Map<String, dynamic> toJson() {
    return {
      'top_row': topRow,
      'left_col': leftCol,
      'bottom_row': bottomRow,
      'right_col': rightCol,
    };
  }

  factory BoundingBox.fromJson(Map<String, dynamic> json) {
    return BoundingBox(
      topRow: (json['top_row'] ?? 0.0).toDouble(),
      leftCol: (json['left_col'] ?? 0.0).toDouble(),
      bottomRow: (json['bottom_row'] ?? 0.0).toDouble(),
      rightCol: (json['right_col'] ?? 0.0).toDouble(),
    );
  }
}

/// 🎨 نتيجة اللون
class ColorResult {
  final String hex;
  final String webSafeHex;
  final String webSafeColorName;
  final double value;
  final int? pixelCount;

  ColorResult({
    required this.hex,
    required this.webSafeHex,
    required this.webSafeColorName,
    required this.value,
    this.pixelCount,
  });

  Map<String, dynamic> toJson() {
    return {
      'hex': hex,
      'web_safe_hex': webSafeHex,
      'web_safe_color_name': webSafeColorName,
      'value': value,
      'pixel_count': pixelCount,
    };
  }

  factory ColorResult.fromJson(Map<String, dynamic> json) {
    return ColorResult(
      hex: json['hex'] ?? '',
      webSafeHex: json['web_safe_hex'] ?? '',
      webSafeColorName: json['web_safe_color_name'] ?? '',
      value: (json['value'] ?? 0.0).toDouble(),
      pixelCount: json['pixel_count'],
    );
  }
}

/// 📝 نتيجة النص
class TextResult {
  final String text;
  final double confidence;
  final BoundingBox? boundingBox;
  final String? language;

  TextResult({
    required this.text,
    required this.confidence,
    this.boundingBox,
    this.language,
  });

  Map<String, dynamic> toJson() {
    return {
      'text': text,
      'confidence': confidence,
      'bounding_box': boundingBox?.toJson(),
      'language': language,
    };
  }

  factory TextResult.fromJson(Map<String, dynamic> json) {
    return TextResult(
      text: json['text'] ?? '',
      confidence: (json['confidence'] ?? 0.0).toDouble(),
      boundingBox: json['bounding_box'] != null 
          ? BoundingBox.fromJson(json['bounding_box'])
          : null,
      language: json['language'],
    );
  }
}

/// 🎯 نتيجة كشف الكائنات
class ObjectDetectionResult {
  final bool success;
  final String? error;
  final List<DetectedObject> objects;
  final int processingTime;
  final DateTime timestamp;

  ObjectDetectionResult({
    required this.success,
    this.error,
    required this.objects,
    required this.processingTime,
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();

  factory ObjectDetectionResult.fromClarifaiResult(ClarifaiResult result) {
    final objects = result.regions.map((region) {
      final mainConcept = region.concepts.isNotEmpty 
          ? region.concepts.first 
          : ConceptResult(id: '', name: 'unknown', confidence: 0.0, appId: '');
      
      return DetectedObject(
        name: mainConcept.name,
        confidence: mainConcept.confidence,
        boundingBox: region.boundingBox,
        concepts: region.concepts,
      );
    }).toList();

    return ObjectDetectionResult(
      success: result.success,
      error: result.error,
      objects: objects,
      processingTime: result.processingTime,
      timestamp: result.timestamp,
    );
  }
}

/// 🎯 كائن مكتشف
class DetectedObject {
  final String name;
  final double confidence;
  final BoundingBox? boundingBox;
  final List<ConceptResult> concepts;

  DetectedObject({
    required this.name,
    required this.confidence,
    this.boundingBox,
    required this.concepts,
  });
}

/// 📝 نتيجة استخراج النص
class TextExtractionResult {
  final bool success;
  final String? error;
  final List<ExtractedText> textBlocks;
  final String fullText;
  final int processingTime;
  final DateTime timestamp;

  TextExtractionResult({
    required this.success,
    this.error,
    required this.textBlocks,
    required this.fullText,
    required this.processingTime,
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();

  factory TextExtractionResult.fromClarifaiResult(ClarifaiResult result) {
    final textBlocks = result.text.map((text) => ExtractedText(
      text: text.text,
      confidence: text.confidence,
      boundingBox: text.boundingBox,
      language: text.language,
    )).toList();

    final fullText = textBlocks.map((block) => block.text).join(' ');

    return TextExtractionResult(
      success: result.success,
      error: result.error,
      textBlocks: textBlocks,
      fullText: fullText,
      processingTime: result.processingTime,
      timestamp: result.timestamp,
    );
  }
}

/// 📝 نص مستخرج
class ExtractedText {
  final String text;
  final double confidence;
  final BoundingBox? boundingBox;
  final String? language;

  ExtractedText({
    required this.text,
    required this.confidence,
    this.boundingBox,
    this.language,
  });
}

/// 🎨 نتيجة تحليل الألوان
class ColorAnalysisResult {
  final bool success;
  final String? error;
  final List<DominantColor> dominantColors;
  final ColorPalette? palette;
  final int processingTime;
  final DateTime timestamp;

  ColorAnalysisResult({
    required this.success,
    this.error,
    required this.dominantColors,
    this.palette,
    required this.processingTime,
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();

  factory ColorAnalysisResult.fromClarifaiResult(ClarifaiResult result) {
    final dominantColors = result.colors.map((color) => DominantColor(
      hex: color.hex,
      name: color.webSafeColorName,
      percentage: color.value,
      pixelCount: color.pixelCount,
    )).toList();

    return ColorAnalysisResult(
      success: result.success,
      error: result.error,
      dominantColors: dominantColors,
      processingTime: result.processingTime,
      timestamp: result.timestamp,
    );
  }
}

/// 🎨 لون سائد
class DominantColor {
  final String hex;
  final String name;
  final double percentage;
  final int? pixelCount;

  DominantColor({
    required this.hex,
    required this.name,
    required this.percentage,
    this.pixelCount,
  });
}

/// 🎨 لوحة الألوان
class ColorPalette {
  final List<String> primaryColors;
  final List<String> secondaryColors;
  final String dominantColor;
  final String accentColor;

  ColorPalette({
    required this.primaryColors,
    required this.secondaryColors,
    required this.dominantColor,
    required this.accentColor,
  });
}

/// 🛡️ نتيجة فلترة المحتوى
class ModerationResult {
  final bool success;
  final String? error;
  final bool isAppropriate;
  final List<ModerationFlag> flags;
  final double overallScore;
  final int processingTime;
  final DateTime timestamp;

  ModerationResult({
    required this.success,
    this.error,
    required this.isAppropriate,
    required this.flags,
    required this.overallScore,
    required this.processingTime,
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();

  factory ModerationResult.fromClarifaiResult(ClarifaiResult result) {
    final flags = result.concepts.map((concept) => ModerationFlag(
      type: concept.name,
      confidence: concept.confidence,
      severity: _getSeverityFromConfidence(concept.confidence),
    )).toList();

    final inappropriateFlags = flags.where((flag) => flag.confidence > 0.5).toList();
    final isAppropriate = inappropriateFlags.isEmpty;
    final overallScore = flags.isNotEmpty 
        ? flags.map((f) => f.confidence).reduce((a, b) => a > b ? a : b)
        : 0.0;

    return ModerationResult(
      success: result.success,
      error: result.error,
      isAppropriate: isAppropriate,
      flags: flags,
      overallScore: overallScore,
      processingTime: result.processingTime,
      timestamp: result.timestamp,
    );
  }

  static ModerationSeverity _getSeverityFromConfidence(double confidence) {
    if (confidence >= 0.8) return ModerationSeverity.high;
    if (confidence >= 0.6) return ModerationSeverity.medium;
    if (confidence >= 0.4) return ModerationSeverity.low;
    return ModerationSeverity.none;
  }
}

/// 🛡️ علامة فلترة
class ModerationFlag {
  final String type;
  final double confidence;
  final ModerationSeverity severity;

  ModerationFlag({
    required this.type,
    required this.confidence,
    required this.severity,
  });
}

/// 🛡️ مستوى خطورة المحتوى
enum ModerationSeverity {
  none,
  low,
  medium,
  high,
}

/// ❌ استثناء Clarifai
class ClarifaiException implements Exception {
  final String message;
  final String? code;
  final Map<String, dynamic>? details;

  ClarifaiException(this.message, {this.code, this.details});

  @override
  String toString() => 'ClarifaiException: $message';
}
