
class PaymentTransactionModel {
  final String id;
  final String orderId;
  final String paymentMethodId;
  final double amount;
  final String status;
  final String? transactionId;
  final String? errorMessage;
  final Map<String, dynamic>? metadata;
  final DateTime createdAt;
  final DateTime? updatedAt;

  PaymentTransactionModel({
    required this.id,
    required this.orderId,
    required this.paymentMethodId,
    required this.amount,
    required this.status,
    this.transactionId,
    this.errorMessage,
    this.metadata,
    required this.createdAt,
    this.updatedAt,
  }) : assert(
         status == 'pending' ||
             status == 'processing' ||
             status == 'completed' ||
             status == 'failed' ||
             status == 'refunded',
         'حالة المعاملة غير صالحة',
       );

  factory PaymentTransactionModel.fromJson(Map<String, dynamic> json) {
    return PaymentTransactionModel(
      id: json['id'] as String,
      orderId: json['order_id'] as String,
      paymentMethodId: json['payment_method_id'] as String,
      amount: (json['amount'] as num).toDouble(),
      status: json['status'] as String,
      transactionId: json['transaction_id'] as String?,
      errorMessage: json['error_message'] as String?,
      metadata:
          json['metadata'] != null
              ? Map<String, dynamic>.from(json['metadata'] as Map)
              : null,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt:
          json['updated_at'] != null
              ? DateTime.parse(json['updated_at'] as String)
              : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'order_id': orderId,
      'payment_method_id': paymentMethodId,
      'amount': amount,
      'status': status,
      'transaction_id': transactionId,
      'error_message': errorMessage,
      'metadata': metadata,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  PaymentTransactionModel copyWith({
    String? id,
    String? orderId,
    String? paymentMethodId,
    double? amount,
    String? status,
    String? transactionId,
    String? errorMessage,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return PaymentTransactionModel(
      id: id ?? this.id,
      orderId: orderId ?? this.orderId,
      paymentMethodId: paymentMethodId ?? this.paymentMethodId,
      amount: amount ?? this.amount,
      status: status ?? this.status,
      transactionId: transactionId ?? this.transactionId,
      errorMessage: errorMessage ?? this.errorMessage,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  bool get isPending => status == 'pending';
  bool get isProcessing => status == 'processing';
  bool get isCompleted => status == 'completed';
  bool get isFailed => status == 'failed';
  bool get isRefunded => status == 'refunded';
  bool get hasError => errorMessage != null;
  bool get hasTransactionId => transactionId != null;
}
