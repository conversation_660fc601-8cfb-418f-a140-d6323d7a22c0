import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image/image.dart' as img;
import 'package:motorcycle_parts_shop/core/services/cloudinary_service.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';

/// 🎬 مدير الوسائط المتقدم والاحترافي
///
/// خدمة شاملة لإدارة جميع أنواع الوسائط (صور، فيديوهات، ملفات)
/// مع ميزات متقدمة ومعالجة احترافية
///
/// 🎯 الميزات الرئيسية:
/// - 📸 معالجة متقدمة للصور
/// - 🎬 دعم كامل للفيديوهات
/// - 📁 إدارة ملفات شاملة
/// - 🎨 تحويلات وتأثيرات متقدمة
/// - 📊 تحليلات وإحصائيات مفصلة
/// - 🔄 معالجة متوازية ومتقدمة
/// - 💾 تخزين ذكي ومحسن
/// - 🛡️ أمان وحماية متقدمة
class AdvancedMediaManager extends ChangeNotifier {
  static final AdvancedMediaManager _instance =
      AdvancedMediaManager._internal();
  final CloudinaryService _cloudinaryService = CloudinaryService();

  // 📊 إحصائيات شاملة
  int _totalProcessedFiles = 0;
  int _successfulProcessing = 0;
  int _failedProcessing = 0;
  final Map<String, int> _fileTypeStats = {};
  final Map<String, double> _averageProcessingTimes = {};

  // ⚙️ إعدادات متقدمة
  static const int _maxConcurrentProcessing = 5;
  static const Duration _processingTimeout = Duration(minutes: 10);
  static const List<String> _supportedImageFormats = [
    'jpg',
    'jpeg',
    'png',
    'webp',
    'gif',
    'bmp',
  ];
  static const List<String> _supportedVideoFormats = [
    'mp4',
    'avi',
    'mov',
    'wmv',
    'flv',
  ];

  bool _isInitialized = false;
  final Map<String, MediaProcessingJob> _activeJobs = {};

  factory AdvancedMediaManager() => _instance;
  AdvancedMediaManager._internal();

  // 📊 Getters للإحصائيات
  bool get isInitialized => _isInitialized;
  int get totalProcessedFiles => _totalProcessedFiles;
  int get successfulProcessing => _successfulProcessing;
  int get failedProcessing => _failedProcessing;
  double get processingSuccessRate =>
      _totalProcessedFiles > 0
          ? (_successfulProcessing / _totalProcessedFiles) * 100
          : 0.0;
  Map<String, int> get fileTypeStats => Map.unmodifiable(_fileTypeStats);
  Map<String, double> get averageProcessingTimes =>
      Map.unmodifiable(_averageProcessingTimes);
  int get activeJobsCount => _activeJobs.length;

  /// 🚀 تهيئة مدير الوسائط
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // تهيئة الخدمات المطلوبة
      if (!_cloudinaryService.isInitialized) {
        await _cloudinaryService.initialize();
      }

      _isInitialized = true;
      notifyListeners();
      debugPrint('✅ تم تهيئة مدير الوسائط المتقدم بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة مدير الوسائط: $e');
      rethrow;
    }
  }

  /// 📸 معالجة متقدمة للصور
  Future<MediaProcessingResult> processImage({
    required File imageFile,
    required String category, // product, category, banner, etc.
    String? customId,
    ImageProcessingOptions? options,
    Function(double progress)? onProgress,
  }) async {
    if (!_isInitialized) await initialize();

    final jobId = _generateJobId();
    final job = MediaProcessingJob(
      id: jobId,
      type: MediaType.image,
      filePath: imageFile.path,
      category: category,
      startTime: DateTime.now(),
    );

    _activeJobs[jobId] = job;
    _totalProcessedFiles++;

    try {
      onProgress?.call(0.1);

      // التحقق من صحة الملف
      final fileInfo = await _cloudinaryService.validateMediaFile(imageFile);
      onProgress?.call(0.2);

      // معالجة الصورة محلياً (إذا لزم الأمر)
      File processedFile = imageFile;
      if (options?.enableLocalProcessing == true) {
        processedFile = await _processImageLocally(imageFile, options!);
        onProgress?.call(0.4);
      }

      // رفع الصورة
      final uploadUrl = await _cloudinaryService.uploadProductImage(
        imageFile: processedFile,
        productId: customId ?? jobId,
      );

      final transformedUrls = _generateProductImageVariants(uploadUrl);

      onProgress?.call(0.9);

      // إنشاء تحويلات إضافية
      final additionalTransforms = await _generateAdditionalTransforms(
        uploadUrl,
        options,
      );

      onProgress?.call(1.0);

      _successfulProcessing++;
      _updateFileTypeStats(path.extension(imageFile.path));
      _updateProcessingTime(MediaType.image, job.processingTime);

      final result = MediaProcessingResult(
        success: true,
        jobId: jobId,
        originalUrl: uploadUrl,
        transformedUrls: {...transformedUrls, ...additionalTransforms},
        fileInfo: fileInfo,
        processingTimeMs: job.processingTime,
      );

      _activeJobs.remove(jobId);
      notifyListeners();
      return result;
    } catch (e) {
      _failedProcessing++;
      _activeJobs.remove(jobId);

      return MediaProcessingResult(
        success: false,
        jobId: jobId,
        error: e.toString(),
        processingTimeMs: job.processingTime,
      );
    }
  }

  /// 🎬 معالجة متقدمة للفيديوهات
  Future<MediaProcessingResult> processVideo({
    required File videoFile,
    required String category,
    String? customId,
    VideoProcessingOptions? options,
    Function(double progress)? onProgress,
  }) async {
    if (!_isInitialized) await initialize();

    final jobId = _generateJobId();
    final job = MediaProcessingJob(
      id: jobId,
      type: MediaType.video,
      filePath: videoFile.path,
      category: category,
      startTime: DateTime.now(),
    );

    _activeJobs[jobId] = job;
    _totalProcessedFiles++;

    try {
      onProgress?.call(0.1);

      // التحقق من صحة الفيديو
      final fileInfo = await _cloudinaryService.validateMediaFile(
        videoFile,
        isVideo: true,
      );
      onProgress?.call(0.2);

      // رفع الفيديو
      final publicId = customId ?? jobId;
      final videoUrl = await _cloudinaryService.uploadImage(
        imageFile: videoFile,
        folder: 'videos/$category',
        publicId: publicId,
        timeout: const Duration(minutes: 5),
      );

      onProgress?.call(0.7);

      // إنشاء تحويلات للفيديو
      final videoTransforms = _generateVideoTransforms(videoUrl, options);

      onProgress?.call(1.0);

      _successfulProcessing++;
      _updateFileTypeStats(path.extension(videoFile.path));
      _updateProcessingTime(MediaType.video, job.processingTime);

      final result = MediaProcessingResult(
        success: true,
        jobId: jobId,
        originalUrl: videoUrl,
        transformedUrls: videoTransforms,
        fileInfo: fileInfo,
        processingTimeMs: job.processingTime,
      );

      _activeJobs.remove(jobId);
      notifyListeners();
      return result;
    } catch (e) {
      _failedProcessing++;
      _activeJobs.remove(jobId);

      return MediaProcessingResult(
        success: false,
        jobId: jobId,
        error: e.toString(),
        processingTimeMs: job.processingTime,
      );
    }
  }

  /// 📁 معالجة متعددة الملفات
  Future<List<MediaProcessingResult>> processMultipleFiles({
    required List<File> files,
    required String category,
    List<String>? customIds,
    MediaProcessingOptions? options,
    Function(int current, int total)? onProgress,
  }) async {
    if (!_isInitialized) await initialize();

    final results = <MediaProcessingResult>[];
    int completed = 0;

    // معالجة متوازية مع حد أقصى
    for (int i = 0; i < files.length; i += _maxConcurrentProcessing) {
      final batch = files.skip(i).take(_maxConcurrentProcessing).toList();
      final batchFutures = batch.asMap().entries.map((entry) async {
        final index = i + entry.key;
        final file = entry.value;
        final customId =
            customIds != null && customIds.length > index
                ? customIds[index]
                : null;

        final fileExtension = path
            .extension(file.path)
            .toLowerCase()
            .replaceAll('.', '');

        MediaProcessingResult result;
        if (_supportedImageFormats.contains(fileExtension)) {
          result = await processImage(
            imageFile: file,
            category: category,
            customId: customId,
            options: options as ImageProcessingOptions?,
          );
        } else if (_supportedVideoFormats.contains(fileExtension)) {
          result = await processVideo(
            videoFile: file,
            category: category,
            customId: customId,
            options: options as VideoProcessingOptions?,
          );
        } else {
          result = MediaProcessingResult(
            success: false,
            jobId: _generateJobId(),
            error: 'نوع الملف غير مدعوم: $fileExtension',
          );
        }

        completed++;
        onProgress?.call(completed, files.length);
        return result;
      });

      final batchResults = await Future.wait(batchFutures);
      results.addAll(batchResults);
    }

    return results;
  }

  /// 🎨 معالجة الصورة محلياً
  Future<File> _processImageLocally(
    File imageFile,
    ImageProcessingOptions options,
  ) async {
    try {
      final imageBytes = await imageFile.readAsBytes();
      img.Image? image = img.decodeImage(imageBytes);

      if (image == null) {
        throw Exception('فشل في قراءة الصورة');
      }

      // تطبيق التحويلات المحلية
      if (options.resize != null) {
        image = img.copyResize(
          image,
          width: options.resize!.width,
          height: options.resize!.height,
        );
      }

      if (options.quality != null && options.quality! < 100) {
        // تقليل الجودة
      }

      if (options.addWatermark) {
        // إضافة علامة مائية
      }

      // حفظ الصورة المعدلة
      final tempDir = await getTemporaryDirectory();
      final processedFile = File(
        '${tempDir.path}/processed_${path.basename(imageFile.path)}',
      );

      final processedBytes = img.encodeJpg(
        image,
        quality: options.quality ?? 85,
      );
      await processedFile.writeAsBytes(processedBytes);

      return processedFile;
    } catch (e) {
      debugPrint('خطأ في معالجة الصورة محلياً: $e');
      return imageFile; // إرجاع الصورة الأصلية في حالة الخطأ
    }
  }

  /// 🎨 إنشاء تحويلات متعددة للمنتج
  Map<String, String> _generateProductImageVariants(String originalUrl) {
    return {
      'thumbnail': _cloudinaryService.getProductImageUrl(
        imageUrl: originalUrl,
        size: 'thumbnail',
      ),
      'small': _cloudinaryService.getProductImageUrl(
        imageUrl: originalUrl,
        size: 'small',
      ),
      'medium': _cloudinaryService.getProductImageUrl(
        imageUrl: originalUrl,
        size: 'medium',
      ),
      'large': _cloudinaryService.getProductImageUrl(
        imageUrl: originalUrl,
        size: 'large',
      ),
    };
  }

  /// 🎨 إنشاء تحويلات إضافية
  Future<Map<String, String>> _generateAdditionalTransforms(
    String originalUrl,
    ImageProcessingOptions? options,
  ) async {
    final transforms = <String, String>{};

    if (options?.generateWebP == true) {
      transforms['webp'] = _cloudinaryService.getSmartTransformedUrl(
        imageUrl: originalUrl,
        autoFormat: true,
      );
    }

    if (options?.generateBlurred == true) {
      transforms['blurred'] = _cloudinaryService.getSmartTransformedUrl(
        imageUrl: originalUrl,
        effect: 'blur:300',
      );
    }

    if (options?.generateGrayscale == true) {
      transforms['grayscale'] = _cloudinaryService.getSmartTransformedUrl(
        imageUrl: originalUrl,
        effect: 'grayscale',
      );
    }

    return transforms;
  }

  /// 🎬 إنشاء تحويلات للفيديو
  Map<String, String> _generateVideoTransforms(
    String videoUrl,
    VideoProcessingOptions? options,
  ) {
    final transforms = <String, String>{};

    // إنشاء صورة مصغرة من الفيديو
    transforms['thumbnail'] = videoUrl.replaceAll(
      '/video/upload/',
      '/video/upload/so_0,w_300,h_200,c_fill/',
    );

    // نسخة مضغوطة
    transforms['compressed'] = videoUrl.replaceAll(
      '/video/upload/',
      '/video/upload/q_auto,f_auto/',
    );

    // نسخة للموبايل
    transforms['mobile'] = videoUrl.replaceAll(
      '/video/upload/',
      '/video/upload/w_480,q_auto,f_auto/',
    );

    return transforms;
  }

  /// 🔧 دوال مساعدة
  String _generateJobId() {
    return 'job_${DateTime.now().millisecondsSinceEpoch}_$_totalProcessedFiles';
  }

  void _updateFileTypeStats(String extension) {
    final ext = extension.toLowerCase().replaceAll('.', '');
    _fileTypeStats[ext] = (_fileTypeStats[ext] ?? 0) + 1;
  }

  void _updateProcessingTime(MediaType type, int timeMs) {
    final key = type.toString();
    final currentAvg = _averageProcessingTimes[key] ?? 0.0;
    final count = _fileTypeStats.values.fold(0, (sum, count) => sum + count);
    _averageProcessingTimes[key] =
        ((currentAvg * (count - 1)) + timeMs) / count;
  }

  /// 📊 الحصول على إحصائيات مفصلة
  Map<String, dynamic> getDetailedStats() {
    return {
      'processing_stats': {
        'total_processed': _totalProcessedFiles,
        'successful': _successfulProcessing,
        'failed': _failedProcessing,
        'success_rate': processingSuccessRate,
        'active_jobs': activeJobsCount,
      },
      'file_type_stats': _fileTypeStats,
      'average_processing_times': _averageProcessingTimes,
      'cloudinary_stats': _cloudinaryService.getDetailedStats(),
    };
  }

  /// 🧹 تنظيف وصيانة
  void performMaintenance() {
    _cloudinaryService.performMaintenance();

    // إزالة الوظائف المنتهية
    final now = DateTime.now();
    _activeJobs.removeWhere((key, job) {
      return now.difference(job.startTime) > _processingTimeout;
    });

    debugPrint('🧹 تم تنظيف مدير الوسائط المتقدم');
    notifyListeners();
  }
}

/// 📋 نماذج البيانات
enum MediaType { image, video, document }

class MediaProcessingJob {
  final String id;
  final MediaType type;
  final String filePath;
  final String category;
  final DateTime startTime;

  MediaProcessingJob({
    required this.id,
    required this.type,
    required this.filePath,
    required this.category,
    required this.startTime,
  });

  int get processingTime => DateTime.now().difference(startTime).inMilliseconds;
}

class MediaProcessingResult {
  final bool success;
  final String jobId;
  final String? originalUrl;
  final Map<String, String>? transformedUrls;
  final Map<String, dynamic>? fileInfo;
  final String? error;
  final int? processingTimeMs;

  MediaProcessingResult({
    required this.success,
    required this.jobId,
    this.originalUrl,
    this.transformedUrls,
    this.fileInfo,
    this.error,
    this.processingTimeMs,
  });
}

class MediaProcessingOptions {}

class ImageProcessingOptions extends MediaProcessingOptions {
  final bool enableLocalProcessing;
  final bool autoOptimize;
  final bool generateWebP;
  final bool generateBlurred;
  final bool generateGrayscale;
  final bool addWatermark;
  final ImageResize? resize;
  final int? quality;

  ImageProcessingOptions({
    this.enableLocalProcessing = false,
    this.autoOptimize = true,
    this.generateWebP = false,
    this.generateBlurred = false,
    this.generateGrayscale = false,
    this.addWatermark = false,
    this.resize,
    this.quality,
  });
}

class VideoProcessingOptions extends MediaProcessingOptions {
  final bool generateThumbnail;
  final bool compressVideo;
  final int? maxDuration;

  VideoProcessingOptions({
    this.generateThumbnail = true,
    this.compressVideo = true,
    this.maxDuration,
  });
}

class ImageResize {
  final int? width;
  final int? height;

  ImageResize({this.width, this.height});
}
