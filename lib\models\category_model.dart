import 'package:flutter/material.dart';

// نموذج بيانات الأقسام (فئات قطع غيار الدراجات النارية)
class CategoryModel {
  final String id; // المعرف الفريد للقسم
  final String name; // اسم القسم
  final String? description; // وصف القسم (اختياري)
  final String imageUrl; // رابط صورة القسم
  final int productsCount; // عدد المنتجات في هذا القسم
  final bool isActive; // حالة القسم (نشط أو غير نشط)
  final DateTime createdAt; // تاريخ إنشاء القسم
  final DateTime updatedAt; // تاريخ آخر تحديث للقسم
  final IconData? iconData; // أيقونة القسم (اختياري)

  CategoryModel({
    required this.id,
    required this.name,
    this.description,
    required this.imageUrl,
    required this.productsCount,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
    this.iconData,
  });

  /// تحويل البيانات من JSON إلى كائن
  factory CategoryModel.fromJson(Map<String, dynamic> json) {
    return CategoryModel(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      imageUrl: json['image_url'],
      productsCount:
          json['products_count'] ??
          0, // القيمة الافتراضية هي 0 إذا لم يتم توفيرها
      isActive:
          json['is_active'] ??
          true, // القيمة الافتراضية هي `true` إذا لم يتم توفيرها
      createdAt: DateTime.parse(
        json['created_at'],
      ), // تحويل تاريخ الإنشاء من نص إلى DateTime
      updatedAt: DateTime.parse(
        json['updated_at'],
      ), // تحويل تاريخ التحديث من نص إلى DateTime
      iconData:
          json['icon_data'] != null
              ? const IconData(
                0xe047,
                fontFamily: 'MaterialIcons',
              ) // استخدام أيقونة ثابتة
              : null,
    );
  }

  /// تحويل الكائن إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'image_url': imageUrl,
      'products_count': productsCount,
      'is_active': isActive,
      'created_at':
          createdAt
              .toIso8601String(), // تحويل تاريخ الإنشاء إلى نص بتنسيق ISO 8601
      'updated_at':
          updatedAt
              .toIso8601String(), // تحويل تاريخ التحديث إلى نص بتنسيق ISO 8601
      'icon_data': iconData?.codePoint,
    };
  }

  /// إنشاء نسخة معدلة من الكائن مع تحديث بعض الخصائص
  CategoryModel copyWith({
    String? id,
    String? name,
    String? description,
    String? imageUrl,
    int? productsCount,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    IconData? iconData,
  }) {
    return CategoryModel(
      id:
          id ??
          this.id, // استخدام القيمة الجديدة إذا تم توفيرها، وإلا استخدام القيمة الحالية
      name: name ?? this.name,
      description: description ?? this.description,
      imageUrl: imageUrl ?? this.imageUrl,
      productsCount: productsCount ?? this.productsCount,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      iconData: iconData ?? this.iconData,
    );
  }
}
