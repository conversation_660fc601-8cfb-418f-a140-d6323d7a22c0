# Flutter wrapper
-keep class io.flutter.app.** { *; }
-keep class io.flutter.plugin.** { *; }
-keep class io.flutter.util.** { *; }
-keep class io.flutter.view.** { *; }
-keep class io.flutter.** { *; }
-keep class io.flutter.plugins.** { *; }
-keep class io.flutter.embedding.** { *; }

# Keep native methods
-keepclassmembers class * {
    native <methods>;
}

# Supabase
-keep class io.supabase.** { *; }
-keep class com.supabase.** { *; }

# Cloudinary
-keep class com.cloudinary.** { *; }

# TensorFlow Lite
-keep class org.tensorflow.lite.** { *; }

# Local Auth
-keep class androidx.biometric.** { *; }

# Google Maps
-keep class com.google.android.gms.maps.** { *; }


# Prevent R8 from stripping interface information
-keepattributes *Annotation*
-keepattributes Signature
-keepattributes Exceptions
-keepattributes InnerClasses

# Prevent R8 from stripping method names
-keepattributes SourceFile,LineNumberTable
-renamesourcefileattribute SourceFile

# Google Sign In
-keep class com.google.android.gms.** { *; }
-keep class com.google.firebase.** { *; }

# JSON serialization
-keepattributes *Annotation*
-keepclassmembers class ** {
    @com.google.gson.annotations.SerializedName <fields>;
}

# HTTP client
-keep class okhttp3.** { *; }
-keep class retrofit2.** { *; }

# Image loading
-keep class com.bumptech.glide.** { *; }

# Notifications
-keep class androidx.work.** { *; }
-keep class com.google.firebase.messaging.** { *; }

# Camera and image processing
-keep class androidx.camera.** { *; }

# Performance optimizations
-optimizations !code/simplification/arithmetic,!code/simplification/cast,!field/*,!class/merging/*
-optimizationpasses 5
-allowaccessmodification
-dontpreverify

# Remove logging in release
-assumenosideeffects class android.util.Log {
    public static boolean isLoggable(java.lang.String, int);
    public static int v(...);
    public static int i(...);
    public static int w(...);
    public static int d(...);
    public static int e(...);
}