import 'package:motorcycle_parts_shop/core/services/auth_supabase_service.dart';

// خدمة المحافظات المصرية - تحصل على البيانات من قاعدة البيانات
class EgyptGovernorates {
  static final AuthSupabaseService _supabaseService = AuthSupabaseService();
  static List<String>? _cachedGovernorates;
  static DateTime? _lastFetchTime;
  static const Duration _cacheDuration = Duration(hours: 24);

  /// جلب قائمة المحافظات من قاعدة البيانات
  static Future<List<String>> getGovernoratesList() async {
    // التحقق من وجود بيانات مخزنة مؤقتاً وصالحة
    if (_cachedGovernorates != null &&
        _lastFetchTime != null &&
        DateTime.now().difference(_lastFetchTime!) < _cacheDuration) {
      return _cachedGovernorates!;
    }

    try {
      final response = await _supabaseService.client
          .from('governorates')
          .select('name')
          .eq('is_active', true)
          .order('name');

      final governorates =
          (response as List).map((item) => item['name'] as String).toList();

      // تخزين البيانات مؤقتاً
      _cachedGovernorates = governorates;
      _lastFetchTime = DateTime.now();

      return governorates;
    } catch (e) {
      // في حالة فشل جلب البيانات، إرجاع قائمة افتراضية محدودة
      return [
        'القاهرة',
        'الجيزة',
        'الإسكندرية',
        'الدقهلية',
        'الغربية',
        'القليوبية',
        'الشرقية',
        'المنوفية',
        'البحيرة',
        'كفر الشيخ',
        'دمياط',
        'الإسماعيلية',
        'بورسعيد',
        'السويس',
        'شمال سيناء',
        'جنوب سيناء',
        'البحر الأحمر',
        'الوادي الجديد',
        'مطروح',
        'الفيوم',
        'بني سويف',
        'المنيا',
        'أسيوط',
        'سوهاج',
        'قنا',
        'الأقصر',
        'أسوان',
      ];
    }
  }

  /// الحصول على قائمة المحافظات كخيارات للقائمة المنسدلة
  static Future<List<Map<String, String>>>
  getGovernoratesToDropdownItems() async {
    final governorates = await getGovernoratesList();
    return governorates
        .map((governorate) => {'value': governorate, 'label': governorate})
        .toList();
  }

  /// مسح البيانات المخزنة مؤقتاً
  static void clearCache() {
    _cachedGovernorates = null;
    _lastFetchTime = null;
  }
}
