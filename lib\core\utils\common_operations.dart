import 'dart:async';
import 'dart:collection';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// فئة العمليات المشتركة مع إدارة الأخطاء والتخزين المؤقت
class CommonOperations {
  static final Map<String, dynamic> _cache = {};
  static final Map<String, DateTime> _cacheTimestamps = {};
  static const Duration _defaultCacheDuration = Duration(minutes: 15);

  /// تنفيذ عملية مع معالجة الأخطاء وإعادة المحاولة
  static Future<T> executeWithErrorHandling<T>(
    Future<T> Function() operation, {
    String? operationName,
    int maxRetries = 3,
    Duration retryDelay = const Duration(seconds: 2),
    T? fallbackValue,
    bool logErrors = true,
  }) async {
    int attempts = 0;
    Exception? lastException;

    while (attempts < maxRetries) {
      try {
        attempts++;
        final result = await operation();

        // تسجيل نجاح العملية
        if (logErrors && attempts > 1) {
          debugPrint(
            '✅ نجحت العملية ${operationName ?? 'غير محددة'} في المحاولة $attempts',
          );
        }

        return result;
      } catch (e) {
        lastException = e is Exception ? e : Exception(e.toString());

        if (logErrors) {
          debugPrint(
            '❌ فشلت العملية ${operationName ?? 'غير محددة'} - المحاولة $attempts: $e',
          );
        }

        // إذا كانت هذه المحاولة الأخيرة، لا نحتاج للانتظار
        if (attempts < maxRetries) {
          await Future.delayed(retryDelay * attempts); // تأخير متدرج
        }
      }
    }

    // إذا فشلت جميع المحاولات
    if (fallbackValue != null) {
      if (logErrors) {
        debugPrint(
          '🔄 استخدام القيمة الاحتياطية للعملية ${operationName ?? 'غير محددة'}',
        );
      }
      return fallbackValue;
    }

    throw lastException ?? Exception('فشلت العملية بعد $maxRetries محاولات');
  }

  /// تنفيذ عملية مع التخزين المؤقت
  static Future<T> executeWithCache<T>(
    String cacheKey,
    Future<T> Function() operation, {
    Duration cacheDuration = _defaultCacheDuration,
    bool forceRefresh = false,
  }) async {
    // التحقق من وجود البيانات في التخزين المؤقت
    if (!forceRefresh && _cache.containsKey(cacheKey)) {
      final timestamp = _cacheTimestamps[cacheKey];
      if (timestamp != null &&
          DateTime.now().difference(timestamp) < cacheDuration) {
        debugPrint('📦 استخدام البيانات المخزنة مؤقتاً: $cacheKey');
        return _cache[cacheKey] as T;
      }
    }

    // تنفيذ العملية وحفظ النتيجة
    try {
      final result = await operation();
      _cache[cacheKey] = result;
      _cacheTimestamps[cacheKey] = DateTime.now();
      debugPrint('💾 حفظ البيانات في التخزين المؤقت: $cacheKey');
      return result;
    } catch (e) {
      // في حالة الخطأ، حاول استخدام البيانات المخزنة القديمة
      if (_cache.containsKey(cacheKey)) {
        debugPrint('⚠️ استخدام البيانات المخزنة القديمة بسبب خطأ: $cacheKey');
        return _cache[cacheKey] as T;
      }
      rethrow;
    }
  }

  /// مسح التخزين المؤقت
  static void clearCache([String? specificKey]) {
    if (specificKey != null) {
      _cache.remove(specificKey);
      _cacheTimestamps.remove(specificKey);
      debugPrint('🗑️ مسح التخزين المؤقت: $specificKey');
    } else {
      _cache.clear();
      _cacheTimestamps.clear();
      debugPrint('🗑️ مسح جميع البيانات المخزنة مؤقتاً');
    }
  }

  /// الحصول على إحصائيات التخزين المؤقت
  static Map<String, dynamic> getCacheStats() {
    final now = DateTime.now();
    int validEntries = 0;
    int expiredEntries = 0;

    for (final entry in _cacheTimestamps.entries) {
      if (now.difference(entry.value) < _defaultCacheDuration) {
        validEntries++;
      } else {
        expiredEntries++;
      }
    }

    return {
      'total_entries': _cache.length,
      'valid_entries': validEntries,
      'expired_entries': expiredEntries,
      'cache_size_mb': _calculateCacheSize(),
    };
  }

  /// حساب حجم التخزين المؤقت بالميجابايت
  static double _calculateCacheSize() {
    try {
      final jsonString = jsonEncode(_cache);
      final sizeInBytes = utf8.encode(jsonString).length;
      return sizeInBytes / (1024 * 1024); // تحويل إلى ميجابايت
    } catch (e) {
      return 0.0;
    }
  }

  /// تنظيف التخزين المؤقت المنتهي الصلاحية
  static void cleanExpiredCache() {
    final now = DateTime.now();
    final expiredKeys = <String>[];

    for (final entry in _cacheTimestamps.entries) {
      if (now.difference(entry.value) > _defaultCacheDuration) {
        expiredKeys.add(entry.key);
      }
    }

    for (final key in expiredKeys) {
      _cache.remove(key);
      _cacheTimestamps.remove(key);
    }

    if (expiredKeys.isNotEmpty) {
      debugPrint(
        '🧹 تم تنظيف ${expiredKeys.length} عنصر منتهي الصلاحية من التخزين المؤقت',
      );
    }
  }

  /// حفظ البيانات محلياً
  static Future<bool> saveToLocalStorage(String key, dynamic value) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = jsonEncode(value);
      return await prefs.setString(key, jsonString);
    } catch (e) {
      debugPrint('❌ خطأ في حفظ البيانات محلياً: $e');
      return false;
    }
  }

  /// قراءة البيانات محلياً
  static Future<T?> loadFromLocalStorage<T>(String key) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString(key);
      if (jsonString != null) {
        return jsonDecode(jsonString) as T;
      }
    } catch (e) {
      debugPrint('❌ خطأ في قراءة البيانات محلياً: $e');
    }
    return null;
  }

  /// تنفيذ عمليات متوازية مع حد أقصى للعمليات المتزامنة
  static Future<List<T>> executeParallel<T>(
    List<Future<T> Function()> operations, {
    int maxConcurrency = 3,
  }) async {
    final results = <T>[];
    final semaphore = Semaphore(maxConcurrency);

    final futures = operations.map((operation) async {
      await semaphore.acquire();
      try {
        return await operation();
      } finally {
        semaphore.release();
      }
    });

    return await Future.wait(futures);
  }

  /// تحويل البيانات بأمان
  static T? safeCast<T>(dynamic value) {
    try {
      return value as T;
    } catch (e) {
      debugPrint('⚠️ فشل في تحويل البيانات: $e');
      return null;
    }
  }

  /// التحقق من صحة البريد الإلكتروني
  static bool isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  /// التحقق من صحة رقم الهاتف المصري
  static bool isValidEgyptianPhone(String phone) {
    return RegExp(r'^(010|011|012|015)[0-9]{8}$').hasMatch(phone);
  }
}

/// فئة مساعدة لإدارة العمليات المتزامنة
class Semaphore {
  final int maxCount;
  int _currentCount;
  final Queue<Completer<void>> _waitQueue = Queue<Completer<void>>();

  Semaphore(this.maxCount) : _currentCount = maxCount;

  Future<void> acquire() async {
    if (_currentCount > 0) {
      _currentCount--;
      return;
    }

    final completer = Completer<void>();
    _waitQueue.add(completer);
    return completer.future;
  }

  void release() {
    if (_waitQueue.isNotEmpty) {
      final completer = _waitQueue.removeFirst();
      completer.complete();
    } else {
      _currentCount++;
    }
  }
}
