import 'package:flutter/material.dart';

/// Custom theme extension for gradient buttons
class GradientButtonTheme extends ThemeExtension<GradientButtonTheme> {
  final BoxDecoration decoration;

  const GradientButtonTheme({required this.decoration});

  @override
  GradientButtonTheme copyWith({BoxDecoration? decoration}) {
    return GradientButtonTheme(decoration: decoration ?? this.decoration);
  }

  @override
  GradientButtonTheme lerp(GradientButtonTheme? other, double t) {
    if (other is! GradientButtonTheme) {
      return this;
    }
    return GradientButtonTheme(
      decoration: BoxDecoration.lerp(decoration, other.decoration, t)!,
    );
  }
}

/// امتداد لـ ElevatedButton لدعم التدرجات اللونية
///
/// يمكن استخدام هذا الزر بدلاً من ElevatedButton العادي لتطبيق التدرجات اللونية
class GradientElevatedButton extends StatelessWidget {
  final Widget child;
  final VoidCallback? onPressed;
  final BoxDecoration? decoration;

  const GradientElevatedButton({
    super.key,
    required this.child,
    this.onPressed,
    this.decoration,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final gradientTheme = theme.extension<GradientButtonTheme>();
    final buttonStyle = theme.elevatedButtonTheme.style;

    // استخدام الديكوريشن المخصص أو الافتراضي من الثيم
    final effectiveDecoration = decoration ?? gradientTheme?.decoration;

    return Container(
      decoration: effectiveDecoration,
      child: ElevatedButton(
        onPressed: onPressed,
        style: buttonStyle?.copyWith(
          backgroundColor: WidgetStateProperty.all(Colors.transparent),
          elevation: WidgetStateProperty.all(0),
        ),
        child: child,
      ),
    );
  }
}
