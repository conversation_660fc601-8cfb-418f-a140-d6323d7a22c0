import 'package:flutter_dotenv/flutter_dotenv.dart';

/// 🔧 تكوين Cloudinary المتقدم والاحترافي
/// 
/// ملف تكوين شامل لجميع إعدادات Cloudinary والخدمات المرتبطة
/// يدعم التخصيص المتقدم والبيئات المختلفة
class CloudinaryConfig {
  // 🌐 إعدادات الاتصال الأساسية
  static String get cloudName => dotenv.env['CLOUDINARY_CLOUD_NAME'] ?? '';
  static String get apiKey => dotenv.env['CLOUDINARY_API_KEY'] ?? '';
  static String get apiSecret => dotenv.env['CLOUDINARY_API_SECRET'] ?? '';
  static String get baseUrl => 'https://res.cloudinary.com/$cloudName';
  static String get uploadUrl => 'https://api.cloudinary.com/v1_1/$cloudName';

  // 📁 مجلدات التنظيم
  static const String productImagesFolder = 'products';
  static const String categoryImagesFolder = 'categories';
  static const String bannerImagesFolder = 'banners';
  static const String videoFolder = 'videos';
  static const String documentsFolder = 'documents';
  static const String tempFolder = 'temp';

  // 📏 حدود الملفات
  static const int maxImageSizeMB = 25;
  static const int maxVideoSizeMB = 100;
  static const int maxDocumentSizeMB = 10;
  static const int maxConcurrentUploads = 5;
  static const int maxRetries = 5;

  // ⏱️ مهلات زمنية
  static const Duration defaultTimeout = Duration(seconds: 45);
  static const Duration uploadTimeout = Duration(minutes: 2);
  static const Duration videoUploadTimeout = Duration(minutes: 5);
  static const Duration connectionTimeout = Duration(seconds: 10);

  // 💾 إعدادات التخزين المؤقت
  static const Duration cacheExpiryDuration = Duration(hours: 6);
  static const Duration urlCacheDuration = Duration(days: 7);
  static const int maxMemoryCacheSize = 50;
  static const int maxMemoryCacheSizeMB = 100;

  // 📱 إعدادات الأجهزة المختلفة
  static const Map<String, DeviceConfig> deviceConfigs = {
    'mobile': DeviceConfig(
      maxWidth: 400,
      quality: 75,
      format: 'webp',
    ),
    'tablet': DeviceConfig(
      maxWidth: 800,
      quality: 80,
      format: 'webp',
    ),
    'desktop': DeviceConfig(
      maxWidth: 1200,
      quality: 85,
      format: 'auto',
    ),
  };

  // 🎯 إعدادات المنتجات
  static const Map<String, ProductImageConfig> productImageConfigs = {
    'thumbnail': ProductImageConfig(
      width: 150,
      height: 150,
      crop: 'fill',
      quality: 70,
    ),
    'small': ProductImageConfig(
      width: 300,
      height: 300,
      crop: 'fit',
      quality: 75,
    ),
    'medium': ProductImageConfig(
      width: 500,
      height: 500,
      crop: 'fit',
      quality: 80,
    ),
    'large': ProductImageConfig(
      width: 800,
      height: 800,
      crop: 'fit',
      quality: 85,
    ),
    'xlarge': ProductImageConfig(
      width: 1200,
      height: 1200,
      crop: 'fit',
      quality: 90,
    ),
  };

  // 📐 أحجام الصور المتجاوبة
  static const List<int> responsiveWidths = [320, 480, 640, 800, 960, 1280, 1600, 1920];

  // 🎨 تحويلات افتراضية
  static const List<String> defaultImageTransformations = [
    'f_auto',    // تحسين الصيغة تلقائياً
    'q_auto',    // تحسين الجودة تلقائياً
    'dpr_auto',  // دعم الشبكية تلقائياً
    'fl_progressive', // تحميل تدريجي
  ];

  // 🎬 إعدادات الفيديو
  static const Map<String, VideoConfig> videoConfigs = {
    'thumbnail': VideoConfig(
      width: 300,
      height: 200,
      crop: 'fill',
      startOffset: 0,
    ),
    'preview': VideoConfig(
      width: 640,
      height: 360,
      quality: 'auto',
      format: 'mp4',
    ),
    'mobile': VideoConfig(
      width: 480,
      quality: 'auto',
      format: 'mp4',
    ),
    'hd': VideoConfig(
      width: 1280,
      height: 720,
      quality: 'auto',
      format: 'mp4',
    ),
  };

  // 📋 أنواع الملفات المدعومة
  static const List<String> supportedImageFormats = [
    'jpg', 'jpeg', 'png', 'webp', 'gif', 'bmp', 'tiff', 'svg'
  ];

  static const List<String> supportedVideoFormats = [
    'mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv', '3gp'
  ];

  static const List<String> supportedDocumentFormats = [
    'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'
  ];

  // 🛡️ إعدادات الأمان
  static const bool enableSignedUrls = true;
  static const bool enableSecureDelivery = true;
  static const Duration signedUrlExpiry = Duration(hours: 24);
  static const int maxUploadAttempts = 3;

  // 📊 إعدادات المراقبة
  static const bool enableAnalytics = true;
  static const bool enablePerformanceMonitoring = true;
  static const Duration analyticsReportInterval = Duration(hours: 1);

  // 🌍 إعدادات CDN
  static const bool enableCdn = true;
  static const String cdnDomain = 'res.cloudinary.com';
  static const bool enableGzip = true;
  static const bool enableBrotli = true;

  // 🔄 إعدادات المزامنة
  static const bool enableAutoSync = true;
  static const Duration syncInterval = Duration(minutes: 30);
  static const int maxSyncRetries = 3;

  // 🧹 إعدادات التنظيف
  static const Duration cleanupInterval = Duration(hours: 6);
  static const Duration tempFileRetention = Duration(hours: 24);
  static const Duration logRetention = Duration(days: 7);

  // 🎛️ إعدادات متقدمة
  static const bool enableImageOptimization = true;
  static const bool enableVideoOptimization = true;
  static const bool enableAutoBackup = true;
  static const bool enableVersioning = true;

  /// 🔍 التحقق من صحة التكوين
  static bool validateConfig() {
    final errors = <String>[];

    if (cloudName.isEmpty) errors.add('CLOUDINARY_CLOUD_NAME مطلوب');
    if (apiKey.isEmpty) errors.add('CLOUDINARY_API_KEY مطلوب');
    if (apiSecret.isEmpty) errors.add('CLOUDINARY_API_SECRET مطلوب');

    if (errors.isNotEmpty) {
      throw Exception('أخطاء في تكوين Cloudinary: ${errors.join(', ')}');
    }

    return true;
  }

  /// 🌍 الحصول على إعدادات البيئة
  static Environment getCurrentEnvironment() {
    final env = dotenv.env['ENVIRONMENT'] ?? 'development';
    switch (env.toLowerCase()) {
      case 'production':
        return Environment.production;
      case 'staging':
        return Environment.staging;
      case 'testing':
        return Environment.testing;
      default:
        return Environment.development;
    }
  }

  /// ⚙️ الحصول على إعدادات مخصصة للبيئة
  static Map<String, dynamic> getEnvironmentConfig() {
    final env = getCurrentEnvironment();
    
    switch (env) {
      case Environment.production:
        return {
          'max_file_size_mb': 50,
          'enable_debug': false,
          'cache_duration_hours': 24,
          'max_concurrent_uploads': 10,
        };
      case Environment.staging:
        return {
          'max_file_size_mb': 30,
          'enable_debug': true,
          'cache_duration_hours': 12,
          'max_concurrent_uploads': 5,
        };
      case Environment.testing:
        return {
          'max_file_size_mb': 10,
          'enable_debug': true,
          'cache_duration_hours': 1,
          'max_concurrent_uploads': 2,
        };
      default: // development
        return {
          'max_file_size_mb': 25,
          'enable_debug': true,
          'cache_duration_hours': 6,
          'max_concurrent_uploads': 3,
        };
    }
  }
}

/// 📱 تكوين الأجهزة
class DeviceConfig {
  final int maxWidth;
  final int quality;
  final String format;

  const DeviceConfig({
    required this.maxWidth,
    required this.quality,
    required this.format,
  });
}

/// 🛍️ تكوين صور المنتجات
class ProductImageConfig {
  final int width;
  final int height;
  final String crop;
  final int quality;

  const ProductImageConfig({
    required this.width,
    required this.height,
    required this.crop,
    required this.quality,
  });
}

/// 🎬 تكوين الفيديوهات
class VideoConfig {
  final int? width;
  final int? height;
  final String? quality;
  final String? format;
  final String? crop;
  final int? startOffset;

  const VideoConfig({
    this.width,
    this.height,
    this.quality,
    this.format,
    this.crop,
    this.startOffset,
  });
}

/// 🌍 بيئات التشغيل
enum Environment {
  development,
  testing,
  staging,
  production,
}

/// 📊 إعدادات التحليلات
class AnalyticsConfig {
  static const bool trackUploads = true;
  static const bool trackTransformations = true;
  static const bool trackErrors = true;
  static const bool trackPerformance = true;
  static const bool trackUsage = true;
}

/// 🔔 إعدادات الإشعارات
class NotificationConfig {
  static const bool enableUploadNotifications = true;
  static const bool enableErrorNotifications = true;
  static const bool enableMaintenanceNotifications = true;
  static const bool enableQuotaNotifications = true;
}
