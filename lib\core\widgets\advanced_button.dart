import 'package:flutter/material.dart';
import 'package:motorcycle_parts_shop/core/theme/app_theme.dart';

/// زر متقدم مع تأثيرات بصرية ورسوم متحركة
/// يوفر هذا الزر مظهرًا متسقًا عبر التطبيق
class AdvancedButton extends StatefulWidget {
  final String text; // النص المعروض على الزر
  final VoidCallback onPressed; // الدالة التي يتم استدعاؤها عند الضغط على الزر
  final IconData? icon; // أيقونة اختيارية تعرض بجانب النص
  final bool isLoading; // حالة التحميل، إذا كانت `true` يتم عرض مؤشر تحميل
  final bool isDisabled; // حالة التعطيل، إذا كانت `true` يتم تعطيل الزر
  final Color? backgroundColor; // لون خلفية الزر
  final Color? textColor; // لون النص
  final double height; // ارتفاع الزر
  final double? width; // عرض الزر (اختياري)
  final double borderRadius; // نصف قطر حافة الزر
  final EdgeInsets padding; // الحشو الداخلي للزر
  final ButtonType buttonType; // نوع الزر (مملوء، مخطط، نصي، نغمي)
  final ButtonSize buttonSize; // حجم الزر (صغير، متوسط، كبير)
  final bool showShadow; // عرض الظل (إذا كان `true`)
  final bool showRipple; // عرض تأثير التموج عند الضغط (إذا كان `true`)
  final Duration animationDuration; // مدة الرسوم المتحركة
  final Widget? prefix; // عنصر اختياري يعرض قبل النص
  final Widget? suffix; // عنصر اختياري يعرض بعد النص

  const AdvancedButton({
    super.key,
    required this.text,
    required this.onPressed,
    this.icon,
    this.isLoading = false,
    this.isDisabled = false,
    this.backgroundColor,
    this.textColor,
    this.height = 48.0,
    this.width,
    this.borderRadius = 12.0,
    this.padding = const EdgeInsets.symmetric(horizontal: 24.0),
    this.buttonType = ButtonType.filled,
    this.buttonSize = ButtonSize.medium,
    this.showShadow = true,
    this.showRipple = true,
    this.animationDuration = const Duration(milliseconds: 200),
    this.prefix,
    this.suffix,
  });

  @override
  State<AdvancedButton> createState() => _AdvancedButtonState();
}

class _AdvancedButtonState extends State<AdvancedButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController; // تحكم في الرسوم المتحركة
  late Animation<double> _scaleAnimation; // تحريك تغيير الحجم
  // حالة الضغط على الزر

  @override
  void initState() {
    super.initState();
    // تهيئة التحكم في الرسوم المتحركة
    _animationController = AnimationController(
      vsync: this,
      duration: widget.animationDuration,
    );
    // تعريف تحريك تغيير الحجم
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.95).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _animationController.dispose(); // التخلص من التحكم في الرسوم المتحركة
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    // عند الضغط على الزر
    if (!widget.isDisabled && !widget.isLoading) {
      setState(() {});
      _animationController.forward(); // تشغيل الرسوم المتحركة
    }
  }

  void _handleTapUp(TapUpDetails details) {
    // عند رفع الضغط عن الزر
    if (!widget.isDisabled && !widget.isLoading) {
      setState(() {});
      _animationController.reverse(); // عكس الرسوم المتحركة
    }
  }

  void _handleTapCancel() {
    // عند إلغاء الضغط
    if (!widget.isDisabled && !widget.isLoading) {
      setState(() {});
      _animationController.reverse(); // عكس الرسوم المتحركة
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    // تحديد الألوان بناءً على نوع الزر وحالته
    Color bgColor;
    Color textCol;
    Color borderColor;

    switch (widget.buttonType) {
      case ButtonType.filled:
        bgColor = widget.backgroundColor ?? theme.colorScheme.primary;
        textCol = widget.textColor ?? theme.colorScheme.onPrimary;
        borderColor = Colors.transparent;
        break;
      case ButtonType.outlined:
        bgColor = Colors.transparent;
        textCol = widget.backgroundColor ?? theme.colorScheme.primary;
        borderColor = widget.backgroundColor ?? theme.colorScheme.primary;
        break;
      case ButtonType.text:
        bgColor = Colors.transparent;
        textCol = widget.backgroundColor ?? theme.colorScheme.primary;
        borderColor = Colors.transparent;
        break;
      case ButtonType.tonal:
        bgColor = (widget.backgroundColor ?? theme.colorScheme.primary)
            .withOpacity(0.12);
        textCol = widget.backgroundColor ?? theme.colorScheme.primary;
        borderColor = Colors.transparent;
        break;
    }

    // تطبيق حالة التعطيل
    if (widget.isDisabled) {
      bgColor =
          widget.buttonType == ButtonType.filled
              ? theme.disabledColor.withOpacity(0.12)
              : Colors.transparent;
      textCol = theme.disabledColor;
      borderColor =
          widget.buttonType == ButtonType.outlined
              ? theme.disabledColor
              : Colors.transparent;
    }

    // تحديد نمط النص بناءً على حجم الزر
    TextStyle textStyle;
    double iconSize;
    double loadingSize;

    switch (widget.buttonSize) {
      case ButtonSize.small:
        textStyle = theme.textTheme.labelMedium!.copyWith(color: textCol);
        iconSize = 16.0;
        loadingSize = 14.0;
        break;
      case ButtonSize.medium:
        textStyle = theme.textTheme.labelLarge!.copyWith(color: textCol);
        iconSize = 18.0;
        loadingSize = 18.0;
        break;
      case ButtonSize.large:
        textStyle = theme.textTheme.titleSmall!.copyWith(color: textCol);
        iconSize = 20.0;
        loadingSize = 20.0;
        break;
    }

    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(scale: _scaleAnimation.value, child: child);
      },
      child: GestureDetector(
        onTapDown: _handleTapDown,
        onTapUp: _handleTapUp,
        onTapCancel: _handleTapCancel,
        child: Container(
          height: widget.height,
          width: widget.width,
          decoration: BoxDecoration(
            color: bgColor,
            borderRadius: BorderRadius.circular(widget.borderRadius),
            border:
                widget.buttonType == ButtonType.outlined
                    ? Border.all(color: borderColor, width: 1.5)
                    : null,
            boxShadow:
                widget.showShadow &&
                        !widget.isDisabled &&
                        widget.buttonType == ButtonType.filled
                    ? [
                      BoxShadow(
                        color: bgColor.withOpacity(isDarkMode ? 0.3 : 0.2),
                        blurRadius: 8,
                        offset: const Offset(0, 3),
                        spreadRadius: 0,
                      ),
                    ]
                    : null,
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap:
                  widget.isDisabled || widget.isLoading
                      ? null
                      : widget.onPressed,
              splashColor:
                  widget.showRipple
                      ? bgColor.withOpacity(0.1)
                      : Colors.transparent,
              highlightColor:
                  widget.showRipple
                      ? bgColor.withOpacity(0.05)
                      : Colors.transparent,
              borderRadius: BorderRadius.circular(widget.borderRadius),
              child: Padding(
                padding: widget.padding,
                child: Row(
                  mainAxisSize:
                      widget.width != null
                          ? MainAxisSize.max
                          : MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    if (widget.prefix != null) ...[
                      widget.prefix!,
                      const SizedBox(width: 8),
                    ],
                    if (widget.icon != null && !widget.isLoading) ...[
                      Icon(widget.icon, size: iconSize, color: textCol),
                      const SizedBox(width: 8),
                    ],
                    if (widget.isLoading) ...[
                      _buildLoadingIndicator(loadingSize, textCol),
                      const SizedBox(width: 8),
                    ],
                    Flexible(
                      child: Text(
                        widget.text,
                        style: textStyle,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    if (widget.suffix != null) ...[
                      const SizedBox(width: 8),
                      widget.suffix!,
                    ],
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingIndicator(double size, Color color) {
    return SizedBox(
      width: size,
      height: size,
      child: CircularProgressIndicator(
        strokeWidth: 2.0,
        valueColor: AlwaysStoppedAnimation<Color>(color),
      ),
    );
  }
}

/// فئة لإنشاء أنماط أزرار شائعة
class AdvancedButtons {
  /// إنشاء زر أساسي
  static AdvancedButton primary({
    required String text,
    required VoidCallback onPressed,
    IconData? icon,
    bool isLoading = false,
    bool isDisabled = false,
    double? width,
    ButtonSize size = ButtonSize.medium,
  }) {
    return AdvancedButton(
      text: text,
      onPressed: onPressed,
      icon: icon,
      isLoading: isLoading,
      isDisabled: isDisabled,
      buttonType: ButtonType.filled,
      buttonSize: size,
      width: width,
    );
  }

  /// إنشاء زر ثانوي
  static AdvancedButton secondary({
    required String text,
    required VoidCallback onPressed,
    IconData? icon,
    bool isLoading = false,
    bool isDisabled = false,
    double? width,
    ButtonSize size = ButtonSize.medium,
  }) {
    return AdvancedButton(
      text: text,
      onPressed: onPressed,
      icon: icon,
      isLoading: isLoading,
      isDisabled: isDisabled,
      buttonType: ButtonType.outlined,
      buttonSize: size,
      width: width,
    );
  }

  /// إنشاء زر نصي
  static AdvancedButton text({
    required String text,
    required VoidCallback onPressed,
    IconData? icon,
    bool isLoading = false,
    bool isDisabled = false,
    ButtonSize size = ButtonSize.medium,
  }) {
    return AdvancedButton(
      text: text,
      onPressed: onPressed,
      icon: icon,
      isLoading: isLoading,
      isDisabled: isDisabled,
      buttonType: ButtonType.text,
      buttonSize: size,
      showShadow: false,
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
    );
  }

  /// إنشاء زر نغمي
  static AdvancedButton tonal({
    required String text,
    required VoidCallback onPressed,
    IconData? icon,
    bool isLoading = false,
    bool isDisabled = false,
    double? width,
    ButtonSize size = ButtonSize.medium,
  }) {
    return AdvancedButton(
      text: text,
      onPressed: onPressed,
      icon: icon,
      isLoading: isLoading,
      isDisabled: isDisabled,
      buttonType: ButtonType.tonal,
      buttonSize: size,
      width: width,
      showShadow: false,
    );
  }

  /// إنشاء زر نجاح
  static AdvancedButton success({
    required String text,
    required VoidCallback onPressed,
    IconData? icon,
    bool isLoading = false,
    bool isDisabled = false,
    double? width,
    ButtonSize size = ButtonSize.medium,
  }) {
    return AdvancedButton(
      text: text,
      onPressed: onPressed,
      icon: icon,
      isLoading: isLoading,
      isDisabled: isDisabled,
      backgroundColor: AppTheme.successColor,
      buttonType: ButtonType.filled,
      buttonSize: size,
      width: width,
    );
  }

  /// إنشاء زر خطر
  static AdvancedButton danger({
    required String text,
    required VoidCallback onPressed,
    IconData? icon,
    bool isLoading = false,
    bool isDisabled = false,
    double? width,
    ButtonSize size = ButtonSize.medium,
  }) {
    return AdvancedButton(
      text: text,
      onPressed: onPressed,
      icon: icon,
      isLoading: isLoading,
      isDisabled: isDisabled,
      backgroundColor: AppTheme.errorColor,
      buttonType: ButtonType.filled,
      buttonSize: size,
      width: width,
    );
  }
}

/// أنواع الأزرار
enum ButtonType {
  filled, // مملوء
  outlined, // مخطط
  text, // نصي
  tonal, // نغمي
}

/// أحجام الأزرار
enum ButtonSize {
  small, // صغير
  medium, // متوسط
  large, // كبير
}
