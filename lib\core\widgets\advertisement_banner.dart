import 'package:cached_network_image/cached_network_image.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:motorcycle_parts_shop/core/services/advertisement_service.dart';
import 'package:motorcycle_parts_shop/models/advertisement_model.dart';
import 'package:url_launcher/url_launcher.dart';

/// بانر الإعلانات
/// يعرض الإعلانات في شكل شريط متحرك (كاروسيل)
class AdvertisementBanner extends StatefulWidget {
  final List<AdvertisementModel> advertisements;
  final double height;
  final bool autoPlay;
  final Duration autoPlayInterval;
  final Duration autoPlayAnimationDuration;
  final bool enlargeCenterPage;
  final Function(int)? onPageChanged;

  const AdvertisementBanner({
    super.key,
    required this.advertisements,
    this.height = 180,
    this.autoPlay = true,
    this.autoPlayInterval = const Duration(seconds: 5),
    this.autoPlayAnimationDuration = const Duration(milliseconds: 800),
    this.enlargeCenterPage = true,
    this.onPageChanged,
  });

  @override
  State<AdvertisementBanner> createState() => _AdvertisementBannerState();
}

class _AdvertisementBannerState extends State<AdvertisementBanner> {
  int _currentIndex = 0;
  final CarouselSliderController _carouselController =
      CarouselSliderController();

  @override
  void initState() {
    super.initState();
    // تسجيل مشاهدة للإعلانات عند تحميل البانر
    _recordImpressions();
  }

  // تسجيل مشاهدة للإعلانات
  void _recordImpressions() {
    if (widget.advertisements.isNotEmpty) {
      for (var ad in widget.advertisements) {
        AdvertisementService().incrementImpressionCount(ad.id);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.advertisements.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      children: [
        CarouselSlider.builder(
          carouselController: _carouselController,
          itemCount: widget.advertisements.length,
          options: CarouselOptions(
            height: widget.height,
            viewportFraction: 0.9,
            initialPage: 0,
            enableInfiniteScroll: widget.advertisements.length > 1,
            reverse: false,
            autoPlay: widget.autoPlay && widget.advertisements.length > 1,
            autoPlayInterval: widget.autoPlayInterval,
            autoPlayAnimationDuration: widget.autoPlayAnimationDuration,
            autoPlayCurve: Curves.fastOutSlowIn,
            enlargeCenterPage: widget.enlargeCenterPage,
            scrollDirection: Axis.horizontal,
            onPageChanged: (index, reason) {
              setState(() {
                _currentIndex = index;
              });
              if (widget.onPageChanged != null) {
                widget.onPageChanged!(index);
              }
            },
          ),
          itemBuilder: (context, index, realIndex) {
            final advertisement = widget.advertisements[index];
            return Container(
              width: MediaQuery.of(context).size.width,
              margin: const EdgeInsets.symmetric(horizontal: 5.0),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.2),
                    spreadRadius: 1,
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: GestureDetector(
                  onTap: () async {
                    // تسجيل نقرة على الإعلان
                    AdvertisementService().incrementClickCount(
                      advertisement.id,
                    );

                    if (advertisement.targetUrl != null &&
                        advertisement.targetUrl!.isNotEmpty) {
                      final Uri url = Uri.parse(advertisement.targetUrl!);
                      if (await canLaunchUrl(url)) {
                        await launchUrl(url);
                      }
                    }
                  },
                  child: Stack(
                    children: [
                      // Advertisement image
                      CachedNetworkImage(
                        imageUrl: advertisement.imageUrl,
                        fit: BoxFit.cover,
                        width: double.infinity,
                        height: double.infinity,
                        errorWidget:
                            (context, url, error) => Container(
                              color: Colors.grey[200],
                              child: const Center(
                                child: Icon(
                                  Icons.image_not_supported_outlined,
                                  size: 40,
                                  color: Colors.grey,
                                ),
                              ),
                            ),
                        placeholder:
                            (context, url) => Container(
                              color: Colors.grey[200],
                              child: const Center(
                                child: CircularProgressIndicator(),
                              ),
                            ),
                      ),
                      // Optional title overlay
                      if (advertisement.title.isNotEmpty)
                        Positioned(
                          bottom: 0,
                          left: 0,
                          right: 0,
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                              vertical: 8.0,
                              horizontal: 16.0,
                            ),
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.bottomCenter,
                                end: Alignment.topCenter,
                                colors: [
                                  Colors.black.withOpacity(0.7),
                                  Colors.transparent,
                                ],
                              ),
                            ),
                            child: Text(
                              advertisement.title,
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 16.0,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
        if (widget.advertisements.length > 1)
          Padding(
            padding: const EdgeInsets.only(top: 8.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children:
                  widget.advertisements.asMap().entries.map((entry) {
                    return GestureDetector(
                      onTap: () => _carouselController.jumpToPage(entry.key),
                      child: Container(
                        width: 8.0,
                        height: 8.0,
                        margin: const EdgeInsets.symmetric(horizontal: 4.0),
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Theme.of(context).primaryColor.withOpacity(
                            _currentIndex == entry.key ? 0.9 : 0.4,
                          ),
                        ),
                      ),
                    );
                  }).toList(),
            ),
          ),
      ],
    );
  }
}
