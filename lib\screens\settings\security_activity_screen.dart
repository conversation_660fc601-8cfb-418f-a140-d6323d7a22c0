import 'package:flutter/material.dart';
import 'package:motorcycle_parts_shop/core/services/auth_supabase_service.dart';
import 'package:motorcycle_parts_shop/core/widgets/custom_app_bar.dart';
import 'package:motorcycle_parts_shop/core/widgets/empty_state_widget.dart';
import 'package:motorcycle_parts_shop/core/widgets/loading_indicator.dart';
import 'package:provider/provider.dart';

/// شاشة سجل النشاط والأجهزة المتصلة
/// تعرض سجل نشاط المستخدم والأجهزة المتصلة بحسابه
class SecurityActivityScreen extends StatefulWidget {
  const SecurityActivityScreen({super.key});

  @override
  State<SecurityActivityScreen> createState() => _SecurityActivityScreenState();
}

class _SecurityActivityScreenState extends State<SecurityActivityScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isLoading = true;
  List<Map<String, dynamic>> _activityLogs = [];
  List<Map<String, dynamic>> _connectedDevices = [];
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final authService = Provider.of<AuthSupabaseService>(
        context,
        listen: false,
      );

      // تحميل سجل النشاط
      final activityLogs = await _fetchActivityLogs(authService);

      // تحميل الأجهزة المتصلة
      final connectedDevices = await _fetchConnectedDevices(authService);

      setState(() {
        _activityLogs = activityLogs;
        _connectedDevices = connectedDevices;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'حدث خطأ أثناء تحميل البيانات: $e';
        _isLoading = false;
      });
    }
  }

  /// جلب سجل النشاط
  Future<List<Map<String, dynamic>>> _fetchActivityLogs(
    AuthSupabaseService authService,
  ) async {
    try {
      // جلب سجل النشاط من قاعدة البيانات
      final userId = authService.currentUser?.id;
      if (userId == null) {
        return [];
      }

      final response = await authService.client
          .from('user_activity_logs')
          .select()
          .eq('user_id', userId)
          .order('created_at', ascending: false)
          .limit(20);

      // تحويل البيانات إلى التنسيق المطلوب
      return (response as List).map((log) {
        return {
          'id': log['id'],
          'activity_type': log['activity_type'],
          'device': log['device_info'],
          'ip_address': log['ip_address'],
          'location': log['location'] ?? 'غير معروف',
          'timestamp': DateTime.parse(log['created_at']),
        };
      }).toList();
    } catch (e) {
      debugPrint('خطأ في جلب سجل النشاط: $e');
      return [];
    }
  }

  /// جلب الأجهزة المتصلة
  Future<List<Map<String, dynamic>>> _fetchConnectedDevices(
    AuthSupabaseService authService,
  ) async {
    try {
      // جلب الأجهزة المتصلة من قاعدة البيانات
      final userId = authService.currentUser?.id;
      if (userId == null) {
        return [];
      }

      // الحصول على معرف الجلسة الحالية
      final currentSession = authService.client.auth.currentSession;
      final currentSessionId = currentSession?.accessToken;

      final response = await authService.client
          .from('user_sessions')
          .select()
          .eq('user_id', userId)
          .order('last_active_at', ascending: false);

      // تحويل البيانات إلى التنسيق المطلوب
      return (response as List).map((device) {
        final sessionId = device['session_id'];
        return {
          'id': sessionId,
          'device_name': device['device_name'] ?? 'جهاز غير معروف',
          'device_type': _getDeviceTypeFromUserAgent(
            device['user_agent'] ?? '',
          ),
          'last_active': DateTime.parse(device['last_active_at']),
          'is_current': sessionId == currentSessionId,
          'location': device['location'] ?? 'غير معروف',
          'ip_address': device['ip_address'] ?? 'غير معروف',
        };
      }).toList();
    } catch (e) {
      debugPrint('خطأ في جلب الأجهزة المتصلة: $e');
      return [];
    }
  }

  /// تحديد نوع الجهاز من معلومات المتصفح
  String _getDeviceTypeFromUserAgent(String userAgent) {
    final userAgentLower = userAgent.toLowerCase();

    if (userAgentLower.contains('iphone') ||
        userAgentLower.contains('android') &&
            !userAgentLower.contains('tablet') ||
        userAgentLower.contains('mobile')) {
      return 'mobile';
    } else if (userAgentLower.contains('ipad') ||
        userAgentLower.contains('tablet')) {
      return 'tablet';
    } else if (userAgentLower.contains('windows') ||
        userAgentLower.contains('macintosh') ||
        userAgentLower.contains('linux')) {
      return 'desktop';
    } else {
      return 'other';
    }
  }

  /// حذف جهاز متصل
  Future<void> _removeDevice(String sessionId) async {
    setState(() {
      _isLoading = true;
    });

    try {
      final authService = Provider.of<AuthSupabaseService>(
        context,
        listen: false,
      );

      // التحقق من أن الجلسة ليست الجلسة الحالية
      final currentSession = authService.client.auth.currentSession;
      final currentSessionId = currentSession?.accessToken;
      if (sessionId == currentSessionId) {
        _showErrorSnackBar('لا يمكن حذف الجهاز الحالي');
        setState(() {
          _isLoading = false;
        });
        return;
      }

      // حذف الجلسة من قاعدة البيانات
      await authService.client
          .from('user_sessions')
          .delete()
          .eq('session_id', sessionId);

      // إعادة تحميل البيانات
      await _loadData();

      _showSuccessSnackBar('تم حذف الجهاز بنجاح');
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showErrorSnackBar('فشل حذف الجهاز: $e');
    }
  }

  /// حذف جميع البيانات
  Future<void> _showDeleteAllDataConfirmation() async {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('حذف جميع البيانات'),
            content: const Text(
              'هل أنت متأكد من حذف جميع بياناتك؟ هذا الإجراء لا يمكن التراجع عنه وسيؤدي إلى حذف جميع بياناتك الشخصية والتفضيلات.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  _deleteAllData();
                },
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: const Text('حذف'),
              ),
            ],
          ),
    );
  }

  /// حذف جميع البيانات
  Future<void> _deleteAllData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final authService = Provider.of<AuthSupabaseService>(
        context,
        listen: false,
      );

      final userId = authService.currentUser?.id;
      if (userId == null) {
        _showErrorSnackBar('المستخدم غير مسجل الدخول');
        setState(() {
          _isLoading = false;
        });
        return;
      }

      // حذف سجل النشاط
      await authService.client
          .from('user_activity_logs')
          .delete()
          .eq('user_id', userId);

      // حذف الأجهزة المتصلة (باستثناء الجهاز الحالي)
      final currentSession = authService.client.auth.currentSession;
      final currentSessionId = currentSession?.accessToken;
      if (currentSessionId != null) {
        await authService.client
            .from('user_sessions')
            .delete()
            .eq('user_id', userId)
            .neq('session_id', currentSessionId);
      }

      // إعادة تحميل البيانات
      await _loadData();

      _showSuccessSnackBar('تم حذف جميع البيانات بنجاح');
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showErrorSnackBar('فشل حذف البيانات: $e');
    }
  }

  /// عرض رسالة خطأ
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// عرض رسالة نجاح
  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// تنسيق التاريخ والوقت
  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays} يوم مضى';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} ساعة مضت';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} دقيقة مضت';
    } else {
      return 'الآن';
    }
  }

  /// الحصول على أيقونة نوع النشاط
  IconData _getActivityIcon(String activityType) {
    switch (activityType) {
      case 'login':
        return Icons.login;
      case 'logout':
        return Icons.logout;
      case 'password_change':
        return Icons.password;
      case 'profile_update':
        return Icons.person;
      case 'order_placed':
        return Icons.shopping_cart;
      default:
        return Icons.info;
    }
  }

  /// الحصول على وصف نوع النشاط
  String _getActivityDescription(String activityType) {
    switch (activityType) {
      case 'login':
        return 'تسجيل دخول';
      case 'logout':
        return 'تسجيل خروج';
      case 'password_change':
        return 'تغيير كلمة المرور';
      case 'profile_update':
        return 'تحديث الملف الشخصي';
      case 'order_placed':
        return 'طلب جديد';
      default:
        return 'نشاط غير معروف';
    }
  }

  /// الحصول على أيقونة نوع الجهاز
  IconData _getDeviceIcon(String deviceType) {
    switch (deviceType) {
      case 'desktop':
        return Icons.computer;
      case 'mobile':
        return Icons.smartphone;
      case 'tablet':
        return Icons.tablet;
      default:
        return Icons.devices;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(title: 'سجل النشاط والأجهزة', showBackButton: true),
      body:
          _isLoading
              ? const Center(child: LoadingIndicator())
              : _errorMessage != null
              ? Center(child: Text(_errorMessage!))
              : Column(
                children: [
                  // شريط التبويب
                  Container(
                    color: Theme.of(context).primaryColor,
                    child: TabBar(
                      controller: _tabController,
                      indicatorColor: Colors.white,
                      labelColor: Colors.white,
                      unselectedLabelColor: Colors.white70,
                      tabs: const [
                        Tab(text: 'سجل النشاط'),
                        Tab(text: 'الأجهزة المتصلة'),
                      ],
                    ),
                  ),

                  // محتوى التبويب
                  Expanded(
                    child: TabBarView(
                      controller: _tabController,
                      children: [
                        _buildActivityLogTab(),
                        _buildConnectedDevicesTab(),
                      ],
                    ),
                  ),
                ],
              ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _showDeleteAllDataConfirmation,
        icon: const Icon(Icons.delete_forever),
        label: const Text('حذف جميع البيانات'),
        backgroundColor: Colors.red,
      ),
    );
  }

  /// بناء تبويب سجل النشاط
  Widget _buildActivityLogTab() {
    if (_activityLogs.isEmpty) {
      return const EmptyStateWidget(
        icon: Icons.history,
        title: 'لا يوجد سجل نشاط',
        message: 'لم يتم تسجيل أي نشاط حتى الآن',
      );
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: ListView.builder(
        itemCount: _activityLogs.length,
        itemBuilder: (context, index) {
          final activity = _activityLogs[index];
          final activityType = activity['activity_type'] as String;
          final timestamp = activity['timestamp'] as DateTime;
          final device = activity['device'] as String;
          final location = activity['location'] as String;
          final ipAddress = activity['ip_address'] as String;

          return ListTile(
            leading: CircleAvatar(
              backgroundColor: Theme.of(context).primaryColor.withOpacity(0.1),
              child: Icon(
                _getActivityIcon(activityType),
                color: Theme.of(context).primaryColor,
              ),
            ),
            title: Text(_getActivityDescription(activityType)),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [Text('$device | $ipAddress'), Text(location)],
            ),
            trailing: Text(_formatDateTime(timestamp)),
            isThreeLine: true,
          );
        },
      ),
    );
  }

  /// بناء تبويب الأجهزة المتصلة
  Widget _buildConnectedDevicesTab() {
    if (_connectedDevices.isEmpty) {
      return const EmptyStateWidget(
        icon: Icons.devices,
        title: 'لا توجد أجهزة متصلة',
        message: 'لم يتم تسجيل أي جهاز متصل حتى الآن',
      );
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: ListView.builder(
        itemCount: _connectedDevices.length,
        itemBuilder: (context, index) {
          final device = _connectedDevices[index];
          final deviceName = device['device_name'] as String;
          final deviceType = device['device_type'] as String;
          final lastActive = device['last_active'] as DateTime;
          final isCurrent = device['is_current'] as bool;
          final location = device['location'] as String;
          final ipAddress = device['ip_address'] as String;

          return ListTile(
            leading: CircleAvatar(
              backgroundColor:
                  isCurrent
                      ? Colors.green.withOpacity(0.1)
                      : Theme.of(context).primaryColor.withOpacity(0.1),
              child: Icon(
                _getDeviceIcon(deviceType),
                color:
                    isCurrent ? Colors.green : Theme.of(context).primaryColor,
              ),
            ),
            title: Row(
              children: [
                Expanded(child: Text(deviceName)),
                if (isCurrent)
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8.0,
                      vertical: 2.0,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.green,
                      borderRadius: BorderRadius.circular(12.0),
                    ),
                    child: const Text(
                      'الجهاز الحالي',
                      style: TextStyle(color: Colors.white, fontSize: 12.0),
                    ),
                  ),
              ],
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('آخر نشاط: ${_formatDateTime(lastActive)}'),
                Text('$location | $ipAddress'),
              ],
            ),
            trailing:
                isCurrent
                    ? null
                    : IconButton(
                      icon: const Icon(Icons.delete, color: Colors.red),
                      onPressed: () => _removeDevice(device['id']),
                      tooltip: 'حذف الجهاز',
                    ),
            isThreeLine: true,
          );
        },
      ),
    );
  }
}
