-- ===================================================================
-- جداول التسوق والطلبات
-- متجر قطع غيار الدراجات النارية - Supabase Database
-- ===================================================================

-- ===================================================================
-- 1. جدول طرق الدفع
-- ===================================================================
CREATE TABLE IF NOT EXISTS payment_methods (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    type VARCHAR(20) NOT NULL CHECK (type IN ('cash', 'wallet', 'instapay', 'credit_card', 'bank_transfer')),
    config JSONB DEFAULT '{}'::jsonb,
    min_amount DECIMAL(12,2),
    max_amount DECIMAL(12,2),
    fees_percentage DECIMAL(5,2) DEFAULT 0.00,
    fees_fixed DECIMAL(8,2) DEFAULT 0.00,
    is_active BOOLEAN DEFAULT true,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_payment_methods_type ON payment_methods(type);
CREATE INDEX IF NOT EXISTS idx_payment_methods_is_active ON payment_methods(is_active);

-- ===================================================================
-- 2. جدول طرق الشحن
-- ===================================================================
CREATE TABLE IF NOT EXISTS shipping_methods (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    cost DECIMAL(8,2) NOT NULL DEFAULT 0.00,
    estimated_days INTEGER,
    rules JSONB DEFAULT '{}'::jsonb,
    is_active BOOLEAN DEFAULT true,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- ===================================================================
-- 3. جدول سلة التسوق
-- ===================================================================
CREATE TABLE IF NOT EXISTS cart_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    product_name VARCHAR(200) NOT NULL,
    product_image TEXT,
    price DECIMAL(12,2) NOT NULL,
    discount_price DECIMAL(12,2),
    quantity INTEGER NOT NULL CHECK (quantity > 0),
    selected BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(user_id, product_id)
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_cart_items_user_id ON cart_items(user_id);
CREATE INDEX IF NOT EXISTS idx_cart_items_product_id ON cart_items(product_id);

-- ===================================================================
-- 4. جدول قائمة الرغبات
-- ===================================================================
CREATE TABLE IF NOT EXISTS wishlists (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(user_id, product_id)
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_wishlists_user_id ON wishlists(user_id);
CREATE INDEX IF NOT EXISTS idx_wishlists_product_id ON wishlists(product_id);

-- ===================================================================
-- 5. جدول الطلبات
-- ===================================================================
CREATE TABLE IF NOT EXISTS orders (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    order_number VARCHAR(20) UNIQUE NOT NULL,
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE RESTRICT,
    address_id UUID NOT NULL REFERENCES addresses(id) ON DELETE RESTRICT,
    payment_method_id UUID NOT NULL REFERENCES payment_methods(id) ON DELETE RESTRICT,
    shipping_method_id UUID REFERENCES shipping_methods(id) ON DELETE SET NULL,
    subtotal DECIMAL(12,2) NOT NULL DEFAULT 0.00,
    tax_amount DECIMAL(12,2) DEFAULT 0.00,
    shipping_cost DECIMAL(8,2) DEFAULT 0.00,
    discount_amount DECIMAL(12,2) DEFAULT 0.00,
    total_amount DECIMAL(12,2) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'shipped', 'delivered', 'cancelled')),
    payment_status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (payment_status IN ('pending', 'paid', 'failed', 'refunded')),
    notes TEXT,
    tracking_number VARCHAR(100),
    estimated_delivery TIMESTAMPTZ,
    delivered_at TIMESTAMPTZ,
    cancelled_at TIMESTAMPTZ,
    cancellation_reason TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_orders_order_number ON orders(order_number);
CREATE INDEX IF NOT EXISTS idx_orders_user_id ON orders(user_id);
CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status);
CREATE INDEX IF NOT EXISTS idx_orders_payment_status ON orders(payment_status);
CREATE INDEX IF NOT EXISTS idx_orders_created_at ON orders(created_at);

-- ===================================================================
-- 6. جدول عناصر الطلبات
-- ===================================================================
CREATE TABLE IF NOT EXISTS order_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    order_id UUID NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE RESTRICT,
    product_name VARCHAR(200) NOT NULL,
    product_sku VARCHAR(50) NOT NULL,
    quantity INTEGER NOT NULL CHECK (quantity > 0),
    unit_price DECIMAL(12,2) NOT NULL,
    total_price DECIMAL(12,2) NOT NULL,
    product_snapshot JSONB, -- نسخة من بيانات المنتج وقت الطلب
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_order_items_order_id ON order_items(order_id);
CREATE INDEX IF NOT EXISTS idx_order_items_product_id ON order_items(product_id);

-- ===================================================================
-- 7. جدول معاملات الدفع
-- ===================================================================
CREATE TABLE IF NOT EXISTS payment_transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    order_id UUID NOT NULL REFERENCES orders(id) ON DELETE RESTRICT,
    payment_method_id UUID NOT NULL REFERENCES payment_methods(id) ON DELETE RESTRICT,
    transaction_id VARCHAR(100) UNIQUE,
    amount DECIMAL(12,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'EGP',
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded')),
    gateway_response JSONB,
    fees DECIMAL(8,2) DEFAULT 0.00,
    net_amount DECIMAL(12,2),
    processed_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_payment_transactions_order_id ON payment_transactions(order_id);
CREATE INDEX IF NOT EXISTS idx_payment_transactions_transaction_id ON payment_transactions(transaction_id);
CREATE INDEX IF NOT EXISTS idx_payment_transactions_status ON payment_transactions(status);

-- ===================================================================
-- الدوال المساعدة
-- ===================================================================

-- دالة تحديث إحصائيات المستخدم
CREATE OR REPLACE FUNCTION update_user_stats()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public, extensions
AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        -- تحديث عدد الطلبات والمبلغ الإجمالي
        IF TG_TABLE_NAME = 'orders' AND NEW.status = 'delivered' THEN
            UPDATE profiles SET
                order_count = order_count + 1,
                total_spent = total_spent + NEW.total_amount
            WHERE id = NEW.user_id;
        END IF;

        -- تحديث عدد قائمة الرغبات
        IF TG_TABLE_NAME = 'wishlists' THEN
            UPDATE profiles SET wishlist_count = wishlist_count + 1 WHERE id = NEW.user_id;
        END IF;
        RETURN NEW;
    END IF;

    IF TG_OP = 'UPDATE' THEN
        -- تحديث الإحصائيات عند تغيير حالة الطلب
        IF TG_TABLE_NAME = 'orders' THEN
            IF OLD.status <> 'delivered' AND NEW.status = 'delivered' THEN
                UPDATE profiles SET
                    order_count = order_count + 1,
                    total_spent = total_spent + NEW.total_amount
                WHERE id = NEW.user_id;
            ELSIF OLD.status = 'delivered' AND NEW.status <> 'delivered' THEN
                UPDATE profiles SET
                    order_count = order_count - 1,
                    total_spent = total_spent - NEW.total_amount
                WHERE id = NEW.user_id;
            END IF;
        END IF;
        RETURN NEW;
    END IF;

    IF TG_OP = 'DELETE' THEN
        -- تحديث عدد قائمة الرغبات
        IF TG_TABLE_NAME = 'wishlists' THEN
            UPDATE profiles SET wishlist_count = wishlist_count - 1 WHERE id = OLD.user_id;
        END IF;
        RETURN OLD;
    END IF;

    RETURN NULL;
END;
$$;

-- محفز إنشاء رقم الطلب
CREATE OR REPLACE FUNCTION set_order_number()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public, extensions
AS $$
BEGIN
    IF NEW.order_number IS NULL OR NEW.order_number = '' THEN
        NEW.order_number := generate_order_number();
    END IF;
    RETURN NEW;
END;
$$;
-- ===================================================================
-- المحفزات
-- ===================================================================

-- محفزات تحديث الطوابع الزمنية
CREATE TRIGGER update_cart_items_updated_at
    BEFORE UPDATE ON cart_items
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_wishlists_updated_at
    BEFORE UPDATE ON wishlists
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_orders_updated_at
    BEFORE UPDATE ON orders
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- محفز إنشاء رقم الطلب
CREATE TRIGGER set_order_number_trigger
    BEFORE INSERT ON orders
    FOR EACH ROW
    EXECUTE FUNCTION set_order_number();

-- محفزات تحديث الإحصائيات
CREATE TRIGGER update_user_stats_on_order
    AFTER INSERT OR UPDATE ON orders
    FOR EACH ROW
    EXECUTE FUNCTION update_user_stats();

CREATE TRIGGER update_user_stats_on_wishlist
    AFTER INSERT OR DELETE ON wishlists
    FOR EACH ROW
    EXECUTE FUNCTION update_user_stats();

-- ===================================================================
-- رسالة نجاح
-- ===================================================================

DO $$
BEGIN
    RAISE NOTICE '✅ تم إنشاء جداول التسوق والطلبات بنجاح!';
    RAISE NOTICE '💳 جدول طرق الدفع (payment_methods)';
    RAISE NOTICE '🚚 جدول طرق الشحن (shipping_methods)';
    RAISE NOTICE '🛒 جدول سلة التسوق (cart_items)';
    RAISE NOTICE '❤️ جدول قائمة الرغبات (wishlists)';
    RAISE NOTICE '📋 جدول الطلبات (orders)';
    RAISE NOTICE '📦 جدول عناصر الطلبات (order_items)';
    RAISE NOTICE '💰 جدول معاملات الدفع (payment_transactions)';
END $$;
