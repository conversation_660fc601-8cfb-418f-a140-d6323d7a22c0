import 'package:flutter/material.dart';
import 'package:motorcycle_parts_shop/core/services/favorites_service.dart';
import 'package:motorcycle_parts_shop/core/theme/app_theme.dart';
import 'package:motorcycle_parts_shop/core/utils/responsive_helper.dart';
import 'package:motorcycle_parts_shop/models/product_model.dart';
import 'package:motorcycle_parts_shop/screens/product/product_details_screen.dart';

class WishlistScreen extends StatefulWidget {
  const WishlistScreen({super.key});

  @override
  State<WishlistScreen> createState() => _WishlistScreenState();
}

class _WishlistScreenState extends State<WishlistScreen> {
  final FavoritesService _favoritesService = FavoritesService();
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadFavorites();
  }

  Future<void> _loadFavorites() async {
    setState(() {
      _isLoading = true;
    });

    await _favoritesService.initialize();

    setState(() {
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    final favoriteProducts = _favoritesService.favoriteProducts;

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(
          ResponsiveHelper.isMobile(context) ? 70 : 80,
        ),
        child: Container(
          decoration: BoxDecoration(
            gradient: AppTheme.primaryGradient,
            boxShadow: AppTheme.cardShadow,
          ),
          child: SafeArea(
            child: Padding(
              padding: EdgeInsets.symmetric(
                horizontal: ResponsiveHelper.getPadding(
                  context,
                  mobile: 12,
                  tablet: 16,
                  desktop: 20,
                ),
                vertical: 8,
              ),
              child: Row(
                children: [
                  // زر الرجوع المحسن
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: IconButton(
                      icon: const Icon(
                        Icons.arrow_back_ios_rounded,
                        color: AppTheme.textLightColor,
                      ),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ),

                  const SizedBox(width: 16),

                  // أيقونة المفضلة والعنوان
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      Icons.favorite_rounded,
                      color: AppTheme.textLightColor,
                      size: 24,
                    ),
                  ),

                  const SizedBox(width: 12),

                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          'قائمة المفضلة',
                          style: AppTheme.cardTitle.copyWith(
                            color: AppTheme.textLightColor,
                            fontSize: 18,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                        if (favoriteProducts.isNotEmpty)
                          Text(
                            '${favoriteProducts.length} منتج مفضل',
                            style: AppTheme.cardSubtitle.copyWith(
                              color: AppTheme.textLightColor.withOpacity(0.8),
                              fontSize: 12,
                            ),
                          ),
                      ],
                    ),
                  ),

                  // زر مسح الكل
                  if (favoriteProducts.isNotEmpty)
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.15),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: Colors.white.withOpacity(0.2),
                          width: 1,
                        ),
                      ),
                      child: IconButton(
                        icon: Icon(
                          Icons.clear_all_rounded,
                          color: AppTheme.textLightColor,
                          size: 22,
                        ),
                        onPressed: _clearAllFavorites,
                        tooltip: 'مسح جميع المفضلة',
                      ),
                    ),
                ],
              ),
            ),
          ),
        ),
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : favoriteProducts.isEmpty
              ? _buildEmptyWishlistView()
              : _buildWishlistView(favoriteProducts),
    );
  }

  Widget _buildEmptyWishlistView() {
    return ResponsiveBuilder(
      builder: (context, constraints) {
        final padding = ResponsiveHelper.getPadding(
          context,
          mobile: 24,
          tablet: 32,
          desktop: 40,
        );
        final iconSize = ResponsiveHelper.isMobile(context) ? 60.0 : 80.0;

        return Center(
          child: Padding(
            padding: EdgeInsets.all(padding),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // أيقونة المفضلة الفارغة المحسنة
                Container(
                  padding: const EdgeInsets.all(32),
                  decoration: BoxDecoration(
                    gradient: AppTheme.cardGradient,
                    shape: BoxShape.circle,
                    boxShadow: AppTheme.cardShadow,
                  ),
                  child: Icon(
                    Icons.favorite_border_rounded,
                    size: iconSize,
                    color: AppTheme.textTertiaryColor,
                  ),
                ),

                const SizedBox(height: 32),

                // النص الرئيسي
                Text(
                  'قائمة المفضلة فارغة',
                  style: AppTheme.heroTitle.copyWith(
                    fontSize: ResponsiveHelper.getFontSize(context, 24),
                    color: AppTheme.textPrimaryColor,
                  ),
                ),

                const SizedBox(height: 12),

                // النص الفرعي
                Text(
                  'اضغط على أيقونة القلب ♥ لإضافة\nمنتجاتك المفضلة هنا',
                  style: AppTheme.cardSubtitle.copyWith(
                    fontSize: 16,
                    height: 1.5,
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 40),

                // زر التصفح المحسن
                Container(
                  decoration: BoxDecoration(
                    gradient: AppTheme.primaryGradient,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: AppTheme.cardShadow,
                  ),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: () => Navigator.pushNamed(context, '/home'),
                      borderRadius: BorderRadius.circular(16),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 32,
                          vertical: 16,
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.explore_rounded,
                              color: AppTheme.textLightColor,
                              size: 24,
                            ),
                            const SizedBox(width: 12),
                            Text(
                              'استكشف المنتجات',
                              style: AppTheme.buttonText.copyWith(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildWishlistView(List<ProductModel> favoriteProducts) {
    return ListView.builder(
      padding: EdgeInsets.all(ResponsiveHelper.getPadding(context)),
      itemCount: favoriteProducts.length,
      itemBuilder: (context, index) {
        final product = favoriteProducts[index];
        return _buildWishlistItem(product);
      },
    );
  }

  Widget _buildWishlistItem(ProductModel product) {
    return ResponsiveBuilder(
      builder: (context, constraints) {
        final margin = ResponsiveHelper.getPadding(
          context,
          mobile: 12,
          tablet: 16,
          desktop: 20,
        );
        final padding = ResponsiveHelper.getPadding(
          context,
          mobile: 12,
          tablet: 16,
          desktop: 20,
        );
        final imageSize = ResponsiveHelper.isMobile(context) ? 80.0 : 100.0;

        return Container(
          margin: EdgeInsets.only(bottom: margin),
          decoration: BoxDecoration(
            gradient: AppTheme.cardGradient,
            borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
            boxShadow: AppTheme.cardShadow,
            border: Border.all(
              color: AppTheme.primaryColor.withOpacity(0.08),
              width: 1,
            ),
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder:
                        (context) => ProductDetailsScreen(product: product),
                  ),
                );
              },
              borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
              child: Padding(
                padding: EdgeInsets.all(padding),
                child: Row(
                  children: [
                    // صورة المنتج المحسنة
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: AppTheme.cardShadow,
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(12),
                        child: Image.network(
                          product.images.isNotEmpty ? product.images.first : '',
                          width: imageSize,
                          height: imageSize,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              width: imageSize,
                              height: imageSize,
                              decoration: BoxDecoration(
                                color: AppTheme.backgroundColor,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Icon(
                                Icons.image_not_supported_rounded,
                                color: AppTheme.textTertiaryColor,
                                size: 32,
                              ),
                            );
                          },
                        ),
                      ),
                    ),

                    const SizedBox(width: 16),

                    // معلومات المنتج
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            product.name,
                            style: AppTheme.cardTitle.copyWith(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),

                          const SizedBox(height: 8),

                          // السعر
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 6,
                            ),
                            decoration: BoxDecoration(
                              gradient: AppTheme.primaryGradient,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              '${product.price.toStringAsFixed(0)} ج.م',
                              style: AppTheme.priceText.copyWith(
                                color: AppTheme.textLightColor,
                                fontSize: 14,
                                fontWeight: FontWeight.w700,
                              ),
                            ),
                          ),

                          const SizedBox(height: 8),

                          // التقييم
                          Row(
                            children: [
                              Icon(
                                Icons.star_rounded,
                                color: AppTheme.warningColor,
                                size: 16,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                product.rating.toStringAsFixed(1),
                                style: AppTheme.cardSubtitle.copyWith(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),

                    // زر الحذف المحسن
                    Container(
                      decoration: BoxDecoration(
                        color: AppTheme.errorColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: IconButton(
                        icon: Icon(
                          Icons.delete_rounded,
                          color: AppTheme.errorColor,
                          size: 22,
                        ),
                        onPressed: () => _removeFromWishlist(product),
                        tooltip: 'إزالة من المفضلة',
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  // دالة مسح جميع المفضلة
  void _clearAllFavorites() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text('مسح جميع المفضلة', style: AppTheme.cardTitle),
            content: Text(
              'هل أنت متأكد من مسح جميع المنتجات من قائمة المفضلة؟',
              style: AppTheme.cardSubtitle,
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(
                  'إلغاء',
                  style: AppTheme.cardTitle.copyWith(
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
              ),
              Container(
                decoration: BoxDecoration(
                  gradient: AppTheme.primaryGradient,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: TextButton(
                  onPressed: () async {
                    Navigator.pop(context);
                    await _favoritesService.clearFavorites();
                    setState(() {});
                    if (!mounted) return;
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          'تم مسح جميع المفضلة',
                          style: AppTheme.buttonText,
                        ),
                        backgroundColor: AppTheme.successColor,
                      ),
                    );
                  },
                  child: Text('مسح الكل', style: AppTheme.buttonText),
                ),
              ),
            ],
          ),
    );
  }

  /// إزالة منتج من قائمة المفضلة
  void _removeFromWishlist(ProductModel product) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('إزالة من المفضلة'),
            content: Text(
              'هل أنت متأكد من إزالة ${product.name} من قائمة المفضلة؟',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              TextButton(
                onPressed: () async {
                  Navigator.pop(context);
                  await _favoritesService.removeFromFavorites(product.id);
                  setState(() {}); // تحديث واجهة المستخدم
                  if (!mounted) return;
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('تم إزالة ${product.name} من المفضلة'),
                    ),
                  );
                },
                child: const Text('إزالة'),
              ),
            ],
          ),
    );
  }
}
