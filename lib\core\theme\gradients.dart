import 'app_colors.dart';
import 'package:flutter/material.dart';

/// تعريف التدرجات اللونية للتطبيق
///
/// يوفر هذا الملف مجموعة من التدرجات اللونية الاحترافية التي يمكن استخدامها
/// في مختلف أجزاء التطبيق مثل الخلفيات والأزرار والبطاقات
/// تمت إضافة تدرجات أمواج البحر لتحسين المظهر الجمالي للتطبيق
class AppGradients {
  // تدرجات الألوان الأساسية

  /// تدرج اللون الأساسي - من الأزرق الفاتح إلى الأزرق الداكن
  static const LinearGradient primaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFF3B82F6), // أزرق فاتح
      Color(0xFF1D4ED8), // أزرق داكن
    ],
  );

  /// تدرج اللون الثانوي - من البرتقالي الفاتح إلى البرتقالي الداكن
  static const LinearGradient secondaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFFFF8A65), // برتقالي فاتح
      Color(0xFFFF5722), // برتقالي داكن
    ],
  );

  /// تدرج أزرق فاتح - مناسب للبطاقات والخلفيات الفاتحة
  static const LinearGradient lightBlueGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Color(0xFFE0F2FE), // أزرق فاتح جداً
      Color(0xFFBAE6FD), // أزرق فاتح
    ],
  );

  /// تدرج أمواج البحر - تدرج متموج للخلفيات والبطاقات
  static const LinearGradient oceanWaveGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFF60A5FA), // أزرق متوسط
      Color(0xFF3B82F6), // أزرق
      Color.fromARGB(255, 19, 88, 236), // أزرق داكن
    ],
    stops: [0.0, 0.5, 1.0],
  );

  /// تدرج أمواج البحر الفاتح - مناسب للخلفيات الفاتحة
  static const LinearGradient lightOceanWaveGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFFDCEDFF), // أزرق فاتح جداً
      Color(0xFFBFDBFE), // أزرق فاتح
      Color(0xFF93C5FD), // أزرق متوسط فاتح
    ],
    stops: [0.0, 0.5, 1.0],
  );

  /// تدرج برتقالي فاتح - مناسب للبطاقات والخلفيات الفاتحة
  static const LinearGradient lightOrangeGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Color(0xFFFFEDE8), // برتقالي فاتح جداً
      Color(0xFFFFD0B5), // برتقالي فاتح
    ],
  );

  // تدرجات للأزرار والعناصر التفاعلية

  /// تدرج للأزرار الرئيسية
  static const LinearGradient buttonGradient = LinearGradient(
    begin: Alignment.centerLeft,
    end: Alignment.centerRight,
    colors: [
      AppColors.primaryColor,
      Color(0xFF1E40AF), // أزرق داكن
    ],
  );

  /// تدرج للأزرار الثانوية
  static const LinearGradient secondaryButtonGradient = LinearGradient(
    begin: Alignment.centerLeft,
    end: Alignment.centerRight,
    colors: [
      AppColors.secondaryColor,
      Color(0xFFE03A0B), // برتقالي داكن
    ],
  );

  /// تدرج للأزرار الخضراء (النجاح)
  static const LinearGradient successGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFF22C55E), // أخضر فاتح
      Color(0xFF16A34A), // أخضر داكن
    ],
  );

  /// تدرج للأزرار الحمراء (الخطأ)
  static const LinearGradient errorGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFFEF4444), // أحمر فاتح
      Color(0xFFDC2626), // أحمر داكن
    ],
  );

  // تدرجات للخلفيات

  /// تدرج للخلفية الرئيسية للتطبيق
  static const LinearGradient backgroundGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Color(0xFFF9FAFB), // رمادي فاتح جداً
      Color(0xFFF3F4F6), // رمادي فاتح
    ],
  );

  /// تدرج للبطاقات والعناصر البارزة
  static const LinearGradient cardGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Colors.white,
      Color(0xFFF9FAFB), // رمادي فاتح جداً
    ],
  );

  /// تدرج للشاشة الترحيبية - محسن بألوان أكثر حيوية وعمق
  static const LinearGradient welcomeGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Color(0xFF3B82F6), // أزرق فاتح
      Color(0xFF2563EB), // أزرق متوسط
      Color(0xFF1E40AF), // أزرق داكن
      Color(0xFF1E3A8A), // أزرق داكن جداً
    ],
    stops: [0.0, 0.3, 0.6, 1.0],
  );

  /// تدرجات لشاشات التعريف (Onboarding)
  static const LinearGradient onboardingPrimaryGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Color(0xFF3B82F6), // أزرق فاتح
      Color(0xFF1E40AF), // أزرق داكن
    ],
    stops: [0.0, 1.0],
  );

  static const LinearGradient onboardingSecondaryGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Color(0xFFFF8A65), // برتقالي فاتح
      Color(0xFFE03A0B), // برتقالي داكن
    ],
    stops: [0.0, 1.0],
  );

  static const LinearGradient onboardingDarkBlueGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Color(0xFF1E3A8A), // أزرق داكن جداً
      Color(0xFF0F172A), // أزرق داكن جداً مائل للأسود
    ],
    stops: [0.0, 1.0],
  );

  /// تدرج للعروض والخصومات
  static const LinearGradient offerGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFFFEF3C7), // أصفر فاتح
      Color(0xFFFCD34D), // أصفر
    ],
  );

  // تدرجات للوضع الداكن

  /// تدرج للخلفية في الوضع الداكن
  static const LinearGradient darkBackgroundGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Color(0xFF1F2937), // رمادي داكن
      Color(0xFF111827), // رمادي داكن جداً
    ],
  );

  /// تدرج للبطاقات في الوضع الداكن
  static const LinearGradient darkCardGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFF374151), // رمادي متوسط
      Color(0xFF1F2937), // رمادي داكن
    ],
  );

  // تدرجات شفافة للتأثيرات

  /// تدرج شفاف للظلال والتأثيرات
  static const LinearGradient transparentGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [Colors.black12, Colors.black26],
  );

  /// تدرج للصور والخلفيات
  static const LinearGradient imageOverlayGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [Colors.transparent, Colors.black54],
  );

  // تدرجات أمواج البحر

  /// تدرج أمواج البحر الأزرق
  static const LinearGradient blueWaveGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFF60A5FA), // أزرق فاتح
      Color(0xFF3B82F6), // أزرق متوسط
      Color(0xFF2563EB), // أزرق داكن
      Color(0xFF1D4ED8), // أزرق داكن جداً
    ],
    stops: [0.0, 0.3, 0.6, 1.0],
    transform: GradientRotation(0.2), // إضافة دوران للتدرج لإعطاء تأثير الأمواج
  );

  /// تدرج أمواج البحر الفيروزي
  static const LinearGradient turquoiseWaveGradient = LinearGradient(
    begin: Alignment.topRight,
    end: Alignment.bottomLeft,
    colors: [
      Color(0xFF67E8F9), // فيروزي فاتح
      Color(0xFF22D3EE), // فيروزي متوسط
      Color(0xFF06B6D4), // فيروزي داكن
      Color(0xFF0891B2), // فيروزي داكن جداً
    ],
    stops: [0.0, 0.3, 0.7, 1.0],
    transform: GradientRotation(-0.2), // دوران عكسي للتدرج
  );

  /// تدرج أمواج البحر الأخضر
  static const LinearGradient greenWaveGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Color(0xFF6EE7B7), // أخضر فاتح
      Color(0xFF34D399), // أخضر متوسط
      Color(0xFF10B981), // أخضر داكن
      Color(0xFF059669), // أخضر داكن جداً
    ],
    stops: [0.0, 0.3, 0.6, 1.0],
    transform: GradientRotation(0.1), // دوران خفيف للتدرج
  );

  /// تدرج أمواج البحر الأرجواني
  static const LinearGradient purpleWaveGradient = LinearGradient(
    begin: Alignment.centerLeft,
    end: Alignment.centerRight,
    colors: [
      Color(0xFFC4B5FD), // أرجواني فاتح
      Color(0xFFA78BFA), // أرجواني متوسط
      Color(0xFF8B5CF6), // أرجواني داكن
      Color(0xFF7C3AED), // أرجواني داكن جداً
    ],
    stops: [0.0, 0.3, 0.6, 1.0],
    transform: GradientRotation(-0.1), // دوران عكسي خفيف للتدرج
  );

  /// تدرج أمواج البحر الذهبي
  static const LinearGradient goldenWaveGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFFFCD34D), // ذهبي فاتح
      Color(0xFFFBBF24), // ذهبي متوسط
      Color(0xFFF59E0B), // ذهبي داكن
      Color(0xFFD97706), // ذهبي داكن جداً
    ],
    stops: [0.0, 0.3, 0.6, 1.0],
    transform: GradientRotation(
      0.3,
    ), // دوران أكبر للتدرج لإعطاء تأثير أمواج أكثر
  );
}
