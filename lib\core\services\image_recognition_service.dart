import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:http/http.dart' as http;
import 'package:image_picker/image_picker.dart';
import 'package:motorcycle_parts_shop/core/services/product_service.dart';
import 'package:motorcycle_parts_shop/models/product_model.dart';

// تعريف أخطاء مخصصة
class ImageRecognitionException implements Exception {
  final String message;
  final String? code;
  final dynamic originalError;

  ImageRecognitionException(this.message, {this.code, this.originalError});

  @override
  String toString() =>
      'ImageRecognitionException: $message${code != null ? ' (Code: $code)' : ''}';
}

class ImageRecognitionService {
  static final ImageRecognitionService _instance =
      ImageRecognitionService._internal();
  late final ProductService _productService;
  final ImagePicker _imagePicker = ImagePicker();

  static const String _modelId = 'aaa03c23b3724a16a56b629203edc62c';
  static const String _apiUrl =
      'https://api.clarifai.com/v2/models/$_modelId/outputs';

  String? _apiKey;
  List<String> _lastExtractedKeywords = [];
  String? _error;
  bool _isProcessing = false;
  bool _isInitialized = false;

  // إضافة التخزين المؤقت
  final Map<String, List<String>> _imageCache = {};
  final Map<String, DateTime> _cacheTimestamps = {};
  static const Duration _cacheDuration = Duration(hours: 24);

  // إعدادات الأمان
  static const int _maxFileSizeMB = 5;
  static const List<String> _allowedExtensions = ['jpg', 'jpeg', 'png', 'webp'];
  static const int _minImageDimension = 100;
  static const int _maxImageDimension = 2000;

  bool get isProcessing => _isProcessing;
  String? get error => _error;
  bool get isInitialized => _isInitialized;

  factory ImageRecognitionService(ProductService productService) {
    _instance._productService = productService;
    return _instance;
  }

  ImageRecognitionService._internal();

  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _apiKey = dotenv.env['CLARIFAI_API_KEY'];

      if (_apiKey == null || _apiKey!.isEmpty) {
        throw ImageRecognitionException(
          'مفتاح API لـ Clarifai غير محدد في ملف .env',
          code: 'API_KEY_MISSING',
        );
      }

      // التحقق من صحة المفتاح
      final response = await http.get(
        Uri.parse('https://api.clarifai.com/v2/models'),
        headers: {'Authorization': 'Key $_apiKey'},
      );

      if (response.statusCode != 200) {
        throw ImageRecognitionException(
          'مفتاح API غير صالح',
          code: 'API_KEY_INVALID',
          originalError: response.body,
        );
      }

      _isInitialized = true;
    } catch (e) {
      _error = 'خطأ في تهيئة خدمة التعرف على الصور: $e';
      debugPrint('Error initializing image recognition service: $e');
      rethrow;
    }
  }

  /// التحقق من صحة ملف الصورة
  Future<void> _validateImageFile(File imageFile) async {
    if (!await imageFile.exists()) {
      throw ImageRecognitionException(
        'ملف الصورة غير موجود',
        code: 'FILE_NOT_FOUND',
      );
    }

    final fileSizeMB = await imageFile.length() / (1024 * 1024);
    if (fileSizeMB > _maxFileSizeMB) {
      throw ImageRecognitionException(
        'حجم الصورة يتجاوز الحد الأقصى ($_maxFileSizeMB ميجابايت)',
        code: 'FILE_TOO_LARGE',
      );
    }

    final extension = imageFile.path.split('.').last.toLowerCase();
    if (!_allowedExtensions.contains(extension)) {
      throw ImageRecognitionException(
        'امتداد الملف غير مدعوم. الامتدادات المدعومة: $_allowedExtensions',
        code: 'INVALID_EXTENSION',
      );
    }

    // التحقق من أبعاد الصورة
    final image = await decodeImageFromList(await imageFile.readAsBytes());
    if (image.width < _minImageDimension || image.height < _minImageDimension) {
      throw ImageRecognitionException(
        'أبعاد الصورة صغيرة جداً. الحد الأدنى هو $_minImageDimension بكسل',
        code: 'IMAGE_TOO_SMALL',
      );
    }
    if (image.width > _maxImageDimension || image.height > _maxImageDimension) {
      throw ImageRecognitionException(
        'أبعاد الصورة كبيرة جداً. الحد الأقصى هو $_maxImageDimension بكسل',
        code: 'IMAGE_TOO_LARGE',
      );
    }
  }

  /// إنشاء معرف فريد للصورة
  String _generateImageId(File imageFile) {
    final bytes = imageFile.readAsBytesSync();
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final fileSize = bytes.length;
    final path = imageFile.path;
    return 'img_${path.hashCode}_${fileSize}_$timestamp';
  }

  /// التقاط صورة من الكاميرا أو المعرض
  Future<File?> captureImage({
    ImageSource source = ImageSource.camera,
    int? maxWidth,
    int? maxHeight,
    int? imageQuality,
  }) async {
    if (!_isInitialized) await initialize();

    try {
      _error = null;
      _isProcessing = true;

      final XFile? pickedFile = await _imagePicker.pickImage(
        source: source,
        imageQuality: imageQuality ?? 80,
        maxWidth: maxWidth?.toDouble() ?? 1200,
        maxHeight: maxHeight?.toDouble() ?? 1200,
      );

      if (pickedFile == null) return null;

      final imageFile = File(pickedFile.path);
      await _validateImageFile(imageFile);
      return imageFile;
    } catch (e) {
      _error = 'فشل في التقاط الصورة: $e';
      debugPrint('Error capturing image: $e');
      return null;
    } finally {
      _isProcessing = false;
    }
  }

  /// تحليل الصورة باستخدام Clarifai
  Future<List<String>> analyzeImage(File imageFile) async {
    if (!_isInitialized) await initialize();

    try {
      _error = null;
      _isProcessing = true;

      await _validateImageFile(imageFile);

      // التحقق من التخزين المؤقت
      final imageId = _generateImageId(imageFile);
      if (_imageCache.containsKey(imageId)) {
        final timestamp = _cacheTimestamps[imageId];
        if (timestamp != null &&
            DateTime.now().difference(timestamp) < _cacheDuration) {
          return _imageCache[imageId]!;
        } else {
          _imageCache.remove(imageId);
          _cacheTimestamps.remove(imageId);
        }
      }

      final bytes = await imageFile.readAsBytes();
      final base64Image = base64Encode(bytes);

      final response = await http
          .post(
            Uri.parse(_apiUrl),
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Key $_apiKey',
            },
            body: jsonEncode({
              'inputs': [
                {
                  'data': {
                    'image': {'base64': base64Image},
                  },
                },
              ],
            }),
          )
          .timeout(
            const Duration(seconds: 10),
            onTimeout: () {
              throw ImageRecognitionException(
                'انتهت مهلة الطلب إلى Clarifai',
                code: 'REQUEST_TIMEOUT',
              );
            },
          );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final outputs = data['outputs'];
        if (outputs != null && outputs.isNotEmpty) {
          final concepts = outputs[0]['data']['concepts'] as List;
          _lastExtractedKeywords =
              concepts
                  .where((concept) => (concept['value'] as num) > 0.6)
                  .map<String>((concept) => concept['name'] as String)
                  .toList();

          if (_lastExtractedKeywords.isEmpty) {
            throw ImageRecognitionException(
              'لم يتم العثور على كلمات مفتاحية مناسبة في الصورة',
              code: 'NO_KEYWORDS_FOUND',
            );
          }

          // تخزين النتائج في التخزين المؤقت
          _imageCache[imageId] = _lastExtractedKeywords;
          _cacheTimestamps[imageId] = DateTime.now();

          return _lastExtractedKeywords;
        } else {
          throw ImageRecognitionException(
            'لم يتم العثور على مفاهيم في استجابة Clarifai',
            code: 'NO_CONCEPTS_FOUND',
          );
        }
      } else {
        final errorData = jsonDecode(response.body);
        throw ImageRecognitionException(
          'خطأ في API: ${errorData['status']['description']}',
          code: 'API_ERROR',
          originalError: errorData,
        );
      }
    } catch (e) {
      _error = 'خطأ في تحليل الصورة: $e';
      debugPrint('Error analyzing image: $e');
      return [];
    } finally {
      _isProcessing = false;
    }
  }

  /// البحث عن المنتجات باستخدام الكلمات المفتاحية
  Future<List<ProductModel>> searchProductsByImage(File imageFile) async {
    if (!_isInitialized) await initialize();

    try {
      final keywords = await analyzeImage(imageFile);
      if (keywords.isEmpty) return [];

      return await _productService.searchProducts(
        searchQuery: keywords.join(' '),
        page: 0,
      );
    } catch (e) {
      _error = 'خطأ في البحث عن المنتجات: $e';
      debugPrint('Error searching products: $e');
      return [];
    }
  }

  Future<List<ProductModel>> searchProductsByCamera() async {
    if (!_isInitialized) await initialize();

    try {
      _error = null;
      _isProcessing = true;

      final imageFile = await captureImage();
      if (imageFile == null) {
        throw ImageRecognitionException(
          'لم يتم التقاط صورة',
          code: 'NO_IMAGE_CAPTURED',
        );
      }

      return await searchProductsByImage(imageFile);
    } catch (e) {
      _error = 'فشل في البحث باستخدام الكاميرا: $e';
      debugPrint('Error searching by camera: $e');
      return [];
    } finally {
      _isProcessing = false;
    }
  }

  /// مسح التخزين المؤقت
  void clearCache() {
    _imageCache.clear();
    _cacheTimestamps.clear();
  }
}
