import 'package:flutter/material.dart';
import 'package:motorcycle_parts_shop/core/services/accessibility_service.dart';
import 'package:motorcycle_parts_shop/core/theme/app_theme.dart';
import 'package:provider/provider.dart';

class AccessibilitySettingsScreen extends StatefulWidget {
  const AccessibilitySettingsScreen({super.key});

  @override
  State<AccessibilitySettingsScreen> createState() =>
      _AccessibilitySettingsScreenState();
}

class _AccessibilitySettingsScreenState
    extends State<AccessibilitySettingsScreen> {
  double _textScaleFactor = 1.0;
  bool _highContrastEnabled = false;
  bool _reduceAnimations = false;
  bool _screenReaderEnabled = false;

  @override
  void initState() {
    super.initState();
    _loadAccessibilitySettings();
  }

  Future<void> _loadAccessibilitySettings() async {
    final accessibilityService = Provider.of<AccessibilityService>(
      context,
      listen: false,
    );
    setState(() {
      _textScaleFactor = accessibilityService.textScaleFactor;
      _highContrastEnabled = accessibilityService.isHighContrastEnabled;
      _reduceAnimations = accessibilityService.reduceAnimations;
      _screenReaderEnabled = accessibilityService.isScreenReaderEnabled;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إمكانية الوصول'),
        centerTitle: true,
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: MediaQuery(
        data: MediaQuery.of(context).copyWith(textScaler: TextScaler.linear(_textScaleFactor)),
        child: ListView(
          padding: const EdgeInsets.all(16.0),
        children: [
          _buildSectionHeader('حجم النص'),
          Consumer<AccessibilityService>(
            builder:
                (context, accessibilityService, _) => Column(
                  children: [
                    Slider(
                      value: _textScaleFactor,
                      min: 0.8,
                      max: 1.4,
                      divisions: 6,
                      label: 'حجم النص: ${(_textScaleFactor * 100).round()}%',
                      onChanged: (value) {
                        setState(() => _textScaleFactor = value);
                        accessibilityService.setTextScaleFactor(value);
                      },
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: const [
                          Text('صغير'),
                          Text('متوسط'),
                          Text('كبير'),
                        ],
                      ),
                    ),
                  ],
                ),
          ),
          const Divider(),
          _buildSectionHeader('التباين والألوان'),
          Consumer<AccessibilityService>(
            builder:
                (context, accessibilityService, _) => SwitchListTile(
                  secondary: const CircleAvatar(
                    backgroundColor: AppTheme.primaryColor,
                    child: Icon(Icons.contrast, color: Colors.white),
                  ),
                  title: const Text('التباين العالي'),
                  subtitle: const Text('تحسين وضوح النصوص والعناصر'),
                  value: _highContrastEnabled,
                  activeColor: AppTheme.primaryColor,
                  onChanged: (bool value) {
                    setState(() => _highContrastEnabled = value);
                    accessibilityService.setHighContrastEnabled(value);
                  },
                ),
          ),
          const Divider(),
          _buildSectionHeader('الحركة'),
          Consumer<AccessibilityService>(
            builder:
                (context, accessibilityService, _) => SwitchListTile(
                  secondary: const CircleAvatar(
                    backgroundColor: AppTheme.primaryColor,
                    child: Icon(Icons.animation, color: Colors.white),
                  ),
                  title: const Text('تقليل الحركة'),
                  subtitle: const Text('تقليل الرسوم المتحركة والانتقالات'),
                  value: _reduceAnimations,
                  activeColor: AppTheme.primaryColor,
                  onChanged: (bool value) {
                    setState(() => _reduceAnimations = value);
                    accessibilityService.setReduceAnimations(value);
                  },
                ),
          ),
          const Divider(),
          _buildSectionHeader('قارئ الشاشة'),
          Consumer<AccessibilityService>(
            builder:
                (context, accessibilityService, _) => SwitchListTile(
                  secondary: const CircleAvatar(
                    backgroundColor: AppTheme.primaryColor,
                    child: Icon(Icons.record_voice_over, color: Colors.white),
                  ),
                  title: const Text('قارئ الشاشة'),
                  subtitle: const Text('تفعيل قراءة محتوى الشاشة'),
                  value: _screenReaderEnabled,
                  activeColor: AppTheme.primaryColor,
                  onChanged: (bool value) {
                    setState(() => _screenReaderEnabled = value);
                    accessibilityService.setScreenReaderEnabled(value);
                  },
                ),
          ),
        ],
      ),
    ));
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: AppTheme.primaryColor,
        ),
      ),
    );
  }
}
