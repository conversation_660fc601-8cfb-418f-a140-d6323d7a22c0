import 'package:motorcycle_parts_shop/models/clarifai_result.dart';

/// نموذج نتيجة تحليل المنتج
class ProductAnalysisResult {
  final bool success;
  final String? error;
  final String? productId;
  final ClarifaiResult? generalAnalysis;
  final ObjectDetectionResult? objectDetection;
  final ColorAnalysisResult? colorAnalysis;
  final ModerationResult? moderation;
  final PartClassification? classification;
  final QualityAssessment? quality;
  final TextExtractionResult? textExtraction;
  final int processingTime;
  final DateTime timestamp;

  ProductAnalysisResult({
    required this.success,
    this.error,
    this.productId,
    this.generalAnalysis,
    this.objectDetection,
    this.colorAnalysis,
    this.moderation,
    this.classification,
    this.quality,
    this.textExtraction,
    required this.processingTime,
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'error': error,
      'product_id': productId,
      'general_analysis': generalAnalysis?.toJson(),
      'object_detection': objectDetection != null ? {
        'success': objectDetection!.success,
        'objects_count': objectDetection!.objects.length,
      } : null,
      'color_analysis': colorAnalysis != null ? {
        'success': colorAnalysis!.success,
        'colors_count': colorAnalysis!.colors.length,
      } : null,
      'moderation': moderation != null ? {
        'is_appropriate': moderation!.isAppropriate,
        'confidence': moderation!.confidence,
      } : null,
      'classification': classification?.toJson(),
      'quality': quality?.toJson(),
      'text_extraction': textExtraction != null ? {
        'success': textExtraction!.success,
        'texts_count': textExtraction!.texts.length,
      } : null,
      'processing_time_ms': processingTime,
      'timestamp': timestamp.toIso8601String(),
    };
  }
}

/// تصنيف قطعة الغيار
class PartClassification {
  final String category;
  final String? subCategory;
  final double confidence;
  final String? brand;
  final Map<String, dynamic>? metadata;

  PartClassification({
    required this.category,
    this.subCategory,
    required this.confidence,
    this.brand,
    this.metadata,
  });

  Map<String, dynamic> toJson() {
    return {
      'category': category,
      'sub_category': subCategory,
      'confidence': confidence,
      'brand': brand,
      'metadata': metadata,
    };
  }
}

/// تقييم جودة الصورة
class QualityAssessment {
  final double overallScore;
  final double sharpness;
  final double brightness;
  final double contrast;
  final bool isBlurry;
  final bool isOverexposed;
  final bool isUnderexposed;
  final List<String> recommendations;

  QualityAssessment({
    required this.overallScore,
    required this.sharpness,
    required this.brightness,
    required this.contrast,
    required this.isBlurry,
    required this.isOverexposed,
    required this.isUnderexposed,
    required this.recommendations,
  });

  Map<String, dynamic> toJson() {
    return {
      'overall_score': overallScore,
      'sharpness': sharpness,
      'brightness': brightness,
      'contrast': contrast,
      'is_blurry': isBlurry,
      'is_overexposed': isOverexposed,
      'is_underexposed': isUnderexposed,
      'recommendations': recommendations,
    };
  }
}
