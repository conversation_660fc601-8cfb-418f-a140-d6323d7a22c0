import 'package:flutter/material.dart';
import 'package:motorcycle_parts_shop/core/services/auth_supabase_service.dart';
import 'package:motorcycle_parts_shop/core/theme/app_theme.dart';
import 'package:provider/provider.dart';

class SecurityPrivacyScreen extends StatefulWidget {
  const SecurityPrivacyScreen({super.key});

  @override
  State<SecurityPrivacyScreen> createState() => _SecurityPrivacyScreenState();
}

class _SecurityPrivacyScreenState extends State<SecurityPrivacyScreen> {
  bool _notificationsEnabled = true;

  @override
  Widget build(BuildContext context) {
    final authService = Provider.of<AuthSupabaseService>(context);

    return Scaffold(
      appBar: AppBar(title: const Text('الأمان والخصوصية'), centerTitle: true),
      body: ListView(
        padding: const EdgeInsets.all(16.0),
        children: [
          _buildSectionHeader('كلمة المرور والأمان'),
          ListTile(
            leading: const CircleAvatar(
              backgroundColor: AppTheme.primaryColor,
              child: Icon(Icons.lock, color: Colors.white),
            ),
            title: const Text('تغيير كلمة المرور'),
            subtitle: const Text('قم بتحديث كلمة المرور الخاصة بك'),
            onTap: () {
              _showChangePasswordDialog(context, authService);
            },
          ),
          // تم إزالة خيار تسجيل الدخول بالبصمة
          const Divider(),
          _buildSectionHeader('الخصوصية'),
          SwitchListTile(
            secondary: const CircleAvatar(
              backgroundColor: AppTheme.primaryColor,
              child: Icon(Icons.notifications, color: Colors.white),
            ),
            title: const Text('الإشعارات'),
            subtitle: const Text('السماح بإرسال إشعارات'),
            value: _notificationsEnabled,
            onChanged: (value) {
              setState(() {
                _notificationsEnabled = value;
              });
            },
          ),

          const Divider(),
          _buildSectionHeader('البيانات'),
          ListTile(
            leading: const CircleAvatar(
              backgroundColor: AppTheme.primaryColor,
              child: Icon(Icons.delete, color: Colors.white),
            ),
            title: const Text('حذف الحساب'),
            subtitle: const Text('حذف حسابك وجميع بياناتك نهائياً'),
            onTap: () {
              _showDeleteAccountDialog(context, authService);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16.0),
      child: Text(
        title,
        style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
      ),
    );
  }

  Future<void> _showDeleteAccountDialog(
    BuildContext context,
    AuthSupabaseService authService,
  ) async {
    return showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('حذف الحساب'),
            content: const Text(
              'هل أنت متأكد من رغبتك في حذف حسابك؟ لا يمكن التراجع عن هذا الإجراء.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              TextButton(
                onPressed: () async {
                  Navigator.pop(context);
                  _showDeleteAccountConfirmationDialog(context, authService);
                },
                style: TextButton.styleFrom(
                  foregroundColor: AppTheme.errorColor,
                ),
                child: const Text('حذف الحساب'),
              ),
            ],
          ),
    );
  }

  Future<void> _showDeleteAccountConfirmationDialog(
    BuildContext context,
    AuthSupabaseService authService,
  ) async {
    final TextEditingController passwordController = TextEditingController();
    bool isLoading = false;

    return showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => StatefulBuilder(
            builder:
                (context, setState) => AlertDialog(
                  title: const Text('تأكيد حذف الحساب'),
                  content: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Text('الرجاء إدخال كلمة المرور لتأكيد حذف الحساب'),
                      const SizedBox(height: 16),
                      TextField(
                        controller: passwordController,
                        obscureText: true,
                        decoration: const InputDecoration(
                          labelText: 'كلمة المرور',
                          border: OutlineInputBorder(),
                        ),
                      ),
                      if (isLoading)
                        const Padding(
                          padding: EdgeInsets.only(top: 16.0),
                          child: CircularProgressIndicator(),
                        ),
                    ],
                  ),
                  actions: [
                    TextButton(
                      onPressed:
                          isLoading ? null : () => Navigator.pop(context),
                      child: const Text('إلغاء'),
                    ),
                    TextButton(
                      onPressed:
                          isLoading
                              ? null
                              : () async {
                                if (passwordController.text.isEmpty) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                      content: Text('الرجاء إدخال كلمة المرور'),
                                      backgroundColor: Colors.red,
                                    ),
                                  );
                                  return;
                                }

                                setState(() {
                                  isLoading = true;
                                });

                                final success = await authService.deleteAccount(
                                  passwordController.text,
                                );

                                if (!mounted) return;

                                setState(() {
                                  isLoading = false;
                                });

                                Navigator.pop(context);

                                if (success) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                      content: Text('تم حذف الحساب بنجاح'),
                                      backgroundColor: Colors.green,
                                    ),
                                  );
                                  Navigator.of(context).pushNamedAndRemoveUntil(
                                    '/welcome',
                                    (route) => false,
                                  );
                                } else {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: Text(
                                        authService.error ?? 'فشل حذف الحساب',
                                      ),
                                      backgroundColor: Colors.red,
                                    ),
                                  );
                                }
                              },
                      style: TextButton.styleFrom(
                        foregroundColor: AppTheme.errorColor,
                      ),
                      child: const Text('حذف الحساب'),
                    ),
                  ],
                ),
          ),
    );
  }

  Future<void> _showChangePasswordDialog(
    BuildContext context,
    AuthSupabaseService authService,
  ) async {
    final TextEditingController currentPasswordController =
        TextEditingController();
    final TextEditingController newPasswordController = TextEditingController();
    final TextEditingController confirmPasswordController =
        TextEditingController();
    bool isLoading = false;

    return showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => StatefulBuilder(
            builder:
                (context, setState) => AlertDialog(
                  title: const Text('تغيير كلمة المرور'),
                  content: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      TextField(
                        controller: currentPasswordController,
                        obscureText: true,
                        decoration: const InputDecoration(
                          labelText: 'كلمة المرور الحالية',
                          border: OutlineInputBorder(),
                        ),
                      ),
                      const SizedBox(height: 16),
                      TextField(
                        controller: newPasswordController,
                        obscureText: true,
                        decoration: const InputDecoration(
                          labelText: 'كلمة المرور الجديدة',
                          border: OutlineInputBorder(),
                        ),
                      ),
                      const SizedBox(height: 16),
                      TextField(
                        controller: confirmPasswordController,
                        obscureText: true,
                        decoration: const InputDecoration(
                          labelText: 'تأكيد كلمة المرور الجديدة',
                          border: OutlineInputBorder(),
                        ),
                      ),
                      if (isLoading)
                        const Padding(
                          padding: EdgeInsets.only(top: 16.0),
                          child: CircularProgressIndicator(),
                        ),
                    ],
                  ),
                  actions: [
                    TextButton(
                      onPressed:
                          isLoading ? null : () => Navigator.pop(context),
                      child: const Text('إلغاء'),
                    ),
                    TextButton(
                      onPressed:
                          isLoading
                              ? null
                              : () async {
                                // التحقق من صحة المدخلات
                                if (currentPasswordController.text.isEmpty ||
                                    newPasswordController.text.isEmpty ||
                                    confirmPasswordController.text.isEmpty) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                      content: Text('الرجاء ملء جميع الحقول'),
                                      backgroundColor: Colors.red,
                                    ),
                                  );
                                  return;
                                }

                                final newPassword = newPasswordController.text;
                                if (newPassword.length < 8) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                      content: Text(
                                        'كلمة المرور الجديدة يجب أن تكون 8 أحرف على الأقل',
                                      ),
                                      backgroundColor: Colors.red,
                                    ),
                                  );
                                  return;
                                }
                                if (!newPassword.contains(RegExp(r'[A-Z]'))) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                      content: Text(
                                        'كلمة المرور يجب أن تحتوي على حرف كبير',
                                      ),
                                      backgroundColor: Colors.red,
                                    ),
                                  );
                                  return;
                                }
                                if (!newPassword.contains(RegExp(r'[a-z]'))) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                      content: Text(
                                        'كلمة المرور يجب أن تحتوي على حرف صغير',
                                      ),
                                      backgroundColor: Colors.red,
                                    ),
                                  );
                                  return;
                                }
                                if (!newPassword.contains(RegExp(r'[0-9]'))) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                      content: Text(
                                        'كلمة المرور يجب أن تحتوي على رقم',
                                      ),
                                      backgroundColor: Colors.red,
                                    ),
                                  );
                                  return;
                                }

                                if (newPasswordController.text !=
                                    confirmPasswordController.text) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                      content: Text('كلمات المرور غير متطابقة'),
                                      backgroundColor: Colors.red,
                                    ),
                                  );
                                  return;
                                }

                                setState(() {
                                  isLoading = true;
                                });

                                final success = await authService
                                    .changePassword(
                                      currentPasswordController.text,
                                      newPasswordController.text,
                                    );

                                if (!mounted) return;

                                setState(() {
                                  isLoading = false;
                                });

                                Navigator.pop(context);

                                if (success) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                      content: Text(
                                        'تم تغيير كلمة المرور بنجاح',
                                      ),
                                      backgroundColor: Colors.green,
                                    ),
                                  );
                                } else {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: Text(
                                        authService.error ??
                                            'فشل تغيير كلمة المرور',
                                      ),
                                      backgroundColor: Colors.red,
                                    ),
                                  );
                                }
                              },
                      child: const Text('تغيير'),
                    ),
                  ],
                ),
          ),
    );
  }
}
