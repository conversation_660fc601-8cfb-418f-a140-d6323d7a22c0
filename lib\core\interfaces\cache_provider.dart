
/// واجهة موحدة لمقدمي إدارة الذاكرة المؤقتة
abstract class CacheProvider {
  /// مسح جميع البيانات المؤقتة
  void clearCache();
  
  /// الحصول على حجم الذاكرة المؤقتة
  int get cacheSize;
  
  /// تعيين الحد الأقصى للذاكرة المؤقتة
  void setCacheLimit(int limit);
  
  /// الحصول على الحد الأقصى للذاكرة المؤقتة
  int get cacheLimit;
  
  /// التحقق من امتلاء الذاكرة المؤقتة
  bool get isCacheFull => cacheSize >= cacheLimit;
  
  /// مسح البيانات المنتهية الصلاحية
  void clearExpiredCache() {
    // يمكن تنفيذها في الكلاسات المشتقة
  }
  
  /// الحصول على إحصائيات الذاكرة المؤقتة
  Map<String, dynamic> getCacheStats() {
    return {
      'cache_size': cacheSize,
      'cache_limit': cacheLimit,
      'cache_usage_percentage': (cacheSize / cacheLimit * 100).toStringAsFixed(2),
      'is_cache_full': isCacheFull,
    };
  }
  
  /// تحسين الذاكرة المؤقتة
  void optimizeCache() {
    if (isCacheFull) {
      clearExpiredCache();
    }
  }
}
