name: motorcycle_parts_shop
description: "تطبيق متجر قطع غيار الدراجات النارية"
publish_to: 'none'

version: 1.2.0+2

environment:
  sdk: ^3.7.0

dependencies:
  flutter:
    sdk: flutter
  http: ^1.2.2
  supabase_flutter: ^2.8.3
  flutter_localizations:
    sdk: flutter
  cached_network_image: ^3.4.1
  connectivity_plus: ^6.0.0
  url_launcher: ^6.3.1
  flutter_svg: ^2.0.17
  image_picker: ^1.1.2
  path_provider: ^2.1.5
  path: ^1.9.0
  sqflite: ^2.4.1
  geolocator: ^14.0.1
  email_validator: ^3.0.0
  vibration: ^3.1.3
  flutter_staggered_animations: ^1.1.1
  supabase: ^2.7.0
  flutter_rating_bar: ^4.0.1
  carousel_slider: ^5.1.1
  device_info_plus: ^11.4.0
  shimmer: ^3.0.0
  flutter_staggered_grid_view: ^0.7.0
  shared_preferences: ^2.3.5
  intl: ^0.19.0
  uuid: ^4.5.1
  cupertino_icons: ^1.0.2
  provider: ^6.1.2
  logger: ^2.5.0
  share_plus: ^11.0.0
  flutter_local_notifications: ^19.2.1
  flutter_cache_manager: ^3.4.1
  flutter_riverpod: ^2.6.1
  fl_chart: ^1.0.0
  data_table_2: ^2.5.11
  printing: ^5.12.0
  pdf: ^3.10.8
  flutter_dotenv: ^5.2.1
  camera: ^0.11.1
  image: ^4.1.7
  cloudinary: ^1.2.0
  google_sign_in: ^6.2.1
  package_info_plus: ^8.2.0
  speech_to_text: ^7.0.0
  pin_code_fields: ^8.0.1
  lottie: ^3.3.1
  open_file: ^3.3.1
  postgrest: any
  confetti: ^0.8.0
  file_picker: ^10.1.9
  json_serializable: ^6.9.5
  json_annotation: ^4.9.0
  google_generative_ai: ^0.4.6
  crypto: ^3.0.3


dev_dependencies:
  flutter_test:
    sdk: flutter
  mockito: ^5.4.0
  build_runner: ^2.4.15
  flutter_lints: ^5.0.0
  integration_test:
    sdk: flutter

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
    - assets/translations/
    - .env

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    # خط القاهرة - للنصوص المتوسطة والأزرار
    - family: Cairo
      fonts:
        - asset: assets/fonts/Cairo-Regular.ttf
          weight: 400
        - asset: assets/fonts/Cairo-Medium.ttf
          weight: 500
        - asset: assets/fonts/Cairo-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Cairo-Bold.ttf
          weight: 700

    # خط تجوال - للعناوين الكبيرة والمهمة
    - family: Tajawal
      fonts:
        - asset: assets/fonts/Tajawal-Regular.ttf
          weight: 400
        - asset: assets/fonts/Tajawal-Medium.ttf
          weight: 500
        - asset: assets/fonts/Tajawal-Bold.ttf
          weight: 700
        - asset: assets/fonts/Tajawal-ExtraBold.ttf
          weight: 800

    # خط ناتو العربي - للنصوص الطويلة والمحتوى
    - family: Noto Sans Arabic
      fonts:
        - asset: assets/fonts/NotoSansArabic-Regular.ttf
          weight: 400
        - asset: assets/fonts/NotoSansArabic-Medium.ttf
          weight: 500
        - asset: assets/fonts/NotoSansArabic-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/NotoSansArabic-Bold.ttf
          weight: 700
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
