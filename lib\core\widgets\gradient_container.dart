import '../theme/gradients.dart';
import 'package:flutter/material.dart';

/// حاوية مع تدرج لوني
///
/// مكون قابل لإعادة الاستخدام يوفر حاوية مع تدرج لوني
/// يمكن استخدامه في مختلف أجزاء التطبيق مثل البطاقات والأزرار والخلفيات
class GradientContainer extends StatelessWidget {
  final Widget child;
  final LinearGradient gradient;
  final BorderRadius? borderRadius;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final double? width;
  final double? height;
  final List<BoxShadow>? boxShadow;
  final Border? border;

  const GradientContainer({
    super.key,
    required this.child,
    required this.gradient,
    this.borderRadius,
    this.padding,
    this.margin,
    this.width,
    this.height,
    this.boxShadow,
    this.border,
  });

  /// إنشاء حاوية بتدرج اللون الأساسي
  factory GradientContainer.primary({
    Key? key,
    required Widget child,
    BorderRadius? borderRadius,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    double? width,
    double? height,
    List<BoxShadow>? boxShadow,
    Border? border,
  }) {
    return GradientContainer(
      key: key,
      gradient: AppGradients.primaryGradient,
      borderRadius: borderRadius,
      padding: padding,
      margin: margin,
      width: width,
      height: height,
      boxShadow: boxShadow,
      border: border,
      child: child,
    );
  }

  /// إنشاء حاوية بتدرج اللون الثانوي
  factory GradientContainer.secondary({
    Key? key,
    required Widget child,
    BorderRadius? borderRadius,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    double? width,
    double? height,
    List<BoxShadow>? boxShadow,
    Border? border,
  }) {
    return GradientContainer(
      key: key,
      gradient: AppGradients.secondaryGradient,
      borderRadius: borderRadius,
      padding: padding,
      margin: margin,
      width: width,
      height: height,
      boxShadow: boxShadow,
      border: border,
      child: child,
    );
  }

  /// إنشاء حاوية بتدرج أمواج البحر الأزرق
  factory GradientContainer.blueWave({
    Key? key,
    required Widget child,
    BorderRadius? borderRadius,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    double? width,
    double? height,
    List<BoxShadow>? boxShadow,
    Border? border,
  }) {
    return GradientContainer(
      key: key,
      gradient: AppGradients.blueWaveGradient,
      borderRadius: borderRadius,
      padding: padding,
      margin: margin,
      width: width,
      height: height,
      boxShadow: boxShadow,
      border: border,
      child: child,
    );
  }

  /// إنشاء حاوية بتدرج أمواج البحر الفيروزي
  factory GradientContainer.turquoiseWave({
    Key? key,
    required Widget child,
    BorderRadius? borderRadius,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    double? width,
    double? height,
    List<BoxShadow>? boxShadow,
    Border? border,
  }) {
    return GradientContainer(
      key: key,
      gradient: AppGradients.turquoiseWaveGradient,
      borderRadius: borderRadius,
      padding: padding,
      margin: margin,
      width: width,
      height: height,
      boxShadow: boxShadow,
      border: border,
      child: child,
    );
  }

  /// إنشاء حاوية بتدرج أمواج البحر الأخضر
  factory GradientContainer.greenWave({
    Key? key,
    required Widget child,
    BorderRadius? borderRadius,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    double? width,
    double? height,
    List<BoxShadow>? boxShadow,
    Border? border,
  }) {
    return GradientContainer(
      key: key,
      gradient: AppGradients.greenWaveGradient,
      borderRadius: borderRadius,
      padding: padding,
      margin: margin,
      width: width,
      height: height,
      boxShadow: boxShadow,
      border: border,
      child: child,
    );
  }

  /// إنشاء حاوية بتدرج أمواج البحر الذهبي
  factory GradientContainer.goldenWave({
    Key? key,
    required Widget child,
    BorderRadius? borderRadius,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    double? width,
    double? height,
    List<BoxShadow>? boxShadow,
    Border? border,
  }) {
    return GradientContainer(
      key: key,
      gradient: AppGradients.goldenWaveGradient,
      borderRadius: borderRadius,
      padding: padding,
      margin: margin,
      width: width,
      height: height,
      boxShadow: boxShadow,
      border: border,
      child: child,
    );
  }

  /// إنشاء حاوية بتدرج لون البطاقة
  factory GradientContainer.card({
    Key? key,
    required Widget child,
    BorderRadius? borderRadius,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    double? width,
    double? height,
    List<BoxShadow>? boxShadow,
    Border? border,
  }) {
    return GradientContainer(
      key: key,
      gradient: AppGradients.cardGradient,
      borderRadius: borderRadius ?? BorderRadius.circular(16),
      padding: padding ?? const EdgeInsets.all(16),
      margin: margin ?? const EdgeInsets.symmetric(vertical: 8),
      width: width,
      height: height,
      boxShadow:
          boxShadow ??
          [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
      border: border,
      child: child,
    );
  }

  /// إنشاء حاوية بتدرج لون الخلفية
  factory GradientContainer.background({
    Key? key,
    required Widget child,
    BorderRadius? borderRadius,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    double? width,
    double? height,
    List<BoxShadow>? boxShadow,
    Border? border,
  }) {
    return GradientContainer(
      key: key,
      gradient: AppGradients.backgroundGradient,
      borderRadius: borderRadius,
      padding: padding,
      margin: margin,
      width: width,
      height: height,
      boxShadow: boxShadow,
      border: border,
      child: child,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      margin: margin,
      decoration: BoxDecoration(
        gradient: gradient,
        borderRadius: borderRadius,
        boxShadow: boxShadow,
        border: border,
      ),
      padding: padding,
      child: child,
    );
  }
}

/// زر مع تدرج لوني
///
/// مكون قابل لإعادة الاستخدام يوفر زر مع تدرج لوني
class GradientButton extends StatelessWidget {
  final VoidCallback onPressed;
  final Widget child;
  final LinearGradient gradient;
  final BorderRadius? borderRadius;
  final EdgeInsetsGeometry? padding;
  final double? width;
  final double? height;
  final List<BoxShadow>? boxShadow;

  const GradientButton({
    super.key,
    required this.onPressed,
    required this.child,
    required this.gradient,
    this.borderRadius,
    this.padding,
    this.width,
    this.height,
    this.boxShadow,
  });

  /// إنشاء زر بتدرج اللون الأساسي
  factory GradientButton.primary({
    Key? key,
    required VoidCallback onPressed,
    required Widget child,
    BorderRadius? borderRadius,
    EdgeInsetsGeometry? padding,
    double? width,
    double? height,
    List<BoxShadow>? boxShadow,
  }) {
    return GradientButton(
      key: key,
      onPressed: onPressed,
      gradient: AppGradients.buttonGradient,
      borderRadius: borderRadius ?? BorderRadius.circular(12),
      padding:
          padding ?? const EdgeInsets.symmetric(horizontal: 24, vertical: 14),
      width: width,
      height: height,
      boxShadow:
          boxShadow ??
          [
            BoxShadow(
              color: Colors.blue.withOpacity(0.3),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
      child: child,
    );
  }

  /// إنشاء زر بتدرج اللون الثانوي
  factory GradientButton.secondary({
    Key? key,
    required VoidCallback onPressed,
    required Widget child,
    BorderRadius? borderRadius,
    EdgeInsetsGeometry? padding,
    double? width,
    double? height,
    List<BoxShadow>? boxShadow,
  }) {
    return GradientButton(
      key: key,
      onPressed: onPressed,
      gradient: AppGradients.secondaryButtonGradient,
      borderRadius: borderRadius ?? BorderRadius.circular(12),
      padding:
          padding ?? const EdgeInsets.symmetric(horizontal: 24, vertical: 14),
      width: width,
      height: height,
      boxShadow:
          boxShadow ??
          [
            BoxShadow(
              color: Colors.orange.withOpacity(0.3),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
      child: child,
    );
  }

  /// إنشاء زر بتدرج لون النجاح
  factory GradientButton.success({
    Key? key,
    required VoidCallback onPressed,
    required Widget child,
    BorderRadius? borderRadius,
    EdgeInsetsGeometry? padding,
    double? width,
    double? height,
    List<BoxShadow>? boxShadow,
  }) {
    return GradientButton(
      key: key,
      onPressed: onPressed,
      gradient: AppGradients.successGradient,
      borderRadius: borderRadius ?? BorderRadius.circular(12),
      padding:
          padding ?? const EdgeInsets.symmetric(horizontal: 24, vertical: 14),
      width: width,
      height: height,
      boxShadow:
          boxShadow ??
          [
            BoxShadow(
              color: Colors.green.withOpacity(0.3),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
      child: child,
    );
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onPressed,
      borderRadius: borderRadius ?? BorderRadius.circular(12),
      child: Ink(
        width: width,
        height: height,
        decoration: BoxDecoration(
          gradient: gradient,
          borderRadius: borderRadius ?? BorderRadius.circular(12),
          boxShadow: boxShadow,
        ),
        child: Container(
          padding:
              padding ??
              const EdgeInsets.symmetric(horizontal: 24, vertical: 14),
          alignment: Alignment.center,
          child: child,
        ),
      ),
    );
  }
}

/// بطاقة مع تدرج لوني
///
/// مكون قابل لإعادة الاستخدام يوفر بطاقة مع تدرج لوني
class GradientCard extends StatelessWidget {
  final Widget child;
  final LinearGradient gradient;
  final BorderRadius? borderRadius;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final double? width;
  final double? height;
  final List<BoxShadow>? boxShadow;
  final VoidCallback? onTap;

  const GradientCard({
    super.key,
    required this.child,
    required this.gradient,
    this.borderRadius,
    this.padding,
    this.margin,
    this.width,
    this.height,
    this.boxShadow,
    this.onTap,
  });

  /// إنشاء بطاقة بتدرج لون البطاقة الافتراضي
  factory GradientCard.defaultCard({
    Key? key,
    required Widget child,
    BorderRadius? borderRadius,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    double? width,
    double? height,
    List<BoxShadow>? boxShadow,
    VoidCallback? onTap,
  }) {
    return GradientCard(
      key: key,
      gradient: AppGradients.cardGradient,
      borderRadius: borderRadius ?? BorderRadius.circular(16),
      padding: padding ?? const EdgeInsets.all(16),
      margin: margin ?? const EdgeInsets.symmetric(vertical: 8),
      width: width,
      height: height,
      boxShadow:
          boxShadow ??
          [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
      onTap: onTap,
      child: child,
    );
  }

  /// إنشاء بطاقة بتدرج اللون الأساسي
  factory GradientCard.primary({
    Key? key,
    required Widget child,
    BorderRadius? borderRadius,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    double? width,
    double? height,
    List<BoxShadow>? boxShadow,
    VoidCallback? onTap,
  }) {
    return GradientCard(
      key: key,
      gradient: AppGradients.primaryGradient,
      borderRadius: borderRadius ?? BorderRadius.circular(16),
      padding: padding ?? const EdgeInsets.all(16),
      margin: margin ?? const EdgeInsets.symmetric(vertical: 8),
      width: width,
      height: height,
      boxShadow:
          boxShadow ??
          [
            BoxShadow(
              color: Colors.blue.withOpacity(0.2),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
      onTap: onTap,
      child: child,
    );
  }

  @override
  Widget build(BuildContext context) {
    final container = Container(
      width: width,
      height: height,
      margin: margin,
      decoration: BoxDecoration(
        gradient: gradient,
        borderRadius: borderRadius ?? BorderRadius.circular(16),
        boxShadow: boxShadow,
      ),
      padding: padding,
      child: child,
    );

    if (onTap != null) {
      return InkWell(
        onTap: onTap,
        borderRadius: borderRadius ?? BorderRadius.circular(16),
        child: container,
      );
    }

    return container;
  }
}
