import 'package:motorcycle_parts_shop/models/product_model.dart';

class WishlistModel {
  final String id;
  final String userId;
  final String productId;
  final DateTime createdAt;
  final DateTime updatedAt;
  final ProductModel? product;

  WishlistModel({
    required this.id,
    required this.userId,
    required this.productId,
    required this.createdAt,
    required this.updatedAt,
    this.product,
  });

  factory WishlistModel.fromJson(Map<String, dynamic> json) {
    return WishlistModel(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      productId: json['product_id'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      product:
          json['product'] != null
              ? ProductModel.fromJson(json['product'] as Map<String, dynamic>)
              : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'product_id': productId,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'product': product?.toJson(),
    };
  }

  WishlistModel copyWith({
    String? id,
    String? userId,
    String? productId,
    DateTime? createdAt,
    DateTime? updatedAt,
    ProductModel? product,
  }) {
    return WishlistModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      productId: productId ?? this.productId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      product: product ?? this.product,
    );
  }

  bool get hasProduct => product != null;

  static WishlistModel fromProduct({
    required String userId,
    required ProductModel product,
  }) {
    return WishlistModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      userId: userId,
      productId: product.id,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      product: product,
    );
  }
}
