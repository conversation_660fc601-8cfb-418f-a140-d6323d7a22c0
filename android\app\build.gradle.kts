import java.util.Properties
import java.io.FileInputStream

plugins {
    id("com.android.application")
    id("kotlin-android")
    id("dev.flutter.flutter-gradle-plugin")
}

// تحميل إعدادات التوقيع
val keystoreProperties = Properties()
val keystorePropertiesFile = rootProject.file("key.properties")
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(FileInputStream(keystorePropertiesFile))
}

dependencies {
    coreLibraryDesugaring("com.android.tools:desugar_jdk_libs:2.1.4")
    implementation("androidx.appcompat:appcompat:1.6.1")
    implementation("com.google.android.material:material:1.9.0")
    implementation("androidx.core:core-ktx:1.12.0") // إضافة لدعم Kotlin extensions
    implementation("com.google.android.play:core:1.10.3") // إضافة Google Play Core
    implementation("com.google.android.play:core-ktx:1.8.1") // إضافة Google Play Core KTX
}

android {
    compileSdk = 35
    ndkVersion = "27.0.12077973"
    namespace = "com.moto_elmagic.app"

    // إعدادات التوقيع
    signingConfigs {
        create("release") {
            keyAlias = keystoreProperties["keyAlias"] as String?
            keyPassword = keystoreProperties["keyPassword"] as String?
            storeFile = keystoreProperties["storeFile"]?.let { file(it) }
            storePassword = keystoreProperties["storePassword"] as String?
        }
    }

    compileOptions {
        isCoreLibraryDesugaringEnabled = true
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = "17"
        freeCompilerArgs += listOf(
            "-Xjvm-default=all",
            "-Xopt-in=kotlin.RequiresOptIn",
            "-Xno-param-assertions",
            "-Xno-call-assertions"
        )
    }

    defaultConfig {
        applicationId = "com.moto_elmagic.app"
        minSdk = 21
        targetSdk = 35 // تحديث ليطابق compileSdk
        versionCode = flutter.versionCode?.toInt() ?: 1
        versionName = flutter.versionName ?: "1.0"
        multiDexEnabled = true // إضافة إذا كنت تستخدم العديد من المكتبات
    }

    buildTypes {
        getByName("debug") {
            applicationIdSuffix = ".debug"
            versionNameSuffix = "-DEBUG"
            isMinifyEnabled = false
            isShrinkResources = false
            // تمكين التنقيح لجميع المكتبات
            packagingOptions {
                jniLibs {
                    useLegacyPackaging = true
                }
            }
        }
        getByName("release") {
            isMinifyEnabled = false
            isShrinkResources = false
            isDebuggable = false
            signingConfig = signingConfigs.getByName("release")
        }
    }

    // إعدادات إضافية لأداء أفضل
    packagingOptions {
        resources {
            excludes += "/META-INF/{AL2.0,LGPL2.1}"
            excludes += "**/attach_hotspot_windows.dll"
        }
        jniLibs {
            useLegacyPackaging = true
        }
    }


}

flutter {
    source = "../.."
}