import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:motorcycle_parts_shop/core/services/unified_storage_service.dart';
import 'package:motorcycle_parts_shop/core/theme/app_theme.dart';

/// خدمة إدارة الثيم المحسنة
class ThemeService extends ChangeNotifier {
  static final ThemeService _instance = ThemeService._internal();
  factory ThemeService({UnifiedStorageService? storageService}) {
    if (storageService != null) {
      _instance._storage = storageService;
    }
    return _instance;
  }
  ThemeService._internal();

  late UnifiedStorageService _storage;
  ThemeMode _themeMode = ThemeMode.system;
  bool _isInitialized = false;
  Color _primaryColor = const Color(0xFF1976D2);
  String _fontFamily = 'Cairo';
  double _textScaleFactor = 1.0;

  // Getters
  ThemeMode get themeMode => _themeMode;
  bool get isInitialized => _isInitialized;
  Color get primaryColor => _primaryColor;
  String get fontFamily => _fontFamily;
  double get textScaleFactor => _textScaleFactor;

  /// تهيئة الخدمة
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _loadThemeSettings();
      _isInitialized = true;
      debugPrint('✅ تم تهيئة خدمة الثيم بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة خدمة الثيم: $e');
      _isInitialized = true;
    }
  }

  /// تحميل إعدادات الثيم
  Future<void> _loadThemeSettings() async {
    try {
      // تحميل وضع الثيم
      final themeModeString = _storage.getString('theme_mode') ?? 'system';
      _themeMode = _parseThemeMode(themeModeString);

      // تحميل اللون الأساسي
      final colorValue = _storage.getInt('primary_color');
      if (colorValue != null) {
        _primaryColor = Color(colorValue);
      }

      // تحميل الخط
      _fontFamily = _storage.getString('font_family') ?? 'Cairo';

      // تحميل معامل النص
      _textScaleFactor = _storage.getDoubleValue('text_scale_factor') ?? 1.0;

      debugPrint('📱 تم تحميل إعدادات الثيم');
    } catch (e) {
      debugPrint('❌ خطأ في تحميل إعدادات الثيم: $e');
    }
  }

  /// حفظ إعدادات الثيم
  Future<void> _saveThemeSettings() async {
    try {
      await _storage.saveString('theme_mode', _themeMode.name);
      await _storage.saveInt('primary_color', _primaryColor.value);
      await _storage.saveString('font_family', _fontFamily);
      await _storage.saveDouble('text_scale_factor', _textScaleFactor);
    } catch (e) {
      debugPrint('❌ خطأ في حفظ إعدادات الثيم: $e');
    }
  }

  /// تحويل النص إلى ThemeMode
  ThemeMode _parseThemeMode(String mode) {
    switch (mode) {
      case 'light':
        return ThemeMode.light;
      case 'dark':
        return ThemeMode.dark;
      case 'system':
      default:
        return ThemeMode.system;
    }
  }

  /// تحديد ما إذا كان الوضع الداكن مفعل
  bool isDarkMode(BuildContext context) {
    switch (_themeMode) {
      case ThemeMode.light:
        return false;
      case ThemeMode.dark:
        return true;
      case ThemeMode.system:
        return MediaQuery.of(context).platformBrightness == Brightness.dark;
    }
  }

  /// تغيير وضع الثيم
  Future<void> setThemeMode(ThemeMode mode) async {
    if (_themeMode == mode) return;

    _themeMode = mode;
    await _saveThemeSettings();
    notifyListeners();

    // تحديث شريط الحالة
    _updateSystemUI();

    debugPrint('🎨 تم تغيير وضع الثيم إلى: ${mode.name}');
  }

  /// تغيير اللون الأساسي
  Future<void> setPrimaryColor(Color color) async {
    if (_primaryColor == color) return;

    _primaryColor = color;
    await _saveThemeSettings();
    notifyListeners();

    debugPrint('🎨 تم تغيير اللون الأساسي');
  }

  /// تغيير الخط
  Future<void> setFontFamily(String fontFamily) async {
    if (_fontFamily == fontFamily) return;

    _fontFamily = fontFamily;
    await _saveThemeSettings();
    notifyListeners();

    debugPrint('🔤 تم تغيير الخط إلى: $fontFamily');
  }

  /// تغيير معامل النص
  Future<void> setTextScaleFactor(double factor) async {
    if (_textScaleFactor == factor) return;

    _textScaleFactor = factor.clamp(0.8, 1.5);
    await _saveThemeSettings();
    notifyListeners();

    debugPrint('📏 تم تغيير معامل النص إلى: $_textScaleFactor');
  }

  /// تبديل الوضع الداكن/الفاتح
  Future<void> toggleTheme() async {
    final newMode =
        _themeMode == ThemeMode.dark ? ThemeMode.light : ThemeMode.dark;
    await setThemeMode(newMode);
  }

  /// الحصول على الثيم الفاتح المخصص
  ThemeData getLightTheme() {
    return AppTheme.lightTheme().copyWith(
      colorScheme: ColorScheme.fromSeed(
        seedColor: _primaryColor,
        brightness: Brightness.light,
      ),
      textTheme: AppTheme.lightTheme().textTheme.apply(
        fontFamily: _fontFamily,
        fontSizeFactor: _textScaleFactor,
      ),
    );
  }

  /// الحصول على الثيم الداكن المخصص
  ThemeData getDarkTheme() {
    return AppTheme.darkTheme().copyWith(
      colorScheme: ColorScheme.fromSeed(
        seedColor: _primaryColor,
        brightness: Brightness.dark,
      ),
      textTheme: AppTheme.darkTheme().textTheme.apply(
        fontFamily: _fontFamily,
        fontSizeFactor: _textScaleFactor,
      ),
    );
  }

  /// تحديث واجهة النظام
  void _updateSystemUI() {
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness:
            _themeMode == ThemeMode.dark ? Brightness.light : Brightness.dark,
        systemNavigationBarColor:
            _themeMode == ThemeMode.dark ? Colors.black : Colors.white,
        systemNavigationBarIconBrightness:
            _themeMode == ThemeMode.dark ? Brightness.light : Brightness.dark,
      ),
    );
  }

  /// الألوان المتاحة للاختيار
  List<Color> get availableColors => [
    const Color(0xFF1976D2), // أزرق
    const Color(0xFF388E3C), // أخضر
    const Color(0xFFD32F2F), // أحمر
    const Color(0xFFF57C00), // برتقالي
    const Color(0xFF7B1FA2), // بنفسجي
    const Color(0xFF00796B), // تيل
    const Color(0xFF5D4037), // بني
    const Color(0xFF455A64), // رمادي مزرق
  ];

  /// الخطوط المتاحة
  List<String> get availableFonts => [
    'Cairo',
    'Amiri',
    'Tajawal',
    'Almarai',
    'Changa',
  ];

  /// معاملات النص المتاحة
  List<double> get availableTextScales => [
    0.8,
    0.9,
    1.0,
    1.1,
    1.2,
    1.3,
    1.4,
    1.5,
  ];

  /// إعادة تعيين الإعدادات للافتراضية
  Future<void> resetToDefaults() async {
    _themeMode = ThemeMode.system;
    _primaryColor = const Color(0xFF1976D2);
    _fontFamily = 'Cairo';
    _textScaleFactor = 1.0;

    await _saveThemeSettings();
    notifyListeners();
    _updateSystemUI();

    debugPrint('🔄 تم إعادة تعيين إعدادات الثيم للافتراضية');
  }

  /// الحصول على معلومات الثيم الحالي
  Map<String, dynamic> getThemeInfo() {
    return {
      'themeMode': _themeMode.name,
      'primaryColor': _primaryColor.value,
      'fontFamily': _fontFamily,
      'textScaleFactor': _textScaleFactor,
      'isInitialized': _isInitialized,
    };
  }

  /// تصدير إعدادات الثيم
  Map<String, dynamic> exportSettings() {
    return {
      'themeMode': _themeMode.name,
      'primaryColor': _primaryColor.value,
      'fontFamily': _fontFamily,
      'textScaleFactor': _textScaleFactor,
      'exportedAt': DateTime.now().toIso8601String(),
    };
  }

  /// استيراد إعدادات الثيم
  Future<void> importSettings(Map<String, dynamic> settings) async {
    try {
      if (settings['themeMode'] != null) {
        _themeMode = _parseThemeMode(settings['themeMode']);
      }

      if (settings['primaryColor'] != null) {
        _primaryColor = Color(settings['primaryColor']);
      }

      if (settings['fontFamily'] != null) {
        _fontFamily = settings['fontFamily'];
      }

      if (settings['textScaleFactor'] != null) {
        _textScaleFactor = settings['textScaleFactor'];
      }

      await _saveThemeSettings();
      notifyListeners();
      _updateSystemUI();

      debugPrint('✅ تم استيراد إعدادات الثيم بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في استيراد إعدادات الثيم: $e');
      throw Exception('فشل في استيراد إعدادات الثيم');
    }
  }
}
