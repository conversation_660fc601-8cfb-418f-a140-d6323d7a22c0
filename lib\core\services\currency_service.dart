
class CurrencyService {
  static final CurrencyService _instance = CurrencyService._internal();

  factory CurrencyService() => _instance;

  CurrencyService._internal();

  String formatPrice(num price) {
    return '${_formatNumber(price)} ج.م';
  }

  String _formatNumber(num number) {
    return number.toStringAsFixed(2).replaceAllMapped(
      RegExp(r'(\d)(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]},',
    );
  }
}