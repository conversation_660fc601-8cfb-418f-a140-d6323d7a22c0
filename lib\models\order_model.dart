import 'package:flutter/material.dart';
import 'package:motorcycle_parts_shop/core/constants/app_constants.dart';
import 'package:motorcycle_parts_shop/models/address_model.dart';
import 'package:motorcycle_parts_shop/models/order_item_model.dart';
import 'package:motorcycle_parts_shop/models/payment_method_model.dart';

class OrderModel {
  final String id;
  final String? orderNumber;
  final String userId;
  final String addressId;
  final double totalAmount;
  final double? taxAmount;
  final double? discountAmount;
  final double? shippingCost;
  final double? subtotal;
  final String status;
  final String paymentStatus;
  final String paymentMethodId;
  final String? shippingMethodId;
  final String? notes;
  final String? trackingNumber;
  final DateTime? estimatedDelivery;
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<OrderItemModel> items;
  final AddressModel address;
  final PaymentMethodModel paymentMethod;

  OrderModel({
    required this.id,
    this.orderNumber,
    required this.userId,
    required this.addressId,
    required this.totalAmount,
    this.taxAmount,
    this.discountAmount,
    this.shippingCost,
    this.subtotal,
    required this.status,
    required this.paymentStatus,
    required this.paymentMethodId,
    this.shippingMethodId,
    this.notes,
    this.trackingNumber,
    this.estimatedDelivery,
    required this.createdAt,
    required this.updatedAt,
    required this.items,
    required this.address,
    required this.paymentMethod,
  }) : assert(
         AppConstants.isValidOrderStatus(status),
         'حالة الطلب غير صالحة: $status',
       ),
       assert(
         paymentStatus == 'pending' ||
             paymentStatus == 'paid' ||
             paymentStatus == 'failed' ||
             paymentStatus == 'refunded',
         'حالة الدفع غير صالحة: $paymentStatus',
       );

  factory OrderModel.fromJson(Map<String, dynamic> json) {
    return OrderModel(
      id: json['id'] as String,
      orderNumber: json['order_number'] as String?,
      userId: json['user_id'] as String,
      addressId: json['address_id'] as String,
      totalAmount: (json['total_amount'] as num).toDouble(),
      taxAmount: (json['tax_amount'] as num?)?.toDouble(),
      discountAmount: (json['discount_amount'] as num?)?.toDouble(),
      shippingCost: (json['shipping_cost'] as num?)?.toDouble(),
      subtotal: (json['subtotal'] as num?)?.toDouble(),
      status: json['status'] as String,
      paymentStatus: json['payment_status'] as String,
      paymentMethodId: json['payment_method_id'] as String,
      shippingMethodId: json['shipping_method_id'] as String?,
      notes: json['notes'] as String?,
      trackingNumber: json['tracking_number'] as String?,
      estimatedDelivery:
          json['estimated_delivery'] != null
              ? DateTime.parse(json['estimated_delivery'] as String)
              : null,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      items:
          (json['items'] as List<dynamic>?)
              ?.map(
                (item) => OrderItemModel.fromJson(item as Map<String, dynamic>),
              )
              .toList() ??
          [],
      address: AddressModel.fromJson(json['address']),
      paymentMethod: PaymentMethodModel.fromJson(json['payment_method']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'order_number': orderNumber,
      'user_id': userId,
      'address_id': addressId,
      'total_amount': totalAmount,
      'tax_amount': taxAmount,
      'discount_amount': discountAmount,
      'shipping_cost': shippingCost,
      'subtotal': subtotal,
      'status': status,
      'payment_status': paymentStatus,
      'payment_method_id': paymentMethodId,
      'shipping_method_id': shippingMethodId,
      'notes': notes,
      'tracking_number': trackingNumber,
      'estimated_delivery': estimatedDelivery?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'items': items.map((item) => item.toJson()).toList(),
      'address': address.toJson(),
      'payment_method': paymentMethod.toJson(),
    };
  }

  OrderModel copyWith({
    String? id,
    String? orderNumber,
    String? userId,
    String? addressId,
    double? totalAmount,
    double? taxAmount,
    double? discountAmount,
    double? shippingCost,
    double? subtotal,
    String? status,
    String? paymentStatus,
    String? paymentMethodId,
    String? shippingMethodId,
    String? notes,
    String? trackingNumber,
    DateTime? estimatedDelivery,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<OrderItemModel>? items,
    AddressModel? address,
    PaymentMethodModel? paymentMethod,
  }) {
    return OrderModel(
      id: id ?? this.id,
      orderNumber: orderNumber ?? this.orderNumber,
      userId: userId ?? this.userId,
      addressId: addressId ?? this.addressId,
      totalAmount: totalAmount ?? this.totalAmount,
      taxAmount: taxAmount ?? this.taxAmount,
      discountAmount: discountAmount ?? this.discountAmount,
      shippingCost: shippingCost ?? this.shippingCost,
      subtotal: subtotal ?? this.subtotal,
      status: status ?? this.status,
      paymentStatus: paymentStatus ?? this.paymentStatus,
      paymentMethodId: paymentMethodId ?? this.paymentMethodId,
      shippingMethodId: shippingMethodId ?? this.shippingMethodId,
      notes: notes ?? this.notes,
      trackingNumber: trackingNumber ?? this.trackingNumber,
      estimatedDelivery: estimatedDelivery ?? this.estimatedDelivery,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      items: items ?? this.items,
      address: address ?? this.address,
      paymentMethod: paymentMethod ?? this.paymentMethod,
    );
  }

  // خصائص مساعدة
  bool get isActive => status != AppConstants.orderStatusDelivered;
  bool get canCancel => status == AppConstants.orderStatusPending;
  bool get canTrack =>
      trackingNumber != null &&
      (status == AppConstants.orderStatusShipped ||
          status == AppConstants.orderStatusDelivered);
  bool get isPending => status == AppConstants.orderStatusPending;
  bool get isProcessing => status == AppConstants.orderStatusProcessing;
  bool get isShipped => status == AppConstants.orderStatusShipped;
  bool get isDelivered => status == AppConstants.orderStatusDelivered;
  bool get isCancelled => status == AppConstants.orderStatusCancelled;
  bool get isPaid => paymentStatus == 'paid';
  bool get isRefunded => paymentStatus == 'refunded';
  bool get hasShippingMethod => shippingMethodId != null;

  // استخدام الدوال المساعدة من AppConstants
  Color get statusColor => AppConstants.getOrderStatusColor(status);
  String get statusText => AppConstants.getOrderStatusText(status);

  // حساب المبالغ
  double get calculatedSubtotal =>
      subtotal ??
      (totalAmount -
          (taxAmount ?? 0) -
          (shippingCost ?? 0) +
          (discountAmount ?? 0));
  double get calculatedTotal =>
      (subtotal ?? 0) +
      (taxAmount ?? 0) +
      (shippingCost ?? 0) -
      (discountAmount ?? 0);
}
