import '../../core/services/auth_supabase_service.dart';
import '../../core/widgets/loading_indicator.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

/// شاشة إعدادات المنتجات الجديدة للمشرفين
class NewProductsSettingsScreen extends StatefulWidget {
  const NewProductsSettingsScreen({super.key});

  @override
  State<NewProductsSettingsScreen> createState() => _NewProductsSettingsScreenState();
}

class _NewProductsSettingsScreenState extends State<NewProductsSettingsScreen> {
  late AuthSupabaseService _supabaseService;
  bool _isLoading = false;
  bool _isSaving = false;
  
  // إعدادات المنتجات الجديدة
  int _durationDays = 10;
  bool _autoCleanupEnabled = true;
  int _cleanupIntervalHours = 24;
  
  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    _supabaseService = Provider.of<AuthSupabaseService>(context, listen: false);
    _loadSettings();
  }

  /// تحميل الإعدادات الحالية
  Future<void> _loadSettings() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final response = await _supabaseService.client
          .from('new_product_settings')
          .select('setting_name, setting_value')
          .eq('is_active', true);

      for (final setting in response) {
        switch (setting['setting_name']) {
          case 'new_product_duration_days':
            _durationDays = setting['setting_value'] ?? 10;
            break;
          case 'auto_cleanup_enabled':
            _autoCleanupEnabled = (setting['setting_value'] ?? 1) == 1;
            break;
          case 'cleanup_interval_hours':
            _cleanupIntervalHours = setting['setting_value'] ?? 24;
            break;
        }
      }
    } catch (e) {
      _showErrorSnackBar('خطأ في تحميل الإعدادات: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// حفظ الإعدادات
  Future<void> _saveSettings() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isSaving = true;
    });

    try {
      // تحديث إعدادات المدة
      await _supabaseService.client.rpc('update_new_product_setting', params: {
        'p_setting_name': 'new_product_duration_days',
        'p_setting_value': _durationDays,
      });

      // تحديث إعدادات التنظيف التلقائي
      await _supabaseService.client.rpc('update_new_product_setting', params: {
        'p_setting_name': 'auto_cleanup_enabled',
        'p_setting_value': _autoCleanupEnabled ? 1 : 0,
      });

      // تحديث فترة التنظيف
      await _supabaseService.client.rpc('update_new_product_setting', params: {
        'p_setting_name': 'cleanup_interval_hours',
        'p_setting_value': _cleanupIntervalHours,
      });

      // إعادة إعداد الجدولة
      await _supabaseService.client.rpc('setup_new_products_scheduler');

      _showSuccessSnackBar('تم حفظ الإعدادات بنجاح');
    } catch (e) {
      _showErrorSnackBar('خطأ في حفظ الإعدادات: $e');
    } finally {
      setState(() {
        _isSaving = false;
      });
    }
  }

  /// تنظيف يدوي للمنتجات الجديدة
  Future<void> _manualCleanup() async {
    final confirmed = await _showConfirmDialog(
      'تنظيف المنتجات الجديدة',
      'هل تريد إزالة علامة "جديد" من جميع المنتجات التي انتهت مدتها؟',
    );

    if (!confirmed) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final response = await _supabaseService.client
          .rpc('update_new_products_status');

      final result = response[0];
      final updatedCount = result['updated_count'] ?? 0;

      _showSuccessSnackBar('تم تنظيف $updatedCount منتج بنجاح');
    } catch (e) {
      _showErrorSnackBar('خطأ في التنظيف: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إعدادات المنتجات الجديدة'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.cleaning_services),
            onPressed: _manualCleanup,
            tooltip: 'تنظيف يدوي',
          ),
        ],
      ),
      body: _isLoading
          ? const LoadingIndicator()
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildSectionHeader('إعدادات المدة'),
                    _buildDurationSettings(),
                    const SizedBox(height: 24.0),
                    
                    _buildSectionHeader('إعدادات التنظيف التلقائي'),
                    _buildCleanupSettings(),
                    const SizedBox(height: 32.0),
                    
                    _buildSaveButton(),
                  ],
                ),
              ),
            ),
    );
  }

  /// بناء رأس القسم
  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).primaryColor,
            ),
      ),
    );
  }

  /// بناء إعدادات المدة
  Widget _buildDurationSettings() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'مدة ظهور علامة "جديد"',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8.0),
            Text(
              'عدد الأيام التي تظهر فيها علامة "جديد" على المنتجات المضافة حديثاً',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                  ),
            ),
            const SizedBox(height: 16.0),
            TextFormField(
              initialValue: _durationDays.toString(),
              decoration: const InputDecoration(
                labelText: 'عدد الأيام',
                suffixText: 'يوم',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى إدخال عدد الأيام';
                }
                final days = int.tryParse(value);
                if (days == null || days < 1 || days > 365) {
                  return 'يجب أن يكون بين 1 و 365 يوم';
                }
                return null;
              },
              onChanged: (value) {
                final days = int.tryParse(value);
                if (days != null) {
                  setState(() {
                    _durationDays = days;
                  });
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  /// بناء إعدادات التنظيف
  Widget _buildCleanupSettings() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SwitchListTile(
              title: const Text('التنظيف التلقائي'),
              subtitle: const Text('إزالة علامة "جديد" تلقائياً بعد انتهاء المدة'),
              value: _autoCleanupEnabled,
              onChanged: (value) {
                setState(() {
                  _autoCleanupEnabled = value;
                });
              },
            ),
            if (_autoCleanupEnabled) ...[
              const Divider(),
              Text(
                'فترة التنظيف',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(height: 8.0),
              Text(
                'كم مرة يتم تشغيل التنظيف التلقائي',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
              ),
              const SizedBox(height: 16.0),
              DropdownButtonFormField<int>(
                value: _cleanupIntervalHours,
                decoration: const InputDecoration(
                  labelText: 'فترة التنظيف',
                  border: OutlineInputBorder(),
                ),
                items: const [
                  DropdownMenuItem(value: 1, child: Text('كل ساعة')),
                  DropdownMenuItem(value: 6, child: Text('كل 6 ساعات')),
                  DropdownMenuItem(value: 12, child: Text('كل 12 ساعة')),
                  DropdownMenuItem(value: 24, child: Text('يومياً')),
                  DropdownMenuItem(value: 168, child: Text('أسبوعياً')),
                ],
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _cleanupIntervalHours = value;
                    });
                  }
                },
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// بناء زر الحفظ
  Widget _buildSaveButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _isSaving ? null : _saveSettings,
        style: ElevatedButton.styleFrom(
          backgroundColor: Theme.of(context).primaryColor,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16.0),
        ),
        child: _isSaving
            ? const SizedBox(
                height: 20.0,
                width: 20.0,
                child: CircularProgressIndicator(
                  strokeWidth: 2.0,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : const Text('حفظ الإعدادات'),
      ),
    );
  }

  /// عرض رسالة نجاح
  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  /// عرض رسالة خطأ
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  /// عرض حوار تأكيد
  Future<bool> _showConfirmDialog(String title, String content) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(content),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('تأكيد'),
          ),
        ],
      ),
    );
    return result ?? false;
  }
}
