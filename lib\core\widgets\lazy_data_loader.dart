import 'package:flutter/material.dart';
import 'package:motorcycle_parts_shop/core/utils/common_operations.dart';

/// ويجت تحميل البيانات بشكل lazy مع إدارة الحالة
class LazyDataLoader<T> extends StatefulWidget {
  final Future<T> Function() dataLoader;
  final Widget Function(BuildContext context, T data) builder;
  final Widget Function(BuildContext context)? loadingBuilder;
  final Widget Function(BuildContext context, dynamic error)? errorBuilder;
  final Widget Function(BuildContext context)? emptyBuilder;
  final bool Function(T data)? isEmpty;
  final Duration cacheDuration;
  final String? cacheKey;
  final bool autoRefresh;
  final Duration refreshInterval;
  final bool enablePullToRefresh;
  final VoidCallback? onRefresh;

  const LazyDataLoader({
    super.key,
    required this.dataLoader,
    required this.builder,
    this.loadingBuilder,
    this.errorBuilder,
    this.emptyBuilder,
    this.isEmpty,
    this.cacheDuration = const Duration(minutes: 5),
    this.cacheKey,
    this.autoRefresh = false,
    this.refreshInterval = const Duration(minutes: 10),
    this.enablePullToRefresh = true,
    this.onRefresh,
  });

  @override
  State<LazyDataLoader<T>> createState() => _LazyDataLoaderState<T>();
}

class _LazyDataLoaderState<T> extends State<LazyDataLoader<T>>
    with AutomaticKeepAliveClientMixin {
  T? _data;
  bool _isLoading = false;
  dynamic _error;
  DateTime? _lastLoadTime;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _loadData();

    if (widget.autoRefresh) {
      _setupAutoRefresh();
    }
  }

  void _setupAutoRefresh() {
    Future.delayed(widget.refreshInterval, () {
      if (mounted) {
        _loadData();
        _setupAutoRefresh();
      }
    });
  }

  Future<void> _loadData() async {
    if (_isLoading) return;

    // التحقق من الكاش
    if (widget.cacheKey != null && _data != null && _lastLoadTime != null) {
      final timeSinceLastLoad = DateTime.now().difference(_lastLoadTime!);
      if (timeSinceLastLoad < widget.cacheDuration) {
        return;
      }
    }

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final result = await CommonOperations.executeWithCache(
        widget.cacheKey ?? 'lazy_data_${widget.hashCode}',
        widget.dataLoader,
        cacheDuration: widget.cacheDuration,
      );

      if (mounted) {
        setState(() {
          _data = result;
          _isLoading = false;
          _lastLoadTime = DateTime.now();
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = e;
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _refresh() async {
    // مسح الكاش للإجبار على إعادة التحميل
    if (widget.cacheKey != null) {
      CommonOperations.clearCache(widget.cacheKey);
    }

    await _loadData();

    if (widget.onRefresh != null) {
      widget.onRefresh!();
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    Widget content;

    if (_isLoading && _data == null) {
      content = widget.loadingBuilder?.call(context) ?? _buildDefaultLoading();
    } else if (_error != null && _data == null) {
      content =
          widget.errorBuilder?.call(context, _error) ?? _buildDefaultError();
    } else if (_data != null) {
      final data = _data as T;
      final isEmpty = widget.isEmpty?.call(data) ?? false;
      if (isEmpty) {
        content = widget.emptyBuilder?.call(context) ?? _buildDefaultEmpty();
      } else {
        content = widget.builder(context, data);
      }
    } else {
      content = widget.loadingBuilder?.call(context) ?? _buildDefaultLoading();
    }

    if (widget.enablePullToRefresh) {
      return RefreshIndicator(onRefresh: _refresh, child: content);
    }

    return content;
  }

  Widget _buildDefaultLoading() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text('جاري التحميل...'),
        ],
      ),
    );
  }

  Widget _buildDefaultError() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 64, color: Colors.red),
          const SizedBox(height: 16),
          Text('حدث خطأ: ${_error.toString()}'),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadData,
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  Widget _buildDefaultEmpty() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.inbox_outlined, size: 64, color: Colors.grey),
          SizedBox(height: 16),
          Text('لا توجد بيانات'),
        ],
      ),
    );
  }
}

/// ويجت تحميل قائمة بشكل lazy مع pagination
class LazyListLoader<T> extends StatefulWidget {
  final Future<List<T>> Function(int page, int limit) dataLoader;
  final Widget Function(BuildContext context, T item, int index) itemBuilder;
  final Widget Function(BuildContext context)? loadingBuilder;
  final Widget Function(BuildContext context, dynamic error)? errorBuilder;
  final Widget Function(BuildContext context)? emptyBuilder;
  final int pageSize;
  final bool enableInfiniteScroll;
  final ScrollController? scrollController;
  final EdgeInsets? padding;
  final Widget? separator;

  const LazyListLoader({
    super.key,
    required this.dataLoader,
    required this.itemBuilder,
    this.loadingBuilder,
    this.errorBuilder,
    this.emptyBuilder,
    this.pageSize = 20,
    this.enableInfiniteScroll = true,
    this.scrollController,
    this.padding,
    this.separator,
  });

  @override
  State<LazyListLoader<T>> createState() => _LazyListLoaderState<T>();
}

class _LazyListLoaderState<T> extends State<LazyListLoader<T>> {
  final List<T> _items = [];
  late ScrollController _scrollController;
  bool _isLoading = false;
  bool _isLoadingMore = false;
  bool _hasMoreData = true;
  int _currentPage = 0;
  dynamic _error;

  @override
  void initState() {
    super.initState();
    _scrollController = widget.scrollController ?? ScrollController();

    if (widget.enableInfiniteScroll) {
      _scrollController.addListener(_onScroll);
    }

    _loadFirstPage();
  }

  @override
  void dispose() {
    if (widget.scrollController == null) {
      _scrollController.dispose();
    }
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent * 0.8) {
      if (!_isLoadingMore && _hasMoreData) {
        _loadMoreData();
      }
    }
  }

  Future<void> _loadFirstPage() async {
    setState(() {
      _isLoading = true;
      _error = null;
      _currentPage = 0;
      _hasMoreData = true;
    });

    try {
      final newItems = await widget.dataLoader(0, widget.pageSize);

      if (mounted) {
        setState(() {
          _items.clear();
          _items.addAll(newItems);
          _isLoading = false;
          _hasMoreData = newItems.length >= widget.pageSize;
          _currentPage = 1;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = e;
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _loadMoreData() async {
    if (_isLoadingMore || !_hasMoreData) return;

    setState(() {
      _isLoadingMore = true;
    });

    try {
      final newItems = await widget.dataLoader(_currentPage, widget.pageSize);

      if (mounted) {
        setState(() {
          _items.addAll(newItems);
          _isLoadingMore = false;
          _hasMoreData = newItems.length >= widget.pageSize;
          _currentPage++;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingMore = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading && _items.isEmpty) {
      return widget.loadingBuilder?.call(context) ?? _buildDefaultLoading();
    }

    if (_error != null && _items.isEmpty) {
      return widget.errorBuilder?.call(context, _error) ?? _buildDefaultError();
    }

    if (_items.isEmpty) {
      return widget.emptyBuilder?.call(context) ?? _buildDefaultEmpty();
    }

    return RefreshIndicator(
      onRefresh: _loadFirstPage,
      child: ListView.separated(
        controller: _scrollController,
        padding: widget.padding,
        itemCount: _items.length + (_isLoadingMore ? 1 : 0),
        separatorBuilder: (context, index) {
          if (index == _items.length - 1 && _isLoadingMore) {
            return const SizedBox.shrink();
          }
          return widget.separator ?? const SizedBox(height: 8);
        },
        itemBuilder: (context, index) {
          if (index == _items.length) {
            return _buildLoadingMoreIndicator();
          }

          return widget.itemBuilder(context, _items[index], index);
        },
      ),
    );
  }

  Widget _buildLoadingMoreIndicator() {
    return Container(
      padding: const EdgeInsets.all(16),
      alignment: Alignment.center,
      child: const CircularProgressIndicator(),
    );
  }

  Widget _buildDefaultLoading() {
    return const Center(child: CircularProgressIndicator());
  }

  Widget _buildDefaultError() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 64, color: Colors.red),
          const SizedBox(height: 16),
          Text('حدث خطأ: ${_error.toString()}'),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadFirstPage,
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  Widget _buildDefaultEmpty() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.inbox_outlined, size: 64, color: Colors.grey),
          SizedBox(height: 16),
          Text('لا توجد عناصر'),
        ],
      ),
    );
  }
}
