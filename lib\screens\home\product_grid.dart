import '../../core/services/product_service.dart';
import '../../models/product_model.dart';
import 'package:flutter/material.dart';
import 'package:motorcycle_parts_shop/core/constants/app_constants.dart';
import 'package:motorcycle_parts_shop/core/theme/app_theme.dart';
import 'package:motorcycle_parts_shop/screens/product/product_details_screen.dart';

/// شبكة المنتجات مع دعم التحميل الكسول
/// تعرض المنتجات في شبكة وتحمل المزيد عند التمرير للأسفل
class ProductGrid extends StatefulWidget {
  final String? categoryId;
  final String title;
  final ProductService productService;

  const ProductGrid({
    super.key,
    required this.title,
    required this.productService,
    this.categoryId,
  });

  @override
  State<ProductGrid> createState() => _ProductGridState();
}

class _ProductGridState extends State<ProductGrid> {
  final List<ProductModel> _products = [];
  bool _isLoading = false;
  bool _hasMore = true;
  int _currentPage = 0;
  final int _pageSize = ProductService.defaultPageSize;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _loadProducts();

    // إضافة مستمع للتمرير لتحميل المزيد من المنتجات عند الوصول للنهاية
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >=
          _scrollController.position.maxScrollExtent * 0.8) {
        if (!_isLoading && _hasMore) {
          _loadMoreProducts();
        }
      }
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  /// تحميل المنتجات الأولية
  Future<void> _loadProducts() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final products = await widget.productService.getProducts(
        page: 0,
        category: widget.categoryId,
      );

      setState(() {
        _products.clear();
        _products.addAll(products);
        _currentPage = 0;
        _hasMore = products.length >= _pageSize;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showError('حدث خطأ أثناء تحميل المنتجات');
    }
  }

  /// تحميل المزيد من المنتجات عند التمرير
  Future<void> _loadMoreProducts() async {
    if (_isLoading || !_hasMore) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final nextPage = _currentPage + 1;
      final moreProducts = await widget.productService.getProducts(
        page: nextPage,
        category: widget.categoryId,
      );

      setState(() {
        if (moreProducts.isNotEmpty) {
          _products.addAll(moreProducts);
          _currentPage = nextPage;
        }
        _hasMore = moreProducts.length >= _pageSize;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showError('حدث خطأ أثناء تحميل المزيد من المنتجات');
    }
  }

  /// تبديل حالة المنتج المفضل
  Future<void> _toggleFavorite(ProductModel product) async {
    // التأكد من أن product.id ليس null قبل استخدامه
    if (product.id == null) {
      debugPrint('خطأ: معرف المنتج فارغ، لا يمكن تبديل حالة التفضيل.');
      _showError('خطأ: معرف المنتج فارغ.');
      return;
    }

    // حفظ الحالة الحالية للتفضيل لاستخدامها في حالة حدوث خطأ
    final bool originalFavoriteState = product.isFavorite ?? false;

    // تحديث الواجهة فورًا لتجربة مستخدم أفضل
    setState(() {
      product.isFavorite = !originalFavoriteState;
    });

    try {
      final newFavoriteState = await widget.productService.toggleFavoriteStatus(
        product.id,
        originalFavoriteState, // إرسال الحالة الأصلية إلى الخدمة
      );

      // التأكد من أن الحالة المرجعة من الخدمة هي نفسها التي تم تحديث الواجهة بها
      // إذا لم تكن كذلك، فهذا يعني أن هناك خطأ ما أو أن الحالة تغيرت من مكان آخر
      if (product.isFavorite != newFavoriteState) {
        setState(() {
          product.isFavorite = newFavoriteState;
        });
      }

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            newFavoriteState
                ? 'تمت إضافة المنتج إلى المفضلة بنجاح'
                : 'تمت إزالة المنتج من المفضلة بنجاح',
          ),
          duration: const Duration(seconds: 2),
        ),
      );
    } catch (e) {
      // في حالة حدوث خطأ، أرجع الحالة إلى ما كانت عليه قبل التغيير
      setState(() {
        product.isFavorite = originalFavoriteState;
      });
      debugPrint('خطأ أثناء تبديل حالة التفضيل: $e');
      _showError('حدث خطأ أثناء تحديث حالة التفضيل.');
    }
  }

  /// عرض رسالة خطأ
  void _showError(String message) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text(message)));
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
          child: Text(
            widget.title,
            style: Theme.of(context).textTheme.titleLarge,
          ),
        ),
        Expanded(
          child:
              _products.isEmpty && !_isLoading
                  ? _buildEmptyView()
                  : _buildProductGrid(),
        ),
      ],
    );
  }

  /// بناء عرض فارغ عندما لا توجد منتجات
  Widget _buildEmptyView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.shopping_bag_outlined, size: 64, color: Colors.grey),
          const SizedBox(height: 16),
          Text(
            'لا توجد منتجات متاحة',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(color: Colors.grey),
          ),
          const SizedBox(height: 8),
          ElevatedButton(
            onPressed: _loadProducts,
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  /// بناء شبكة المنتجات
  Widget _buildProductGrid() {
    return RefreshIndicator(
      onRefresh: _loadProducts,
      child: GridView.builder(
        controller: _scrollController,
        padding: const EdgeInsets.all(8.0),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: 0.7,
          crossAxisSpacing: 10,
          mainAxisSpacing: 10,
        ),
        itemCount: _products.length + (_hasMore ? 1 : 0),
        itemBuilder: (context, index) {
          if (index >= _products.length) {
            return _buildLoadingIndicator();
          }
          return _buildProductCard(_products[index]);
        },
      ),
    );
  }

  /// بناء مؤشر التحميل
  Widget _buildLoadingIndicator() {
    return const Center(child: CircularProgressIndicator());
  }

  /// بناء بطاقة المنتج
  Widget _buildProductCard(ProductModel product) {
    return Card(
      elevation: AppTheme.cardElevation,
      shape: RoundedRectangleBorder(borderRadius: AppTheme.borderRadiusLarge),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => ProductDetailsScreen(product: product),
            ),
          );
        },
        borderRadius: AppTheme.borderRadiusLarge,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: Hero(
                tag: 'product_image_${product.id}',
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.vertical(
                      top: Radius.circular(
                        AppTheme.borderRadiusLarge.topLeft.x,
                      ),
                    ),
                    image: DecorationImage(
                      image: NetworkImage(product.images.first),
                      fit: BoxFit.cover,
                      colorFilter: ColorFilter.mode(
                        Colors.black.withOpacity(0.1),
                        BlendMode.darken,
                      ),
                    ),
                  ),
                  child: Stack(
                    children: [
                      Positioned(
                        top: 8,
                        right: 8,
                        child: IconButton(
                          icon: Icon(
                            product.isFavorite ?? false
                                ? Icons.favorite_rounded
                                : Icons.favorite_border_rounded,
                            color:
                                product.isFavorite ?? false
                                    ? Theme.of(context).colorScheme.error
                                    : Colors.white,
                          ),
                          onPressed: () {
                            _toggleFavorite(product);
                          },
                          splashRadius: 20,
                        ),
                      ),
                      if (product.discountPercentage != null &&
                          product.discountPercentage! > 0)
                        Positioned(
                          top: 8,
                          left: 8,
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: Theme.of(context).colorScheme.error,
                              borderRadius: AppTheme.borderRadiusSmall,
                            ),
                            child: Text(
                              '-${product.discountPercentage!.toStringAsFixed(0)}%',
                              style: Theme.of(context).textTheme.labelSmall
                                  ?.copyWith(color: Colors.white),
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(12.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    product.name,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '${product.price.toStringAsFixed(2)} ${AppConstants.currencySymbol}',
                        style: Theme.of(context).textTheme.titleSmall?.copyWith(
                          color: Theme.of(context).primaryColor,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      if (product.originalPrice != null &&
                          product.originalPrice! > product.price)
                        Text(
                          '${product.originalPrice!.toStringAsFixed(2)} ${AppConstants.currencySymbol}',
                          style: Theme.of(
                            context,
                          ).textTheme.bodySmall?.copyWith(
                            decoration: TextDecoration.lineThrough,
                            color: Theme.of(context).hintColor,
                          ),
                        ),
                    ],
                  ),
                  const SizedBox(height: 6),
                  Row(
                    children: [
                      const Icon(
                        Icons.star_rounded,
                        color: Colors.amber,
                        size: 18,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        product.rating.toStringAsFixed(1),
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                      const SizedBox(width: 2),
                      Text(
                        '(${product.reviewsCount})',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).hintColor,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
