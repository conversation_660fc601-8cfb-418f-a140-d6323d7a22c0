import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:motorcycle_parts_shop/core/constants/egypt_governorates.dart';
import 'package:motorcycle_parts_shop/core/services/auth_supabase_service.dart';
import 'package:motorcycle_parts_shop/core/theme/app_theme.dart';
import 'package:motorcycle_parts_shop/core/utils/responsive_helper.dart';
import 'package:motorcycle_parts_shop/core/widgets/animation_helper_wrapper.dart';
import 'package:motorcycle_parts_shop/core/widgets/common_form_fields.dart';
import 'package:motorcycle_parts_shop/features/auth/widgets/governorate_center_selector.dart';
import 'package:motorcycle_parts_shop/screens/auth/verify_email_screen.dart';
import 'package:provider/provider.dart';

/// شاشة التسجيل لإنشاء حساب جديد
class RegisterScreen extends StatefulWidget {
  const RegisterScreen({super.key});

  @override
  State<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends State<RegisterScreen> {
  // متحكمات حقول النموذج
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _addressController = TextEditingController();
  final _phoneController = TextEditingController();

  // متغيرات الحالة
  DateTime? _selectedBirthDate;
  String? _selectedGovernorate;
  String? _selectedCenter;
  bool _isPasswordVisible = false;
  bool _isConfirmPasswordVisible = false;
  bool _isLoading = false;

  late AuthSupabaseService _authService;

  @override
  void initState() {
    super.initState();
    _initializeAuthService();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _addressController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  Future<void> _initializeAuthService() async {
    try {
      _authService = Provider.of<AuthSupabaseService>(context, listen: false);
      await _authService.initialize();
    } catch (e) {
      debugPrint('خطأ في تهيئة خدمة المصادقة: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
              'فشل في تهيئة خدمة المصادقة، يرجى إعادة تشغيل التطبيق',
            ),
          ),
        );
      }
    }
  }

  /// دالة تسجيل المستخدم
  Future<void> _register() async {
    // التحقق من الاتصال بالإنترنت
    final List<ConnectivityResult> connectivityResults =
        await Connectivity().checkConnectivity();
    final connectivityResult =
        connectivityResults.isEmpty
            ? ConnectivityResult.none
            : connectivityResults.first;
    if (connectivityResult == ConnectivityResult.none) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('لا يوجد اتصال بالإنترنت')),
        );
      }
      return;
    }

    // التحقق من صحة النموذج
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // التحقق من تطابق كلمة المرور قبل إرسال الطلب
    if (_passwordController.text != _confirmPasswordController.text) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('كلمة المرور غير متطابقة')),
        );
      }
      return;
    }

    setState(() => _isLoading = true);

    try {
      // التحقق من تهيئة خدمة المصادقة
      if (!_authService.isInitialized) {
        throw Exception('خدمة المصادقة غير مهيأة، يرجى إعادة تشغيل التطبيق');
      }

      await _verifyEmailUniqueness(_authService);
      await _sendVerificationEmail(_authService);
    } catch (e) {
      _handleRegistrationError(e);
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }

  /// التحقق من عدم وجود البريد الإلكتروني مسبقًا
  Future<void> _verifyEmailUniqueness(AuthSupabaseService service) async {
    try {
      final email = _emailController.text.trim();

      // التحقق من صحة تنسيق البريد الإلكتروني
      if (!RegExp(
        r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
      ).hasMatch(email)) {
        throw Exception('تنسيق البريد الإلكتروني غير صالح');
      }

      final emailExists = await service.checkEmailExists(email);
      if (emailExists && mounted) {
        throw Exception('البريد الإلكتروني مسجل بالفعل');
      }
    } catch (e) {
      debugPrint('خطأ في التحقق من البريد الإلكتروني: $e');
      rethrow;
    }
  }

  /// إرسال بريد التحقق
  Future<void> _sendVerificationEmail(AuthSupabaseService service) async {
    try {
      final email = _emailController.text.trim();

      // التحقق من صحة تنسيق البريد الإلكتروني مرة أخرى
      if (!RegExp(
        r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
      ).hasMatch(email)) {
        throw Exception('تنسيق البريد الإلكتروني غير صالح');
      }

      // تمرير معلمة isRegistration=true لإخبار الخدمة أن هذا تسجيل جديد
      final otpSent = await service.sendEmailOtp(email, isRegistration: true);

      if (!otpSent) {
        throw Exception('فشل في إرسال رمز التحقق');
      }

      if (!mounted) return;

      // إعلام المستخدم بنجاح إرسال OTP
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('تم إرسال رمز التحقق بنجاح')),
      );

      Navigator.push(
        context,
        MaterialPageRoute(
          builder:
              (context) => VerifyEmailScreen(
                email: email,
                password: _passwordController.text,
                name: _nameController.text.trim(),
                address: _addressController.text.trim(),
                birthDate: _selectedBirthDate,
                phone: _phoneController.text.trim(),
                governorate: _selectedGovernorate,
                center: _selectedCenter,
              ),
        ),
      );
    } catch (e) {
      debugPrint('خطأ في إرسال رمز التحقق: $e');
      rethrow;
    }
  }

  /// معالجة أخطاء التسجيل
  void _handleRegistrationError(dynamic error) {
    if (!mounted) return;

    final errorMessage = _getErrorMessage(error);
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text(errorMessage)));
    debugPrint('خطأ في التسجيل: $error');
  }

  /// تحويل الخطأ إلى رسالة مفهومة للمستخدم
  String _getErrorMessage(dynamic error) {
    final errorStr = error.toString();
    if (errorStr.contains('permission denied')) {
      return 'فشل التسجيل: ليس لديك الصلاحية الكافية';
    } else if (errorStr.contains('already registered') ||
        errorStr.contains('البريد الإلكتروني مسجل بالفعل')) {
      return 'البريد الإلكتروني مسجل بالفعل';
    } else if (errorStr.contains('invalid phone')) {
      return 'رقم الهاتف غير صالح';
    } else if (errorStr.contains('فشل في إرسال رمز التحقق')) {
      return 'فشل في إرسال رمز التحقق، تحقق من إعدادات البريد أو حاول لاحقًا';
    } else if (errorStr.contains('network')) {
      return 'فشل الاتصال بالخادم، تحقق من الإنترنت';
    } else if (errorStr.contains('خدمة المصادقة غير مهيأة')) {
      return 'فشل في تهيئة خدمة المصادقة، يرجى المحاولة مرة أخرى';
    } else {
      return 'حدث خطأ أثناء التسجيل: ${errorStr.split('Exception: ').last}';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppTheme.primaryColor.withOpacity(0.8),
              AppTheme.primaryColor.withOpacity(0.6),
              Colors.white,
            ],
            stops: [0.0, 0.3, 1.0],
          ),
        ),
        child: SafeArea(
          child: ResponsiveBuilder(
            builder: (context, constraints) {
              final padding = ResponsiveHelper.getPadding(context);
              final verticalPadding =
                  ResponsiveHelper.isMobile(context) ? 16.0 : 20.0;

              return SingleChildScrollView(
                child: Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: padding,
                    vertical: verticalPadding,
                  ),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        SizedBox(
                          height: ResponsiveHelper.isMobile(context) ? 30 : 40,
                        ),
                        // Enhanced Header Section
                        AnimationHelper.slideTransition(
                          animation: CurvedAnimation(
                            parent: ModalRoute.of(context)!.animation!,
                            curve: Interval(0.0, 0.6, curve: Curves.easeOut),
                          ),
                          child: AnimationHelper.fadeTransition(
                            animation: CurvedAnimation(
                              parent: ModalRoute.of(context)!.animation!,
                              curve: Interval(0.0, 0.6, curve: Curves.easeOut),
                            ),
                            child: Column(
                              children: [
                                Container(
                                  height:
                                      ResponsiveHelper.isMobile(context)
                                          ? 100
                                          : 120,
                                  width:
                                      ResponsiveHelper.isMobile(context)
                                          ? 100
                                          : 120,
                                  decoration: BoxDecoration(
                                    gradient: LinearGradient(
                                      begin: Alignment.topLeft,
                                      end: Alignment.bottomRight,
                                      colors: [
                                        Colors.white,
                                        Colors.white.withOpacity(0.9),
                                      ],
                                    ),
                                    shape: BoxShape.circle,
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withOpacity(0.1),
                                        blurRadius: 30,
                                        spreadRadius: 5,
                                        offset: Offset(0, 10),
                                      ),
                                      BoxShadow(
                                        color: AppTheme.primaryColor
                                            .withOpacity(0.3),
                                        blurRadius: 20,
                                        spreadRadius: 2,
                                      ),
                                    ],
                                  ),
                                  child: Icon(
                                    Icons.person_add_alt_1,
                                    size:
                                        ResponsiveHelper.isMobile(context)
                                            ? 50
                                            : 60,
                                    color: AppTheme.primaryColor,
                                  ),
                                ),
                                SizedBox(
                                  height:
                                      ResponsiveHelper.isMobile(context)
                                          ? 20
                                          : 24,
                                ),
                                Text(
                                  'إنشاء حساب جديد',
                                  style: Theme.of(
                                    context,
                                  ).textTheme.headlineLarge?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white,
                                    fontSize: ResponsiveHelper.getFontSize(
                                      context,
                                      ResponsiveHelper.isMobile(context)
                                          ? 24
                                          : 28,
                                    ),
                                    shadows: [
                                      Shadow(
                                        color: Colors.black.withOpacity(0.3),
                                        offset: Offset(0, 2),
                                        blurRadius: 4,
                                      ),
                                    ],
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  'انضم إلينا واستمتع بتجربة تسوق رائعة',
                                  style: Theme.of(
                                    context,
                                  ).textTheme.bodyLarge?.copyWith(
                                    color: Colors.white.withOpacity(0.9),
                                    fontSize: 16,
                                    shadows: [
                                      Shadow(
                                        color: Colors.black.withOpacity(0.2),
                                        offset: Offset(0, 1),
                                        blurRadius: 2,
                                      ),
                                    ],
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(height: 40),
                        // Form Container
                        Container(
                          padding: const EdgeInsets.all(24.0),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(20),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.1),
                                blurRadius: 20,
                                spreadRadius: 5,
                                offset: Offset(0, 10),
                              ),
                            ],
                          ),
                          child: Column(
                            children: [
                              _buildNameField(),
                              const SizedBox(height: 16),
                              _buildEmailField(),
                              const SizedBox(height: 16),
                              _buildPasswordField(),
                              const SizedBox(height: 16),
                              _buildConfirmPasswordField(),
                              const SizedBox(height: 16),
                              _buildGovernorateCenterSelector(),
                              const SizedBox(height: 16),
                              _buildAddressField(),
                              const SizedBox(height: 16),
                              _buildBirthDateField(),
                              const SizedBox(height: 16),
                              _buildPhoneField(),
                              const SizedBox(height: 32),
                              _buildRegisterButton(),
                            ],
                          ),
                        ),
                        const SizedBox(height: 24),
                        _buildLoginLink(),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  /// حقول النموذج كدوال منفصلة لتحسين التنظيم
  Widget _buildNameField() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: AppTheme.primaryColor.withOpacity(0.1),
            blurRadius: 10,
            offset: Offset(0, 5),
          ),
        ],
      ),
      child: TextFormField(
        controller: _nameController,
        decoration: InputDecoration(
          labelText: 'الاسم الكامل',
          labelStyle: TextStyle(
            color: AppTheme.primaryColor,
            fontWeight: FontWeight.w500,
          ),
          prefixIcon: Container(
            margin: EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(Icons.person_outline, color: AppTheme.primaryColor),
          ),
          filled: true,
          fillColor: Colors.grey[50],
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(15),
            borderSide: BorderSide.none,
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(15),
            borderSide: BorderSide(
              color: Colors.grey.withOpacity(0.3),
              width: 1,
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(15),
            borderSide: BorderSide(color: AppTheme.primaryColor, width: 2),
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(15),
            borderSide: BorderSide(color: Colors.red, width: 1),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(15),
            borderSide: BorderSide(color: Colors.red, width: 2),
          ),
        ),
        textInputAction: TextInputAction.next,
        onFieldSubmitted: (_) => FocusScope.of(context).nextFocus(),
        validator:
            (value) => value?.isEmpty ?? true ? 'الرجاء إدخال الاسم' : null,
      ),
    );
  }

  Widget _buildEmailField() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: AppTheme.primaryColor.withOpacity(0.1),
            blurRadius: 10,
            offset: Offset(0, 5),
          ),
        ],
      ),
      child: TextFormField(
        controller: _emailController,
        keyboardType: TextInputType.emailAddress,
        textDirection: TextDirection.ltr,
        decoration: InputDecoration(
          labelText: 'البريد الإلكتروني',
          labelStyle: TextStyle(
            color: AppTheme.primaryColor,
            fontWeight: FontWeight.w500,
          ),
          prefixIcon: Container(
            margin: EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(Icons.email_outlined, color: AppTheme.primaryColor),
          ),
          filled: true,
          fillColor: Colors.grey[50],
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(15),
            borderSide: BorderSide.none,
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(15),
            borderSide: BorderSide(
              color: Colors.grey.withOpacity(0.3),
              width: 1,
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(15),
            borderSide: BorderSide(color: AppTheme.primaryColor, width: 2),
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(15),
            borderSide: BorderSide(color: Colors.red, width: 1),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(15),
            borderSide: BorderSide(color: Colors.red, width: 2),
          ),
        ),
        textInputAction: TextInputAction.next,
        onFieldSubmitted: (_) => FocusScope.of(context).nextFocus(),
        validator: (value) {
          if (value?.isEmpty ?? true) return 'الرجاء إدخال البريد الإلكتروني';
          if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value!)) {
            return 'البريد الإلكتروني غير صحيح';
          }
          return null;
        },
      ),
    );
  }

  Widget _buildPasswordField() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: AppTheme.primaryColor.withOpacity(0.1),
            blurRadius: 10,
            offset: Offset(0, 5),
          ),
        ],
      ),
      child: TextFormField(
        controller: _passwordController,
        obscureText: !_isPasswordVisible,
        textDirection: TextDirection.ltr,
        decoration: InputDecoration(
          labelText: 'كلمة المرور',
          labelStyle: TextStyle(
            color: AppTheme.primaryColor,
            fontWeight: FontWeight.w500,
          ),
          prefixIcon: Container(
            margin: EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(Icons.lock_outline, color: AppTheme.primaryColor),
          ),
          suffixIcon: Container(
            margin: EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(10),
            ),
            child: IconButton(
              icon: Icon(
                _isPasswordVisible ? Icons.visibility_off : Icons.visibility,
                color: AppTheme.primaryColor,
              ),
              onPressed:
                  () =>
                      setState(() => _isPasswordVisible = !_isPasswordVisible),
            ),
          ),
          filled: true,
          fillColor: Colors.grey[50],
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(15),
            borderSide: BorderSide.none,
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(15),
            borderSide: BorderSide(
              color: Colors.grey.withOpacity(0.3),
              width: 1,
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(15),
            borderSide: BorderSide(color: AppTheme.primaryColor, width: 2),
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(15),
            borderSide: BorderSide(color: Colors.red, width: 1),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(15),
            borderSide: BorderSide(color: Colors.red, width: 2),
          ),
        ),
        textInputAction: TextInputAction.next,
        onFieldSubmitted: (_) => FocusScope.of(context).nextFocus(),
        validator: (value) {
          if (value?.isEmpty ?? true) return 'الرجاء إدخال كلمة المرور';
          if (value!.length < 6) {
            return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
          }
          return null;
        },
      ),
    );
  }

  Widget _buildConfirmPasswordField() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: AppTheme.primaryColor.withOpacity(0.1),
            blurRadius: 10,
            offset: Offset(0, 5),
          ),
        ],
      ),
      child: TextFormField(
        controller: _confirmPasswordController,
        obscureText: !_isConfirmPasswordVisible,
        textDirection: TextDirection.ltr,
        decoration: InputDecoration(
          labelText: 'تأكيد كلمة المرور',
          labelStyle: TextStyle(
            color: AppTheme.primaryColor,
            fontWeight: FontWeight.w500,
          ),
          prefixIcon: Container(
            margin: EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(Icons.lock_outline, color: AppTheme.primaryColor),
          ),
          suffixIcon: Container(
            margin: EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(10),
            ),
            child: IconButton(
              icon: Icon(
                _isConfirmPasswordVisible
                    ? Icons.visibility_off
                    : Icons.visibility,
                color: AppTheme.primaryColor,
              ),
              onPressed:
                  () => setState(
                    () =>
                        _isConfirmPasswordVisible = !_isConfirmPasswordVisible,
                  ),
            ),
          ),
          filled: true,
          fillColor: Colors.grey[50],
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(15),
            borderSide: BorderSide.none,
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(15),
            borderSide: BorderSide(
              color: Colors.grey.withOpacity(0.3),
              width: 1,
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(15),
            borderSide: BorderSide(color: AppTheme.primaryColor, width: 2),
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(15),
            borderSide: BorderSide(color: Colors.red, width: 1),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(15),
            borderSide: BorderSide(color: Colors.red, width: 2),
          ),
        ),
        textInputAction: TextInputAction.next,
        onFieldSubmitted: (_) => FocusScope.of(context).nextFocus(),
        validator: (value) {
          if (value?.isEmpty ?? true) return 'الرجاء تأكيد كلمة المرور';
          if (value != _passwordController.text) {
            return 'كلمة المرور غير متطابقة';
          }
          return null;
        },
      ),
    );
  }

  Widget _buildGovernorateCenterSelector() {
    return AnimationHelper.fadeTransition(
      animation: CurvedAnimation(
        parent: ModalRoute.of(context)!.animation!,
        curve: Interval(0.5, 1.0, curve: Curves.easeOut),
      ),
      child: Card(
        elevation: 2,
        shadowColor: AppTheme.shadowColor,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: GovernorateCenterSelector(
            selectedGovernorate: _selectedGovernorate,
            selectedCenter: _selectedCenter,
            onGovernorateChanged: (governorate) {
              setState(() {
                _selectedGovernorate = governorate;
              });
            },
            onCenterChanged: (center) {
              setState(() {
                _selectedCenter = center;
              });
            },
            governorateLabel: 'المحافظة',
            centerLabel: 'المركز/المدينة',
            governorateHint: 'اختر المحافظة',
            centerHint: 'اختر المركز أو المدينة',
            isRequired: true,
          ),
        ),
      ),
    );
  }

  Widget _buildAddressField() {
    return AnimationHelper.fadeTransition(
      animation: CurvedAnimation(
        parent: ModalRoute.of(context)!.animation!,
        curve: Interval(0.6, 1.0, curve: Curves.easeOut),
      ),
      child: Card(
        elevation: 2,
        shadowColor: AppTheme.shadowColor,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
          child: TextFormField(
            controller: _addressController,
            decoration: InputDecoration(
              labelText: 'العنوان',
              prefixIcon: Icon(
                Icons.home_outlined,
                color: AppTheme.primaryColor,
              ),
              border: InputBorder.none,
            ),
            textInputAction: TextInputAction.next,
            onFieldSubmitted: (_) => FocusScope.of(context).nextFocus(),
            validator:
                (value) =>
                    value?.isEmpty ?? true ? 'الرجاء إدخال العنوان' : null,
          ),
        ),
      ),
    );
  }

  Widget _buildBirthDateField() {
    return AnimationHelper.fadeTransition(
      animation: CurvedAnimation(
        parent: ModalRoute.of(context)!.animation!,
        curve: Interval(0.7, 1.0, curve: Curves.easeOut),
      ),
      child: Card(
        elevation: 2,
        shadowColor: AppTheme.shadowColor,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () async {
            final DateTime? pickedDate = await showDatePicker(
              context: context,
              initialDate: _selectedBirthDate ?? DateTime.now(),
              firstDate: DateTime(1920),
              lastDate: DateTime.now(),
              locale: const Locale('ar'),
              builder: (context, child) {
                return Theme(
                  data: Theme.of(context).copyWith(
                    colorScheme: ColorScheme.light(
                      primary: AppTheme.primaryColor,
                      onPrimary: Colors.white,
                      surface: Colors.white,
                      onSurface: AppTheme.textPrimaryColor,
                    ),
                  ),
                  child: child!,
                );
              },
            );
            if (pickedDate != null && pickedDate != _selectedBirthDate) {
              setState(() {
                _selectedBirthDate = pickedDate;
              });
            }
          },
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
            child: InputDecorator(
              decoration: InputDecoration(
                labelText: 'تاريخ الميلاد',
                prefixIcon: Icon(
                  Icons.calendar_today_outlined,
                  color: AppTheme.primaryColor,
                ),
                border: InputBorder.none,
              ),
              child: Text(
                _selectedBirthDate == null
                    ? 'اختر تاريخ الميلاد'
                    : '${_selectedBirthDate!.day}/${_selectedBirthDate!.month}/${_selectedBirthDate!.year}',
                style: TextStyle(color: AppTheme.textPrimaryColor),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPhoneField() {
    return AnimationHelper.fadeTransition(
      animation: CurvedAnimation(
        parent: ModalRoute.of(context)!.animation!,
        curve: Interval(0.8, 1.0, curve: Curves.easeOut),
      ),
      child: Card(
        elevation: 2,
        shadowColor: AppTheme.shadowColor,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
          child: CommonFormFields.buildPhoneField(
            controller: _phoneController,
            onFieldSubmitted: (_) => _register(),
          ),
        ),
      ),
    );
  }

  Widget _buildRegisterButton() {
    return Container(
      height: 56,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppTheme.primaryColor,
            AppTheme.primaryColor.withOpacity(0.8),
          ],
        ),
        boxShadow: [
          BoxShadow(
            color: AppTheme.primaryColor.withOpacity(0.4),
            blurRadius: 15,
            offset: Offset(0, 8),
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: ElevatedButton(
        onPressed: _isLoading ? null : _register,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
        ),
        child:
            _isLoading
                ? Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        strokeWidth: 2.0,
                      ),
                    ),
                    SizedBox(width: 12),
                    Text(
                      'جاري إنشاء الحساب...',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ],
                )
                : Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.person_add, color: Colors.white, size: 20),
                    SizedBox(width: 8),
                    Text(
                      'إنشاء حساب جديد',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
      ),
    );
  }

  Widget _buildLoginLink() {
    return Container(
      margin: EdgeInsets.only(top: 16),
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.9),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          color: AppTheme.primaryColor.withOpacity(0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: Offset(0, 5),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.login, color: AppTheme.primaryColor, size: 20),
          SizedBox(width: 8),
          Text(
            'لديك حساب بالفعل؟',
            style: TextStyle(color: Colors.grey[700], fontSize: 16),
          ),
          SizedBox(width: 4),
          GestureDetector(
            onTap: () => Navigator.of(context).pop(),
            child: Text(
              'تسجيل الدخول',
              style: TextStyle(
                color: AppTheme.primaryColor,
                fontSize: 16,
                fontWeight: FontWeight.bold,
                decoration: TextDecoration.underline,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
