import 'dart:io';
import 'package:cloudinary/cloudinary.dart';
import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:motorcycle_parts_shop/core/constants/app_constants.dart';
import 'package:motorcycle_parts_shop/core/services/auth_supabase_service.dart';
import 'package:path/path.dart' as path;

/// 🚀 خدمة Cloudinary الاحترافية المتقدمة
///
/// خدمة شاملة ومتطورة لإدارة الصور والفيديوهات مع Cloudinary
/// تدعم جميع الميزات المتقدمة والتحسينات الاحترافية
///
/// 🎯 الميزات الرئيسية:
/// - 📤 رفع متقدم مع إعادة المحاولة والتقدم
/// - 🎨 تحويلات ذكية وتحسين تلقائي
/// - 🗂️ إدارة مجلدات ومجموعات منظمة
/// - 💾 تخزين مؤقت ذكي ومتقدم
/// - 🔍 بحث وتصفية متقدم
/// - 📊 تحليلات وإحصائيات مفصلة
/// - 🛡️ أمان وحماية متقدمة
/// - ⚡ أداء محسن ومعالجة متوازية
/// - 🎬 دعم الفيديوهات والملفات المتعددة
/// - 🌐 CDN عالمي وتحسين التوصيل
class CloudinaryService extends ChangeNotifier {
  static final CloudinaryService _instance = CloudinaryService._internal();
  Cloudinary? _cloudinary;
  bool _isInitialized = false;

  // 💾 التخزين المؤقت المتقدم للصور والتحويلات
  final Map<String, Map<String, dynamic>> _transformationCache = {};
  final Map<String, DateTime> _uploadHistory = {};
  final Map<String, int> _uploadRetryCount = {};
  static const Duration _cacheExpiryDuration = Duration(hours: 6);

  // ⚙️ إعدادات متقدمة قابلة للتخصيص (يتم جلبها من قاعدة البيانات)
  int _maxFileSizeMB = 25; // الحد الأقصى لحجم الملف (ميجابايت)
  List<String> _allowedImageExtensions = [];
  List<String> _allowedVideoExtensions = [];
  static const int _maxRetries = 5; // عدد محاولات إعادة الرفع
  static const Duration _defaultTimeout = Duration(seconds: 45);
  static const Duration _uploadTimeout = Duration(minutes: 2);

  // 📊 إحصائيات الأداء
  int _totalUploads = 0;
  int _successfulUploads = 0;
  int _failedUploads = 0;
  double _averageUploadTime = 0.0;

  factory CloudinaryService() => _instance;

  CloudinaryService._internal();

  // 📊 Getters للإحصائيات والحالة
  bool get isInitialized => _isInitialized;
  Cloudinary? get cloudinary => _cloudinary;
  String get cloudName => _cloudinary?.cloudName ?? '';
  int get totalUploads => _totalUploads;
  int get successfulUploads => _successfulUploads;
  int get failedUploads => _failedUploads;
  double get averageUploadTime => _averageUploadTime;
  double get successRate =>
      _totalUploads > 0 ? (_successfulUploads / _totalUploads) * 100 : 0.0;

  // 🎯 Getters للإعدادات
  List<String> get allowedImageExtensions => _allowedImageExtensions;
  List<String> get allowedVideoExtensions => _allowedVideoExtensions;
  int get maxFileSizeMB => _maxFileSizeMB;

  /// تحميل الإعدادات من قاعدة البيانات
  Future<void> _loadSettingsFromDatabase() async {
    try {
      // محاولة جلب الإعدادات من قاعدة البيانات
      final supabaseService = AuthSupabaseService();
      if (!supabaseService.isInitialized) {
        await supabaseService.initialize();
      }

      final response = await supabaseService.client
          .from('cloudinary_settings')
          .select('setting_key, setting_value')
          .eq('is_active', true);

      // تطبيق الإعدادات المجلبة
      for (final setting in response) {
        final key = setting['setting_key'] as String;
        final value = setting['setting_value'];

        switch (key) {
          case 'max_file_size_mb':
            _maxFileSizeMB = int.tryParse(value.toString()) ?? 25;
            break;
          case 'allowed_image_extensions':
            _allowedImageExtensions = (value as String).split(',');
            break;
          case 'allowed_video_extensions':
            _allowedVideoExtensions = (value as String).split(',');
            break;
        }
      }
    } catch (e) {
      debugPrint('خطأ في تحميل إعدادات Cloudinary من قاعدة البيانات: $e');
      // استخدام الإعدادات الافتراضية في حالة الفشل
      _maxFileSizeMB = 25;
      _allowedImageExtensions = [
        'jpg',
        'jpeg',
        'png',
        'webp',
        'gif',
        'bmp',
        'tiff',
      ];
      _allowedVideoExtensions = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm'];
    }
  }

  /// تهيئة خدمة Cloudinary
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // تحميل الإعدادات من قاعدة البيانات
      await _loadSettingsFromDatabase();

      final cloudName =
          dotenv.env['CLOUDINARY_CLOUD_NAME'] ??
          AppConstants.cloudinaryCloudName;
      final apiKey =
          dotenv.env['CLOUDINARY_API_KEY'] ?? AppConstants.cloudinaryApiKey;
      final apiSecret =
          dotenv.env['CLOUDINARY_API_SECRET'] ??
          AppConstants.cloudinaryApiSecret;

      if (cloudName.isEmpty || apiKey.isEmpty || apiSecret.isEmpty) {
        throw Exception('بيانات اعتماد Cloudinary غير مكتملة');
      }

      _cloudinary = Cloudinary.signedConfig(
        cloudName: cloudName,
        apiKey: apiKey,
        apiSecret: apiSecret,
      );

      // التحقق من صحة الاتصال
      try {
        await _cloudinary!
            .upload(
              file: 'test',
              folder: 'test',
              publicId: 'test',
              resourceType: CloudinaryResourceType.image,
            )
            .timeout(const Duration(seconds: 5));
      } catch (e) {
        debugPrint('تحذير: فشل التحقق من اتصال Cloudinary: $e');
      }

      _isInitialized = true;
      notifyListeners();
      debugPrint('تم تهيئة خدمة Cloudinary بنجاح');
    } catch (e) {
      debugPrint('خطأ في تهيئة خدمة Cloudinary: $e');
      rethrow;
    }
  }

  /// التحقق من صحة ملف الصورة
  Future<void> _validateImageFile(File imageFile) async {
    if (!await imageFile.exists()) {
      throw Exception('ملف الصورة غير موجود');
    }

    final fileSizeMB = await imageFile.length() / (1024 * 1024);
    if (fileSizeMB > _maxFileSizeMB) {
      throw Exception(
        'حجم الصورة يتجاوز الحد الأقصى ($_maxFileSizeMB ميجابايت)',
      );
    }

    final extension = path
        .extension(imageFile.path)
        .toLowerCase()
        .replaceAll('.', '');
    if (!_allowedImageExtensions.contains(extension)) {
      throw Exception(
        'امتداد الملف غير مدعوم. الامتدادات المدعومة: $_allowedImageExtensions',
      );
    }
  }

  /// رفع صورة إلى Cloudinary مع إعادة المحاولة وإدارة الأخطاء المحسنة
  Future<String> uploadImage({
    required File imageFile,
    required String folder,
    String? publicId,
    Duration timeout = _uploadTimeout,
    int retryCount = 0,
  }) async {
    if (!_isInitialized) await initialize();
    if (retryCount >= _maxRetries) {
      throw Exception('تم تجاوز الحد الأقصى لمحاولات الرفع ($_maxRetries)');
    }

    await _validateImageFile(imageFile);

    try {
      final uniqueId =
          publicId ?? DateTime.now().millisecondsSinceEpoch.toString();
      final response = await _cloudinary!
          .upload(
            file: imageFile.path,
            folder: folder,
            publicId: uniqueId,
            resourceType: CloudinaryResourceType.image,
            progressCallback: (count, total) {
              debugPrint(
                'تقدم رفع الصورة: ${(count / total * 100).toStringAsFixed(2)}%',
              );
            },
          )
          .timeout(timeout);

      if (response.isSuccessful && response.secureUrl != null) {
        debugPrint('تم رفع الصورة بنجاح: ${response.secureUrl}');
        return response.secureUrl!;
      } else {
        throw Exception('فشل في رفع الصورة: ${response.error}');
      }
    } catch (e) {
      if (retryCount < _maxRetries) {
        debugPrint('إعادة محاولة الرفع (${retryCount + 1}/$_maxRetries)...');
        await Future.delayed(Duration(seconds: 2 * (retryCount + 1)));
        return uploadImage(
          imageFile: imageFile,
          folder: folder,
          publicId: publicId,
          timeout: timeout,
          retryCount: retryCount + 1,
        );
      }
      debugPrint('خطأ في رفع الصورة بعد $_maxRetries محاولات: $e');
      rethrow;
    }
  }

  /// رفع صور متعددة إلى Cloudinary
  Future<List<String>> uploadMultipleImages({
    required List<File> imageFiles,
    required String folder,
    Duration timeout = _defaultTimeout,
  }) async {
    if (!_isInitialized) await initialize();

    final List<String> uploadedUrls = [];
    final List<String> failedUploads = [];

    for (final imageFile in imageFiles) {
      try {
        final url = await uploadImage(
          imageFile: imageFile,
          folder: folder,
          timeout: timeout,
        );
        uploadedUrls.add(url);
      } catch (e) {
        debugPrint('فشل رفع إحدى الصور: $e');
        failedUploads.add(imageFile.path);
      }
    }

    if (failedUploads.isNotEmpty) {
      debugPrint('فشل رفع ${failedUploads.length} صور');
    }

    return uploadedUrls;
  }

  /// رفع صورة المنتج
  Future<String> uploadProductImage({
    required File imageFile,
    required String productId,
    int? index,
    Duration timeout = _defaultTimeout,
  }) async {
    final publicId = index != null ? '${productId}_$index' : productId;
    return uploadImage(
      imageFile: imageFile,
      folder: AppConstants.cloudinaryProductImagesFolder,
      publicId: publicId,
      timeout: timeout,
    );
  }

  // تم حذف دالة رفع صورة المستخدم

  /// رفع صورة الفئة
  Future<String> uploadCategoryImage({
    required File imageFile,
    required String categoryId,
    Duration timeout = _defaultTimeout,
  }) async {
    return uploadImage(
      imageFile: imageFile,
      folder: AppConstants.cloudinaryCategoryImagesFolder,
      publicId: categoryId,
      timeout: timeout,
    );
  }

  /// إنشاء رابط صورة محولة
  String getTransformedImageUrl({
    required String imageUrl,
    int? width,
    int? height,
    String crop = 'fill',
    int quality = 80,
    String? format,
  }) {
    if (!imageUrl.contains('cloudinary.com')) return imageUrl;

    final cacheKey = '$imageUrl-$width-$height-$crop-$quality-$format';
    if (_transformationCache.containsKey(cacheKey)) {
      final cacheEntry = _transformationCache[cacheKey]!;
      final timestamp = cacheEntry['timestamp'] as DateTime?;
      if (timestamp != null &&
          DateTime.now().difference(timestamp) < _cacheExpiryDuration) {
        return cacheEntry['url'] ?? imageUrl;
      }
    }

    try {
      final uri = Uri.parse(imageUrl);
      final pathSegments = uri.pathSegments;
      final cloudName = pathSegments[1];
      final version =
          pathSegments.length > 3 && pathSegments[2].startsWith('v')
              ? pathSegments[2]
              : null;
      final resourceTypeIndex = pathSegments.indexOf('image');
      final resourcePath = pathSegments
          .sublist(resourceTypeIndex + 1)
          .join('/');

      final transformations = [];
      if (width != null || height != null) {
        final dimensions = [];
        if (width != null) dimensions.add('w_$width');
        if (height != null) dimensions.add('h_$height');
        dimensions.add('c_$crop');
        transformations.add(dimensions.join(','));
      }
      transformations.add('q_$quality');
      if (format != null) transformations.add('f_$format');

      final transformationPath = transformations.join('/');
      final baseUrl = 'https://res.cloudinary.com/$cloudName/image/upload';
      final versionPath = version != null ? '/$version' : '';
      final transformedUrl =
          '$baseUrl/$transformationPath$versionPath/$resourcePath';

      _transformationCache[cacheKey] = {
        'url': transformedUrl,
        'timestamp': DateTime.now(),
      };
      return transformedUrl;
    } catch (e) {
      debugPrint('خطأ في إنشاء رابط الصورة المحولة: $e');
      return imageUrl;
    }
  }

  /// الحصول على صورة مصغرة
  String getThumbnailUrl(String imageUrl, {String? format}) {
    return getTransformedImageUrl(
      imageUrl: imageUrl,
      width: 100,
      height: 100,
      crop: 'fill',
      quality: 70,
      format: format,
    );
  }

  /// الحصول على صورة متوسطة الحجم
  String getMediumSizeUrl(String imageUrl, {String? format}) {
    return getTransformedImageUrl(
      imageUrl: imageUrl,
      width: 400,
      height: 400,
      crop: 'fill',
      quality: 80,
      format: format,
    );
  }

  /// الحصول على صورة كبيرة الحجم
  String getLargeSizeUrl(String imageUrl, {String? format}) {
    return getTransformedImageUrl(
      imageUrl: imageUrl,
      width: 800,
      height: 800,
      crop: 'fill',
      quality: 90,
      format: format,
    );
  }

  /// حذف صورة من Cloudinary
  Future<bool> deleteImage({
    required String publicId,
    required String folder,
  }) async {
    if (!_isInitialized) await initialize();

    try {
      final fullPublicId = '$folder/$publicId';
      final response = await _cloudinary!.destroy(fullPublicId);

      if (response.isSuccessful) {
        debugPrint('تم حذف الصورة بنجاح: $fullPublicId');
        return true;
      } else {
        debugPrint('فشل في حذف الصورة: ${response.error}');
        return false;
      }
    } catch (e) {
      debugPrint('خطأ في حذف الصورة: $e');
      return false;
    }
  }

  /// 🚀 رفع متعدد الملفات مع معالجة متوازية
  Future<List<Map<String, dynamic>>> uploadMultipleFiles({
    required List<File> files,
    required String folder,
    List<String>? publicIds,
    Function(int current, int total)? onProgress,
    int maxConcurrent = 3,
  }) async {
    if (!_isInitialized) await initialize();

    final results = <Map<String, dynamic>>[];
    final futures = <Future>[];
    int completed = 0;

    for (int i = 0; i < files.length; i += maxConcurrent) {
      final batch = files.skip(i).take(maxConcurrent).toList();
      final batchFutures = batch.asMap().entries.map((entry) async {
        final index = i + entry.key;
        final file = entry.value;
        final publicId =
            publicIds != null && publicIds.length > index
                ? publicIds[index]
                : null;

        try {
          final startTime = DateTime.now();
          final url = await uploadImage(
            imageFile: file,
            folder: folder,
            publicId: publicId,
          );
          final uploadTime =
              DateTime.now().difference(startTime).inMilliseconds;

          _updateStatistics(true, uploadTime);

          final result = {
            'success': true,
            'url': url,
            'file_path': file.path,
            'upload_time_ms': uploadTime,
            'index': index,
          };

          completed++;
          onProgress?.call(completed, files.length);

          return result;
        } catch (e) {
          _updateStatistics(false, 0);

          return {
            'success': false,
            'error': e.toString(),
            'file_path': file.path,
            'index': index,
          };
        }
      });

      futures.addAll(batchFutures);
      final batchResults = await Future.wait(batchFutures);
      results.addAll(batchResults);
    }

    return results;
  }

  /// 🔍 التحقق من صحة ملف الوسائط
  Future<Map<String, dynamic>> validateMediaFile(
    File file, {
    bool isVideo = false,
  }) async {
    if (!await file.exists()) {
      throw Exception('الملف غير موجود');
    }

    final fileSizeBytes = await file.length();
    final fileSizeMB = fileSizeBytes / (1024 * 1024);
    final extension = file.path.split('.').last.toLowerCase();

    // التحقق من نوع الملف
    final allowedExtensions =
        isVideo
            ? ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm']
            : _allowedImageExtensions;

    if (!allowedExtensions.contains(extension)) {
      throw Exception('نوع الملف غير مدعوم: $extension');
    }

    // التحقق من حجم الملف
    final maxSize = isVideo ? 100 : _maxFileSizeMB; // 100MB للفيديو
    if (fileSizeMB > maxSize) {
      throw Exception(
        'حجم الملف كبير جداً: ${fileSizeMB.toStringAsFixed(2)} MB',
      );
    }

    return {
      'size_bytes': fileSizeBytes,
      'size_mb': fileSizeMB,
      'extension': extension,
      'is_video': isVideo,
      'is_valid': true,
    };
  }

  /// 📊 تحديث إحصائيات الأداء
  void _updateStatistics(bool success, int uploadTimeMs) {
    _totalUploads++;
    if (success) {
      _successfulUploads++;
      // تحديث متوسط وقت الرفع
      _averageUploadTime =
          ((_averageUploadTime * (_successfulUploads - 1)) + uploadTimeMs) /
          _successfulUploads;
    } else {
      _failedUploads++;
    }
    notifyListeners();
  }

  /// 🎨 تحويلات ذكية متقدمة للصور
  String getSmartTransformedUrl({
    required String imageUrl,
    int? width,
    int? height,
    String? deviceType, // mobile, tablet, desktop
    bool autoOptimize = true,
    bool autoFormat = true,
    int? quality,
    String? effect,
    bool progressive = true,
  }) {
    if (!imageUrl.contains('cloudinary.com')) return imageUrl;

    final transformations = <String>[];

    // تحسين تلقائي حسب نوع الجهاز
    if (deviceType != null) {
      switch (deviceType.toLowerCase()) {
        case 'mobile':
          width ??= 400;
          quality ??= 75;
          break;
        case 'tablet':
          width ??= 800;
          quality ??= 80;
          break;
        case 'desktop':
          width ??= 1200;
          quality ??= 85;
          break;
      }
    }

    // إعدادات التحسين التلقائي
    if (autoOptimize) {
      transformations.add('f_auto');
      transformations.add('q_auto');
    }

    if (autoFormat && !autoOptimize) {
      transformations.add('f_auto');
    }

    // الأبعاد
    if (width != null) transformations.add('w_$width');
    if (height != null) transformations.add('h_$height');

    // الجودة المخصصة
    if (quality != null && !autoOptimize) {
      transformations.add('q_$quality');
    }

    // التأثيرات
    if (effect != null) transformations.add('e_$effect');

    // التحميل التدريجي
    if (progressive) transformations.add('fl_progressive');

    // دعم الشبكية
    transformations.add('dpr_auto');

    return _buildTransformedUrl(imageUrl, transformations);
  }

  /// 🖼️ إنشاء مجموعة من الأحجام المختلفة (Responsive Images)
  Map<String, String> generateResponsiveUrls({
    required String imageUrl,
    List<int> widths = const [320, 640, 960, 1280, 1920],
    bool autoOptimize = true,
  }) {
    final urls = <String, String>{};

    for (final width in widths) {
      final key = '${width}w';
      urls[key] = getSmartTransformedUrl(
        imageUrl: imageUrl,
        width: width,
        autoOptimize: autoOptimize,
      );
    }

    return urls;
  }

  /// 🎯 تحويلات متخصصة للمنتجات
  String getProductImageUrl({
    required String imageUrl,
    String size = 'medium', // thumbnail, small, medium, large, xlarge
    bool watermark = false,
    String? backgroundColor,
  }) {
    final transformations = <String>[];

    // أحجام محددة مسبقاً للمنتجات
    switch (size.toLowerCase()) {
      case 'thumbnail':
        transformations.addAll(['w_150', 'h_150', 'c_fill']);
        break;
      case 'small':
        transformations.addAll(['w_300', 'h_300', 'c_fit']);
        break;
      case 'medium':
        transformations.addAll(['w_500', 'h_500', 'c_fit']);
        break;
      case 'large':
        transformations.addAll(['w_800', 'h_800', 'c_fit']);
        break;
      case 'xlarge':
        transformations.addAll(['w_1200', 'h_1200', 'c_fit']);
        break;
    }

    // خلفية بيضاء للمنتجات
    if (backgroundColor != null) {
      transformations.add('b_$backgroundColor');
    } else {
      transformations.add('b_white');
    }

    // علامة مائية
    if (watermark) {
      transformations.add('l_watermark,o_30,g_south_east');
    }

    // تحسين للمنتجات
    transformations.addAll(['f_auto', 'q_auto', 'dpr_auto']);

    return _buildTransformedUrl(imageUrl, transformations);
  }

  /// 🔧 بناء رابط محول
  String _buildTransformedUrl(String imageUrl, List<String> transformations) {
    if (transformations.isEmpty) return imageUrl;

    try {
      final uri = Uri.parse(imageUrl);
      final pathSegments = uri.pathSegments.toList();

      // العثور على موقع 'upload' في المسار
      final uploadIndex = pathSegments.indexOf('upload');
      if (uploadIndex == -1) return imageUrl;

      // إدراج التحويلات بعد 'upload'
      pathSegments.insert(uploadIndex + 1, transformations.join(','));

      final newUri = uri.replace(pathSegments: pathSegments);
      return newUri.toString();
    } catch (e) {
      debugPrint('خطأ في بناء رابط محول: $e');
      return imageUrl;
    }
  }

  /// 📈 الحصول على إحصائيات مفصلة
  Map<String, dynamic> getDetailedStats() {
    return {
      'total_uploads': _totalUploads,
      'successful_uploads': _successfulUploads,
      'failed_uploads': _failedUploads,
      'success_rate': successRate,
      'average_upload_time_ms': _averageUploadTime,
      'cache_size': _transformationCache.length,
      'upload_history_size': _uploadHistory.length,
      'is_initialized': _isInitialized,
      'cloud_name': _isInitialized ? cloudName : null,
    };
  }

  /// 🧹 تنظيف شامل للذاكرة المؤقتة والإحصائيات
  void performMaintenance() {
    // تنظيف الذاكرة المؤقتة المنتهية الصلاحية
    final now = DateTime.now();
    _transformationCache.removeWhere((key, value) {
      final timestamp = value['timestamp'] as DateTime?;
      return timestamp == null ||
          now.difference(timestamp) > _cacheExpiryDuration;
    });

    // تنظيف تاريخ الرفع القديم
    _uploadHistory.removeWhere((key, timestamp) {
      return now.difference(timestamp) > const Duration(days: 7);
    });

    // إعادة تعيين عدادات إعادة المحاولة
    _uploadRetryCount.clear();

    debugPrint(
      'تم تنظيف خدمة Cloudinary - الذاكرة المؤقتة: ${_transformationCache.length}',
    );
    notifyListeners();
  }

  /// 🔄 إعادة تعيين الإحصائيات
  void resetStatistics() {
    _totalUploads = 0;
    _successfulUploads = 0;
    _failedUploads = 0;
    _averageUploadTime = 0.0;
    notifyListeners();
  }

  /// مسح ذاكرة التخزين المؤقت
  void clearCache() {
    _transformationCache.clear();
    notifyListeners();
  }
}
