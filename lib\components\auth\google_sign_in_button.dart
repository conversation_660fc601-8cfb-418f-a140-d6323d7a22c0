import 'package:flutter/material.dart';
import 'package:motorcycle_parts_shop/core/services/google_auth_service.dart';
import 'package:motorcycle_parts_shop/core/theme/app_theme.dart';
import 'package:motorcycle_parts_shop/screens/auth/complete_google_profile_screen.dart';
import 'package:provider/provider.dart';

class GoogleSignInButton extends StatelessWidget {
  final VoidCallback? onSuccess;
  final VoidCallback? onError;
  final String? customText;
  final bool showIcon;
  final bool isCompact;

  const GoogleSignInButton({
    super.key,
    this.onSuccess,
    this.onError,
    this.customText,
    this.showIcon = true,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<GoogleAuthService>(
      builder: (context, googleAuthService, child) {
        return Container(
          width: double.infinity,
          height: isCompact ? 45 : 55,
          margin: const EdgeInsets.symmetric(vertical: 8),
          child: ElevatedButton(
            onPressed:
                googleAuthService.isSigningIn
                    ? null
                    : () => _handleGoogleSignIn(context, googleAuthService),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.white,
              foregroundColor: Colors.black87,
              elevation: 2,
              shadowColor: Colors.black26,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
                side: BorderSide(color: Colors.grey.shade300, width: 1),
              ),
            ),
            child:
                googleAuthService.isSigningIn
                    ? _buildLoadingWidget()
                    : _buildButtonContent(),
          ),
        );
      },
    );
  }

  Widget _buildButtonContent() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (showIcon) ...[
          Image.asset(
            'assets/images/google_logo.png',
            height: isCompact ? 20 : 24,
            width: isCompact ? 20 : 24,
            errorBuilder: (context, error, stackTrace) {
              // في حالة عدم وجود الصورة، استخدم أيقونة
              return Icon(
                Icons.login,
                size: isCompact ? 20 : 24,
                color: AppTheme.primaryColor,
              );
            },
          ),
          SizedBox(width: isCompact ? 8 : 12),
        ],
        Text(
          customText ?? 'تسجيل الدخول مع Google',
          style: TextStyle(
            fontSize: isCompact ? 14 : 16,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
      ],
    );
  }

  Widget _buildLoadingWidget() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SizedBox(
          width: isCompact ? 16 : 20,
          height: isCompact ? 16 : 20,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
          ),
        ),
        SizedBox(width: isCompact ? 8 : 12),
        Text(
          'جاري تسجيل الدخول...',
          style: TextStyle(
            fontSize: isCompact ? 14 : 16,
            color: Colors.black54,
          ),
        ),
      ],
    );
  }

  Future<void> _handleGoogleSignIn(
    BuildContext context,
    GoogleAuthService googleAuthService,
  ) async {
    try {
      final response = await googleAuthService.signInWithGoogle();

      if (response != null && response.user != null) {
        // التحقق من إذا كان المستخدم جديد يحتاج لإكمال البيانات
        final isNewUser = response.user!.userMetadata?['is_new_user'] == true;

        if (isNewUser) {
          // توجيه المستخدم الجديد لشاشة إكمال البيانات
          if (context.mounted) {
            Navigator.of(context).pushReplacement(
              MaterialPageRoute(
                builder:
                    (context) => CompleteGoogleProfileScreen(
                      email: googleAuthService.currentUser?.email ?? '',
                      name:
                          googleAuthService.currentUser?.displayName ??
                          'مستخدم Google',
                      userId: response.user!.id,
                    ),
              ),
            );
          }
        } else {
          // مستخدم موجود - الانتقال للشاشة الرئيسية
          if (onSuccess != null) {
            onSuccess!();
          } else {
            if (context.mounted) {
              Navigator.of(context).pushReplacementNamed('/home');
            }
          }

          // إظهار رسالة ترحيب للمستخدم الموجود
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  'مرحباً بعودتك ${googleAuthService.currentUser?.displayName ?? ''}!',
                ),
                backgroundColor: Colors.green,
                duration: const Duration(seconds: 3),
              ),
            );
          }
        }
      } else if (googleAuthService.errorMessage != null) {
        // حدث خطأ
        if (onError != null) {
          onError!();
        }

        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(googleAuthService.errorMessage!),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 4),
            ),
          );
        }
      }
      // إذا كان response null، فهذا يعني أن المستخدم ألغى العملية
    } catch (e) {
      // معالجة الأخطاء غير المتوقعة
      if (onError != null) {
        onError!();
      }

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ غير متوقع: ${e.toString()}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    }
  }
}

/// زر مخصص لتسجيل الخروج من Google
class GoogleSignOutButton extends StatelessWidget {
  final VoidCallback? onSuccess;
  final String? customText;
  final bool isCompact;

  const GoogleSignOutButton({
    super.key,
    this.onSuccess,
    this.customText,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<GoogleAuthService>(
      builder: (context, googleAuthService, child) {
        if (!googleAuthService.isSignedIn) {
          return const SizedBox.shrink();
        }

        return Container(
          width: double.infinity,
          height: isCompact ? 45 : 55,
          margin: const EdgeInsets.symmetric(vertical: 8),
          child: ElevatedButton(
            onPressed: () => _handleSignOut(context, googleAuthService),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red.shade50,
              foregroundColor: Colors.red.shade700,
              elevation: 1,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
                side: BorderSide(color: Colors.red.shade200, width: 1),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.logout, size: isCompact ? 20 : 24),
                SizedBox(width: isCompact ? 8 : 12),
                Text(
                  customText ?? 'تسجيل الخروج من Google',
                  style: TextStyle(
                    fontSize: isCompact ? 14 : 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Future<void> _handleSignOut(
    BuildContext context,
    GoogleAuthService googleAuthService,
  ) async {
    try {
      await googleAuthService.signOut();

      if (onSuccess != null) {
        onSuccess!();
      } else {
        // الانتقال إلى شاشة تسجيل الدخول
        if (context.mounted) {
          Navigator.of(context).pushReplacementNamed('/login');
        }
      }

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تسجيل الخروج بنجاح'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في تسجيل الخروج: ${e.toString()}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }
}
