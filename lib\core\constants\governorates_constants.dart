
/// ثوابت المحافظات المصرية
/// يحتوي على قائمة جميع المحافظات المصرية مع أكوادها ومراكزها
class GovernoratesConstants {
  /// قائمة جميع المحافظات المصرية مع مراكزها
  static const List<GovernorateModel> allGovernorates = [
    // محافظات القاهرة الكبرى
    GovernorateModel(
      id: 'CAI',
      name: 'القاهرة',
      region: 'القاهرة الكبرى',
      centers: [
        'مصر الجديدة',
        'المعادي',
        'حلوان',
        'شبرا الخيمة',
        'الزيتون',
        'مدينة نصر',
        'الخليفة',
        'الجمالية',
        'الأزبكية',
        'بولاق',
        'الوايلي',
        'حدائق القبة',
        'الساحل',
        'الزاوية الحمراء',
        'منشأة ناصر',
        'الخانكة',
        'القاهرة الجديدة',
        'التجمع الخامس',
      ],
    ),
    GovernorateModel(
      id: 'GIZ',
      name: 'الجيزة',
      region: 'القاهرة الكبرى',
      centers: [
        'الجيزة',
        'أكتوبر',
        'الحوامدية',
        'البدرشين',
        'الصف',
        'أطفيح',
        'العياط',
        'الباويطي',
        'منشأة القناطر',
        'أوسيم',
        'كرداسة',
        'أبو النمرس',
        'الواحات البحرية',
        'الشيخ زايد',
        'أكتوبر الجديدة',
      ],
    ),
    GovernorateModel(
      id: 'QAL',
      name: 'القليوبية',
      region: 'القاهرة الكبرى',
      centers: [
        'بنها',
        'قليوب',
        'شبرا الخيمة',
        'القناطر الخيرية',
        'كفر شكر',
        'طوخ',
        'قها',
        'العبور',
        'الخصوص',
        'شبين القناطر',
        'الخانكة',
      ],
    ),

    // محافظات الإسكندرية والساحل الشمالي
    GovernorateModel(
      id: 'ALX',
      name: 'الإسكندرية',
      region: 'الساحل الشمالي',
      centers: [
        'المنتزه',
        'شرق الإسكندرية',
        'وسط الإسكندرية',
        'غرب الإسكندرية',
        'الجمرك',
        'العامرية',
        'برج العرب',
        'الدخيلة',
        'أبيس',
        'النهضة',
      ],
    ),
    GovernorateModel(
      id: 'MAT',
      name: 'مطروح',
      region: 'الساحل الشمالي',
      centers: [
        'مرسى مطروح',
        'الحمام',
        'العلمين',
        'الضبعة',
        'النجيلة',
        'سيدي براني',
        'السلوم',
        'سيوة',
      ],
    ),
    GovernorateModel(
      id: 'KFS',
      name: 'كفر الشيخ',
      region: 'الدلتا',
      centers: [
        'كفر الشيخ',
        'دسوق',
        'فوه',
        'مطوبس',
        'بيلا',
        'الحامول',
        'بلطيم',
        'الرياض',
        'سيدي سالم',
        'قلين',
      ],
    ),

    // محافظات الدلتا
    GovernorateModel(
      id: 'DKH',
      name: 'الدقهلية',
      region: 'الدلتا',
      centers: [
        'المنصورة',
        'طلخا',
        'ميت غمر',
        'دكرنس',
        'أجا',
        'منية النصر',
        'السنبلاوين',
        'الكردي',
        'بني عبيد',
        'المطرية',
        'تمي الأمديد',
        'الجمالية',
        'شربين',
        'المنزلة',
        'بلقاس',
      ],
    ),
    GovernorateModel(
      id: 'SHR',
      name: 'الشرقية',
      region: 'الدلتا',
      centers: [
        'الزقازيق',
        'العاشر من رمضان',
        'بلبيس',
        'مشتول السوق',
        'القنايات',
        'أبو حماد',
        'القرين',
        'ههيا',
        'أبو كبير',
        'فاقوس',
        'الصالحية الجديدة',
        'ديرب نجم',
        'كفر صقر',
        'أولاد صقر',
        'الحسينية',
        'صان الحجر القبلية',
        'منيا القمح',
      ],
    ),
    GovernorateModel(
      id: 'GHR',
      name: 'الغربية',
      region: 'الدلتا',
      centers: [
        'طنطا',
        'المحلة الكبرى',
        'كفر الزيات',
        'زفتى',
        'السنطة',
        'قطور',
        'بسيون',
        'سمنود',
      ],
    ),
    GovernorateModel(
      id: 'MNF',
      name: 'المنوفية',
      region: 'الدلتا',
      centers: [
        'شبين الكوم',
        'منوف',
        'سرس الليان',
        'أشمون',
        'الباجور',
        'قويسنا',
        'بركة السبع',
        'تلا',
        'الشهداء',
      ],
    ),
    GovernorateModel(
      id: 'BHR',
      name: 'البحيرة',
      region: 'الدلتا',
      centers: [
        'دمنهور',
        'كفر الدوار',
        'رشيد',
        'إدكو',
        'أبو المطامير',
        'أبو حمص',
        'الدلنجات',
        'المحمودية',
        'كوم حمادة',
        'بدر',
        'وادي النطرون',
        'النوبارية الجديدة',
        'الرحمانية',
        'شبراخيت',
        'حوش عيسى',
      ],
    ),
    GovernorateModel(
      id: 'DMT',
      name: 'دمياط',
      region: 'الدلتا',
      centers: [
        'دمياط',
        'دمياط الجديدة',
        'رأس البر',
        'فارسكور',
        'الزرقا',
        'كفر سعد',
      ],
    ),

    // محافظات القناة
    GovernorateModel(
      id: 'PTS',
      name: 'بورسعيد',
      region: 'قناة السويس',
      centers: [
        'بورسعيد',
        'بورفؤاد',
        'العرب',
        'الزهور',
        'الشرق',
        'الضواحي',
        'المناخ',
      ],
    ),
    GovernorateModel(
      id: 'SUZ',
      name: 'السويس',
      region: 'قناة السويس',
      centers: ['السويس', 'الأربعين', 'عتاقة', 'فيصل', 'الجناين'],
    ),
    GovernorateModel(
      id: 'ISM',
      name: 'الإسماعيلية',
      region: 'قناة السويس',
      centers: [
        'الإسماعيلية',
        'فايد',
        'القنطرة شرق',
        'القنطرة غرب',
        'التل الكبير',
        'أبو صوير',
        'القصاصين الجديدة',
        'نفيشة',
      ],
    ),
    GovernorateModel(
      id: 'SIN',
      name: 'شمال سيناء',
      region: 'سيناء',
      centers: ['العريش', 'الشيخ زويد', 'رفح', 'بئر العبد', 'الحسنة', 'نخل'],
    ),
    GovernorateModel(
      id: 'JS',
      name: 'جنوب سيناء',
      region: 'سيناء',
      centers: [
        'الطور',
        'شرم الشيخ',
        'دهب',
        'نويبع',
        'طابا',
        'كاترين',
        'رأس سدر',
        'أبو رديس',
        'أبو زنيمة',
      ],
    ),

    // محافظات الصعيد
    GovernorateModel(
      id: 'BNS',
      name: 'بني سويف',
      region: 'الصعيد',
      centers: [
        'بني سويف',
        'الواسطى',
        'ناصر',
        'إهناسيا',
        'ببا',
        'الفشن',
        'سمسطا',
      ],
    ),
    GovernorateModel(
      id: 'FYM',
      name: 'الفيوم',
      region: 'الصعيد',
      centers: ['الفيوم', 'طامية', 'سنورس', 'إطسا', 'إبشواي', 'يوسف الصديق'],
    ),
    GovernorateModel(
      id: 'MN',
      name: 'المنيا',
      region: 'الصعيد',
      centers: [
        'المنيا',
        'العدوة',
        'مغاغة',
        'بني مزار',
        'مطاي',
        'سمالوط',
        'المنيا الجديدة',
        'ملوي',
        'دير مواس',
        'أبو قرقاص',
      ],
    ),
    GovernorateModel(
      id: 'AST',
      name: 'أسيوط',
      region: 'الصعيد',
      centers: [
        'أسيوط',
        'أسيوط الجديدة',
        'ديروط',
        'القوصية',
        'منفلوط',
        'أبنوب',
        'الفتح',
        'ساحل سليم',
        'البداري',
        'صدفا',
        'الغنايم',
      ],
    ),
    GovernorateModel(
      id: 'SHG',
      name: 'سوهاج',
      region: 'الصعيد',
      centers: [
        'سوهاج',
        'سوهاج الجديدة',
        'أخميم',
        'البلينا',
        'المراغة',
        'المنشأة',
        'دار السلام',
        'جرجا',
        'العسيرات',
        'ساقلتة',
        'طما',
      ],
    ),
    GovernorateModel(
      id: 'QN',
      name: 'قنا',
      region: 'الصعيد',
      centers: [
        'قنا',
        'قنا الجديدة',
        'أبو تشت',
        'فرشوط',
        'نجع حمادي',
        'دشنا',
        'الوقف',
        'قفط',
        'نقادة',
      ],
    ),
    GovernorateModel(
      id: 'LUX',
      name: 'الأقصر',
      region: 'الصعيد',
      centers: [
        'الأقصر',
        'الأقصر الجديدة',
        'إسنا',
        'أرمنت',
        'الطود',
        'الزينية',
        'القرنة',
      ],
    ),
    GovernorateModel(
      id: 'ASW',
      name: 'أسوان',
      region: 'الصعيد',
      centers: [
        'أسوان',
        'أسوان الجديدة',
        'دراو',
        'كوم أمبو',
        'نصر النوبة',
        'كلابشة',
        'إدفو',
        'الرديسية',
        'أبو سمبل',
      ],
    ),

    // محافظات الصحراء الغربية
    GovernorateModel(
      id: 'WAD',
      name: 'الوادي الجديد',
      region: 'الصحراء الغربية',
      centers: ['الخارجة', 'باريس', 'بلاط', 'الداخلة', 'الفرافرة'],
    ),

    // محافظات البحر الأحمر
    GovernorateModel(
      id: 'BA',
      name: 'البحر الأحمر',
      region: 'البحر الأحمر',
      centers: [
        'الغردقة',
        'رأس غارب',
        'سفاجا',
        'القصير',
        'مرسى علم',
        'الشلاتين',
        'حلايب',
        'برنيس',
      ],
    ),
  ];

  /// الحصول على جميع المحافظات
  static List<GovernorateModel> getAllGovernorates() {
    return allGovernorates;
  }

  /// الحصول على المحافظات حسب المنطقة
  static List<GovernorateModel> getGovernoratesByRegion(String region) {
    return allGovernorates.where((gov) => gov.region == region).toList();
  }

  /// البحث عن محافظة بالاسم
  static GovernorateModel? findGovernorateByName(String name) {
    try {
      return allGovernorates.firstWhere((gov) => gov.name == name);
    } catch (e) {
      return null;
    }
  }

  /// البحث عن محافظة بالكود
  static GovernorateModel? findGovernorateById(String id) {
    try {
      return allGovernorates.firstWhere((gov) => gov.id == id);
    } catch (e) {
      return null;
    }
  }

  /// الحصول على أسماء جميع المحافظات فقط
  static List<String> getAllGovernorateNames() {
    return allGovernorates.map((gov) => gov.name).toList();
  }

  /// الحصول على جميع المناطق
  static List<String> getAllRegions() {
    return allGovernorates.map((gov) => gov.region).toSet().toList();
  }

  /// التحقق من صحة اسم المحافظة
  static bool isValidGovernorateName(String name) {
    return allGovernorates.any((gov) => gov.name == name);
  }

  /// التحقق من صحة كود المحافظة
  static bool isValidGovernorateId(String id) {
    return allGovernorates.any((gov) => gov.id == id);
  }

  /// الحصول على المحافظات الرئيسية (الأكثر شهرة)
  static List<GovernorateModel> getMainGovernorates() {
    const mainIds = ['CAI', 'ALX', 'GIZ', 'SHR', 'DKH', 'GHR'];
    return allGovernorates.where((gov) => mainIds.contains(gov.id)).toList();
  }

  /// ترتيب المحافظات أبجدياً
  static List<GovernorateModel> getSortedGovernorates() {
    List<GovernorateModel> sorted = List.from(allGovernorates);
    sorted.sort((a, b) => a.name.compareTo(b.name));
    return sorted;
  }

  /// الحصول على مراكز محافظة معينة
  static List<String> getCentersByGovernorateId(String governorateId) {
    try {
      return allGovernorates
          .firstWhere((gov) => gov.id == governorateId)
          .centers;
    } catch (e) {
      return [];
    }
  }

  /// الحصول على مراكز محافظة معينة بالاسم
  static List<String> getCentersByGovernorateName(String governorateName) {
    try {
      return allGovernorates
          .firstWhere((gov) => gov.name == governorateName)
          .centers;
    } catch (e) {
      return [];
    }
  }

  /// التحقق من صحة اسم المركز في محافظة معينة
  static bool isValidCenterInGovernorate(
    String centerName,
    String governorateId,
  ) {
    final centers = getCentersByGovernorateId(governorateId);
    return centers.contains(centerName);
  }
}

/// نموذج بيانات المحافظة
class GovernorateModel {
  final String id;
  final String name;
  final String region;
  final List<String> centers;

  const GovernorateModel({
    required this.id,
    required this.name,
    required this.region,
    this.centers = const [],
  });

  /// تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {'id': id, 'name': name, 'region': region, 'centers': centers};
  }

  /// إنشاء من Map
  factory GovernorateModel.fromMap(Map<String, dynamic> map) {
    return GovernorateModel(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      region: map['region'] ?? '',
      centers: List<String>.from(map['centers'] ?? []),
    );
  }

  /// تحويل إلى JSON
  String toJson() {
    return '''{"id": "$id", "name": "$name", "region": "$region"}''';
  }

  /// إنشاء من JSON
  factory GovernorateModel.fromJson(String json) {
    final Map<String, dynamic> map = {};
    // تحليل JSON بسيط
    final cleanJson = json
        .replaceAll('{', '')
        .replaceAll('}', '')
        .replaceAll('"', '');
    final pairs = cleanJson.split(', ');
    for (String pair in pairs) {
      final keyValue = pair.split(': ');
      if (keyValue.length == 2) {
        map[keyValue[0]] = keyValue[1];
      }
    }
    return GovernorateModel.fromMap(map);
  }

  /// نسخ مع تعديل
  GovernorateModel copyWith({
    String? id,
    String? name,
    String? region,
    List<String>? centers,
  }) {
    return GovernorateModel(
      id: id ?? this.id,
      name: name ?? this.name,
      region: region ?? this.region,
      centers: centers ?? this.centers,
    );
  }

  @override
  String toString() {
    return 'GovernorateModel(id: $id, name: $name, region: $region)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is GovernorateModel &&
        other.id == id &&
        other.name == name &&
        other.region == region;
  }

  @override
  int get hashCode {
    return id.hashCode ^ name.hashCode ^ region.hashCode;
  }
}

/// مساعد لاختيار المحافظة
class GovernorateHelper {
  /// إنشاء قائمة منسدلة للمحافظات
  static List<Map<String, String>> getDropdownItems() {
    return GovernoratesConstants.getAllGovernorates()
        .map(
          (gov) => {'value': gov.id, 'label': gov.name, 'region': gov.region},
        )
        .toList();
  }

  /// إنشاء قائمة منسدلة مجمعة حسب المنطقة
  static Map<String, List<GovernorateModel>> getGroupedGovernorates() {
    Map<String, List<GovernorateModel>> grouped = {};
    for (GovernorateModel gov in GovernoratesConstants.getAllGovernorates()) {
      if (!grouped.containsKey(gov.region)) {
        grouped[gov.region] = [];
      }
      grouped[gov.region]!.add(gov);
    }
    return grouped;
  }

  /// البحث في المحافظات
  static List<GovernorateModel> searchGovernorates(String query) {
    if (query.isEmpty) return GovernoratesConstants.getAllGovernorates();

    return GovernoratesConstants.getAllGovernorates()
        .where(
          (gov) =>
              gov.name.contains(query) ||
              gov.region.contains(query) ||
              gov.id.toLowerCase().contains(query.toLowerCase()),
        )
        .toList();
  }
}
