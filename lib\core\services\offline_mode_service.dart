import 'dart:async';
import 'package:flutter/material.dart';
import 'package:motorcycle_parts_shop/core/services/connectivity_service.dart';

/// خدمة وضع عدم الاتصال المحسن
/// توفر هذه الخدمة آليات متقدمة للتعامل مع حالات عدم الاتصال بالإنترنت
/// وتخزين العمليات المعلقة وتنفيذها عند استعادة الاتصال
class OfflineModeService extends ChangeNotifier {
  static final OfflineModeService _instance = OfflineModeService._internal();

  // حالة الاتصال الحالية
  bool _isOffline = false;

  // قائمة العمليات المعلقة للتنفيذ عند استعادة الاتصال
  final List<PendingOperation> _pendingOperations = [];

  // الحد الأقصى لعدد العمليات المعلقة
  static const int _maxPendingOperations = 50;

  // خدمة مراقبة الاتصال
  final ConnectivityService _connectivityService = ConnectivityService();

  StreamSubscription<bool>? _connectivitySubscription;

  // تطبيق نمط Singleton لإنشاء كائن واحد فقط من هذه الخدمة
  factory OfflineModeService() {
    return _instance;
  }

  OfflineModeService._internal();

  /// تهيئة خدمة وضع عدم الاتصال
  Future<void> initialize() async {
    try {
      await _connectivityService.initialize();

      // الاستماع لتغييرات حالة الاتصال
      _connectivitySubscription = _connectivityService.onConnectivityChanged
          .listen((isConnected) {
            _handleConnectivityChange(isConnected);
          });

      // تحديث حالة الاتصال الأولية
      final bool isConnected = await _connectivityService.checkConnectivity();
      _isOffline = !isConnected;

      debugPrint('تم تهيئة خدمة وضع عدم الاتصال المحسن بنجاح');
      debugPrint('حالة الاتصال الحالية: ${_isOffline ? "غير متصل" : "متصل"}');

      notifyListeners();
    } catch (e) {
      debugPrint('خطأ أثناء تهيئة خدمة وضع عدم الاتصال: $e');
      rethrow;
    }
  }

  /// معالجة تغيير حالة الاتصال
  void _handleConnectivityChange(bool isConnected) {
    final bool wasOffline = _isOffline;
    _isOffline = !isConnected;

    // إذا تم استعادة الاتصال بعد انقطاع
    if (wasOffline && !_isOffline) {
      debugPrint('تم استعادة الاتصال. جاري تنفيذ العمليات المعلقة...');
      processPendingOperations();
    } else if (!wasOffline && _isOffline) {
      debugPrint('تم فقدان الاتصال. سيتم تخزين العمليات حتى استعادة الاتصال.');
    }

    notifyListeners();
  }

  /// إضافة عملية معلقة للتنفيذ عند استعادة الاتصال
  void addPendingOperation(PendingOperation operation) {
    // التأكد من عدم تجاوز الحد الأقصى للعمليات المعلقة
    if (_pendingOperations.length >= _maxPendingOperations) {
      // إزالة أقدم عملية
      _pendingOperations.removeAt(0);
    }

    _pendingOperations.add(operation);
    debugPrint('تمت إضافة عملية معلقة: ${operation.type}');
    notifyListeners();
  }

  /// تنفيذ العمليات المعلقة عند استعادة الاتصال
  Future<void> processPendingOperations() async {
    if (_pendingOperations.isEmpty) return;

    debugPrint(
      'بدء تنفيذ العمليات المعلقة (${_pendingOperations.length} عملية)',
    );

    final operationsToProcess = List<PendingOperation>.from(_pendingOperations);
    _pendingOperations.clear();

    for (final operation in operationsToProcess) {
      try {
        await operation.execute();
        debugPrint('تم تنفيذ العملية المعلقة بنجاح: ${operation.type}');
      } catch (e) {
        debugPrint('فشل تنفيذ العملية المعلقة: ${operation.type} - $e');
        // إعادة إضافة العملية للمحاولة مرة أخرى إذا كانت مهمة
        if (operation.isImportant) {
          addPendingOperation(operation);
        }
      }
    }

    notifyListeners();
  }

  /// الحصول على عدد العمليات المعلقة
  int getPendingOperationsCount() {
    return _pendingOperations.length;
  }

  /// الحصول على قائمة العمليات المعلقة
  List<PendingOperation> getPendingOperations() {
    return List<PendingOperation>.from(_pendingOperations);
  }

  /// مسح جميع العمليات المعلقة
  void clearPendingOperations() {
    _pendingOperations.clear();
    notifyListeners();
  }

  /// التحقق مما إذا كان الجهاز في وضع عدم الاتصال
  bool isOffline() {
    return _isOffline;
  }

  /// تحديث حالة الاتصال يدويًا
  void updateConnectionStatus(bool isConnected) {
    final bool wasOffline = _isOffline;
    _isOffline = !isConnected;

    // إذا تم استعادة الاتصال بعد انقطاع
    if (wasOffline && !_isOffline) {
      processPendingOperations();
    }

    notifyListeners();
  }

  /// التخلص من الموارد عند إنهاء الخدمة
  @override
  void dispose() {
    _connectivitySubscription?.cancel();
    super.dispose();
  }
}

/// نموذج للعملية المعلقة
class PendingOperation {
  final String type; // نوع العملية (مثل: إضافة للسلة، تقييم منتج، إلخ)
  final Future<void> Function() execute; // دالة التنفيذ
  final bool isImportant; // هل العملية مهمة ويجب إعادة المحاولة في حالة الفشل
  final DateTime createdAt; // وقت إنشاء العملية
  final Map<String, dynamic>? metadata; // بيانات إضافية للعملية

  PendingOperation({
    required this.type,
    required this.execute,
    this.isImportant = false,
    this.metadata,
  }) : createdAt = DateTime.now();

  /// تحويل العملية المعلقة إلى نص للعرض
  @override
  String toString() {
    return 'PendingOperation{type: $type, isImportant: $isImportant, createdAt: $createdAt, metadata: $metadata}';
  }
}
