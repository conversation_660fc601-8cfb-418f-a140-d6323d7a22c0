import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// معالج أخطاء المصادقة المتخصص
class AuthErrorHandler {
  static final AuthErrorHandler _instance = AuthErrorHandler._internal();
  factory AuthErrorHandler() => _instance;
  AuthErrorHandler._internal();

  /// معالجة أخطاء المصادقة وإرجاع رسالة مناسبة
  String handleAuthError(dynamic error) {
    debugPrint('معالجة خطأ المصادقة: $error');

    if (error is AuthException) {
      return _handleAuthException(error);
    }

    if (error is PostgrestException) {
      return _handlePostgrestException(error);
    }

    if (error is Exception) {
      return _handleGeneralException(error);
    }

    return _getDefaultErrorMessage(error.toString());
  }

  /// معالجة أخطاء Auth المحددة
  String _handleAuthException(AuthException error) {
    switch (error.statusCode) {
      case '400':
        if (error.message.contains('Invalid login credentials')) {
          return 'البريد الإلكتروني أو كلمة المرور غير صحيحة';
        }
        if (error.message.contains('Email not confirmed')) {
          return 'يرجى تأكيد البريد الإلكتروني أولاً';
        }
        if (error.message.contains('Invalid email')) {
          return 'البريد الإلكتروني غير صالح';
        }
        if (error.message.contains('Password should be at least')) {
          return 'كلمة المرور قصيرة جداً';
        }
        if (error.message.contains('User already registered')) {
          return 'البريد الإلكتروني مسجل بالفعل';
        }
        if (error.message.contains('Invalid token')) {
          return 'رمز التحقق غير صحيح أو منتهي الصلاحية';
        }
        break;

      case '401':
        return 'غير مصرح لك بالوصول';

      case '403':
        return 'تم رفض الوصول';

      case '404':
        return 'المستخدم غير موجود';

      case '422':
        if (error.message.contains('email')) {
          return 'البريد الإلكتروني غير صالح';
        }
        if (error.message.contains('password')) {
          return 'كلمة المرور غير صالحة';
        }
        break;

      case '429':
        return 'تم تجاوز الحد المسموح من المحاولات، يرجى المحاولة لاحقاً';

      case '500':
        return 'خطأ في الخادم، يرجى المحاولة لاحقاً';

      case '503':
        return 'الخدمة غير متاحة حالياً';
    }

    // رسائل خطأ محددة بناءً على المحتوى
    final message = error.message.toLowerCase();

    if (message.contains('network')) {
      return 'تحقق من اتصالك بالإنترنت';
    }

    if (message.contains('timeout')) {
      return 'انتهت مهلة الطلب، يرجى المحاولة مرة أخرى';
    }

    if (message.contains('email')) {
      return 'مشكلة في البريد الإلكتروني';
    }

    if (message.contains('password')) {
      return 'مشكلة في كلمة المرور';
    }

    return 'خطأ في المصادقة: ${error.message}';
  }

  /// معالجة أخطاء قاعدة البيانات
  String _handlePostgrestException(PostgrestException error) {
    switch (error.code) {
      case '23505': // unique_violation
        if (error.message.contains('email')) {
          return 'البريد الإلكتروني مسجل بالفعل';
        }
        if (error.message.contains('phone')) {
          return 'رقم الهاتف مسجل بالفعل';
        }
        return 'البيانات مكررة';

      case '23503': // foreign_key_violation
        return 'خطأ في ربط البيانات';

      case '23502': // not_null_violation
        return 'بعض البيانات المطلوبة مفقودة';

      case '42501': // insufficient_privilege
        return 'ليس لديك صلاحية للقيام بهذا الإجراء';

      case '42P01': // undefined_table
        return 'خطأ في قاعدة البيانات';

      default:
        if (error.message.contains('duplicate key')) {
          return 'البيانات مكررة';
        }
        if (error.message.contains('not found')) {
          return 'البيانات غير موجودة';
        }
        return 'خطأ في قاعدة البيانات: ${error.message}';
    }
  }

  /// معالجة الاستثناءات العامة
  String _handleGeneralException(Exception error) {
    final message = error.toString().toLowerCase();

    if (message.contains('timeout')) {
      return 'انتهت مهلة الطلب، تحقق من اتصالك بالإنترنت';
    }

    if (message.contains('network') || message.contains('connection')) {
      return 'تحقق من اتصالك بالإنترنت وحاول مرة أخرى';
    }

    if (message.contains('format')) {
      return 'تنسيق البيانات غير صحيح';
    }

    if (message.contains('permission')) {
      return 'ليس لديك صلاحية للقيام بهذا الإجراء';
    }

    return _getDefaultErrorMessage(error.toString());
  }

  /// الحصول على رسالة خطأ افتراضية
  String _getDefaultErrorMessage(String error) {
    // إزالة التفاصيل التقنية وإظهار رسالة مفهومة للمستخدم
    if (error.length > 100) {
      return 'حدث خطأ غير متوقع، يرجى المحاولة مرة أخرى';
    }
    return 'حدث خطأ: $error';
  }

  /// التحقق من نوع الخطأ
  AuthErrorType getErrorType(dynamic error) {
    if (error is AuthException) {
      switch (error.statusCode) {
        case '400':
          if (error.message.contains('Invalid login credentials')) {
            return AuthErrorType.invalidCredentials;
          }
          if (error.message.contains('Email not confirmed')) {
            return AuthErrorType.emailNotConfirmed;
          }
          return AuthErrorType.badRequest;
        case '401':
          return AuthErrorType.unauthorized;
        case '403':
          return AuthErrorType.forbidden;
        case '404':
          return AuthErrorType.notFound;
        case '429':
          return AuthErrorType.rateLimited;
        case '500':
          return AuthErrorType.serverError;
        default:
          return AuthErrorType.authError;
      }
    }

    if (error is PostgrestException) {
      return AuthErrorType.databaseError;
    }

    final message = error.toString().toLowerCase();
    if (message.contains('network') || message.contains('connection')) {
      return AuthErrorType.networkError;
    }

    if (message.contains('timeout')) {
      return AuthErrorType.timeout;
    }

    return AuthErrorType.unknown;
  }

  /// الحصول على اقتراحات لحل الخطأ
  List<String> getErrorSuggestions(AuthErrorType errorType) {
    switch (errorType) {
      case AuthErrorType.invalidCredentials:
        return [
          'تأكد من صحة البريد الإلكتروني',
          'تأكد من صحة كلمة المرور',
          'جرب إعادة تعيين كلمة المرور',
        ];

      case AuthErrorType.emailNotConfirmed:
        return [
          'تحقق من بريدك الإلكتروني',
          'ابحث في مجلد الرسائل غير المرغوب فيها',
          'اطلب إرسال رمز تحقق جديد',
        ];

      case AuthErrorType.networkError:
        return [
          'تحقق من اتصالك بالإنترنت',
          'جرب شبكة أخرى',
          'أعد تشغيل التطبيق',
        ];

      case AuthErrorType.rateLimited:
        return [
          'انتظر قليلاً قبل المحاولة مرة أخرى',
          'تجنب المحاولات المتكررة',
        ];

      case AuthErrorType.serverError:
        return [
          'المشكلة من الخادم',
          'حاول مرة أخرى بعد قليل',
          'تواصل مع الدعم الفني إذا استمرت المشكلة',
        ];

      default:
        return [
          'حاول مرة أخرى',
          'أعد تشغيل التطبيق',
          'تواصل مع الدعم الفني',
        ];
    }
  }
}

/// أنواع أخطاء المصادقة
enum AuthErrorType {
  invalidCredentials,
  emailNotConfirmed,
  badRequest,
  unauthorized,
  forbidden,
  notFound,
  rateLimited,
  serverError,
  authError,
  databaseError,
  networkError,
  timeout,
  unknown,
}

/// امتدادات مفيدة لأنواع الأخطاء
extension AuthErrorTypeExtension on AuthErrorType {
  String get displayName {
    switch (this) {
      case AuthErrorType.invalidCredentials:
        return 'بيانات دخول خاطئة';
      case AuthErrorType.emailNotConfirmed:
        return 'البريد الإلكتروني غير مؤكد';
      case AuthErrorType.badRequest:
        return 'طلب غير صحيح';
      case AuthErrorType.unauthorized:
        return 'غير مصرح';
      case AuthErrorType.forbidden:
        return 'ممنوع';
      case AuthErrorType.notFound:
        return 'غير موجود';
      case AuthErrorType.rateLimited:
        return 'تجاوز الحد المسموح';
      case AuthErrorType.serverError:
        return 'خطأ في الخادم';
      case AuthErrorType.authError:
        return 'خطأ في المصادقة';
      case AuthErrorType.databaseError:
        return 'خطأ في قاعدة البيانات';
      case AuthErrorType.networkError:
        return 'خطأ في الشبكة';
      case AuthErrorType.timeout:
        return 'انتهاء المهلة';
      case AuthErrorType.unknown:
        return 'خطأ غير معروف';
    }
  }

  bool get isRetryable {
    switch (this) {
      case AuthErrorType.networkError:
      case AuthErrorType.timeout:
      case AuthErrorType.serverError:
        return true;
      default:
        return false;
    }
  }
}
