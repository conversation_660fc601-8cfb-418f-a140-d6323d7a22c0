import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:motorcycle_parts_shop/core/services/local_storage_service.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class AnalyticsService extends ChangeNotifier {
  final SupabaseClient _client;
  final LocalStorageService _storageService;
  final SharedPreferences _prefs;

  // بيانات التحليلات
  final Map<String, int> _screenViews = {};
  final Map<String, int> _productViews = {};
  final Map<String, int> _searchQueries = {};
  final Map<String, int> _buttonClicks = {};
  final List<Map<String, dynamic>> _userJourney = [];
  final Map<String, DateTime> _lastEventTimes = {};
  final Map<String, int> _eventCounts = {};
  final Map<String, dynamic> _userMetrics = {};

  bool _isEnabled = true;
  DateTime? _sessionStartTime;
  String? _currentScreen;

  AnalyticsService({
    required SupabaseClient client,
    required LocalStorageService storageService,
    required SharedPreferences prefs,
  }) : _client = client,
       _storageService = storageService,
       _prefs = prefs {
    _initialize();
  }

  @protected
  SupabaseClient get client => _client;

  Future<void> _initialize() async {
    try {
      _isEnabled = _prefs.getBool('analytics_enabled') ?? true;
      debugPrint('تم تهيئة خدمة التحليلات بنجاح');
    } catch (e) {
      debugPrint('Error getting analytics enabled: $e');
      _isEnabled = true;
    }
  }

  Future<void> initialize() async {
    try {
      _isEnabled = _storageService.getAnalyticsEnabled() ?? true;
      _loadAnalyticsData();
      _startSession();
      notifyListeners();
    } catch (e) {
      debugPrint('Error initializing analytics service: $e');
    }
  }

  void _loadAnalyticsData() {
    try {
      final screenViewsData = _storageService.getAnalyticsData('screen_views');
      if (screenViewsData != null) {
        _updateMapFromDynamic(_screenViews, screenViewsData);
      }

      final productViewsData = _storageService.getAnalyticsData(
        'product_views',
      );
      if (productViewsData != null) {
        _updateMapFromDynamic(_productViews, productViewsData);
      }

      final searchQueriesData = _storageService.getAnalyticsData(
        'search_queries',
      );
      if (searchQueriesData != null) {
        _updateMapFromDynamic(_searchQueries, searchQueriesData);
      }

      final buttonClicksData = _storageService.getAnalyticsData(
        'button_clicks',
      );
      if (buttonClicksData != null) {
        _updateMapFromDynamic(_buttonClicks, buttonClicksData);
      }

      final userJourneyData = _storageService.getAnalyticsData('user_journey');
      if (userJourneyData != null) {
        _userJourney.addAll(List<Map<String, dynamic>>.from(userJourneyData));
      }
    } catch (e) {
      debugPrint('Error loading analytics data: $e');
    }
  }

  void _updateMapFromDynamic(Map<String, int> targetMap, dynamic sourceData) {
    final Map<String, dynamic> dynamicMap = Map<String, dynamic>.from(
      sourceData,
    );
    dynamicMap.forEach((key, value) {
      targetMap[key] =
          value is int ? value : int.tryParse(value.toString()) ?? 0;
    });
  }

  void _startSession() {
    if (!_isEnabled) return;
    _sessionStartTime = DateTime.now();
    _logEvent('session_start', {
      'timestamp': _sessionStartTime!.toIso8601String(),
      'user_id': _getUserId(),
    });
  }

  String _getUserId() {
    final user = _storageService.getUser();
    return user?.id ?? 'anonymous';
  }

  void logScreenView(String screenName) {
    if (!_isEnabled) return;
    _currentScreen = screenName;
    _screenViews[screenName] = (_screenViews[screenName] ?? 0) + 1;
    _logEvent('screen_view', {
      'screen_name': screenName,
      'timestamp': DateTime.now().toIso8601String(),
    });
    _saveAnalyticsData();
  }

  void logProductView(String productId, String productName) {
    if (!_isEnabled) return;
    _productViews[productId] = (_productViews[productId] ?? 0) + 1;
    _logEvent('product_view', {
      'product_id': productId,
      'product_name': productName,
      'screen': _currentScreen,
      'timestamp': DateTime.now().toIso8601String(),
    });
    _saveAnalyticsData();
  }

  void logSearch(String query) {
    if (!_isEnabled || query.trim().isEmpty) return;
    _searchQueries[query] = (_searchQueries[query] ?? 0) + 1;
    _logEvent('search', {
      'query': query,
      'timestamp': DateTime.now().toIso8601String(),
    });
    _saveAnalyticsData();
  }

  void logButtonClick(String buttonId, {Map<String, dynamic>? additionalData}) {
    if (!_isEnabled) return;
    _buttonClicks[buttonId] = (_buttonClicks[buttonId] ?? 0) + 1;
    final eventData = {
      'button_id': buttonId,
      'screen': _currentScreen,
      'timestamp': DateTime.now().toIso8601String(),
    };
    if (additionalData != null) {
      additionalData.forEach((key, value) {
        eventData[key] = value?.toString();
      });
    }
    _logEvent('button_click', eventData);
    _saveAnalyticsData();
  }

  void logAddToCart(
    String productId,
    String productName,
    int quantity,
    double price,
  ) {
    if (!_isEnabled) return;
    _logEvent('add_to_cart', {
      'product_id': productId,
      'product_name': productName,
      'quantity': quantity,
      'price': price,
      'screen': _currentScreen,
      'timestamp': DateTime.now().toIso8601String(),
    });
    _saveAnalyticsData();
  }

  void logPurchase(
    String orderId,
    double totalAmount,
    List<Map<String, dynamic>> items,
  ) {
    if (!_isEnabled) return;
    _logEvent('purchase', {
      'order_id': orderId,
      'total_amount': totalAmount,
      'items': items,
      'user_id': _getUserId(),
      'timestamp': DateTime.now().toIso8601String(),
    });
    _saveAnalyticsData();
  }

  void logCustomEvent(String eventName, Map<String, dynamic> eventData) {
    if (!_isEnabled) return;
    final data = Map<String, dynamic>.from(eventData);
    data['screen'] = _currentScreen;
    data['timestamp'] = DateTime.now().toIso8601String();
    _logEvent(eventName, data);
    _saveAnalyticsData();
  }

  void _logEvent(String eventName, Map<String, dynamic> eventData) {
    if (!_isEnabled) return;
    final now = DateTime.now();
    final lastEventTime = _lastEventTimes[eventName];

    if (lastEventTime != null && now.difference(lastEventTime).inSeconds < 1) {
      return;
    }

    final event = {
      'event': eventName,
      'data': Map<String, dynamic>.from(eventData),
      'timestamp': now.toIso8601String(),
    };

    _userJourney.add(event);
    _lastEventTimes[eventName] = now;

    while (_userJourney.length > 100) {
      _userJourney.removeAt(0);
    }

    _eventCounts[eventName] = (_eventCounts[eventName] ?? 0) + 1;
  }

  /// الحصول على بيانات المبيعات الشهرية
  Future<List<double>> getMonthlySalesData() async {
    if (!_isEnabled) return [];

    try {
      // في حالة وجود اتصال بقاعدة البيانات، نجلب البيانات منها
      final response = await _client
          .from('monthly_sales')
          .select('*')
          .order('month', ascending: true)
          .limit(6);

      if (response.isNotEmpty) {
        return List<double>.from(
          response.map((item) => item['amount'] as double),
        );
      }

      // في حالة عدم وجود بيانات، نعيد بيانات افتراضية
      return [3000, 4500, 3800, 5200, 4800, 6000];
    } catch (e) {
      debugPrint('خطأ في الحصول على بيانات المبيعات الشهرية: $e');
      // في حالة حدوث خطأ، نعيد بيانات افتراضية
      return [3000, 4500, 3800, 5200, 4800, 6000];
    }
  }

  /// الحصول على بيانات توزيع المبيعات حسب الفئة
  Future<Map<String, double>> getCategorySalesData() async {
    if (!_isEnabled) return {};

    try {
      // في حالة وجود اتصال بقاعدة البيانات، نجلب البيانات منها
      final response = await _client
          .from('category_sales')
          .select('*')
          .order('percentage', ascending: false);

      if (response.isNotEmpty) {
        final Map<String, double> result = {};
        for (final item in response) {
          result[item['category']] = item['percentage'];
        }
        return result;
      }

      // في حالة عدم وجود بيانات، نعيد بيانات افتراضية
      return {'المحرك': 35, 'الفرامل': 25, 'الإطارات': 20, 'أخرى': 20};
    } catch (e) {
      debugPrint('خطأ في الحصول على بيانات توزيع المبيعات حسب الفئة: $e');
      // في حالة حدوث خطأ، نعيد بيانات افتراضية
      return {'المحرك': 35, 'الفرامل': 25, 'الإطارات': 20, 'أخرى': 20};
    }
  }

  void _saveAnalyticsData() {
    try {
      _storageService.saveAnalyticsData('screen_views', _screenViews);
      _storageService.saveAnalyticsData('product_views', _productViews);
      _storageService.saveAnalyticsData('search_queries', _searchQueries);
      _storageService.saveAnalyticsData('button_clicks', _buttonClicks);
      _storageService.saveAnalyticsData('user_journey', _userJourney);
    } catch (e) {
      debugPrint('Error saving analytics data: $e');
    }
  }

  Future<void> setAnalyticsEnabled(bool enabled) async {
    try {
      await _storageService.saveAnalyticsEnabled(enabled);
      _isEnabled = enabled;
      if (enabled && _sessionStartTime == null) {
        _startSession();
      }
      notifyListeners();
    } catch (e) {
      debugPrint('Error setting analytics enabled: $e');
    }
  }

  bool get isEnabled => _isEnabled;

  List<MapEntry<String, int>> getTopScreens({int limit = 5}) {
    final sortedEntries =
        _screenViews.entries.toList()
          ..sort((a, b) => b.value.compareTo(a.value));
    return sortedEntries.take(limit).toList();
  }

  List<MapEntry<String, int>> getTopProducts({int limit = 5}) {
    final sortedEntries =
        _productViews.entries.toList()
          ..sort((a, b) => b.value.compareTo(a.value));
    return sortedEntries.take(limit).toList();
  }

  List<MapEntry<String, int>> getTopSearches({int limit = 5}) {
    final sortedEntries =
        _searchQueries.entries.toList()
          ..sort((a, b) => b.value.compareTo(a.value));
    return sortedEntries.take(limit).toList();
  }

  Future<void> clearAllData() async {
    try {
      _screenViews.clear();
      _productViews.clear();
      _searchQueries.clear();
      _buttonClicks.clear();
      _userJourney.clear();
      await _storageService.clearAnalyticsData();
      notifyListeners();
    } catch (e) {
      debugPrint('Error clearing analytics data: $e');
    }
  }

  Future<void> trackUserEvent(
    String eventName, {
    Map<String, dynamic>? properties,
  }) async {
    try {
      final now = DateTime.now();
      _eventCounts[eventName] = (_eventCounts[eventName] ?? 0) + 1;
      _lastEventTimes[eventName] = now;

      await _client.from('user_events').insert({
        'event_name': eventName,
        'properties': properties,
        'timestamp': now.toIso8601String(),
      });

      _updateUserMetrics(eventName, properties);
    } catch (e) {
      debugPrint('Error tracking user event: $e');
    }
  }

  void _updateUserMetrics(String eventName, Map<String, dynamic>? properties) {
    if (properties != null) {
      switch (eventName) {
        case 'product_view':
          _userMetrics['last_viewed_product'] = properties['product_id'];
          _userMetrics['view_count'] = (_userMetrics['view_count'] ?? 0) + 1;
          break;
        case 'cart_add':
          _userMetrics['cart_items_count'] =
              (_userMetrics['cart_items_count'] ?? 0) + 1;
          break;
        case 'purchase':
          _userMetrics['purchase_count'] =
              (_userMetrics['purchase_count'] ?? 0) + 1;
          _userMetrics['total_spent'] =
              (_userMetrics['total_spent'] ?? 0) + (properties['amount'] ?? 0);
          break;
      }
    }
  }

  Future<Map<String, dynamic>> getUserMetrics(String userId) async {
    try {
      final response =
          await _client
              .from('user_metrics')
              .select()
              .eq('user_id', userId)
              .single();
      final metrics = Map<String, dynamic>.from(response);
      metrics.addAll(_userMetrics);
      return metrics;
    } catch (e) {
      debugPrint('Error getting user metrics: $e');
      return _userMetrics;
    }
  }

  Future<void> saveUserPreferences(Map<String, dynamic> preferences) async {
    try {
      await _prefs.setString('user_preferences', preferences.toString());
      _userMetrics['last_preferences_update'] =
          DateTime.now().toIso8601String();
    } catch (e) {
      debugPrint('Error saving user preferences: $e');
      await logError('preferences_save_error', e.toString());
    }
  }

  Future<Map<String, dynamic>> getUserPreferences() async {
    try {
      final prefsString = _prefs.getString('user_preferences');
      if (prefsString != null) {
        final prefs = Map<String, dynamic>.from(prefsString as Map);
        _userMetrics['preferences_access_count'] =
            (_userMetrics['preferences_access_count'] ?? 0) + 1;
        return prefs;
      }
      return {};
    } catch (e) {
      debugPrint('Error getting user preferences: $e');
      await logError('preferences_read_error', e.toString());
      return {};
    }
  }

  Future<void> trackUserSession() async {
    try {
      final startTime = DateTime.now();
      await _client.from('user_sessions').insert({
        'start_time': startTime.toIso8601String(),
        'device_info': await _getDeviceInfo(),
      });
      await _prefs.setString('last_session_start', startTime.toIso8601String());
    } catch (e) {
      debugPrint('Error tracking user session: $e');
    }
  }

  Future<void> logError(String errorType, String errorMessage) async {
    if (!_isEnabled) return;
    try {
      await _client.from('error_logs').insert({
        'error_type': errorType,
        'error_message': errorMessage,
        'timestamp': DateTime.now().toIso8601String(),
        'user_id': _getUserId(),
        'screen': _currentScreen,
      });
      _logEvent('error', {
        'error_type': errorType,
        'error_message': errorMessage,
        'screen': _currentScreen,
        'timestamp': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      debugPrint('Error logging error: $e');
    }
  }

  Future<Map<String, dynamic>> _getDeviceInfo() async {
    return {'platform': 'unknown', 'version': 'unknown'};
  }

  Future<void> endUserSession() async {
    try {
      final startTimeString = _prefs.getString('last_session_start');
      if (startTimeString != null) {
        final startTime = DateTime.parse(startTimeString);
        final endTime = DateTime.now();
        final duration = endTime.difference(startTime).inSeconds;

        await _client
            .from('user_sessions')
            .update({
              'end_time': endTime.toIso8601String(),
              'duration': duration,
            })
            .eq('start_time', startTimeString);

        await _prefs.remove('last_session_start');
      }
    } catch (e) {
      debugPrint('Error ending user session: $e');
    }
  }

  Future<Map<String, dynamic>> getGlobalMetrics() async {
    if (!_isEnabled) {
      return {
        'total_products': 0,
        'total_orders': 0,
        'total_users': 0,
        'total_revenue': 0.0,
        'low_stock_count': 0,
        'conversion_rate': 0.0,
        'average_rating': 0.0,
        'total_visits': 0,
      };
    }
    try {
      final response =
          await _client.from('analytics_metrics').select().single();
      return {
        'total_products': response['total_products'] ?? 0,
        'total_orders': response['total_orders'] ?? 0,
        'total_users': response['total_users'] ?? 0,
        'total_revenue': response['total_revenue'] ?? 0.0,
        'low_stock_count': response['low_stock_count'] ?? 0,
        'conversion_rate': response['conversion_rate'] ?? 0.0,
        'average_rating': response['average_rating'] ?? 0.0,
        'total_visits': response['total_visits'] ?? 0,
      };
    } catch (e) {
      debugPrint('خطأ في جلب إحصائيات النظام: $e');
      return {
        'total_products': 0,
        'total_orders': 0,
        'total_users': 0,
        'total_revenue': 0.0,
        'low_stock_count': 0,
        'conversion_rate': 0.0,
        'average_rating': 0.0,
        'total_visits': 0,
      };
    }
  }

  /// تتبع البحث بالصورة
  Future<void> trackImageSearch(
    String imageUrl,
    List<String> detectedConcepts,
  ) async {
    if (!_isEnabled) return;

    try {
      await _client.from('ai_analytics').insert({
        'analysis_type': 'image_search',
        'input_data': {'image_url': imageUrl},
        'output_data': {'detected_concepts': detectedConcepts},
        'user_id': _getUserId(),
        'created_at': DateTime.now().toIso8601String(),
      });

      _logEvent('image_search', {
        'image_url': imageUrl,
        'detected_concepts': detectedConcepts,
        'concepts_count': detectedConcepts.length,
        'timestamp': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      debugPrint('Error tracking image search: $e');
    }
  }

  /// تتبع تفاعل المستخدم المتقدم
  Future<void> trackUserInteraction(
    String interactionType,
    Map<String, dynamic> details,
  ) async {
    if (!_isEnabled) return;

    final userId = _getUserId();
    if (userId == 'anonymous') return;

    try {
      await _client.from('user_interactions').insert({
        'user_id': userId,
        'interaction_type': interactionType,
        'details': details,
        'session_id': _sessionStartTime?.millisecondsSinceEpoch.toString(),
        'created_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      debugPrint('Error tracking user interaction: $e');
    }
  }

  /// تتبع الأداء
  Future<void> trackPerformance(String operation, int durationMs) async {
    if (!_isEnabled) return;

    _logEvent('performance', {
      'operation': operation,
      'duration_ms': durationMs,
      'timestamp': DateTime.now().toIso8601String(),
    });
  }

  /// الحصول على إحصائيات المستخدم المتقدمة
  Future<Map<String, dynamic>?> getUserAnalytics(String userId) async {
    if (!_isEnabled) return null;

    try {
      final response = await _client
          .from('user_interactions')
          .select('interaction_type, created_at')
          .eq('user_id', userId)
          .gte(
            'created_at',
            DateTime.now().subtract(const Duration(days: 30)).toIso8601String(),
          );

      final interactions = response as List<dynamic>;

      return {
        'total_interactions': interactions.length,
        'interactions_by_type': _groupInteractionsByType(interactions),
        'daily_activity': _calculateDailyActivity(interactions),
        'last_activity':
            interactions.isNotEmpty ? interactions.last['created_at'] : null,
      };
    } catch (e) {
      debugPrint('Error getting user analytics: $e');
      return null;
    }
  }

  /// الحصول على المنتجات الشائعة
  Future<List<Map<String, dynamic>>> getPopularProducts({
    int limit = 10,
  }) async {
    if (!_isEnabled) return [];

    try {
      final response = await _client
          .from('product_analytics')
          .select('product_id, views, cart_additions, purchases')
          .order('views', ascending: false)
          .limit(limit);

      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      debugPrint('Error getting popular products: $e');
      return [];
    }
  }

  /// الحصول على اتجاهات البحث
  Future<List<Map<String, dynamic>>> getSearchTrends({int limit = 20}) async {
    if (!_isEnabled) return [];

    try {
      final response = await _client
          .from('smart_search_log')
          .select('query, COUNT(*) as search_count')
          .gte(
            'created_at',
            DateTime.now().subtract(const Duration(days: 7)).toIso8601String(),
          )
          .order('search_count', ascending: false)
          .limit(limit);

      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      debugPrint('Error getting search trends: $e');
      return [];
    }
  }

  /// تجميع التفاعلات حسب النوع
  Map<String, int> _groupInteractionsByType(List<dynamic> interactions) {
    final Map<String, int> grouped = {};
    for (final interaction in interactions) {
      final type = interaction['interaction_type'] as String;
      grouped[type] = (grouped[type] ?? 0) + 1;
    }
    return grouped;
  }

  /// حساب النشاط اليومي
  Map<String, int> _calculateDailyActivity(List<dynamic> interactions) {
    final Map<String, int> daily = {};
    for (final interaction in interactions) {
      final date = DateTime.parse(interaction['created_at']).toLocal();
      final dateKey =
          '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
      daily[dateKey] = (daily[dateKey] ?? 0) + 1;
    }
    return daily;
  }

  // Getters
  Map<String, int> get screenViews => Map.unmodifiable(_screenViews);
  Map<String, int> get productViews => Map.unmodifiable(_productViews);
  Map<String, int> get searchQueries => Map.unmodifiable(_searchQueries);
  Map<String, int> get buttonClicks => Map.unmodifiable(_buttonClicks);
  List<Map<String, dynamic>> get userJourney => List.unmodifiable(_userJourney);
  Map<String, int> get eventCounts => Map.unmodifiable(_eventCounts);
  Map<String, dynamic> get userMetrics => Map.unmodifiable(_userMetrics);
  DateTime? get sessionStartTime => _sessionStartTime;
  String? get currentScreen => _currentScreen;
}
