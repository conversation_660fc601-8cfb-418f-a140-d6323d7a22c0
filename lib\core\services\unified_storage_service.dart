import '../interfaces/cache_provider.dart';
import '../interfaces/maintenance_provider.dart';
import 'dart:convert';
import 'local_storage_service.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:motorcycle_parts_shop/models/user_model.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// خدمة التخزين الموحدة - تدمج وظائف التخزين المحلي والمشترك
class UnifiedStorageService extends LocalStorageService {
  static final UnifiedStorageService _instance =
      UnifiedStorageService.internalConstructor();
  factory UnifiedStorageService() => _instance;
  UnifiedStorageService.internalConstructor() : super.internalConstructor();

  late SharedPreferences _prefs;
  bool _isInitialized = false;
  final Map<String, dynamic> _memoryCache = {};
  int _cacheLimit = 1000;
  bool _autoCleanEnabled = true;

  bool get autoCleanEnabled => _autoCleanEnabled;

  /// تهيئة الخدمة
  @override
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _prefs = await SharedPreferences.getInstance();
      _autoCleanEnabled = _prefs.getBool('auto_clean_enabled') ?? true;
      _isInitialized = true;
      state = true;
      debugPrint('✅ تم تهيئة خدمة التخزين الموحدة');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة خدمة التخزين: $e');
      rethrow;
    }
  }

  // ========================================
  // وظائف التخزين الأساسية
  // ========================================

  /// حفظ بيانات نصية
  Future<bool> setString(String key, String value) async {
    if (!_isInitialized) await initialize();
    try {
      final success = await _prefs.setString(key, value);
      if (success) {
        _memoryCache[key] = value;
      }
      return success;
    } catch (e) {
      debugPrint('خطأ في حفظ البيانات النصية: $e');
      return false;
    }
  }

  /// جلب بيانات نصية
  String? getString(String key) {
    if (!_isInitialized) return null;
    try {
      if (_memoryCache.containsKey(key)) {
        return _memoryCache[key] as String?;
      }

      final value = _prefs.getString(key);
      if (value != null) {
        _memoryCache[key] = value;
      }
      return value;
    } catch (e) {
      debugPrint('خطأ في جلب البيانات النصية: $e');
      return null;
    }
  }

  /// حفظ بيانات رقمية صحيحة
  Future<bool> setInt(String key, int value) async {
    if (!_isInitialized) await initialize();
    try {
      final success = await _prefs.setInt(key, value);
      if (success) {
        _memoryCache[key] = value;
      }
      return success;
    } catch (e) {
      debugPrint('خطأ في حفظ البيانات الرقمية: $e');
      return false;
    }
  }

  /// جلب بيانات رقمية صحيحة
  int? getInt(String key) {
    if (!_isInitialized) return null;
    try {
      if (_memoryCache.containsKey(key)) {
        return _memoryCache[key] as int?;
      }

      final value = _prefs.getInt(key);
      if (value != null) {
        _memoryCache[key] = value;
      }
      return value;
    } catch (e) {
      debugPrint('خطأ في جلب البيانات الرقمية: $e');
      return null;
    }
  }

  /// حفظ بيانات منطقية
  Future<bool> setBool(String key, bool value) async {
    if (!_isInitialized) await initialize();
    try {
      final success = await _prefs.setBool(key, value);
      if (success) {
        _memoryCache[key] = value;
      }
      return success;
    } catch (e) {
      debugPrint('خطأ في حفظ البيانات المنطقية: $e');
      return false;
    }
  }

  /// جلب بيانات منطقية
  bool? getBool(String key) {
    if (!_isInitialized) return null;
    try {
      if (_memoryCache.containsKey(key)) {
        return _memoryCache[key] as bool?;
      }

      final value = _prefs.getBool(key);
      if (value != null) {
        _memoryCache[key] = value;
      }
      return value;
    } catch (e) {
      debugPrint('خطأ في جلب البيانات المنطقية: $e');
      return null;
    }
  }

  /// حفظ قائمة نصوص
  Future<bool> setStringList(String key, List<String> value) async {
    if (!_isInitialized) await initialize();
    try {
      final success = await _prefs.setStringList(key, value);
      if (success) {
        _memoryCache[key] = value;
      }
      return success;
    } catch (e) {
      debugPrint('خطأ في حفظ قائمة النصوص: $e');
      return false;
    }
  }

  /// جلب قائمة نصوص
  List<String>? getStringList(String key) {
    if (!_isInitialized) return null;
    try {
      if (_memoryCache.containsKey(key)) {
        return _memoryCache[key] as List<String>?;
      }

      final value = _prefs.getStringList(key);
      if (value != null) {
        _memoryCache[key] = value;
      }
      return value;
    } catch (e) {
      debugPrint('خطأ في جلب قائمة النصوص: $e');
      return null;
    }
  }

  /// حفظ كائن JSON
  Future<bool> setJson(String key, Map<String, dynamic> value) async {
    try {
      final jsonString = json.encode(value);
      return await setString(key, jsonString);
    } catch (e) {
      debugPrint('خطأ في حفظ JSON: $e');
      return false;
    }
  }

  /// جلب كائن JSON
  Map<String, dynamic>? getJson(String key) {
    try {
      final jsonString = getString(key);
      if (jsonString != null) {
        return json.decode(jsonString) as Map<String, dynamic>;
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في جلب JSON: $e');
      return null;
    }
  }

  /// حذف مفتاح
  Future<bool> remove(String key) async {
    if (!_isInitialized) await initialize();
    try {
      _memoryCache.remove(key);
      return await _prefs.remove(key);
    } catch (e) {
      debugPrint('خطأ في حذف المفتاح: $e');
      return false;
    }
  }

  /// التحقق من وجود مفتاح
  bool containsKey(String key) {
    if (!_isInitialized) return false;
    return _memoryCache.containsKey(key) || _prefs.containsKey(key);
  }

  /// مسح جميع البيانات
  Future<bool> clear() async {
    if (!_isInitialized) await initialize();
    try {
      _memoryCache.clear();
      return await _prefs.clear();
    } catch (e) {
      debugPrint('خطأ في مسح البيانات: $e');
      return false;
    }
  }

  // ========================================
  // تطبيق CacheProvider
  // ========================================

  void clearCache() {
    _memoryCache.clear();
    debugPrint('🧹 تم مسح الذاكرة المؤقتة');
  }

  int get cacheSize => _memoryCache.length;

  void setCacheLimit(int limit) {
    _cacheLimit = limit;
  }

  int get cacheLimit => _cacheLimit;

  bool get isCacheFull => cacheSize >= cacheLimit;

  void clearExpiredCache() {
    if (cacheSize > cacheLimit) {
      final keysToRemove =
          _memoryCache.keys.take(cacheSize - cacheLimit).toList();
      for (final key in keysToRemove) {
        _memoryCache.remove(key);
      }
      debugPrint('🧹 تم مسح ${keysToRemove.length} عنصر منتهي الصلاحية');
    }
  }

  Map<String, dynamic> getCacheStats() {
    return {
      'cache_size': cacheSize,
      'cache_limit': cacheLimit,
      'cache_usage_percentage': (cacheSize / cacheLimit * 100).toStringAsFixed(
        2,
      ),
      'is_cache_full': isCacheFull,
    };
  }

  void optimizeCache() {
    if (isCacheFull) {
      clearExpiredCache();
    }
  }

  // ========================================
  // تطبيق MaintenanceProvider
  // ========================================

  void performMaintenance() {
    clearExpiredCache();
    optimizeCache();
    debugPrint('🔧 تم تنفيذ صيانة خدمة التخزين');
  }

  bool get isHealthy => _isInitialized && cacheSize < cacheLimit;

  MaintenanceStatus get maintenanceStatus {
    if (!_isInitialized) return MaintenanceStatus.critical;
    if (cacheSize > cacheLimit * 0.9) return MaintenanceStatus.needsMaintenance;
    return MaintenanceStatus.healthy;
  }

  void cleanup() {
    clearCache();
    performMaintenance();
  }

  bool quickHealthCheck() {
    return isHealthy;
  }

  void schedulePeriodicMaintenance(Duration interval) {
    // يمكن تطوير هذه الوظيفة لاحقاً لجدولة صيانة دورية
  }

  // ========================================
  // وظائف خاصة بالتطبيق
  // ========================================

  Future<bool> saveLastSyncTime(DateTime time) async {
    return await setString('last_sync_time', time.toIso8601String());
  }

  // ========================================
  // دوال إضافية مطلوبة للخدمات الأخرى
  // ========================================

  /// حفظ int (alias)
  Future<void> saveInt(String key, int value) async {
    await setInt(key, value);
  }

  /// حفظ double (alias)
  Future<void> saveDouble(String key, double value) async {
    if (!_isInitialized) await initialize();
    try {
      final success = await _prefs.setDouble(key, value);
      if (success) {
        _memoryCache[key] = value;
      }
    } catch (e) {
      debugPrint('خطأ في حفظ double: $e');
    }
  }

  /// الحصول على double (alias)
  double? getDoubleValue(String key) {
    if (!_isInitialized) return null;

    if (_memoryCache.containsKey(key)) {
      final value = _memoryCache[key];
      return value is double ? value : null;
    }

    try {
      final value = _prefs.getDouble(key);
      if (value != null) {
        _memoryCache[key] = value;
      }
      return value;
    } catch (e) {
      debugPrint('خطأ في قراءة double: $e');
      return null;
    }
  }

  /// الحصول على int (alias)
  int? getIntValue(String key) {
    if (!_isInitialized) return null;

    if (_memoryCache.containsKey(key)) {
      final value = _memoryCache[key];
      return value is int ? value : null;
    }

    try {
      final value = _prefs.getInt(key);
      if (value != null) {
        _memoryCache[key] = value;
      }
      return value;
    } catch (e) {
      debugPrint('خطأ في قراءة int: $e');
      return null;
    }
  }

  /// حفظ bool
  Future<void> saveBool(String key, bool value) async {
    await setBool(key, value);
  }

  /// حفظ string
  Future<void> saveString(String key, String value) async {
    await setString(key, value);
  }

  /// مسح جميع البيانات
  @override
  Future<void> clearAll() async {
    await super.clearAll();
  }
}

/// مزود خدمة التخزين الموحدة
final unifiedStorageServiceProvider =
    StateNotifierProvider<UnifiedStorageService, bool>((ref) {
      return UnifiedStorageService();
    });

/// استثناء خدمة التخزين
class StorageException implements Exception {
  final String message;
  StorageException(this.message);

  @override
  String toString() => 'StorageException: $message';
}
