import 'package:json_annotation/json_annotation.dart';

part 'coupon_model.g.dart';

@JsonSerializable()
class CouponModel {
  final String id;
  final String code;
  final String description;
  final String discountType;
  final double discountValue;
  final double? minPurchaseAmount;
  final double? maxDiscountAmount;
  final DateTime startDate;
  final DateTime endDate;
  final int? usageLimit;
  final int usageCount;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? createdBy;

  CouponModel({
    required this.id,
    required this.code,
    required this.description,
    required this.discountType,
    required this.discountValue,
    this.minPurchaseAmount,
    this.maxDiscountAmount,
    required this.startDate,
    required this.endDate,
    this.usageLimit,
    required this.usageCount,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
    this.createdBy,
  });

  factory CouponModel.fromJson(Map<String, dynamic> json) =>
      _$CouponModelFromJson(json);
  Map<String, dynamic> toJson() => _$CouponModelToJson(this);

  bool get isValid =>
      isActive &&
      DateTime.now().isAfter(startDate) &&
      DateTime.now().isBefore(endDate) &&
      (usageLimit == null || usageCount < usageLimit!);

  double calculateDiscount(double purchaseAmount) {
    if (!isValid ||
        (minPurchaseAmount != null && purchaseAmount < minPurchaseAmount!)) {
      return 0;
    }

    double discount =
        discountType == 'percentage'
            ? purchaseAmount * (discountValue / 100)
            : discountValue;

    if (maxDiscountAmount != null) {
      discount = discount > maxDiscountAmount! ? maxDiscountAmount! : discount;
    }

    return discount;
  }

  CouponModel copyWith({
    String? id,
    String? code,
    String? description,
    String? discountType,
    double? discountValue,
    double? minPurchaseAmount,
    double? maxDiscountAmount,
    DateTime? startDate,
    DateTime? endDate,
    int? usageLimit,
    int? usageCount,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
  }) {
    return CouponModel(
      id: id ?? this.id,
      code: code ?? this.code,
      description: description ?? this.description,
      discountType: discountType ?? this.discountType,
      discountValue: discountValue ?? this.discountValue,
      minPurchaseAmount: minPurchaseAmount ?? this.minPurchaseAmount,
      maxDiscountAmount: maxDiscountAmount ?? this.maxDiscountAmount,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      usageLimit: usageLimit ?? this.usageLimit,
      usageCount: usageCount ?? this.usageCount,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      createdBy: createdBy ?? this.createdBy,
    );
  }
}
