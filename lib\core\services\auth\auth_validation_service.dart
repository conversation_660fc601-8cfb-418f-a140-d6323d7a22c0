import 'package:flutter/material.dart';

/// خدمة التحقق من صحة البيانات للمصادقة
class AuthValidationService {
  static final AuthValidationService _instance = AuthValidationService._internal();
  factory AuthValidationService() => _instance;
  AuthValidationService._internal();

  /// التحقق من صحة البريد الإلكتروني
  bool isValidEmail(String email) {
    if (email.isEmpty) return false;
    return RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    ).hasMatch(email);
  }

  /// التحقق من صحة رقم الهاتف
  bool isValidPhone(String? phone) {
    if (phone == null || phone.isEmpty) return true;
    final cleanPhone = phone.replaceAll(RegExp(r'[\s\-()]'), '');
    return RegExp(r'^\+?[0-9]{8,15}$').hasMatch(cleanPhone);
  }

  /// التحقق من صحة كلمة المرور
  bool isValidPassword(String password) {
    return password.length >= 6;
  }

  /// التحقق من صحة كلمة المرور المعقدة
  PasswordValidationResult validatePasswordComplex(String password) {
    final result = PasswordValidationResult();
    
    if (password.length < 8) {
      result.errors.add('كلمة المرور يجب أن تكون 8 أحرف على الأقل');
    }
    
    if (!RegExp(r'[A-Z]').hasMatch(password)) {
      result.errors.add('يجب أن تحتوي على حرف كبير واحد على الأقل');
    }
    
    if (!RegExp(r'[a-z]').hasMatch(password)) {
      result.errors.add('يجب أن تحتوي على حرف صغير واحد على الأقل');
    }
    
    if (!RegExp(r'[0-9]').hasMatch(password)) {
      result.errors.add('يجب أن تحتوي على رقم واحد على الأقل');
    }
    
    if (!RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(password)) {
      result.errors.add('يجب أن تحتوي على رمز خاص واحد على الأقل');
    }

    result.isValid = result.errors.isEmpty;
    result.strength = _calculatePasswordStrength(password);
    
    return result;
  }

  /// حساب قوة كلمة المرور
  PasswordStrength _calculatePasswordStrength(String password) {
    int score = 0;
    
    if (password.length >= 8) score++;
    if (password.length >= 12) score++;
    if (RegExp(r'[A-Z]').hasMatch(password)) score++;
    if (RegExp(r'[a-z]').hasMatch(password)) score++;
    if (RegExp(r'[0-9]').hasMatch(password)) score++;
    if (RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(password)) score++;
    if (password.length >= 16) score++;
    
    if (score <= 2) return PasswordStrength.weak;
    if (score <= 4) return PasswordStrength.medium;
    if (score <= 6) return PasswordStrength.strong;
    return PasswordStrength.veryStrong;
  }

  /// التحقق من صحة الاسم الكامل
  bool isValidFullName(String name) {
    if (name.trim().isEmpty) return false;
    if (name.trim().length < 2) return false;
    // التحقق من وجود حرفين على الأقل
    return RegExp(r'^[a-zA-Zأ-ي\s]{2,}$').hasMatch(name.trim());
  }

  /// التحقق من صحة تاريخ الميلاد
  bool isValidBirthDate(DateTime? birthDate) {
    if (birthDate == null) return false;
    final now = DateTime.now();
    final age = now.year - birthDate.year;
    
    // يجب أن يكون العمر بين 13 و 120 سنة
    return age >= 13 && age <= 120;
  }

  /// التحقق من تطابق كلمات المرور
  bool doPasswordsMatch(String password, String confirmPassword) {
    return password == confirmPassword;
  }

  /// التحقق من صحة رمز OTP
  bool isValidOTP(String otp) {
    if (otp.isEmpty) return false;
    // رمز OTP يجب أن يكون 6 أرقام
    return RegExp(r'^\d{6}$').hasMatch(otp);
  }

  /// التحقق من صحة العنوان
  bool isValidAddress(String address) {
    return address.trim().length >= 10;
  }

  /// التحقق الشامل لبيانات التسجيل
  RegistrationValidationResult validateRegistrationData({
    required String email,
    required String password,
    required String confirmPassword,
    required String fullName,
    String? phone,
    DateTime? birthDate,
  }) {
    final result = RegistrationValidationResult();

    // التحقق من البريد الإلكتروني
    if (!isValidEmail(email)) {
      result.errors['email'] = 'البريد الإلكتروني غير صحيح';
    }

    // التحقق من كلمة المرور
    final passwordValidation = validatePasswordComplex(password);
    if (!passwordValidation.isValid) {
      result.errors['password'] = passwordValidation.errors.join('\n');
    }

    // التحقق من تطابق كلمات المرور
    if (!doPasswordsMatch(password, confirmPassword)) {
      result.errors['confirmPassword'] = 'كلمات المرور غير متطابقة';
    }

    // التحقق من الاسم الكامل
    if (!isValidFullName(fullName)) {
      result.errors['fullName'] = 'الاسم الكامل غير صحيح';
    }

    // التحقق من رقم الهاتف (اختياري)
    if (phone != null && phone.isNotEmpty && !isValidPhone(phone)) {
      result.errors['phone'] = 'رقم الهاتف غير صحيح';
    }

    // التحقق من تاريخ الميلاد (اختياري)
    if (birthDate != null && !isValidBirthDate(birthDate)) {
      result.errors['birthDate'] = 'تاريخ الميلاد غير صحيح';
    }

    result.isValid = result.errors.isEmpty;
    return result;
  }

  /// التحقق من صحة بيانات تسجيل الدخول
  LoginValidationResult validateLoginData({
    required String email,
    required String password,
  }) {
    final result = LoginValidationResult();

    if (email.isEmpty) {
      result.errors['email'] = 'البريد الإلكتروني مطلوب';
    } else if (!isValidEmail(email)) {
      result.errors['email'] = 'البريد الإلكتروني غير صحيح';
    }

    if (password.isEmpty) {
      result.errors['password'] = 'كلمة المرور مطلوبة';
    } else if (!isValidPassword(password)) {
      result.errors['password'] = 'كلمة المرور قصيرة جداً';
    }

    result.isValid = result.errors.isEmpty;
    return result;
  }
}

/// نتيجة التحقق من كلمة المرور
class PasswordValidationResult {
  bool isValid = false;
  List<String> errors = [];
  PasswordStrength strength = PasswordStrength.weak;
}

/// قوة كلمة المرور
enum PasswordStrength { weak, medium, strong, veryStrong }

/// نتيجة التحقق من بيانات التسجيل
class RegistrationValidationResult {
  bool isValid = false;
  Map<String, String> errors = {};
}

/// نتيجة التحقق من بيانات تسجيل الدخول
class LoginValidationResult {
  bool isValid = false;
  Map<String, String> errors = {};
}

/// امتدادات مفيدة لقوة كلمة المرور
extension PasswordStrengthExtension on PasswordStrength {
  String get displayName {
    switch (this) {
      case PasswordStrength.weak:
        return 'ضعيفة';
      case PasswordStrength.medium:
        return 'متوسطة';
      case PasswordStrength.strong:
        return 'قوية';
      case PasswordStrength.veryStrong:
        return 'قوية جداً';
    }
  }

  Color get color {
    switch (this) {
      case PasswordStrength.weak:
        return Colors.red;
      case PasswordStrength.medium:
        return Colors.orange;
      case PasswordStrength.strong:
        return Colors.blue;
      case PasswordStrength.veryStrong:
        return Colors.green;
    }
  }

  double get progress {
    switch (this) {
      case PasswordStrength.weak:
        return 0.25;
      case PasswordStrength.medium:
        return 0.5;
      case PasswordStrength.strong:
        return 0.75;
      case PasswordStrength.veryStrong:
        return 1.0;
    }
  }
}
