import 'package:cloudinary/cloudinary.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

/// فئة إعداد Cloudinary وإنشاء المجلدات المطلوبة
class CloudinarySetup {
  static Cloudinary? _cloudinary;
  static bool _isInitialized = false;

  /// قائمة المجلدات المطلوبة للمشروع
  static const List<String> requiredFolders = [
    'motorcycle_parts_shop/products',
    'motorcycle_parts_shop/products/categories',
    'motorcycle_parts_shop/products/featured',
    'motorcycle_parts_shop/products/offers',
    'motorcycle_parts_shop/products/thumbnails',
    'motorcycle_parts_shop/products/gallery',
    'motorcycle_parts_shop/users',
    'motorcycle_parts_shop/users/profiles',
    'motorcycle_parts_shop/users/avatars',
    'motorcycle_parts_shop/companies',
    'motorcycle_parts_shop/companies/logos',
    'motorcycle_parts_shop/companies/banners',
    'motorcycle_parts_shop/banners',
    'motorcycle_parts_shop/banners/home',
    'motorcycle_parts_shop/banners/promotions',
    'motorcycle_parts_shop/banners/advertisements',
    'motorcycle_parts_shop/banners/seasonal',
    'motorcycle_parts_shop/categories',
    'motorcycle_parts_shop/categories/icons',
    'motorcycle_parts_shop/categories/backgrounds',
    'motorcycle_parts_shop/temp',
    'motorcycle_parts_shop/temp/uploads',
    'motorcycle_parts_shop/temp/processing',
  ];

  /// تهيئة Cloudinary
  static Future<bool> initialize() async {
    if (_isInitialized) return true;

    try {
      debugPrint('🌤️ تهيئة Cloudinary...');

      // قراءة بيانات API من متغيرات البيئة
      final cloudName = dotenv.env['CLOUDINARY_CLOUD_NAME'];
      final apiKey = dotenv.env['CLOUDINARY_API_KEY'];
      final apiSecret = dotenv.env['CLOUDINARY_API_SECRET'];

      if (cloudName == null || apiKey == null || apiSecret == null) {
        debugPrint('❌ بيانات Cloudinary غير مكتملة في ملف .env');
        return false;
      }

      if (cloudName == 'your_cloud_name_here' ||
          apiKey == 'your_api_key_here' ||
          apiSecret == 'your_api_secret_here') {
        debugPrint('⚠️ يجب تحديث بيانات Cloudinary في ملف .env');
        return false;
      }

      // إنشاء instance من Cloudinary
      _cloudinary = Cloudinary.signedConfig(
        cloudName: cloudName,
        apiKey: apiKey,
        apiSecret: apiSecret,
      );

      _isInitialized = true;
      debugPrint('✅ تم تهيئة Cloudinary بنجاح');
      return true;
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة Cloudinary: $e');
      return false;
    }
  }

  /// إنشاء المجلدات المطلوبة
  static Future<bool> createRequiredFolders() async {
    if (!_isInitialized) {
      final initialized = await initialize();
      if (!initialized) return false;
    }

    try {
      debugPrint('📁 إنشاء المجلدات المطلوبة...');

      // إنشاء ملف وهمي في كل مجلد لضمان إنشاؤه
      for (final folder in requiredFolders) {
        await _createFolderIfNotExists(folder);
      }

      debugPrint('✅ تم إنشاء جميع المجلدات المطلوبة');
      return true;
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء المجلدات: $e');
      return false;
    }
  }

  /// إنشاء مجلد إذا لم يكن موجوداً
  static Future<void> _createFolderIfNotExists(String folderPath) async {
    try {
      // إنشاء ملف وهمي صغير لضمان إنشاء المجلد
      final placeholderPublicId = '$folderPath/.placeholder';

      // رفع ملف نصي صغير كـ placeholder
      await _cloudinary!.upload(
        file:
            'data:text/plain;base64,cGxhY2Vob2xkZXI=', // "placeholder" in base64
        resourceType: CloudinaryResourceType.raw,
        publicId: placeholderPublicId,
        optParams: {'overwrite': false},
      );

      debugPrint('📁 تم إنشاء المجلد: $folderPath');
    } catch (e) {
      // إذا كان الملف موجود بالفعل، فهذا يعني أن المجلد موجود
      if (e.toString().contains('already exists')) {
        debugPrint('📁 المجلد موجود بالفعل: $folderPath');
      } else {
        debugPrint('⚠️ تحذير في إنشاء المجلد $folderPath: $e');
      }
    }
  }

  /// التحقق من صحة الإعداد
  static Future<Map<String, dynamic>> validateSetup() async {
    final result = <String, dynamic>{
      'cloudinary_initialized': _isInitialized,
      'folders_created': false,
      'api_connection': false,
      'errors': <String>[],
    };

    try {
      // التحقق من تهيئة Cloudinary
      if (!_isInitialized) {
        final initialized = await initialize();
        result['cloudinary_initialized'] = initialized;
        if (!initialized) {
          result['errors'].add('فشل في تهيئة Cloudinary');
          return result;
        }
      }

      // التحقق من إنشاء Cloudinary instance
      final testResult = _cloudinary != null;
      result['api_connection'] = testResult;
      if (testResult) {
        debugPrint('✅ تم إنشاء Cloudinary instance بنجاح');
      } else {
        debugPrint('❌ فشل في إنشاء Cloudinary instance');
      }

      // التحقق من وجود المجلدات
      result['folders_created'] = await _checkFoldersExist();
    } catch (e) {
      result['errors'].add('خطأ في التحقق من الإعداد: $e');
      debugPrint('❌ خطأ في التحقق من إعداد Cloudinary: $e');
    }

    return result;
  }

  /// التحقق من إمكانية إنشاء المجلدات
  static Future<bool> _checkFoldersExist() async {
    try {
      // إذا تم إنشاء Cloudinary instance بنجاح، فيمكن إنشاء المجلدات
      final canCreateFolders = _cloudinary != null;

      if (canCreateFolders) {
        debugPrint('✅ يمكن إنشاء المجلدات المطلوبة');
      } else {
        debugPrint('❌ لا يمكن إنشاء المجلدات');
      }

      return canCreateFolders;
    } catch (e) {
      debugPrint('⚠️ خطأ في التحقق من المجلدات: $e');
      return false;
    }
  }

  /// الحصول على instance Cloudinary
  static Cloudinary? get instance => _cloudinary;

  /// التحقق من حالة التهيئة
  static bool get isInitialized => _isInitialized;

  /// إعادة تعيين الإعدادات
  static void reset() {
    _cloudinary = null;
    _isInitialized = false;
    debugPrint('🔄 تم إعادة تعيين إعدادات Cloudinary');
  }

  /// الحصول على تقرير شامل عن الإعداد
  static Future<Map<String, dynamic>> getSetupReport() async {
    final report = <String, dynamic>{
      'timestamp': DateTime.now().toIso8601String(),
      'setup_status': 'checking',
    };

    try {
      // التحقق من صحة الإعداد
      final validation = await validateSetup();
      report.addAll(validation);

      // تحديد حالة الإعداد العامة
      if (validation['cloudinary_initialized'] == true &&
          validation['api_connection'] == true &&
          validation['folders_created'] == true) {
        report['setup_status'] = 'complete';
      } else if (validation['cloudinary_initialized'] == true &&
          validation['api_connection'] == true) {
        report['setup_status'] = 'partial';
      } else {
        report['setup_status'] = 'failed';
      }

      // إضافة معلومات إضافية
      report['required_folders_count'] = requiredFolders.length;
      report['cloud_name'] = dotenv.env['CLOUDINARY_CLOUD_NAME'];
    } catch (e) {
      report['setup_status'] = 'error';
      report['error'] = e.toString();
    }

    return report;
  }
}
