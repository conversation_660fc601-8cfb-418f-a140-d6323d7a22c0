import 'package:json_annotation/json_annotation.dart';

part 'backup_log_model.g.dart';

@JsonSerializable()
class BackupLogModel {
  final String id;
  final String backupName;
  final String backupPath;
  final String? backupSize;
  final String status;
  final DateTime createdAt;
  final String? errorMessage;

  BackupLogModel({
    required this.id,
    required this.backupName,
    required this.backupPath,
    this.backupSize,
    required this.status,
    required this.createdAt,
    this.errorMessage,
  });

  factory BackupLogModel.fromJson(Map<String, dynamic> json) =>
      _$BackupLogModelFromJson(json);
  Map<String, dynamic> toJson() => _$BackupLogModelToJson(this);

  bool get isSuccessful => status == 'completed';

  BackupLogModel copyWith({
    String? id,
    String? backupName,
    String? backupPath,
    String? backupSize,
    String? status,
    DateTime? createdAt,
    String? errorMessage,
  }) {
    return BackupLogModel(
      id: id ?? this.id,
      backupName: backupName ?? this.backupName,
      backupPath: backupPath ?? this.backupPath,
      backupSize: backupSize ?? this.backupSize,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }
}
