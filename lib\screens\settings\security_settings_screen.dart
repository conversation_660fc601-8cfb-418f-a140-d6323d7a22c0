import 'package:flutter/material.dart';
import 'package:motorcycle_parts_shop/core/services/analytics_service.dart';
import 'package:motorcycle_parts_shop/core/services/auth_supabase_service.dart';
import 'package:motorcycle_parts_shop/core/theme/app_theme.dart';
import 'package:provider/provider.dart';

class SecuritySettingsScreen extends StatefulWidget {
  const SecuritySettingsScreen({super.key});

  @override
  State<SecuritySettingsScreen> createState() => _SecuritySettingsScreenState();
}

class _SecuritySettingsScreenState extends State<SecuritySettingsScreen> {
  bool _isPasswordEnabled = true;
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الأمان والخصوصية'),
        centerTitle: true,
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: Stack(
        children: [
          ListView(
            padding: const EdgeInsets.all(16.0),
            children: [
              _buildSectionHeader('المصادقة'),

              SwitchListTile(
                secondary: const CircleAvatar(
                  backgroundColor: AppTheme.primaryColor,
                  child: Icon(Icons.password, color: Colors.white),
                ),
                title: const Text('كلمة المرور'),
                subtitle: const Text('استخدام كلمة مرور للتطبيق'),
                value: _isPasswordEnabled,
                activeColor: AppTheme.primaryColor,
                onChanged: (bool value) {
                  setState(() {
                    _isPasswordEnabled = value;
                  });
                  _updateSecuritySettings('password_enabled', value);
                },
              ),

              // تم إزالة المصادقة الثنائية بناءً على طلب المستخدم
              const Divider(),
              _buildSectionHeader('الخصوصية'),
              Consumer<AnalyticsService>(
                builder:
                    (context, analyticsService, _) => SwitchListTile(
                      secondary: const CircleAvatar(
                        backgroundColor: AppTheme.primaryColor,
                        child: Icon(Icons.analytics, color: Colors.white),
                      ),
                      title: const Text('تحليلات التطبيق'),
                      subtitle: const Text(
                        'السماح بجمع بيانات الاستخدام لتحسين التطبيق',
                      ),
                      value: analyticsService.isEnabled,
                      activeColor: AppTheme.primaryColor,
                      onChanged: (bool value) async {
                        await analyticsService.setAnalyticsEnabled(value);
                        if (mounted) setState(() {});
                      },
                    ),
              ),
              ListTile(
                leading: const CircleAvatar(
                  backgroundColor: AppTheme.primaryColor,
                  child: Icon(Icons.delete, color: Colors.white),
                ),
                title: const Text('حذف البيانات'),
                subtitle: const Text('حذف جميع البيانات الشخصية'),
                onTap: () {
                  _showDeleteDataDialog();
                },
              ),
              const Divider(),
              _buildSectionHeader('السجل'),
              ListTile(
                leading: const CircleAvatar(
                  backgroundColor: AppTheme.primaryColor,
                  child: Icon(Icons.history, color: Colors.white),
                ),
                title: const Text('سجل النشاط'),
                subtitle: const Text('عرض سجل نشاط الحساب'),
                onTap: () {
                  _navigateToActivityLog();
                },
              ),
              ListTile(
                leading: const CircleAvatar(
                  backgroundColor: AppTheme.primaryColor,
                  child: Icon(Icons.devices, color: Colors.white),
                ),
                title: const Text('الأجهزة المتصلة'),
                subtitle: const Text('إدارة الأجهزة المتصلة بحسابك'),
                onTap: () {
                  _navigateToConnectedDevices();
                },
              ),
            ],
          ),
          if (_isLoading)
            Container(
              color: Colors.black.withOpacity(0.3),
              child: const Center(child: CircularProgressIndicator()),
            ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: AppTheme.primaryColor,
        ),
      ),
    );
  }

  // تحديث إعدادات الأمان
  Future<void> _updateSecuritySettings(String setting, bool value) async {
    setState(() => _isLoading = true);
    try {
      final authService = Provider.of<AuthSupabaseService>(
        context,
        listen: false,
      );

      switch (setting) {
        case 'password_enabled':
          // تحديث إعدادات كلمة المرور
          await authService.updateSecuritySettings(setting, value);
          break;
      }

      _showMessage('تم تحديث الإعدادات بنجاح');
    } catch (e) {
      _showMessage('حدث خطأ أثناء تحديث الإعدادات');
      debugPrint('خطأ: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // حذف بيانات المستخدم
  Future<void> _deleteUserData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final authService = Provider.of<AuthSupabaseService>(
        context,
        listen: false,
      );
      // حذف البيانات من قاعدة البيانات
      await Future.delayed(const Duration(seconds: 2)); // محاكاة تأخير الشبكة
      debugPrint('جاري حذف بيانات المستخدم...');

      // استخدام المتغير authService لحذف البيانات
      await authService.deleteAllUserData();

      _showMessage('تم حذف البيانات بنجاح');
    } catch (e) {
      _showMessage('حدث خطأ أثناء حذف البيانات');
      debugPrint('خطأ: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // عرض رسالة للمستخدم
  void _showMessage(String message) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text(message)));
  }

  // الانتقال إلى شاشة سجل النشاط
  void _navigateToActivityLog() {
    Navigator.pushNamed(context, '/activity-log');
  }

  // الانتقال إلى شاشة الأجهزة المتصلة
  void _navigateToConnectedDevices() {
    Navigator.pushNamed(context, '/connected-devices');
  }

  Future<void> _showDeleteDataDialog() async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('حذف البيانات'),
          content: const SingleChildScrollView(
            child: ListBody(
              children: [
                Text('هل أنت متأكد من حذف جميع بياناتك الشخصية؟'),
                Text('لا يمكن التراجع عن هذا الإجراء.'),
              ],
            ),
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('إلغاء'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: const Text('حذف', style: TextStyle(color: Colors.red)),
              onPressed: () {
                Navigator.of(context).pop();
                _deleteUserData();
              },
            ),
          ],
        );
      },
    );
  }
}
