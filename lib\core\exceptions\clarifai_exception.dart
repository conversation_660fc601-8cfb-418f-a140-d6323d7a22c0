
/// استثناءات خدمة Clarifai
class ClarifaiException implements Exception {
  final String message;
  final String? code;
  final Map<String, dynamic>? details;
  final DateTime timestamp;

  ClarifaiException(
    this.message, {
    this.code,
    this.details,
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();

  @override
  String toString() {
    return 'ClarifaiException: $message${code != null ? ' (Code: $code)' : ''}';
  }

  Map<String, dynamic> toJson() {
    return {
      'message': message,
      'code': code,
      'details': details,
      'timestamp': timestamp.toIso8601String(),
    };
  }
}

/// استثناءات محددة
class ClarifaiNetworkException extends ClarifaiException {
  ClarifaiNetworkException(super.message) : super(code: 'NETWORK_ERROR');
}

class ClarifaiAuthException extends ClarifaiException {
  ClarifaiAuthException(super.message) : super(code: 'AUTH_ERROR');
}

class ClarifaiValidationException extends ClarifaiException {
  ClarifaiValidationException(super.message) : super(code: 'VALIDATION_ERROR');
}

class ClarifaiRateLimitException extends ClarifaiException {
  ClarifaiRateLimitException(super.message) : super(code: 'RATE_LIMIT');
}

class ClarifaiModelException extends ClarifaiException {
  ClarifaiModelException(super.message) : super(code: 'MODEL_ERROR');
}
