-- ===================================================================
-- جداول الإدارة والدعم الفني
-- متجر قطع غيار الدراجات النارية - Supabase Database
-- ===================================================================

-- ===================================================================
-- 1. جدول تفاعلات الدعم الفني
-- ===================================================================
CREATE TABLE IF NOT EXISTS support_interactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    interaction_type VARCHAR(20) NOT NULL CHECK (interaction_type IN ('chat', 'email', 'phone', 'ticket')),
    subject VARCHAR(200),
    message TEXT NOT NULL,
    status VARCHAR(20) DEFAULT 'open' CHECK (status IN ('open', 'in_progress', 'resolved', 'closed')),
    priority VARCHAR(10) DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    assigned_to UUID REFERENCES profiles(id) ON DELETE SET NULL,
    resolution TEXT,
    satisfaction_rating INTEGER CHECK (satisfaction_rating >= 1 AND satisfaction_rating <= 5),
    resolved_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_support_interactions_user_id ON support_interactions(user_id);
CREATE INDEX IF NOT EXISTS idx_support_interactions_status ON support_interactions(status);
CREATE INDEX IF NOT EXISTS idx_support_interactions_priority ON support_interactions(priority);
CREATE INDEX IF NOT EXISTS idx_support_interactions_assigned_to ON support_interactions(assigned_to);

-- ===================================================================
-- 2. جدول سجلات النسخ الاحتياطي
-- ===================================================================
CREATE TABLE IF NOT EXISTS backup_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    backup_type VARCHAR(20) NOT NULL CHECK (backup_type IN ('full', 'incremental', 'differential')),
    table_name VARCHAR(100),
    file_path TEXT,
    file_size_bytes BIGINT,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'in_progress', 'completed', 'failed')),
    error_message TEXT,
    started_at TIMESTAMPTZ DEFAULT NOW(),
    completed_at TIMESTAMPTZ,
    created_by UUID REFERENCES profiles(id) ON DELETE SET NULL
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_backup_logs_backup_type ON backup_logs(backup_type);
CREATE INDEX IF NOT EXISTS idx_backup_logs_status ON backup_logs(status);
CREATE INDEX IF NOT EXISTS idx_backup_logs_started_at ON backup_logs(started_at);

-- ===================================================================
-- الدوال الإدارية المساعدة
-- ===================================================================

-- دالة إنشاء تذكرة دعم فني
CREATE OR REPLACE FUNCTION create_support_ticket(
    p_user_id UUID,
    p_subject VARCHAR,
    p_message TEXT,
    p_priority VARCHAR DEFAULT 'medium'
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    v_ticket_id UUID;
BEGIN
    -- إنشاء تذكرة الدعم
    INSERT INTO support_interactions (
        user_id, interaction_type, subject, message, priority
    ) VALUES (
        p_user_id, 'ticket', p_subject, p_message, p_priority
    ) RETURNING id INTO v_ticket_id;

    -- إرسال إشعار للمشرفين
    INSERT INTO notifications (user_id, title, body, type, data)
    SELECT
        id,
        'تذكرة دعم جديدة',
        'تم إنشاء تذكرة دعم جديدة من ' || (SELECT name FROM profiles WHERE id = p_user_id),
        'support',
        jsonb_build_object('ticket_id', v_ticket_id, 'priority', p_priority)
    FROM profiles
    WHERE profile_type = 'admin';

    RETURN v_ticket_id;
END;
$$;

-- دالة تحديث حالة تذكرة الدعم
CREATE OR REPLACE FUNCTION update_support_ticket_status(
    p_ticket_id UUID,
    p_status VARCHAR,
    p_resolution TEXT DEFAULT NULL,
    p_assigned_to UUID DEFAULT NULL
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    v_user_id UUID;
    v_row_count INTEGER;
BEGIN
    -- الحصول على معرف المستخدم
    SELECT user_id INTO v_user_id
    FROM support_interactions
    WHERE id = p_ticket_id;

    IF NOT FOUND THEN
        RETURN false;
    END IF;

    -- تحديث حالة التذكرة
    UPDATE support_interactions
    SET
        status = p_status,
        resolution = COALESCE(p_resolution, resolution),
        assigned_to = COALESCE(p_assigned_to, assigned_to),
        resolved_at = CASE WHEN p_status IN ('resolved', 'closed') THEN NOW() ELSE resolved_at END,
        updated_at = NOW()
    WHERE id = p_ticket_id;

    GET DIAGNOSTICS v_row_count = ROW_COUNT;

    IF v_row_count > 0 THEN
        -- إرسال إشعار للمستخدم
        PERFORM send_notification(
            v_user_id,
            'support_update',
            jsonb_build_object(
                'ticket_id', p_ticket_id,
                'status', p_status,
                'resolution', COALESCE(p_resolution, '')
            )
        );
        RETURN true;
    END IF;

    RETURN false;
END;
$$;

-- دالة إحصائيات الدعم الفني
CREATE OR REPLACE FUNCTION get_support_statistics(
    start_date TIMESTAMPTZ DEFAULT NULL,
    end_date TIMESTAMPTZ DEFAULT NULL
)
RETURNS TABLE (
    total_tickets BIGINT,
    open_tickets BIGINT,
    resolved_tickets BIGINT,
    average_resolution_time INTERVAL,
    satisfaction_average DECIMAL
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    start_date_filter TIMESTAMPTZ;
    end_date_filter TIMESTAMPTZ;
BEGIN
    start_date_filter := COALESCE(start_date, DATE_TRUNC('month', NOW()));
    end_date_filter := COALESCE(end_date, NOW());

    RETURN QUERY
    SELECT
        COUNT(*) as total_tickets,
        COUNT(*) FILTER (WHERE status IN ('open', 'in_progress')) as open_tickets,
        COUNT(*) FILTER (WHERE status IN ('resolved', 'closed')) as resolved_tickets,
        AVG(resolved_at - created_at) FILTER (WHERE resolved_at IS NOT NULL) as average_resolution_time,
        AVG(satisfaction_rating) FILTER (WHERE satisfaction_rating IS NOT NULL) as satisfaction_average
    FROM support_interactions
    WHERE created_at BETWEEN start_date_filter AND end_date_filter;
END;
$$;

-- دالة تنظيف البيانات الإدارية
CREATE OR REPLACE FUNCTION cleanup_admin_data()
RETURNS TABLE (
    table_name TEXT,
    deleted_count INTEGER
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    v_deleted_count INTEGER;
BEGIN
    -- تنظيف سجلات النسخ الاحتياطي القديمة
    DELETE FROM backup_logs WHERE started_at < NOW() - INTERVAL '3 months';
    GET DIAGNOSTICS v_deleted_count = ROW_COUNT;

    table_name := 'backup_logs';
    deleted_count := v_deleted_count;
    RETURN NEXT;

    -- تنظيف تذاكر الدعم المغلقة القديمة
    DELETE FROM support_interactions
    WHERE status = 'closed' AND updated_at < NOW() - INTERVAL '1 year';
    GET DIAGNOSTICS v_deleted_count = ROW_COUNT;

    table_name := 'support_interactions';
    deleted_count := v_deleted_count;
    RETURN NEXT;

    -- تنظيف سجلات التحليلات القديمة
    DELETE FROM user_behavior_analytics WHERE created_at < NOW() - INTERVAL '1 year';
    GET DIAGNOSTICS v_deleted_count = ROW_COUNT;

    table_name := 'user_behavior_analytics';
    deleted_count := v_deleted_count;
    RETURN NEXT;

    -- تنظيف مشاهدات المنتجات القديمة
    DELETE FROM product_views WHERE created_at < NOW() - INTERVAL '1 year';
    GET DIAGNOSTICS v_deleted_count = ROW_COUNT;

    table_name := 'product_views';
    deleted_count := v_deleted_count;
    RETURN NEXT;

    -- تنظيف سجل البحث القديم
    DELETE FROM smart_search_log WHERE created_at < NOW() - INTERVAL '6 months';
    GET DIAGNOSTICS v_deleted_count = ROW_COUNT;

    table_name := 'smart_search_log';
    deleted_count := v_deleted_count;
    RETURN NEXT;
END;
$$;

-- دالة إحصائيات النظام العامة
CREATE OR REPLACE FUNCTION get_system_statistics()
RETURNS TABLE (
    metric_name TEXT,
    metric_value BIGINT,
    metric_description TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    RETURN QUERY
    SELECT
        'total_users'::TEXT,
        COUNT(*)::BIGINT,
        'إجمالي عدد المستخدمين'::TEXT
    FROM profiles

    UNION ALL

    SELECT
        'total_products'::TEXT,
        COUNT(*)::BIGINT,
        'إجمالي عدد المنتجات'::TEXT
    FROM products

    UNION ALL

    SELECT
        'total_orders'::TEXT,
        COUNT(*)::BIGINT,
        'إجمالي عدد الطلبات'::TEXT
    FROM orders

    UNION ALL

    SELECT
        'active_users_today'::TEXT,
        COUNT(DISTINCT user_id)::BIGINT,
        'المستخدمون النشطون اليوم'::TEXT
    FROM user_behavior_analytics
    WHERE created_at >= CURRENT_DATE

    UNION ALL

    SELECT
        'orders_today'::TEXT,
        COUNT(*)::BIGINT,
        'طلبات اليوم'::TEXT
    FROM orders
    WHERE created_at >= CURRENT_DATE

    UNION ALL

    SELECT
        'revenue_today'::TEXT,
        COALESCE(SUM(total_amount), 0)::BIGINT,
        'إيرادات اليوم'::TEXT
    FROM orders
    WHERE created_at >= CURRENT_DATE AND status = 'delivered';
END;
$$;

-- ===================================================================
-- المحفزات
-- ===================================================================

-- محفزات تحديث الطوابع الزمنية
CREATE TRIGGER update_support_interactions_updated_at
    BEFORE UPDATE ON support_interactions
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- ===================================================================
-- سياسات الأمان (RLS)
-- ===================================================================

-- تفعيل RLS
ALTER TABLE support_interactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE backup_logs ENABLE ROW LEVEL SECURITY;

-- سياسات لتفاعلات الدعم الفني
CREATE POLICY "Users can view own support tickets" ON support_interactions FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can create support tickets" ON support_interactions FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Admins can manage all support tickets" ON support_interactions FOR ALL USING (
    EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND profile_type = 'admin')
);

-- سياسات لسجلات النسخ الاحتياطي
CREATE POLICY "Admins can manage backup logs" ON backup_logs FOR ALL USING (
    EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND profile_type = 'admin')
);

-- ===================================================================
-- قوالب إشعارات الدعم الفني
-- ===================================================================

INSERT INTO notification_templates (name, title_template, body_template, type, variables, is_active) VALUES
    ('support_ticket_created', 'تم إنشاء تذكرة دعم', 'تم إنشاء تذكرة دعم جديدة رقم {{ticket_id}}. سنتواصل معك قريباً.', 'support', '["ticket_id"]', true),
    ('support_update', 'تحديث تذكرة الدعم', 'تم تحديث حالة تذكرة الدعم {{ticket_id}} إلى: {{status}}', 'support', '["ticket_id", "status", "resolution"]', true),
    ('support_resolved', 'تم حل مشكلتك', 'تم حل تذكرة الدعم {{ticket_id}} بنجاح. {{resolution}}', 'support', '["ticket_id", "resolution"]', true)
ON CONFLICT (name) DO NOTHING;

-- ===================================================================
-- رسالة نجاح
-- ===================================================================

DO $$
BEGIN
    RAISE NOTICE '✅ تم إنشاء جداول الإدارة والدعم الفني بنجاح!';
    RAISE NOTICE '🎫 جدول تفاعلات الدعم الفني (support_interactions)';
    RAISE NOTICE '💾 جدول سجلات النسخ الاحتياطي (backup_logs)';
    RAISE NOTICE '⚡ تم إنشاء الدوال الإدارية';
    RAISE NOTICE '🔒 تم تطبيق سياسات الأمان';
    RAISE NOTICE '📝 تم إدراج قوالب إشعارات الدعم';
END $$;
