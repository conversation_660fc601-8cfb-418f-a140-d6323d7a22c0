import 'package:flutter/material.dart';
import 'package:motorcycle_parts_shop/core/services/notification_service.dart';
import 'package:motorcycle_parts_shop/core/theme/app_theme.dart';
import 'package:motorcycle_parts_shop/core/utils/responsive_helper.dart';
import 'package:motorcycle_parts_shop/core/widgets/empty_state_widget.dart';
import 'package:provider/provider.dart';

class NotificationsScreen extends StatefulWidget {
  const NotificationsScreen({super.key});

  @override
  State<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen> {
  late NotificationService _notificationService;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _initializeNotifications();
  }

  Future<void> _initializeNotifications() async {
    _notificationService = Provider.of<NotificationService>(
      context,
      listen: false,
    );

    if (!_notificationService.isInitialized) {
      await _notificationService.initialize();
    }

    if (mounted) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(80),
        child: Container(
          decoration: BoxDecoration(
            gradient: AppTheme.primaryGradient,
            boxShadow: AppTheme.cardShadow,
          ),
          child: SafeArea(
            child: ResponsiveBuilder(
              builder: (context, constraints) {
                final padding = ResponsiveHelper.getPadding(
                  context,
                  mobile: 12,
                  tablet: 16,
                  desktop: 20,
                );

                return Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: padding,
                    vertical: 8,
                  ),
                  child: Row(
                    children: [
                      // زر الرجوع المحسن
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: IconButton(
                          icon: const Icon(
                            Icons.arrow_back_ios_rounded,
                            color: AppTheme.textLightColor,
                          ),
                          onPressed: () => Navigator.pop(context),
                        ),
                      ),

                      const SizedBox(width: 16),

                      // أيقونة الإشعارات والعنوان
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Icon(
                          Icons.notifications_rounded,
                          color: AppTheme.textLightColor,
                          size: 24,
                        ),
                      ),

                      const SizedBox(width: 12),

                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              'الإشعارات',
                              style: AppTheme.cardTitle.copyWith(
                                color: AppTheme.textLightColor,
                                fontSize: 18,
                                fontWeight: FontWeight.w700,
                              ),
                            ),
                            Consumer<NotificationService>(
                              builder: (context, notificationService, child) {
                                final unreadCount =
                                    notificationService.notifications
                                        .where((n) => !n.isRead)
                                        .length;
                                return Text(
                                  unreadCount > 0
                                      ? '$unreadCount إشعار جديد'
                                      : 'جميع الإشعارات مقروءة',
                                  style: AppTheme.cardSubtitle.copyWith(
                                    color: AppTheme.textLightColor.withOpacity(
                                      0.8,
                                    ),
                                    fontSize: 12,
                                  ),
                                );
                              },
                            ),
                          ],
                        ),
                      ),

                      // زر تحديد الكل كمقروء
                      Consumer<NotificationService>(
                        builder: (context, notificationService, child) {
                          if (notificationService.notifications.isEmpty) {
                            return const SizedBox.shrink();
                          }
                          return Container(
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.15),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: Colors.white.withOpacity(0.2),
                                width: 1,
                              ),
                            ),
                            child: IconButton(
                              icon: Icon(
                                Icons.done_all_rounded,
                                color: AppTheme.textLightColor,
                                size: 22,
                              ),
                              onPressed: () async {
                                await notificationService.markAllAsRead();
                                if (!context.mounted) return;
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: Text(
                                      'تم تحديد جميع الإشعارات كمقروءة',
                                      style: AppTheme.buttonText,
                                    ),
                                    backgroundColor: AppTheme.successColor,
                                  ),
                                );
                              },
                              tooltip: 'تحديد الكل كمقروء',
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ),
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : Consumer<NotificationService>(
                builder: (context, notificationService, child) {
                  final notifications = notificationService.notifications;

                  if (notifications.isEmpty) {
                    return const EmptyStateWidget(
                      icon: Icons.notifications_off,
                      title: 'لا توجد إشعارات',
                      message: 'ستظهر هنا الإشعارات الخاصة بك عند وصولها',
                    );
                  }

                  return RefreshIndicator(
                    onRefresh: () async {
                      await _notificationService.initialize();
                    },
                    child: ListView.builder(
                      itemCount: notifications.length,
                      itemBuilder: (context, index) {
                        final notification = notifications[index];
                        final bool isRead = notification.isRead;
                        final DateTime createdAt = notification.createdAt;
                        final String timeAgo = _getTimeAgo(createdAt);

                        return Dismissible(
                          key: Key(notification.id),
                          background: Container(
                            color: Colors.red,
                            alignment: Alignment.centerRight,
                            padding: const EdgeInsets.only(right: 20.0),
                            child: const Icon(
                              Icons.delete,
                              color: Colors.white,
                            ),
                          ),
                          direction: DismissDirection.endToStart,
                          onDismissed: (direction) async {
                            await notificationService.deleteNotification(
                              notification.id,
                            );
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(content: Text('تم حذف الإشعار')),
                            );
                          },
                          child: ListTile(
                            leading: _getNotificationIcon(notification.type),
                            title: Text(
                              notification.title,
                              style: TextStyle(
                                fontWeight:
                                    isRead
                                        ? FontWeight.normal
                                        : FontWeight.bold,
                              ),
                            ),
                            subtitle: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(notification.body),
                                const SizedBox(height: 4),
                                Text(
                                  timeAgo,
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.grey[600],
                                  ),
                                ),
                              ],
                            ),
                            isThreeLine: true,
                            tileColor:
                                isRead
                                    ? null
                                    : Colors.blue.withValues(alpha: 0.05 * 255),
                            onTap: () async {
                              if (!isRead) {
                                await notificationService.markAsRead(
                                  notification.id,
                                );
                              }

                              // الانتقال إلى المحتوى المرتبط بنوع الإشعار والمعرف المرتبط
                              _navigateToRelatedContent(
                                context,
                                notification.type,
                                notification.data?['related_id'] ?? '',
                                notification.title,
                              );
                            },
                          ),
                        );
                      },
                    ),
                  );
                },
              ),
    );
  }

  Widget _getNotificationIcon(String type) {
    IconData iconData;
    Color iconColor;

    switch (type) {
      case 'order_update':
        iconData = Icons.local_shipping;
        iconColor = Colors.blue;
        break;
      case 'product_restock':
        iconData = Icons.inventory;
        iconColor = Colors.green;
        break;
      case 'price_drop':
        iconData = Icons.trending_down;
        iconColor = Colors.orange;
        break;
      case 'promotion':
        iconData = Icons.discount;
        iconColor = Colors.purple;
        break;
      case 'system':
      default:
        iconData = Icons.notifications;
        iconColor = Colors.grey;
        break;
    }

    return CircleAvatar(
      backgroundColor: iconColor.withValues(alpha: 0.1 * 255),
      child: Icon(iconData, color: iconColor),
    );
  }

  String _getTimeAgo(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 365) {
      return '${(difference.inDays / 365).floor()} سنة مضت';
    } else if (difference.inDays > 30) {
      return '${(difference.inDays / 30).floor()} شهر مضى';
    } else if (difference.inDays > 0) {
      return '${difference.inDays} يوم مضى';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} ساعة مضت';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} دقيقة مضت';
    } else {
      return 'الآن';
    }
  }

  /// الانتقال إلى المحتوى المرتبط بنوع الإشعار والمعرف المرتبط
  void _navigateToRelatedContent(
    BuildContext context,
    String type,
    String? relatedId,
    String title,
  ) {
    if (relatedId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('لا يمكن الانتقال: معرف المحتوى غير متوفر'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    switch (type) {
      case 'order_update':
        // الانتقال إلى صفحة تتبع الطلب
        Navigator.pushNamed(
          context,
          '/order-tracking',
          arguments: {'orderId': relatedId},
        );
        break;
      case 'product_restock':
        // الانتقال إلى صفحة تفاصيل المنتج
        Navigator.pushNamed(
          context,
          '/product-details',
          arguments: {'productId': relatedId},
        );
        break;
      case 'price_drop':
        // الانتقال إلى صفحة تفاصيل المنتج
        Navigator.pushNamed(
          context,
          '/product-details',
          arguments: {'productId': relatedId},
        );
        break;
      case 'promotion':
        // الانتقال إلى صفحة العروض
        Navigator.pushNamed(
          context,
          '/promotions',
          arguments: {'promotionId': relatedId},
        );
        break;
      case 'system':
      default:
        // عرض رسالة توضيحية
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('إشعار نظام: $title'),
            backgroundColor: Colors.blue,
          ),
        );
        break;
    }
  }
}
