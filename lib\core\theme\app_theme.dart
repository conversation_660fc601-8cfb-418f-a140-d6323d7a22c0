import 'gradient_button_theme.dart';
import 'gradients.dart';
import 'package:flutter/material.dart';

class AppTheme {
  // 🎨 نظام الألوان المتطور والاحترافي
  // الألوان الأساسية - تدرجات زرقاء عصرية وأنيقة
  static const Color primaryColor = Color(0xFF1E40AF); // أزرق داكن أنيق
  static const Color primaryLightColor = Color(0xFF3B82F6); // أزرق متوسط
  static const Color primaryExtraLightColor = Color(0xFF93C5FD); // أزرق فاتح
  static const Color primaryDarkColor = Color(0xFF1E3A8A); // أزرق داكن جداً

  // ألوان ثانوية - تدرجات برتقالية دافئة ومتوازنة
  static const Color secondaryColor = Color(0xFFEA580C); // برتقالي نابض
  static const Color secondaryLightColor = Color(0xFFFF8A50); // برتقالي فاتح
  static const Color secondaryDarkColor = Color(0xFFDC2626); // أحمر برتقالي
  static const Color accentColor = Color(0xFF0EA5E9); // أزرق فاتح عصري

  // ألوان الخلفية - تدرجات رمادية راقية ومريحة للعين
  static const Color backgroundColor = Color(0xFFF8FAFC); // رمادي فاتح جداً
  static const Color surfaceColor = Color(0xFFFFFFFF); // أبيض نقي
  static const Color cardColor = Color(0xFFFFFFFF); // أبيض للكروت
  static const Color dividerColor = Color(0xFFE2E8F0); // رمادي فاتح للفواصل

  // ألوان الحالة - ألوان معبرة وواضحة للمستخدم
  static const Color errorColor = Color(0xFFDC2626); // أحمر قوي للأخطاء
  static const Color successColor = Color(0xFF059669); // أخضر طبيعي للنجاح
  static const Color warningColor = Color(0xFFD97706); // برتقالي تحذيري
  static const Color infoColor = Color(0xFF0284C7); // أزرق معلوماتي

  // ألوان النصوص - تدرجات رمادية متوازنة وسهلة القراءة
  static const Color textPrimaryColor = Color(0xFF0F172A); // رمادي داكن جداً
  static const Color textSecondaryColor = Color(0xFF475569); // رمادي متوسط
  static const Color textTertiaryColor = Color(0xFF94A3B8); // رمادي فاتح
  static const Color textLightColor = Color(
    0xFFFFFFFF,
  ); // أبيض للنصوص على الخلفيات الداكنة
  static const Color textOnPrimaryColor = Color(
    0xFFFFFFFF,
  ); // أبيض على الألوان الأساسية

  // ألوان تفاعلية متطورة
  static const Color hoverColor = Color(0xFFF1F5F9); // لون التمرير
  static const Color focusColor = Color(0xFFDDD6FE); // لون التركيز
  static const Color disabledColor = Color(0xFFCBD5E1); // لون العناصر المعطلة
  static const Color shadowColor = Color(0x1A000000); // لون الظلال
  static const Color shimmerBaseColor = Color(0xFFE2E8F0); // لون قاعدة التحميل
  static const Color shimmerHighlightColor = Color(
    0xFFF1F5F9,
  ); // لون إبراز التحميل
  static const Color lightGreyColor = Color(0xFFE5E7EB); // رمادي فاتح قياسي

  // ألوان تحسين تجربة المستخدم المتقدمة
  static const Color overlayColor = Color(0x80000000); // خلفية شفافة للحوارات
  static const Color rippleColor = Color(0x1A000000); // تأثير النقر
  static const Color borderColor = Color(0xFFE5E7EB); // حدود العناصر
  static const Color loadingColor = Color(0xFF3B82F6); // لون التحميل
  static const Color retryColor = Color(0xFFEF4444); // لون إعادة المحاولة

  // ألوان حالة الاتصال
  static const Color onlineColor = Color(0xFF10B981); // متصل
  static const Color offlineColor = Color(0xFF6B7280); // غير متصل
  static const Color syncingColor = Color(0xFFF59E0B); // جاري المزامنة

  // 🎭 تدرجات لونية عصرية ومتطورة
  static const LinearGradient primaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [primaryColor, primaryLightColor],
    stops: [0.0, 1.0],
  );

  static const LinearGradient secondaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [secondaryColor, secondaryLightColor],
    stops: [0.0, 1.0],
  );

  static const LinearGradient accentGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [accentColor, primaryLightColor],
    stops: [0.0, 1.0],
  );

  static const LinearGradient cardGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [Color(0xFFFFFFFF), Color(0xFFF8FAFC)],
    stops: [0.0, 1.0],
  );

  static const LinearGradient shimmerGradient = LinearGradient(
    begin: Alignment(-1.0, -0.3),
    end: Alignment(1.0, 0.3),
    colors: [shimmerBaseColor, shimmerHighlightColor, shimmerBaseColor],
    stops: [0.0, 0.5, 1.0],
  );

  // Define common border radius values
  static final BorderRadius borderRadiusSmall = BorderRadius.circular(4.0);
  static final BorderRadius borderRadiusMedium = BorderRadius.circular(8.0);
  static final BorderRadius borderRadiusLarge = BorderRadius.circular(16.0);
  static final BorderRadius borderRadiusXLarge = BorderRadius.circular(24.0);

  // Define common elevation values
  static const double cardElevation = 2.0;

  // Define common shadow effects
  static final List<BoxShadow> smoothShadow = [
    BoxShadow(
      color: AppTheme.shadowColor.withOpacity(0.1),
      blurRadius: 10,
      offset: const Offset(0, 5),
    ),
  ];

  static final List<BoxShadow> subtleShadow = [
    BoxShadow(
      color: AppTheme.shadowColor.withOpacity(0.05),
      blurRadius: 5,
      offset: const Offset(0, 2),
    ),
  ];

  // 🌟 ظلال متدرجة ومتطورة
  static const List<BoxShadow> cardShadow = [
    BoxShadow(color: Color(0x0A000000), blurRadius: 10, offset: Offset(0, 4)),
    BoxShadow(color: Color(0x05000000), blurRadius: 20, offset: Offset(0, 8)),
  ];

  static const List<BoxShadow> elevatedShadow = [
    BoxShadow(color: Color(0x15000000), blurRadius: 20, offset: Offset(0, 10)),
    BoxShadow(color: Color(0x0A000000), blurRadius: 40, offset: Offset(0, 20)),
  ];

  // 📐 أنصاف أقطار متناسقة
  static const double radiusSmall = 8.0;
  static const double radiusMedium = 12.0;
  static const double radiusLarge = 16.0;
  static const double radiusXLarge = 24.0;

  // 📏 مسافات متناسقة
  static const double spacingXSmall = 4.0;
  static const double spacingSmall = 8.0;
  static const double spacingMedium = 16.0;
  static const double spacingLarge = 24.0;
  static const double spacingXLarge = 32.0;
  static const double spacingXXLarge = 48.0;

  // 🔤 أنماط النصوص المتطورة والاحترافية
  static TextTheme _buildTextTheme(TextTheme base, Color textColor) {
    return base.copyWith(
      // العناوين الكبيرة - للصفحات الرئيسية والعناوين المهمة
      displayLarge: base.displayLarge!.copyWith(
        fontFamily: 'Tajawal', // خط عربي أنيق وحديث
        fontSize: 32,
        fontWeight: FontWeight.w800,
        color: textColor,
        letterSpacing: -0.8,
        height: 1.2,
      ),
      displayMedium: base.displayMedium!.copyWith(
        fontFamily: 'Tajawal',
        fontSize: 28,
        fontWeight: FontWeight.w700,
        color: textColor,
        letterSpacing: -0.6,
        height: 1.25,
      ),
      displaySmall: base.displaySmall!.copyWith(
        fontFamily: 'Tajawal',
        fontSize: 24,
        fontWeight: FontWeight.w700,
        color: textColor,
        letterSpacing: -0.4,
        height: 1.3,
      ),

      // العناوين المتوسطة - للأقسام والفئات
      headlineLarge: base.headlineLarge!.copyWith(
        fontFamily: 'Tajawal',
        fontSize: 22,
        fontWeight: FontWeight.w600,
        color: textColor,
        letterSpacing: -0.2,
        height: 1.35,
      ),
      headlineMedium: base.headlineMedium!.copyWith(
        fontFamily: 'Tajawal',
        fontSize: 20,
        fontWeight: FontWeight.w600,
        color: textColor,
        height: 1.4,
      ),
      headlineSmall: base.headlineSmall!.copyWith(
        fontFamily: 'Tajawal',
        fontSize: 18,
        fontWeight: FontWeight.w600,
        color: textColor,
        height: 1.4,
      ),

      // العناوين الصغيرة - للكروت والعناصر
      titleLarge: base.titleLarge!.copyWith(
        fontFamily: 'Cairo', // خط القاهرة للنصوص المتوسطة
        fontSize: 18,
        fontWeight: FontWeight.w600,
        color: textColor,
        height: 1.45,
      ),
      titleMedium: base.titleMedium!.copyWith(
        fontFamily: 'Cairo',
        fontSize: 16,
        fontWeight: FontWeight.w500,
        color: textColor,
        height: 1.5,
      ),
      titleSmall: base.titleSmall!.copyWith(
        fontFamily: 'Cairo',
        fontSize: 14,
        fontWeight: FontWeight.w500,
        color: textColor,
        height: 1.5,
      ),

      // النصوص الأساسية - للمحتوى والوصف
      bodyLarge: base.bodyLarge!.copyWith(
        fontFamily: 'Noto Sans Arabic', // خط ناتو للنصوص الطويلة
        fontSize: 16,
        fontWeight: FontWeight.w400,
        color: textColor,
        height: 1.6,
        letterSpacing: 0.1,
      ),
      bodyMedium: base.bodyMedium!.copyWith(
        fontFamily: 'Noto Sans Arabic',
        fontSize: 14,
        fontWeight: FontWeight.w400,
        color: textColor,
        height: 1.6,
        letterSpacing: 0.1,
      ),
      bodySmall: base.bodySmall!.copyWith(
        fontFamily: 'Noto Sans Arabic',
        fontSize: 12,
        fontWeight: FontWeight.w400,
        color: textColor,
        height: 1.5,
        letterSpacing: 0.05,
      ),

      // التسميات والأزرار - للعناصر التفاعلية
      labelLarge: base.labelLarge!.copyWith(
        fontFamily: 'Cairo',
        fontSize: 14,
        fontWeight: FontWeight.w600,
        color: textColor,
        letterSpacing: 0.3,
        height: 1.4,
      ),
      labelMedium: base.labelMedium!.copyWith(
        fontFamily: 'Cairo',
        fontSize: 12,
        fontWeight: FontWeight.w500,
        color: textColor,
        letterSpacing: 0.25,
        height: 1.4,
      ),
      labelSmall: base.labelSmall!.copyWith(
        fontFamily: 'Cairo',
        fontSize: 10,
        fontWeight: FontWeight.w500,
        color: textColor,
        letterSpacing: 0.2,
        height: 1.3,
      ),
    );
  }

  // 🎨 أنماط نصوص مخصصة إضافية
  static TextStyle get heroTitle => const TextStyle(
    fontFamily: 'Tajawal',
    fontSize: 36,
    fontWeight: FontWeight.w900,
    color: textPrimaryColor,
    letterSpacing: -1.0,
    height: 1.1,
  );

  static TextStyle get cardTitle => const TextStyle(
    fontFamily: 'Cairo',
    fontSize: 16,
    fontWeight: FontWeight.w600,
    color: textPrimaryColor,
    height: 1.4,
  );

  static TextStyle get cardSubtitle => const TextStyle(
    fontFamily: 'Noto Sans Arabic',
    fontSize: 14,
    fontWeight: FontWeight.w400,
    color: textSecondaryColor,
    height: 1.5,
  );

  static TextStyle get buttonText => const TextStyle(
    fontFamily: 'Cairo',
    fontSize: 16,
    fontWeight: FontWeight.w600,
    color: textLightColor,
    letterSpacing: 0.2,
  );

  static TextStyle get priceText => const TextStyle(
    fontFamily: 'Cairo',
    fontSize: 18,
    fontWeight: FontWeight.w700,
    color: primaryColor,
    height: 1.2,
  );

  static TextStyle get discountText => const TextStyle(
    fontFamily: 'Cairo',
    fontSize: 14,
    fontWeight: FontWeight.w500,
    color: textTertiaryColor,
    decoration: TextDecoration.lineThrough,
    height: 1.2,
  );

  // تعريف ثيم الوضع العادي - تحسين المظهر العام
  // تعريف التدرجات اللونية المستخدمة في السمة
  static final BoxDecoration primaryGradientDecoration = BoxDecoration(
    gradient: AppGradients.primaryGradient,
    borderRadius: BorderRadius.circular(16),
    boxShadow: [
      BoxShadow(
        color: Colors.black.withOpacity(0.1),
        spreadRadius: 2,
        blurRadius: 10,
        offset: Offset(0, 4),
      ),
    ],
  );

  static final BoxDecoration secondaryGradientDecoration = BoxDecoration(
    gradient: AppGradients.secondaryGradient,
    borderRadius: BorderRadius.circular(16),
    boxShadow: [
      BoxShadow(
        color: Colors.black.withOpacity(0.1),
        spreadRadius: 2,
        blurRadius: 10,
        offset: Offset(0, 4),
      ),
    ],
  );

  static final BoxDecoration cardGradientDecoration = BoxDecoration(
    gradient: AppGradients.cardGradient,
    borderRadius: BorderRadius.circular(16),
    boxShadow: [
      BoxShadow(
        color: Colors.black.withOpacity(0.15),
        spreadRadius: 3,
        blurRadius: 12,
        offset: Offset(0, 6),
      ),
      BoxShadow(color: shadowColor, blurRadius: 10, offset: const Offset(0, 4)),
    ],
  );

  static ThemeData lightTheme() {
    final ThemeData base = ThemeData.light(useMaterial3: true);
    return base.copyWith(
      primaryColor: primaryColor,
      scaffoldBackgroundColor: backgroundColor,
      cardColor: cardColor,
      colorScheme: ColorScheme.fromSeed(
        seedColor: primaryColor,
        primary: primaryColor,
        secondary: secondaryColor,
        error: errorColor,
        background: backgroundColor,
        surface: cardColor,
        onSurface: textPrimaryColor,
        onBackground: textPrimaryColor,
        onPrimary: textLightColor,
        onSecondary: textLightColor,
        surfaceTint: primaryColor.withOpacity(0.05),
        primaryContainer: primaryLightColor,
        secondaryContainer: secondaryLightColor,
      ),
      cardTheme: CardTheme(
        elevation: 2,
        shadowColor: shadowColor,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        clipBehavior: Clip.antiAlias,
        margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 0),
        surfaceTintColor: Colors.transparent,
        color: Colors.transparent, // جعل لون البطاقة شفاف لإظهار التدرج
      ),
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: cardColor,
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 16,
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: dividerColor, width: 1.5),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: dividerColor, width: 1.5),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: primaryColor, width: 1.5),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: errorColor, width: 1.5),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: errorColor, width: 2.0),
        ),
        labelStyle: const TextStyle(
          fontFamily: 'Cairo',
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: textSecondaryColor,
        ),
        hintStyle: const TextStyle(
          fontFamily: 'Cairo',
          fontSize: 14,
          color: textSecondaryColor,
        ),
        prefixIconColor: textSecondaryColor,
        suffixIconColor: textSecondaryColor,
        floatingLabelStyle: const TextStyle(
          fontFamily: 'Cairo',
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: primaryColor,
        ),
        isDense: true,
      ),
      textTheme: _buildTextTheme(base.textTheme, textPrimaryColor),
      primaryTextTheme: _buildTextTheme(
        base.primaryTextTheme,
        textPrimaryColor,
      ),
      appBarTheme: AppBarTheme(
        color: primaryColor,
        elevation: 0,
        centerTitle: true,
        iconTheme: const IconThemeData(color: textLightColor),
        titleTextStyle: _buildTextTheme(
          base.textTheme,
          textLightColor,
        ).titleLarge!.copyWith(fontWeight: FontWeight.w600),
        toolbarHeight: 64,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ButtonStyle(
          // استخدام Material 3 للتدرجات اللونية في الأزرار
          backgroundColor: WidgetStateProperty.all(Colors.transparent),
          foregroundColor: WidgetStateProperty.all(textLightColor),
          textStyle: WidgetStateProperty.all(
            const TextStyle(
              fontFamily: 'Cairo',
              fontWeight: FontWeight.w600,
              fontSize: 16,
            ),
          ),
          padding: WidgetStateProperty.all(
            const EdgeInsets.symmetric(horizontal: 24, vertical: 14),
          ),
          shape: WidgetStateProperty.all(
            RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          ),
          elevation: WidgetStateProperty.all(2),
          shadowColor: WidgetStateProperty.all(primaryColor.withOpacity(0.3)),
          overlayColor: WidgetStateProperty.resolveWith<Color>((states) {
            if (states.contains(WidgetState.pressed)) {
              return Colors.white.withOpacity(0.1);
            }
            return Colors.transparent;
          }),
          // تطبيق التدرج اللوني باستخدام surfaceTintColor للشفافية
          surfaceTintColor: WidgetStateProperty.all(Colors.transparent),
        ),
      ),
      // إضافة مزود مخصص للأزرار ذات التدرج اللوني
      extensions: [
        GradientButtonTheme(
          decoration: BoxDecoration(
            gradient: AppGradients.buttonGradient,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: primaryColor.withOpacity(0.3),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
        ),
      ],
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: ButtonStyle(
          foregroundColor: WidgetStateProperty.all(primaryColor),
          side: WidgetStateProperty.all(
            const BorderSide(color: primaryColor, width: 1.5),
          ),
          textStyle: WidgetStateProperty.all(
            const TextStyle(
              fontFamily: 'Cairo',
              fontWeight: FontWeight.w600,
              fontSize: 16,
            ),
          ),
          padding: WidgetStateProperty.all(
            const EdgeInsets.symmetric(horizontal: 24, vertical: 14),
          ),
          shape: WidgetStateProperty.all(
            RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          ),
          overlayColor: WidgetStateProperty.resolveWith<Color>((states) {
            if (states.contains(WidgetState.pressed)) {
              return primaryLightColor.withOpacity(0.5);
            }
            return Colors.transparent;
          }),
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: ButtonStyle(
          foregroundColor: WidgetStateProperty.all(primaryColor),
          textStyle: WidgetStateProperty.all(
            const TextStyle(
              fontFamily: 'Cairo',
              fontWeight: FontWeight.w600,
              fontSize: 16,
            ),
          ),
          padding: WidgetStateProperty.all(
            const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          ),
          overlayColor: WidgetStateProperty.resolveWith<Color>((states) {
            if (states.contains(WidgetState.pressed)) {
              return primaryLightColor.withOpacity(0.3);
            }
            return Colors.transparent;
          }),
        ),
      ),
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        backgroundColor: cardColor,
        selectedItemColor: primaryColor,
        unselectedItemColor: textSecondaryColor,
        type: BottomNavigationBarType.fixed,
        elevation: 8,
        selectedLabelStyle: TextStyle(
          fontFamily: 'Cairo',
          fontSize: 12,
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelStyle: TextStyle(
          fontFamily: 'Cairo',
          fontSize: 12,
          fontWeight: FontWeight.w400,
        ),
      ),
      dividerTheme: const DividerThemeData(
        color: dividerColor,
        thickness: 1,
        space: 1,
      ),
      checkboxTheme: CheckboxThemeData(
        fillColor: WidgetStateProperty.resolveWith<Color>((
          Set<WidgetState> states,
        ) {
          if (states.contains(WidgetState.disabled)) {
            return textSecondaryColor.withOpacity(.32);
          }
          return primaryColor;
        }),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
      ),
      radioTheme: RadioThemeData(
        fillColor: WidgetStateProperty.resolveWith<Color>((
          Set<WidgetState> states,
        ) {
          if (states.contains(WidgetState.disabled)) {
            return textSecondaryColor.withOpacity(.32);
          }
          return primaryColor;
        }),
      ),
      switchTheme: SwitchThemeData(
        thumbColor: WidgetStateProperty.resolveWith<Color>((
          Set<WidgetState> states,
        ) {
          if (states.contains(WidgetState.disabled)) {
            return textSecondaryColor.withOpacity(.32);
          }
          if (states.contains(WidgetState.selected)) {
            return primaryColor;
          }
          return textSecondaryColor;
        }),
        trackColor: WidgetStateProperty.resolveWith<Color>((
          Set<WidgetState> states,
        ) {
          if (states.contains(WidgetState.disabled)) {
            return textSecondaryColor.withOpacity(.12);
          }
          if (states.contains(WidgetState.selected)) {
            return primaryColor.withOpacity(.5);
          }
          return textSecondaryColor.withOpacity(.38);
        }),
        trackOutlineColor: WidgetStateProperty.resolveWith<Color>((
          Set<WidgetState> states,
        ) {
          return Colors.transparent;
        }),
      ),
      // إضافة تأثيرات الظل والتحولات
      shadowColor: shadowColor,
      pageTransitionsTheme: const PageTransitionsTheme(
        builders: {
          TargetPlatform.android: CupertinoPageTransitionsBuilder(),
          TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
        },
      ),
      snackBarTheme: SnackBarThemeData(
        backgroundColor: textPrimaryColor,
        contentTextStyle: const TextStyle(
          fontFamily: 'Cairo',
          color: textLightColor,
          fontSize: 14,
        ),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        behavior: SnackBarBehavior.floating,
        elevation: 4,
      ),
      dialogTheme: DialogTheme(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        elevation: 8,
        backgroundColor: cardColor,
        titleTextStyle:
            _buildTextTheme(base.textTheme, textPrimaryColor).titleLarge,
      ),
    );
  }

  // تعريف ثيم الوضع الداكن - تحسين المظهر العام
  static ThemeData darkTheme() {
    final ThemeData base = ThemeData.dark(useMaterial3: true);
    const Color darkBackgroundColor = Color(0xFF121212);
    const Color darkCardColor = Color(0xFF1E1E1E);
    const Color darkTextColor = Color(0xFFE0E0E0);
    const Color darkDividerColor = Color(0xFF2C2C2C);

    return base.copyWith(
      primaryColor: primaryColor,
      scaffoldBackgroundColor: darkBackgroundColor,
      cardColor: darkCardColor,
      colorScheme: ColorScheme.fromSeed(
        brightness: Brightness.dark,
        seedColor: primaryColor,
        primary: primaryColor,
        secondary: secondaryColor,
        error: errorColor,
        background: darkBackgroundColor,
        surface: darkCardColor,
        onSurface: darkTextColor,
        onBackground: darkTextColor,
        onPrimary: textLightColor,
        onSecondary: textLightColor,
        surfaceTint: primaryColor.withOpacity(0.05),
        primaryContainer: primaryColor.withOpacity(0.2),
        secondaryContainer: secondaryColor.withOpacity(0.2),
      ),
      cardTheme: CardTheme(
        elevation: 4,
        shadowColor: Colors.black.withOpacity(0.4),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        clipBehavior: Clip.antiAlias,
        margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 0),
      ),
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: darkCardColor,
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 16,
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: darkDividerColor, width: 1.5),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: darkDividerColor, width: 1.5),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: primaryColor, width: 1.5),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: errorColor, width: 1.5),
        ),
        labelStyle: const TextStyle(
          fontFamily: 'Cairo',
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: Color(0xFFBDBDBD),
        ),
        hintStyle: const TextStyle(
          fontFamily: 'Cairo',
          fontSize: 14,
          color: Color(0xFFBDBDBD),
        ),
        prefixIconColor: Color(0xFFBDBDBD),
        suffixIconColor: Color(0xFFBDBDBD),
        floatingLabelStyle: const TextStyle(
          fontFamily: 'Cairo',
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: primaryColor,
        ),
      ),
      textTheme: _buildTextTheme(base.textTheme, darkTextColor),
      primaryTextTheme: _buildTextTheme(base.primaryTextTheme, darkTextColor),
      appBarTheme: AppBarTheme(
        color: darkCardColor,
        elevation: 0,
        centerTitle: true,
        iconTheme: const IconThemeData(color: darkTextColor),
        titleTextStyle: _buildTextTheme(
          base.textTheme,
          darkTextColor,
        ).titleLarge!.copyWith(fontWeight: FontWeight.w600),
        toolbarHeight: 64,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ButtonStyle(
          backgroundColor: WidgetStateProperty.all(Colors.transparent),
          foregroundColor: WidgetStateProperty.all(textLightColor),
          textStyle: WidgetStateProperty.all(
            const TextStyle(
              fontFamily: 'Cairo',
              fontWeight: FontWeight.w600,
              fontSize: 16,
            ),
          ),
          padding: WidgetStateProperty.all(
            const EdgeInsets.symmetric(horizontal: 24, vertical: 14),
          ),
          shape: WidgetStateProperty.all(
            RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          ),
          elevation: WidgetStateProperty.all(4),
          shadowColor: WidgetStateProperty.all(Colors.black.withOpacity(0.3)),
          surfaceTintColor: WidgetStateProperty.all(Colors.transparent),
        ),
      ),
      // إضافة مزود مخصص للأزرار ذات التدرج اللوني في الوضع الداكن
      extensions: [
        GradientButtonTheme(
          decoration: BoxDecoration(
            gradient: AppGradients.buttonGradient,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.4),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
        ),
      ],
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: ButtonStyle(
          foregroundColor: WidgetStateProperty.all(primaryColor),
          side: WidgetStateProperty.all(
            const BorderSide(color: primaryColor, width: 1.5),
          ),
          textStyle: WidgetStateProperty.all(
            const TextStyle(
              fontFamily: 'Cairo',
              fontWeight: FontWeight.w600,
              fontSize: 16,
            ),
          ),
          padding: WidgetStateProperty.all(
            const EdgeInsets.symmetric(horizontal: 24, vertical: 14),
          ),
          shape: WidgetStateProperty.all(
            RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          ),
          overlayColor: WidgetStateProperty.resolveWith<Color>((states) {
            if (states.contains(WidgetState.pressed)) {
              return primaryLightColor.withOpacity(0.5);
            }
            return Colors.transparent;
          }),
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: ButtonStyle(
          foregroundColor: WidgetStateProperty.all(primaryColor),
          textStyle: WidgetStateProperty.all(
            const TextStyle(
              fontFamily: 'Cairo',
              fontWeight: FontWeight.w600,
              fontSize: 16,
            ),
          ),
          padding: WidgetStateProperty.all(
            const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          ),
          overlayColor: WidgetStateProperty.resolveWith<Color>((states) {
            if (states.contains(WidgetState.pressed)) {
              return primaryLightColor.withOpacity(0.3);
            }
            return Colors.transparent;
          }),
        ),
      ),
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        backgroundColor: darkCardColor,
        selectedItemColor: primaryColor,
        unselectedItemColor: Color(0xFFBDBDBD),
        type: BottomNavigationBarType.fixed,
        elevation: 8,
        selectedLabelStyle: TextStyle(
          fontFamily: 'Cairo',
          fontSize: 12,
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelStyle: TextStyle(
          fontFamily: 'Cairo',
          fontSize: 12,
          fontWeight: FontWeight.w400,
        ),
      ),
      dividerTheme: const DividerThemeData(
        color: darkDividerColor,
        thickness: 1,
        space: 1,
      ),
      checkboxTheme: CheckboxThemeData(
        fillColor: WidgetStateProperty.resolveWith<Color>((
          Set<WidgetState> states,
        ) {
          if (states.contains(WidgetState.disabled)) {
            return Color(0xFFBDBDBD).withOpacity(.32);
          }
          return primaryColor;
        }),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
      ),
      radioTheme: RadioThemeData(
        fillColor: WidgetStateProperty.resolveWith<Color>((
          Set<WidgetState> states,
        ) {
          if (states.contains(WidgetState.disabled)) {
            return Color(0xFFBDBDBD).withOpacity(.32);
          }
          return primaryColor;
        }),
      ),
      switchTheme: SwitchThemeData(
        thumbColor: WidgetStateProperty.resolveWith<Color>((
          Set<WidgetState> states,
        ) {
          if (states.contains(WidgetState.disabled)) {
            return Color(0xFFBDBDBD).withOpacity(.32);
          }
          if (states.contains(WidgetState.selected)) {
            return primaryColor;
          }
          return Color(0xFFBDBDBD);
        }),
        trackColor: WidgetStateProperty.resolveWith<Color>((
          Set<WidgetState> states,
        ) {
          if (states.contains(WidgetState.disabled)) {
            return Color(0xFFBDBDBD).withOpacity(.12);
          }
          if (states.contains(WidgetState.selected)) {
            return primaryColor.withOpacity(.5);
          }
          return Color(0xFFBDBDBD).withOpacity(.38);
        }),
        trackOutlineColor: WidgetStateProperty.resolveWith<Color>((
          Set<WidgetState> states,
        ) {
          return Colors.transparent;
        }),
      ),
      // إضافة تأثيرات الظل والتحولات
      shadowColor: Colors.black.withOpacity(0.2),
      pageTransitionsTheme: const PageTransitionsTheme(
        builders: {
          TargetPlatform.android: CupertinoPageTransitionsBuilder(),
          TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
        },
      ),
      snackBarTheme: SnackBarThemeData(
        backgroundColor: darkCardColor,
        contentTextStyle: const TextStyle(
          fontFamily: 'Cairo',
          color: darkTextColor,
          fontSize: 14,
        ),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        behavior: SnackBarBehavior.floating,
        elevation: 4,
      ),
      dialogTheme: DialogTheme(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        elevation: 8,
        backgroundColor: darkCardColor,
        titleTextStyle:
            _buildTextTheme(base.textTheme, darkTextColor).titleLarge,
      ),
    );
  }
}
