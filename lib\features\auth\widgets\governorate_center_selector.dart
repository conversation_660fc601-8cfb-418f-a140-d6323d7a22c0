import '../../../core/constants/governorates_constants.dart';
import 'package:flutter/material.dart';

/// Widget لاختيار المحافظة والمركز
class GovernorateCenterSelector extends StatefulWidget {
  final String? selectedGovernorate;
  final String? selectedCenter;
  final Function(String?) onGovernorateChanged;
  final Function(String?) onCenterChanged;
  final String? governorateLabel;
  final String? centerLabel;
  final String? governorateHint;
  final String? centerHint;
  final bool isRequired;

  const GovernorateCenterSelector({
    super.key,
    this.selectedGovernorate,
    this.selectedCenter,
    required this.onGovernorateChanged,
    required this.onCenterChanged,
    this.governorateLabel = 'المحافظة',
    this.centerLabel = 'المركز/المدينة',
    this.governorateHint = 'اختر المحافظة',
    this.centerHint = 'اختر المركز أو المدينة',
    this.isRequired = true,
  });

  @override
  State<GovernorateCenterSelector> createState() =>
      _GovernorateCenterSelectorState();
}

class _GovernorateCenterSelectorState extends State<GovernorateCenterSelector> {
  String? _selectedGovernorate;
  String? _selectedCenter;
  List<String> _availableCenters = [];

  @override
  void initState() {
    super.initState();
    _selectedGovernorate = widget.selectedGovernorate;
    _selectedCenter = widget.selectedCenter;
    _initializeAvailableCenters();
  }

  @override
  void didUpdateWidget(GovernorateCenterSelector oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.selectedGovernorate != widget.selectedGovernorate) {
      _selectedGovernorate = widget.selectedGovernorate;
      _updateAvailableCenters();
    }
    if (oldWidget.selectedCenter != widget.selectedCenter) {
      _selectedCenter = widget.selectedCenter;
    }
  }

  void _initializeAvailableCenters() {
    if (_selectedGovernorate != null) {
      _availableCenters = GovernoratesConstants.getCentersByGovernorateName(
        _selectedGovernorate!,
      );
      // إذا كان المركز المحدد غير متوفر في المحافظة الجديدة، قم بإلغاء تحديده
      if (_selectedCenter != null &&
          !_availableCenters.contains(_selectedCenter)) {
        _selectedCenter = null;
      }
    } else {
      _availableCenters = [];
      _selectedCenter = null;
    }
  }

  void _updateAvailableCenters() {
    if (_selectedGovernorate != null) {
      _availableCenters = GovernoratesConstants.getCentersByGovernorateName(
        _selectedGovernorate!,
      );
      // إذا كان المركز المحدد غير متوفر في المحافظة الجديدة، قم بإلغاء تحديده
      if (_selectedCenter != null &&
          !_availableCenters.contains(_selectedCenter)) {
        _selectedCenter = null;
        WidgetsBinding.instance.addPostFrameCallback((_) {
          widget.onCenterChanged(null);
        });
      }
    } else {
      _availableCenters = [];
      _selectedCenter = null;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        widget.onCenterChanged(null);
      });
    }
    if (mounted) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // اختيار المحافظة
        _buildGovernorateDropdown(),

        const SizedBox(height: 16),

        // اختيار المركز
        _buildCenterDropdown(),
      ],
    );
  }

  Widget _buildGovernorateDropdown() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.governorateLabel != null) ...[
          Row(
            children: [
              Text(
                widget.governorateLabel!,
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
              ),
              if (widget.isRequired)
                Text(
                  ' *',
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.error,
                    fontSize: 16,
                  ),
                ),
            ],
          ),
          const SizedBox(height: 8),
        ],
        Container(
          decoration: BoxDecoration(
            border: Border.all(
              color: Theme.of(context).colorScheme.outline.withOpacity(0.5),
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          child: DropdownButtonFormField<String>(
            value: _selectedGovernorate,
            hint: Text(
              widget.governorateHint ?? 'اختر المحافظة',
              style: TextStyle(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
              ),
            ),
            decoration: const InputDecoration(
              border: InputBorder.none,
              contentPadding: EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 12,
              ),
            ),
            isExpanded: true,
            items:
                GovernoratesConstants.getSortedGovernorates()
                    .map(
                      (governorate) => DropdownMenuItem<String>(
                        value: governorate.name,
                        child: Text(
                          governorate.name,
                          style: const TextStyle(fontSize: 16),
                        ),
                      ),
                    )
                    .toList(),
            onChanged: (String? newValue) {
              setState(() {
                _selectedGovernorate = newValue;
                _updateAvailableCenters();
              });
              widget.onGovernorateChanged(newValue);
            },
            validator:
                widget.isRequired
                    ? (value) {
                      if (value == null || value.isEmpty) {
                        return 'يرجى اختيار المحافظة';
                      }
                      return null;
                    }
                    : null,
          ),
        ),
      ],
    );
  }

  Widget _buildCenterDropdown() {
    final bool isEnabled =
        _selectedGovernorate != null && _availableCenters.isNotEmpty;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.centerLabel != null) ...[
          Row(
            children: [
              Text(
                widget.centerLabel!,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color:
                      isEnabled
                          ? null
                          : Theme.of(
                            context,
                          ).colorScheme.onSurface.withOpacity(0.4),
                ),
              ),
              if (widget.isRequired)
                Text(
                  ' *',
                  style: TextStyle(
                    color:
                        isEnabled
                            ? Theme.of(context).colorScheme.error
                            : Theme.of(
                              context,
                            ).colorScheme.error.withOpacity(0.4),
                    fontSize: 16,
                  ),
                ),
            ],
          ),
          const SizedBox(height: 8),
        ],
        Container(
          decoration: BoxDecoration(
            border: Border.all(
              color:
                  isEnabled
                      ? Theme.of(context).colorScheme.outline.withOpacity(0.5)
                      : Theme.of(context).colorScheme.outline.withOpacity(0.2),
            ),
            borderRadius: BorderRadius.circular(12),
            color:
                isEnabled
                    ? null
                    : Theme.of(context).colorScheme.surface.withOpacity(0.5),
          ),
          child: DropdownButtonFormField<String>(
            value: _selectedCenter,
            hint: Text(
              _selectedGovernorate == null
                  ? 'اختر المحافظة أولاً'
                  : (widget.centerHint ?? 'اختر المركز أو المدينة'),
              style: TextStyle(
                color: Theme.of(
                  context,
                ).colorScheme.onSurface.withOpacity(isEnabled ? 0.6 : 0.3),
              ),
            ),
            decoration: const InputDecoration(
              border: InputBorder.none,
              contentPadding: EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 12,
              ),
            ),
            isExpanded: true,
            items:
                isEnabled
                    ? _availableCenters
                        .map(
                          (center) => DropdownMenuItem<String>(
                            value: center,
                            child: Text(
                              center,
                              style: const TextStyle(fontSize: 16),
                            ),
                          ),
                        )
                        .toList()
                    : [],
            onChanged:
                isEnabled
                    ? (String? newValue) {
                      setState(() {
                        _selectedCenter = newValue;
                      });
                      widget.onCenterChanged(newValue);
                    }
                    : null,
            validator:
                widget.isRequired
                    ? (value) {
                      if (!isEnabled) return null; // لا نتحقق إذا كان غير مفعل
                      if (value == null || value.isEmpty) {
                        return 'يرجى اختيار المركز أو المدينة';
                      }
                      return null;
                    }
                    : null,
          ),
        ),
        if (_selectedGovernorate != null && _availableCenters.isEmpty)
          Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Text(
              'لا توجد مراكز متاحة لهذه المحافظة',
              style: TextStyle(
                color: Theme.of(context).colorScheme.error,
                fontSize: 12,
              ),
            ),
          ),
      ],
    );
  }
}

/// Widget مبسط لاختيار المحافظة فقط
class GovernorateSelector extends StatelessWidget {
  final String? selectedGovernorate;
  final Function(String?) onChanged;
  final String? label;
  final String? hint;
  final bool isRequired;

  const GovernorateSelector({
    super.key,
    this.selectedGovernorate,
    required this.onChanged,
    this.label = 'المحافظة',
    this.hint = 'اختر المحافظة',
    this.isRequired = true,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (label != null) ...[
          Row(
            children: [
              Text(
                label!,
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
              ),
              if (isRequired)
                Text(
                  ' *',
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.error,
                    fontSize: 16,
                  ),
                ),
            ],
          ),
          const SizedBox(height: 8),
        ],
        Container(
          decoration: BoxDecoration(
            border: Border.all(
              color: Theme.of(context).colorScheme.outline.withOpacity(0.5),
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          child: DropdownButtonFormField<String>(
            value: selectedGovernorate,
            hint: Text(
              hint ?? 'اختر المحافظة',
              style: TextStyle(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
              ),
            ),
            decoration: const InputDecoration(
              border: InputBorder.none,
              contentPadding: EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 12,
              ),
            ),
            isExpanded: true,
            items:
                GovernoratesConstants.getSortedGovernorates()
                    .map(
                      (governorate) => DropdownMenuItem<String>(
                        value: governorate.name,
                        child: Text(
                          governorate.name,
                          style: const TextStyle(fontSize: 16),
                        ),
                      ),
                    )
                    .toList(),
            onChanged: onChanged,
            validator:
                isRequired
                    ? (value) {
                      if (value == null || value.isEmpty) {
                        return 'يرجى اختيار المحافظة';
                      }
                      return null;
                    }
                    : null,
          ),
        ),
      ],
    );
  }
}
