import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:motorcycle_parts_shop/core/services/auth_supabase_service.dart';
import 'package:motorcycle_parts_shop/core/services/cart_service.dart';
import 'package:motorcycle_parts_shop/core/services/currency_service.dart';
import 'package:motorcycle_parts_shop/core/services/favorites_service.dart';
import 'package:motorcycle_parts_shop/core/services/user_interaction_service.dart';
import 'package:motorcycle_parts_shop/core/theme/app_theme.dart';
import 'package:motorcycle_parts_shop/core/utils/responsive_helper.dart';
import 'package:motorcycle_parts_shop/core/widgets/loading_indicator.dart'; // Import LoadingIndicator
import 'package:motorcycle_parts_shop/models/product_model.dart';
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';

class ProductDetailsScreen extends StatefulWidget {
  final ProductModel product;

  const ProductDetailsScreen({super.key, required this.product});

  @override
  State<ProductDetailsScreen> createState() => _ProductDetailsScreenState();
}

class _ProductDetailsScreenState extends State<ProductDetailsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  int _quantity = 1;
  late UserInteractionService _userInteractionService;
  String? _userId;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  int _currentImageIndex = 0;
  bool _isFavorite = false;
  bool _isLoading = true; // Add loading state

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeIn),
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.1),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );
    _animationController.forward();
    _initializeScreenData(); // Call a new method to handle async initialization
  }

  Future<void> _initializeScreenData() async {
    await _checkFavoriteStatus();
    _recordProductView();
    if (mounted) {
      setState(() {
        _isLoading = false; // Set loading to false after data is loaded
      });
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final supabaseService = Provider.of<AuthSupabaseService>(
      context,
      listen: false,
    );

    _userInteractionService = UserInteractionService(supabaseService.client);
    _userId = supabaseService.currentUser?.id;

    // تسجيل مشاهدة المنتج
    _recordProductView();
  }

  // تسجيل مشاهدة المنتج
  void _recordProductView() {
    if (_userId != null) {
      _userInteractionService.recordProductView(
        userId: _userId!,
        productId: widget.product.id,
      );
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _checkFavoriteStatus() async {
    final favoriteService = Provider.of<FavoritesService>(
      context,
      listen: false,
    );
    final isFavorite = favoriteService.isFavorite(widget.product.id);
    setState(() {
      _isFavorite = isFavorite;
    });
  }

  Future<void> _toggleFavorite() async {
    final favoriteService = Provider.of<FavoritesService>(
      context,
      listen: false,
    );
    if (_isFavorite) {
      await favoriteService.toggleFavorite(widget.product.id);
    } else {
      await favoriteService.toggleFavorite(widget.product.id);
    }
    setState(() {
      _isFavorite = !_isFavorite;
    });
  }

  // مشاركة المنتج
  void _shareProduct() {
    final String text =
        "${widget.product.name} - ${CurrencyService().formatPrice(widget.product.price)}\n\n${widget.product.description}";
    Share.share(text);

    // تسجيل تفاعل المشاركة
    if (_userId != null) {
      _userInteractionService.recordSearch(
        userId: _userId!,
        searchQuery: 'share_product_${widget.product.id}',
      );
    }
  }

  // إضافة المنتج إلى السلة
  Future<void> _addToCart() async {
    setState(() {});

    try {
      final cartService = Provider.of<CartService>(context, listen: false);
      await cartService.addToCart(widget.product, quantity: _quantity);

      // تسجيل تفاعل إضافة للسلة
      if (_userId != null) {
        _userInteractionService.recordAddToCart(
          userId: _userId!,
          productId: widget.product.id,
          quantity: _quantity,
        );
      }

      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('تمت إضافة ${widget.product.name} إلى السلة'),
          backgroundColor: AppTheme.successColor,
          duration: const Duration(seconds: 2),
        ),
      );
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('حدث خطأ أثناء إضافة المنتج إلى السلة'),
          backgroundColor: AppTheme.errorColor,
        ),
      );
    } finally {
      if (mounted) {
        setState(() {});
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body:
          _isLoading
              ? const Center(child: LoadingIndicator())
              : CustomScrollView(
                slivers: [
                  // شريط التطبيق المتطور مع الصور
                  SliverAppBar(
                    expandedHeight: ResponsiveHelper.getAppBarHeight(context),
                    pinned: true,
                    elevation: 0,
                    backgroundColor: AppTheme.primaryColor,
                    leading: Container(
                      margin: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.3),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: IconButton(
                        icon: const Icon(
                          Icons.arrow_back_ios_rounded,
                          color: Colors.white,
                        ),
                        onPressed: () => Navigator.pop(context),
                      ),
                    ),
                    flexibleSpace: FlexibleSpaceBar(
                      background: Stack(
                        fit: StackFit.expand,
                        children: [
                          // خلفية متدرجة
                          Container(
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.topCenter,
                                end: Alignment.bottomCenter,
                                colors: [
                                  AppTheme.primaryColor.withOpacity(0.8),
                                  Colors.transparent,
                                ],
                              ),
                            ),
                          ),

                          // عرض الصور المحسن
                          CarouselSlider(
                            options: CarouselOptions(
                              height: ResponsiveHelper.getAppBarHeight(context),
                              viewportFraction: 1.0,
                              enableInfiniteScroll:
                                  widget.product.images.length > 1,
                              autoPlay: widget.product.images.length > 1,
                              autoPlayInterval: const Duration(seconds: 4),
                              onPageChanged: (index, reason) {
                                setState(() {
                                  _currentImageIndex = index;
                                });
                              },
                            ),
                            items:
                                widget.product.images.map((image) {
                                  return Hero(
                                    tag: 'product_image_${widget.product.id}',
                                    child: Container(
                                      decoration: BoxDecoration(
                                        borderRadius: const BorderRadius.only(
                                          bottomLeft: Radius.circular(24),
                                          bottomRight: Radius.circular(24),
                                        ),
                                        boxShadow: AppTheme.elevatedShadow,
                                      ),
                                      child: ClipRRect(
                                        borderRadius: const BorderRadius.only(
                                          bottomLeft: Radius.circular(24),
                                          bottomRight: Radius.circular(24),
                                        ),
                                        child: Image.network(
                                          image,
                                          fit: BoxFit.cover,
                                          errorBuilder: (
                                            context,
                                            error,
                                            stackTrace,
                                          ) {
                                            return Container(
                                              decoration: BoxDecoration(
                                                gradient: AppTheme.cardGradient,
                                                borderRadius:
                                                    const BorderRadius.only(
                                                      bottomLeft:
                                                          Radius.circular(24),
                                                      bottomRight:
                                                          Radius.circular(24),
                                                    ),
                                              ),
                                              child: Center(
                                                child: Column(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.center,
                                                  children: [
                                                    Icon(
                                                      Icons
                                                          .image_not_supported_rounded,
                                                      size: 64,
                                                      color:
                                                          AppTheme
                                                              .textTertiaryColor,
                                                    ),
                                                    const SizedBox(height: 8),
                                                    Text(
                                                      'لا توجد صورة',
                                                      style:
                                                          AppTheme.cardSubtitle,
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            );
                                          },
                                        ),
                                      ),
                                    ),
                                  );
                                }).toList(),
                          ),

                          // مؤشرات الصور المحسنة
                          if (widget.product.images.length > 1)
                            Positioned(
                              bottom: 20,
                              left: 0,
                              right: 0,
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children:
                                    widget.product.images.asMap().entries.map((
                                      entry,
                                    ) {
                                      return AnimatedContainer(
                                        duration: const Duration(
                                          milliseconds: 300,
                                        ),
                                        width:
                                            _currentImageIndex == entry.key
                                                ? 24
                                                : 8,
                                        height: 8,
                                        margin: const EdgeInsets.symmetric(
                                          horizontal: 4,
                                        ),
                                        decoration: BoxDecoration(
                                          borderRadius: BorderRadius.circular(
                                            4,
                                          ),
                                          color:
                                              _currentImageIndex == entry.key
                                                  ? AppTheme.secondaryColor
                                                  : Colors.white.withOpacity(
                                                    0.5,
                                                  ),
                                          boxShadow:
                                              _currentImageIndex == entry.key
                                                  ? [
                                                    BoxShadow(
                                                      color: AppTheme
                                                          .secondaryColor
                                                          .withOpacity(0.4),
                                                      blurRadius: 8,
                                                      offset: const Offset(
                                                        0,
                                                        2,
                                                      ),
                                                    ),
                                                  ]
                                                  : null,
                                        ),
                                      );
                                    }).toList(),
                              ),
                            ),
                        ],
                      ),
                    ),
                    actions: [
                      // زر المفضلة المحسن
                      Container(
                        margin: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.3),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: IconButton(
                          icon: AnimatedSwitcher(
                            duration: const Duration(milliseconds: 300),
                            child: Icon(
                              _isFavorite
                                  ? Icons.favorite_rounded
                                  : Icons.favorite_border_rounded,
                              key: ValueKey(_isFavorite),
                              color:
                                  _isFavorite
                                      ? AppTheme.secondaryColor
                                      : Colors.white,
                              size: 24,
                            ),
                          ),
                          onPressed: _toggleFavorite,
                        ),
                      ),

                      // زر المشاركة المحسن
                      Container(
                        margin: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.3),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: IconButton(
                          icon: const Icon(
                            Icons.share_rounded,
                            color: Colors.white,
                          ),
                          onPressed: _shareProduct,
                        ),
                      ),
                    ],
                  ),
                  SliverToBoxAdapter(
                    child: FadeTransition(
                      opacity: _fadeAnimation,
                      child: SlideTransition(
                        position: _slideAnimation,
                        child: Container(
                          margin: const EdgeInsets.only(top: 16),
                          decoration: BoxDecoration(
                            gradient: AppTheme.cardGradient,
                            borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(24),
                              topRight: Radius.circular(24),
                            ),
                            boxShadow: AppTheme.elevatedShadow,
                          ),
                          child: Padding(
                            padding: EdgeInsets.all(
                              ResponsiveHelper.getPadding(
                                context,
                                mobile: 16,
                                tablet: 20,
                                desktop: 24,
                              ),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // اسم المنتج والعلامة التجارية
                                Row(
                                  children: [
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            widget.product.name,
                                            style: AppTheme.heroTitle.copyWith(
                                              fontSize:
                                                  ResponsiveHelper.getFontSize(
                                                    context,
                                                    ResponsiveHelper.isMobile(
                                                          context,
                                                        )
                                                        ? 20
                                                        : 24,
                                                  ),
                                              color: AppTheme.textPrimaryColor,
                                            ),
                                          ),
                                          SizedBox(
                                            height:
                                                ResponsiveHelper.isMobile(
                                                      context,
                                                    )
                                                    ? 6
                                                    : 8,
                                          ),
                                          Container(
                                            padding: EdgeInsets.symmetric(
                                              horizontal:
                                                  ResponsiveHelper.isMobile(
                                                        context,
                                                      )
                                                      ? 10
                                                      : 12,
                                              vertical:
                                                  ResponsiveHelper.isMobile(
                                                        context,
                                                      )
                                                      ? 4
                                                      : 6,
                                            ),
                                            decoration: BoxDecoration(
                                              gradient: AppTheme.accentGradient,
                                              borderRadius:
                                                  BorderRadius.circular(8),
                                            ),
                                            child: Text(
                                              widget.product.brand,
                                              style: AppTheme.buttonText.copyWith(
                                                fontSize:
                                                    ResponsiveHelper.getFontSize(
                                                      context,
                                                      ResponsiveHelper.isMobile(
                                                            context,
                                                          )
                                                          ? 12
                                                          : 14,
                                                    ),
                                                fontWeight: FontWeight.w600,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    // حالة التوفر
                                    Container(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 12,
                                        vertical: 8,
                                      ),
                                      decoration: BoxDecoration(
                                        gradient:
                                            widget.product.isAvailable
                                                ? const LinearGradient(
                                                  colors: [
                                                    AppTheme.successColor,
                                                    Color(0xFF10B981),
                                                  ],
                                                )
                                                : const LinearGradient(
                                                  colors: [
                                                    AppTheme.errorColor,
                                                    Color(0xFFEF4444),
                                                  ],
                                                ),
                                        borderRadius: BorderRadius.circular(12),
                                        boxShadow: [
                                          BoxShadow(
                                            color: (widget.product.isAvailable
                                                    ? AppTheme.successColor
                                                    : AppTheme.errorColor)
                                                .withOpacity(0.3),
                                            blurRadius: 8,
                                            offset: const Offset(0, 2),
                                          ),
                                        ],
                                      ),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Icon(
                                            widget.product.isAvailable
                                                ? Icons.check_circle_rounded
                                                : Icons.cancel_rounded,
                                            color: AppTheme.textLightColor,
                                            size: 16,
                                          ),
                                          const SizedBox(width: 6),
                                          Text(
                                            widget.product.isAvailable
                                                ? 'متوفر'
                                                : 'غير متوفر',
                                            style: AppTheme.buttonText.copyWith(
                                              fontSize: 12,
                                              fontWeight: FontWeight.w600,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),

                                const SizedBox(height: 24),

                                // السعر والتقييم
                                ResponsiveBuilder(
                                  builder: (context, constraints) {
                                    final isMobile = ResponsiveHelper.isMobile(
                                      context,
                                    );
                                    final padding = isMobile ? 12.0 : 16.0;
                                    final spacing = isMobile ? 12.0 : 16.0;

                                    return Row(
                                      children: [
                                        Expanded(
                                          child: Container(
                                            padding: EdgeInsets.all(padding),
                                            decoration: BoxDecoration(
                                              gradient:
                                                  AppTheme.primaryGradient,
                                              borderRadius:
                                                  BorderRadius.circular(16),
                                              boxShadow: AppTheme.cardShadow,
                                            ),
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  'السعر',
                                                  style: AppTheme.cardSubtitle
                                                      .copyWith(
                                                        color: AppTheme
                                                            .textLightColor
                                                            .withOpacity(0.9),
                                                        fontSize:
                                                            ResponsiveHelper.getFontSize(
                                                              context,
                                                              12,
                                                            ),
                                                      ),
                                                ),
                                                SizedBox(
                                                  height: isMobile ? 2 : 4,
                                                ),
                                                if (widget
                                                        .product
                                                        .discountPrice !=
                                                    null) ...[
                                                  Text(
                                                    '${widget.product.discountPrice!.toStringAsFixed(0)} ج.م',
                                                    style: AppTheme.priceText
                                                        .copyWith(
                                                          color:
                                                              AppTheme
                                                                  .textLightColor,
                                                          fontSize:
                                                              ResponsiveHelper.getFontSize(
                                                                context,
                                                                isMobile
                                                                    ? 16
                                                                    : 20,
                                                              ),
                                                          fontWeight:
                                                              FontWeight.w800,
                                                        ),
                                                  ),
                                                  Text(
                                                    '${widget.product.price.toStringAsFixed(0)} ج.م',
                                                    style: AppTheme.discountText
                                                        .copyWith(
                                                          color: AppTheme
                                                              .textLightColor
                                                              .withOpacity(0.7),
                                                          fontSize:
                                                              ResponsiveHelper.getFontSize(
                                                                context,
                                                                isMobile
                                                                    ? 12
                                                                    : 14,
                                                              ),
                                                        ),
                                                  ),
                                                ] else ...[
                                                  Text(
                                                    '${widget.product.price.toStringAsFixed(0)} ج.م',
                                                    style: AppTheme.priceText
                                                        .copyWith(
                                                          color:
                                                              AppTheme
                                                                  .textLightColor,
                                                          fontSize:
                                                              ResponsiveHelper.getFontSize(
                                                                context,
                                                                isMobile
                                                                    ? 16
                                                                    : 20,
                                                              ),
                                                          fontWeight:
                                                              FontWeight.w800,
                                                        ),
                                                  ),
                                                ],
                                              ],
                                            ),
                                          ),
                                        ),

                                        SizedBox(width: spacing),

                                        // التقييم
                                        Container(
                                          padding: EdgeInsets.all(padding),
                                          decoration: BoxDecoration(
                                            gradient:
                                                AppTheme.secondaryGradient,
                                            borderRadius: BorderRadius.circular(
                                              16,
                                            ),
                                            boxShadow: AppTheme.cardShadow,
                                          ),
                                          child: Column(
                                            children: [
                                              Icon(
                                                Icons.star_rounded,
                                                color: AppTheme.textLightColor,
                                                size: isMobile ? 20 : 24,
                                              ),
                                              SizedBox(
                                                height: isMobile ? 2 : 4,
                                              ),
                                              Text(
                                                widget.product.rating
                                                    .toStringAsFixed(1),
                                                style: AppTheme.priceText.copyWith(
                                                  color:
                                                      AppTheme.textLightColor,
                                                  fontSize:
                                                      ResponsiveHelper.getFontSize(
                                                        context,
                                                        isMobile ? 14 : 18,
                                                      ),
                                                  fontWeight: FontWeight.w700,
                                                ),
                                              ),
                                              Text(
                                                '(${widget.product.reviewsCount})',
                                                style: AppTheme.cardSubtitle
                                                    .copyWith(
                                                      color: AppTheme
                                                          .textLightColor
                                                          .withOpacity(0.9),
                                                      fontSize:
                                                          ResponsiveHelper.getFontSize(
                                                            context,
                                                            isMobile ? 8 : 10,
                                                          ),
                                                    ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    );
                                  },
                                ),

                                const SizedBox(height: 24),

                                // الوصف
                                Container(
                                  width: double.infinity,
                                  padding: const EdgeInsets.all(20),
                                  decoration: BoxDecoration(
                                    color: AppTheme.backgroundColor,
                                    borderRadius: BorderRadius.circular(16),
                                    border: Border.all(
                                      color: AppTheme.primaryColor.withOpacity(
                                        0.1,
                                      ),
                                      width: 1,
                                    ),
                                  ),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        children: [
                                          Container(
                                            padding: const EdgeInsets.all(8),
                                            decoration: BoxDecoration(
                                              gradient: AppTheme.accentGradient,
                                              borderRadius:
                                                  BorderRadius.circular(8),
                                            ),
                                            child: Icon(
                                              Icons.description_rounded,
                                              color: AppTheme.textLightColor,
                                              size: 20,
                                            ),
                                          ),
                                          const SizedBox(width: 12),
                                          Text(
                                            'وصف المنتج',
                                            style: AppTheme.cardTitle.copyWith(
                                              fontSize: 18,
                                              fontWeight: FontWeight.w700,
                                            ),
                                          ),
                                        ],
                                      ),
                                      const SizedBox(height: 16),
                                      Text(
                                        widget.product.description,
                                        style: AppTheme.cardSubtitle.copyWith(
                                          fontSize: 15,
                                          height: 1.6,
                                          color: AppTheme.textSecondaryColor,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                const SizedBox(height: 32),

                                // قسم الكمية والإضافة للسلة
                                Container(
                                  padding: const EdgeInsets.all(20),
                                  decoration: BoxDecoration(
                                    gradient: AppTheme.cardGradient,
                                    borderRadius: BorderRadius.circular(20),
                                    boxShadow: AppTheme.cardShadow,
                                    border: Border.all(
                                      color: AppTheme.primaryColor.withOpacity(
                                        0.1,
                                      ),
                                      width: 1,
                                    ),
                                  ),
                                  child: Column(
                                    children: [
                                      // عنوان القسم
                                      Row(
                                        children: [
                                          Container(
                                            padding: const EdgeInsets.all(8),
                                            decoration: BoxDecoration(
                                              gradient:
                                                  AppTheme.primaryGradient,
                                              borderRadius:
                                                  BorderRadius.circular(8),
                                            ),
                                            child: Icon(
                                              Icons.shopping_cart_rounded,
                                              color: AppTheme.textLightColor,
                                              size: 20,
                                            ),
                                          ),
                                          const SizedBox(width: 12),
                                          Text(
                                            'إضافة إلى السلة',
                                            style: AppTheme.cardTitle.copyWith(
                                              fontSize: 18,
                                              fontWeight: FontWeight.w700,
                                            ),
                                          ),
                                        ],
                                      ),

                                      const SizedBox(height: 20),

                                      Row(
                                        children: [
                                          // محدد الكمية المحسن
                                          Container(
                                            decoration: BoxDecoration(
                                              gradient: AppTheme.cardGradient,
                                              borderRadius:
                                                  BorderRadius.circular(16),
                                              border: Border.all(
                                                color: AppTheme.primaryColor
                                                    .withOpacity(0.2),
                                                width: 1,
                                              ),
                                              boxShadow: AppTheme.cardShadow,
                                            ),
                                            child: Row(
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                Container(
                                                  decoration: BoxDecoration(
                                                    gradient:
                                                        _quantity > 1
                                                            ? AppTheme
                                                                .primaryGradient
                                                            : null,
                                                    color:
                                                        _quantity <= 1
                                                            ? AppTheme
                                                                .disabledColor
                                                            : null,
                                                    borderRadius:
                                                        const BorderRadius.only(
                                                          topLeft:
                                                              Radius.circular(
                                                                16,
                                                              ),
                                                          bottomLeft:
                                                              Radius.circular(
                                                                16,
                                                              ),
                                                        ),
                                                  ),
                                                  child: IconButton(
                                                    icon: Icon(
                                                      Icons.remove_rounded,
                                                      color:
                                                          _quantity > 1
                                                              ? AppTheme
                                                                  .textLightColor
                                                              : AppTheme
                                                                  .textTertiaryColor,
                                                    ),
                                                    onPressed:
                                                        _quantity > 1
                                                            ? () {
                                                              setState(() {
                                                                _quantity--;
                                                              });
                                                            }
                                                            : null,
                                                  ),
                                                ),
                                                Container(
                                                  padding:
                                                      const EdgeInsets.symmetric(
                                                        horizontal: 20,
                                                      ),
                                                  child: Text(
                                                    _quantity.toString(),
                                                    style: AppTheme.cardTitle
                                                        .copyWith(
                                                          fontSize: 18,
                                                          fontWeight:
                                                              FontWeight.w700,
                                                        ),
                                                  ),
                                                ),
                                                Container(
                                                  decoration: BoxDecoration(
                                                    gradient:
                                                        AppTheme
                                                            .primaryGradient,
                                                    borderRadius:
                                                        const BorderRadius.only(
                                                          topRight:
                                                              Radius.circular(
                                                                16,
                                                              ),
                                                          bottomRight:
                                                              Radius.circular(
                                                                16,
                                                              ),
                                                        ),
                                                  ),
                                                  child: IconButton(
                                                    icon: Icon(
                                                      Icons.add_rounded,
                                                      color:
                                                          AppTheme
                                                              .textLightColor,
                                                    ),
                                                    onPressed: () {
                                                      setState(() {
                                                        _quantity++;
                                                      });
                                                    },
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),

                                          const SizedBox(width: 16),

                                          // زر الإضافة للسلة المحسن
                                          Expanded(
                                            child: Container(
                                              decoration: BoxDecoration(
                                                gradient:
                                                    widget.product.isAvailable
                                                        ? AppTheme
                                                            .secondaryGradient
                                                        : LinearGradient(
                                                          colors: [
                                                            AppTheme
                                                                .disabledColor,
                                                            AppTheme
                                                                .disabledColor,
                                                          ],
                                                        ),
                                                borderRadius:
                                                    BorderRadius.circular(16),
                                                boxShadow:
                                                    widget.product.isAvailable
                                                        ? AppTheme.cardShadow
                                                        : null,
                                              ),
                                              child: Material(
                                                color: Colors.transparent,
                                                child: InkWell(
                                                  onTap:
                                                      widget.product.isAvailable
                                                          ? _addToCart
                                                          : null,
                                                  borderRadius:
                                                      BorderRadius.circular(16),
                                                  child: Padding(
                                                    padding:
                                                        const EdgeInsets.symmetric(
                                                          vertical: 16,
                                                        ),
                                                    child: Row(
                                                      mainAxisAlignment:
                                                          MainAxisAlignment
                                                              .center,
                                                      children: [
                                                        Icon(
                                                          Icons
                                                              .add_shopping_cart_rounded,
                                                          color:
                                                              AppTheme
                                                                  .textLightColor,
                                                          size: 20,
                                                        ),
                                                        const SizedBox(
                                                          width: 8,
                                                        ),
                                                        Text(
                                                          widget
                                                                  .product
                                                                  .isAvailable
                                                              ? 'إضافة إلى السلة'
                                                              : 'غير متوفر',
                                                          style: AppTheme
                                                              .buttonText
                                                              .copyWith(
                                                                fontSize: 16,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .w600,
                                                              ),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),

                                      const SizedBox(height: 16),

                                      // زر عرض السلة
                                      Container(
                                        width: double.infinity,
                                        decoration: BoxDecoration(
                                          border: Border.all(
                                            color: AppTheme.primaryColor
                                                .withOpacity(0.3),
                                            width: 1,
                                          ),
                                          borderRadius: BorderRadius.circular(
                                            12,
                                          ),
                                        ),
                                        child: Material(
                                          color: Colors.transparent,
                                          child: InkWell(
                                            onTap:
                                                () => Navigator.pushNamed(
                                                  context,
                                                  '/cart',
                                                ),
                                            borderRadius: BorderRadius.circular(
                                              12,
                                            ),
                                            child: Padding(
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                    vertical: 12,
                                                  ),
                                              child: Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.center,
                                                children: [
                                                  Icon(
                                                    Icons
                                                        .shopping_cart_outlined,
                                                    color:
                                                        AppTheme.primaryColor,
                                                    size: 20,
                                                  ),
                                                  const SizedBox(width: 8),
                                                  Text(
                                                    'عرض السلة',
                                                    style: AppTheme.cardTitle
                                                        .copyWith(
                                                          color:
                                                              AppTheme
                                                                  .primaryColor,
                                                          fontSize: 14,
                                                          fontWeight:
                                                              FontWeight.w600,
                                                        ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                const SizedBox(height: 32),

                                // قسم المواصفات المحسن
                                if (widget
                                    .product
                                    .specifications
                                    .isNotEmpty) ...[
                                  Container(
                                    width: double.infinity,
                                    padding: const EdgeInsets.all(20),
                                    decoration: BoxDecoration(
                                      gradient: AppTheme.cardGradient,
                                      borderRadius: BorderRadius.circular(20),
                                      boxShadow: AppTheme.cardShadow,
                                      border: Border.all(
                                        color: AppTheme.primaryColor
                                            .withOpacity(0.1),
                                        width: 1,
                                      ),
                                    ),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        // عنوان المواصفات
                                        Row(
                                          children: [
                                            Container(
                                              padding: const EdgeInsets.all(8),
                                              decoration: BoxDecoration(
                                                gradient:
                                                    AppTheme.accentGradient,
                                                borderRadius:
                                                    BorderRadius.circular(8),
                                              ),
                                              child: Icon(
                                                Icons.settings_rounded,
                                                color: AppTheme.textLightColor,
                                                size: 20,
                                              ),
                                            ),
                                            const SizedBox(width: 12),
                                            Text(
                                              'المواصفات التقنية',
                                              style: AppTheme.cardTitle
                                                  .copyWith(
                                                    fontSize: 18,
                                                    fontWeight: FontWeight.w700,
                                                  ),
                                            ),
                                          ],
                                        ),

                                        const SizedBox(height: 20),

                                        // قائمة المواصفات
                                        ...widget.product.specifications.entries.map(
                                          (entry) => Container(
                                            margin: const EdgeInsets.only(
                                              bottom: 12,
                                            ),
                                            padding: const EdgeInsets.all(16),
                                            decoration: BoxDecoration(
                                              color: AppTheme.backgroundColor,
                                              borderRadius:
                                                  BorderRadius.circular(12),
                                              border: Border.all(
                                                color: AppTheme.primaryColor
                                                    .withOpacity(0.08),
                                                width: 1,
                                              ),
                                            ),
                                            child: Row(
                                              children: [
                                                Container(
                                                  width: 6,
                                                  height: 6,
                                                  decoration: BoxDecoration(
                                                    gradient:
                                                        AppTheme
                                                            .primaryGradient,
                                                    shape: BoxShape.circle,
                                                  ),
                                                ),
                                                const SizedBox(width: 12),
                                                Expanded(
                                                  flex: 2,
                                                  child: Text(
                                                    entry.key,
                                                    style: AppTheme.cardTitle
                                                        .copyWith(
                                                          fontSize: 14,
                                                          fontWeight:
                                                              FontWeight.w600,
                                                          color:
                                                              AppTheme
                                                                  .textSecondaryColor,
                                                        ),
                                                  ),
                                                ),
                                                const SizedBox(width: 12),
                                                Expanded(
                                                  flex: 3,
                                                  child: Text(
                                                    entry.value.toString(),
                                                    style: AppTheme.cardSubtitle
                                                        .copyWith(
                                                          fontSize: 14,
                                                          fontWeight:
                                                              FontWeight.w500,
                                                          color:
                                                              AppTheme
                                                                  .textPrimaryColor,
                                                        ),
                                                    textAlign: TextAlign.end,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),

                                  const SizedBox(height: 32),
                                ],

                                // مساحة إضافية في النهاية
                                const SizedBox(height: 24),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
    );
  }
}
