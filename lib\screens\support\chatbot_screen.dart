import 'package:flutter/material.dart';
import 'package:motorcycle_parts_shop/core/services/chatbot_service.dart';
import 'package:motorcycle_parts_shop/core/theme/app_theme.dart';
import 'package:motorcycle_parts_shop/core/utils/responsive_helper.dart';
import 'package:provider/provider.dart';

class ChatbotScreen extends StatefulWidget {
  const ChatbotScreen({super.key});

  @override
  State<ChatbotScreen> createState() => _ChatbotScreenState();
}

class _ChatbotScreenState extends State<ChatbotScreen> {
  final TextEditingController _messageController = TextEditingController();
  final List<Map<String, dynamic>> _messages = [];
  bool _isLoading = false;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _initChatbot();
  }

  @override
  void dispose() {
    // تنظيف موارد التعرف الصوتي عند إغلاق الشاشة
    final chatbotService = Provider.of<ChatbotService>(context, listen: false);
    chatbotService.stopListening();
    super.dispose();
  }

  Future<void> _initChatbot() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final chatbotService = Provider.of<ChatbotService>(
        context,
        listen: false,
      );

      // استرجاع تاريخ المحادثة
      final chatHistory = await chatbotService.getChatHistory();

      if (mounted) {
        setState(() {
          if (chatHistory.isNotEmpty) {
            _messages.addAll(chatHistory);
          } else {
            // إضافة رسالة ترحيبية إذا لم يكن هناك تاريخ محادثة
            _messages.add({
              'text':
                  '🤖 مرحباً بك في مساعد المتجر الذكي!\n\n'
                  'أنا هنا لمساعدتك في:\n'
                  '• البحث عن قطع الغيار\n'
                  '• معرفة أسعار المنتجات\n'
                  '• تتبع طلباتك\n'
                  '• الإجابة على استفساراتك\n\n'
                  'كيف يمكنني مساعدتك اليوم؟ 😊',
              'isUser': false,
              'timestamp': DateTime.now(),
            });
          }
          _isLoading = false;
          _isInitialized = true;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _messages.add({
            'text': 'حدث خطأ أثناء تحميل المحادثة. يرجى المحاولة مرة أخرى.',
            'isUser': false,
          });
          _isLoading = false;
          _isInitialized = true;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final chatbotService = Provider.of<ChatbotService>(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('مساعد المتجر الذكي'),
        backgroundColor: AppTheme.primaryColor,
      ),
      body: Column(
        children: [
          Expanded(
            child:
                _isInitialized
                    ? ListView.builder(
                      itemCount: _messages.length,
                      itemBuilder: (context, index) {
                        final message = _messages[index];
                        return Align(
                          alignment:
                              message['isUser']
                                  ? Alignment.centerRight
                                  : Alignment.centerLeft,
                          child: Container(
                            margin: const EdgeInsets.symmetric(
                              vertical: 4,
                              horizontal: 8,
                            ),
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color:
                                  message['isUser']
                                      ? AppTheme.primaryColor
                                      : Colors.grey[200],
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  message['text'],
                                  style: TextStyle(
                                    color:
                                        message['isUser']
                                            ? Colors.white
                                            : Colors.black,
                                    fontSize: 16,
                                  ),
                                ),
                                if (message['timestamp'] != null)
                                  Padding(
                                    padding: const EdgeInsets.only(top: 4),
                                    child: Text(
                                      _formatTime(message['timestamp']),
                                      style: TextStyle(
                                        color:
                                            message['isUser']
                                                ? Colors.white70
                                                : Colors.grey[600],
                                        fontSize: 12,
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        );
                      },
                    )
                    : const Center(child: Text('جاري تحميل المحادثة...')),
          ),
          if (_isLoading) const LinearProgressIndicator(),
          ResponsiveBuilder(
            builder: (context, constraints) {
              final padding = ResponsiveHelper.getPadding(
                context,
                mobile: 8,
                tablet: 12,
                desktop: 16,
              );

              return Padding(
                padding: EdgeInsets.all(padding),
                child: Row(
                  children: [
                    Expanded(
                      child: TextField(
                        controller: _messageController,
                        decoration: InputDecoration(
                          hintText: 'اكتب رسالتك هنا...',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(20),
                          ),
                        ),
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.send),
                      color: AppTheme.primaryColor,
                      onPressed: () async {
                        if (_messageController.text.trim().isEmpty) return;

                        final userMessage = _messageController.text;
                        _messageController.clear();

                        setState(() {
                          _messages.add({
                            'text': userMessage,
                            'isUser': true,
                            'timestamp': DateTime.now(),
                          });
                          _isLoading = true;
                        });

                        try {
                          final response = await chatbotService
                              .getChatbotResponse(userMessage);

                          setState(() {
                            _messages.add({
                              'text': response,
                              'isUser': false,
                              'timestamp': DateTime.now(),
                            });
                            _isLoading = false;
                          });

                          await chatbotService.saveChatHistory(_messages);
                        } catch (e) {
                          setState(() {
                            _messages.add({
                              'text':
                                  'عذراً، حدث خطأ أثناء معالجة طلبك. يرجى المحاولة مرة أخرى.',
                              'isUser': false,
                            });
                            _isLoading = false;
                          });
                        }
                      },
                    ),
                    IconButton(
                      icon: const Icon(Icons.mic),
                      color: AppTheme.primaryColor,
                      onPressed: () async {
                        try {
                          setState(() {
                            _isLoading = true;
                          });

                          final voiceCommand =
                              await chatbotService.listenToVoiceCommand();

                          if (voiceCommand.isNotEmpty) {
                            setState(() {
                              _messages.add({
                                'text': voiceCommand,
                                'isUser': true,
                              });
                            });

                            final response = await chatbotService
                                .getChatbotResponse(voiceCommand);

                            setState(() {
                              _messages.add({
                                'text': response,
                                'isUser': false,
                              });
                              _isLoading = false;
                            });

                            await chatbotService.saveChatHistory(_messages);
                          } else {
                            setState(() {
                              _isLoading = false;
                            });
                          }
                        } catch (e) {
                          setState(() {
                            _messages.add({
                              'text':
                                  'عذراً، حدث خطأ أثناء التعرف الصوتي. يرجى المحاولة مرة أخرى.',
                              'isUser': false,
                            });
                            _isLoading = false;
                          });
                        }
                      },
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  /// تنسيق الوقت لعرضه في الرسائل
  String _formatTime(DateTime? timestamp) {
    if (timestamp == null) return '';

    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inHours < 1) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inDays < 1) {
      return 'منذ ${difference.inHours} ساعة';
    } else {
      return '${timestamp.day}/${timestamp.month} ${timestamp.hour}:${timestamp.minute.toString().padLeft(2, '0')}';
    }
  }
}
