import 'dart:async';
import 'package:email_validator/email_validator.dart';
import 'package:flutter/material.dart';
import 'package:motorcycle_parts_shop/core/services/auth_supabase_service.dart';
import 'package:motorcycle_parts_shop/core/theme/app_theme.dart';
import 'package:motorcycle_parts_shop/core/utils/responsive_helper.dart';
import 'package:provider/provider.dart';
import 'package:vibration/vibration.dart';

class ForgotPasswordState extends ChangeNotifier {
  bool _isLoading = false;
  bool _resetSent = false;
  int _resendCountdown = 0;

  bool get isLoading => _isLoading;
  bool get resetSent => _resetSent;
  int get resendCountdown => _resendCountdown;

  void setLoading(bool value) {
    _isLoading = value;
    notifyListeners();
  }

  void setResetSent(bool value) {
    _resetSent = value;
    notifyListeners();
  }

  void setResendCountdown(int value) {
    _resendCountdown = value;
    notifyListeners();
  }
}

class ForgotPasswordScreen extends StatefulWidget {
  const ForgotPasswordScreen({super.key});

  @override
  State<ForgotPasswordScreen> createState() => _ForgotPasswordScreenState();
}

class _ForgotPasswordScreenState extends State<ForgotPasswordScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  late ForgotPasswordState _state;
  final String _language = 'ar';

  @override
  void initState() {
    super.initState();
    _state = ForgotPasswordState();
    _state.setResendCountdown(60);
    _startResendCountdown();
    // تهيئة خدمة المصادقة
    Provider.of<AuthSupabaseService>(context, listen: false).initialize();
  }

  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }

  void _startResendCountdown() {
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted && _state.resendCountdown > 0) {
        _state.setResendCountdown(_state.resendCountdown - 1);
        _startResendCountdown();
      }
    });
  }

  Future<void> _resetPassword() async {
    if (_formKey.currentState!.validate()) {
      _state.setLoading(true);

      try {
        final supabaseService = Provider.of<AuthSupabaseService>(
          context,
          listen: false,
        );
        await supabaseService.resetPassword(_emailController.text.trim());

        if (!mounted) return;

        _state.setResetSent(true);
        _state.setResendCountdown(60);
        _startResendCountdown();

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              AppLocalizations.translate('reset_success_message', _language),
            ),
            backgroundColor: AppTheme.successColor,
            behavior: SnackBarBehavior.floating,
          ),
        );
      } catch (e) {
        if (!mounted) return;
        _state.setResetSent(false);
        String errorMessage;
        if (e.toString().contains('User not found')) {
          errorMessage = AppLocalizations.translate(
            'user_not_found',
            _language,
          );
        } else if (e.toString().contains('Rate limit')) {
          errorMessage = AppLocalizations.translate(
            'rate_limit_exceeded',
            _language,
          );
        } else {
          errorMessage = 'حدث خطأ: ${e.toString()}';
        }
        Vibration.vibrate(duration: 200);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      } finally {
        if (mounted) {
          _state.setLoading(false);
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return ChangeNotifierProvider.value(
      value: _state,
      child: Directionality(
        textDirection: TextDirection.rtl,
        child: Scaffold(
          appBar: AppBar(
            title: Text(
              AppLocalizations.translate('reset_password_title', _language),
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            centerTitle: true,
            elevation: 4,
            flexibleSpace: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [Color(0xFF00C6FB), Color(0xFF005BEA)],
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.blue.withOpacity(0.3),
                    spreadRadius: 2,
                    blurRadius: 10,
                    offset: Offset(0, 3),
                  ),
                ],
              ),
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.vertical(bottom: Radius.circular(20)),
            ),
          ),
          body: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [Color(0xFFE0F7FA), Colors.white],
              ),
            ),
            child: SafeArea(
              child: ResponsiveBuilder(
                builder: (context, constraints) {
                  final padding = ResponsiveHelper.getPadding(context);

                  return Padding(
                    padding: EdgeInsets.all(padding),
                    child: Form(
                      key: _formKey,
                      child: Consumer<ForgotPasswordState>(
                        builder: (context, state, _) {
                          return AnimatedSwitcher(
                            duration: const Duration(milliseconds: 300),
                            child:
                                state.resetSent
                                    ? _buildSuccessView()
                                    : _buildFormView(state, isDarkMode),
                          );
                        },
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFormView(ForgotPasswordState state, bool isDarkMode) {
    return Column(
      key: const ValueKey('form'),
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Icon(
          Icons.lock_reset,
          size: ResponsiveHelper.isMobile(context) ? 60 : 80,
          color: AppTheme.primaryColor,
        ),
        SizedBox(height: ResponsiveHelper.isMobile(context) ? 24 : 32),
        Text(
          AppLocalizations.translate('reset_password_title', _language),
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            fontSize: ResponsiveHelper.getFontSize(context, 24),
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 16),
        Text(
          AppLocalizations.translate('reset_password_description', _language),
          style: Theme.of(context).textTheme.bodyLarge,
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 32),
        TextFormField(
          controller: _emailController,
          keyboardType: TextInputType.emailAddress,
          textDirection: TextDirection.ltr,
          decoration: InputDecoration(
            labelText: AppLocalizations.translate('email_label', _language),
            prefixIcon: const Icon(Icons.email_outlined),
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return AppLocalizations.translate('email_empty_error', _language);
            }
            if (!EmailValidator.validate(value)) {
              return AppLocalizations.translate(
                'email_invalid_error',
                _language,
              );
            }
            return null;
          },
        ),
        const SizedBox(height: 32),
        ElevatedButton(
          onPressed:
              (state.isLoading || state.resendCountdown > 0)
                  ? null
                  : _resetPassword,
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 16),
            backgroundColor:
                isDarkMode ? Colors.grey[800] : AppTheme.primaryColor,
            disabledBackgroundColor: AppTheme.primaryColor.withOpacity(0.5),
          ),
          child:
              state.isLoading
                  ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: Colors.white,
                    ),
                  )
                  : Text(
                    state.resendCountdown > 0
                        ? '${AppLocalizations.translate('send_reset_link', _language)} (${state.resendCountdown} ث)'
                        : AppLocalizations.translate(
                          'send_reset_link',
                          _language,
                        ),
                    style: const TextStyle(fontSize: 16, color: Colors.white),
                  ),
        ),
        const SizedBox(height: 16),
        TextButton(
          onPressed: () {
            Navigator.of(context).pop();
          },
          child: Text(AppLocalizations.translate('back_to_login', _language)),
        ),
      ],
    );
  }

  Widget _buildSuccessView() {
    return Column(
      key: const ValueKey('success'),
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        const Icon(
          Icons.check_circle_outline,
          size: 80,
          color: AppTheme.successColor,
        ),
        const SizedBox(height: 32),
        Text(
          AppLocalizations.translate('reset_link_sent', _language),
          style: Theme.of(context).textTheme.headlineMedium,
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 16),
        Text(
          AppLocalizations.translate(
            'reset_link_sent_description',
            _language,
            _emailController.text,
          ),
          style: Theme.of(context).textTheme.bodyLarge,
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 32),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppTheme.successColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: AppTheme.successColor),
          ),
          child: Row(
            children: [
              const Icon(Icons.info_outline, color: AppTheme.successColor),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  AppLocalizations.translate('check_spam', _language),
                  style: const TextStyle(color: AppTheme.successColor),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 32),
        TextButton(
          onPressed: () {
            Navigator.of(context).pop();
          },
          child: Text(AppLocalizations.translate('back_to_login', _language)),
        ),
      ],
    );
  }
}

class AppLocalizations {
  static const Map<String, Map<String, String>> _localizedValues = {
    'ar': {
      'reset_password_title': 'استعادة كلمة المرور',
      'reset_password_description':
          'أدخل بريدك الإلكتروني وسنرسل لك رابطاً لإعادة تعيين كلمة المرور',
      'email_label': 'البريد الإلكتروني',
      'email_empty_error': 'الرجاء إدخال البريد الإلكتروني',
      'email_invalid_error': 'الرجاء إدخال بريد إلكتروني صحيح',
      'send_reset_link': 'إرسال رابط الاستعادة',
      'reset_link_sent': 'تم إرسال رابط الاستعادة',
      'reset_link_sent_description':
          'تم إرسال رابط استعادة كلمة المرور إلى بريدك الإلكتروني:\n%s\n\nيرجى التحقق من بريدك الإلكتروني واتباع التعليمات لإعادة تعيين كلمة المرور.',
      'check_spam':
          'إذا لم تجد الرسالة في البريد الوارد، يرجى التحقق من مجلد البريد غير المرغوب فيه (Spam).',
      'back_to_login': 'العودة إلى تسجيل الدخول',
      'reset_success_message':
          'تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني',
      'user_not_found': 'البريد الإلكتروني غير مسجل',
      'rate_limit_exceeded':
          'تم تجاوز الحد الأقصى للطلبات، حاول مرة أخرى لاحقًا',
    },
    'en': {
      'reset_password_title': 'Reset Password',
      'reset_password_description':
          'Enter your email and we will send you a link to reset your password',
      'email_label': 'Email',
      'email_empty_error': 'Please enter your email',
      'email_invalid_error': 'Please enter a valid email',
      'send_reset_link': 'Send Reset Link',
      'reset_link_sent': 'Reset Link Sent',
      'reset_link_sent_description':
          'A password reset link has been sent to your email:\n%s\n\nPlease check your email and follow the instructions to reset your password.',
      'check_spam':
          'If you don’t see the email in your inbox, please check your spam folder.',
      'back_to_login': 'Back to Login',
      'reset_success_message':
          'A password reset link has been sent to your email',
      'user_not_found': 'Email not registered',
      'rate_limit_exceeded': 'Rate limit exceeded, please try again later',
    },
  };

  static String translate(String key, String language, [String? param]) {
    final translation = _localizedValues[language]?[key] ?? key;
    return param != null ? translation.replaceFirst('%s', param) : translation;
  }
}
