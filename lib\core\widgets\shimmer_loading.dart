
// استيراد حزمة فلاتر الأساسية
import 'package:flutter/material.dart';
// استيراد حزمة Shimmer لإنشاء تأثيرات التحميل
import 'package:shimmer/shimmer.dart';
// استيراد ملف الثيم الخاص بالتطبيق
import 'package:motorcycle_parts_shop/core/theme/app_theme.dart';

// كائن لعرض تأثير تحميل بسيط
class ShimmerLoading extends StatelessWidget {
  final double width; // عرض العنصر
  final double height; // ارتفاع العنصر
  final double borderRadius; // نصف قطر الزوايا
  final BoxShape shape; // شكل العنصر (مستطيل أو دائرة)
  final Duration animationDuration; // مدة الرسوم المتحركة
  final Curve animationCurve; // منحنى الحركة للرسوم

  const ShimmerLoading({
    super.key,
    required this.width,
    required this.height,
    this.borderRadius = 12.0,
    this.shape = BoxShape.rectangle,
    this.animationDuration = const Duration(milliseconds: 1200),
    this.animationCurve = Curves.easeInOut,
  });

  @override
  Widget build(BuildContext context) {
    final isDarkMode =
        Theme.of(context).brightness == Brightness.dark; // تحديد وضع الثيم

    return Shimmer.fromColors(
      baseColor:
          isDarkMode
              ? Colors.grey[800]!
              : AppTheme.shimmerBaseColor, // لون الأساس
      highlightColor:
          isDarkMode
              ? Colors.grey[700]!
              : AppTheme.shimmerHighlightColor, // لون الإبراز
      period: animationDuration, // مدة الدورة
      direction: ShimmerDirection.ltr, // اتجاه التأثير
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: Colors.white, // لون الخلفية
          borderRadius:
              shape == BoxShape.rectangle
                  ? BorderRadius.circular(borderRadius)
                  : null,
          shape: shape,
          boxShadow: [
            if (!isDarkMode) // إضافة ظل في الوضع الفاتح فقط
              BoxShadow(
                color: Colors.black.withOpacity(0.08),
                blurRadius: 6,
                spreadRadius: 1,
                offset: const Offset(0, 2),
              ),
          ],
        ),
      ),
    );
  }
}

// كائن لعرض قائمة تحميل
class ShimmerListLoading extends StatelessWidget {
  final int itemCount; // عدد العناصر
  final double itemHeight; // ارتفاع كل عنصر
  final EdgeInsets padding; // الهوامش
  final double spacing; // المسافة بين العناصر
  final bool showDividers; // إظهار الفواصل
  final BorderRadius borderRadius; // نصف قطر الزوايا

  const ShimmerListLoading({
    super.key,
    this.itemCount = 5,
    this.itemHeight = 80,
    this.padding = const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
    this.spacing = 16.0,
    this.showDividers = false,
    this.borderRadius = const BorderRadius.all(Radius.circular(12.0)),
  });

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      itemCount: itemCount,
      padding: padding,
      physics: const NeverScrollableScrollPhysics(), // تعطيل التمرير
      separatorBuilder:
          (context, index) =>
              showDividers
                  ? Divider(
                    height: spacing,
                    color: Colors.grey.withOpacity(0.1),
                  )
                  : SizedBox(height: spacing),
      itemBuilder: (context, index) {
        return Container(
          decoration: BoxDecoration(
            borderRadius: borderRadius,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.03),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: ShimmerLoading(
            width: double.infinity, // عرض كامل
            height: itemHeight,
            borderRadius: borderRadius.topLeft.x,
          ),
        );
      },
    );
  }
}

// كائن لبطاقة منتج أثناء التحميل
class ShimmerProductCard extends StatelessWidget {
  final double width;
  final double height;
  final double elevation; // ارتفاع الظل
  final BorderRadius borderRadius;
  final Duration animationDuration;

  const ShimmerProductCard({
    super.key,
    this.width = 180,
    this.height = 250,
    this.elevation = 3,
    this.borderRadius = const BorderRadius.all(Radius.circular(16)),
    this.animationDuration = const Duration(milliseconds: 1200),
  });

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Container(
      width: width,
      height: height,
      margin: const EdgeInsets.only(right: 12),
      decoration: BoxDecoration(
        borderRadius: borderRadius,
        color: Theme.of(context).cardColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(isDarkMode ? 0.2 : 0.08),
            blurRadius: 8,
            spreadRadius: 1,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Stack(
            children: [
              ShimmerLoading(
                // حامل مكان الصورة
                width: width,
                height: height * 0.55,
                borderRadius: borderRadius.topLeft.x,
                animationDuration: const Duration(milliseconds: 1500),
              ),
              if (!isDarkMode) // تدرج لوني في الوضع الفاتح
                Positioned.fill(
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(borderRadius.topLeft.x),
                        topRight: Radius.circular(borderRadius.topRight.x),
                      ),
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          Colors.black.withOpacity(0.05),
                        ],
                      ),
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 12),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12),
            child: ShimmerLoading(
              // حامل مكان العنوان
              width: width * 0.8,
              height: 16,
              borderRadius: 4,
              animationDuration: const Duration(milliseconds: 1300),
            ),
          ),
          const SizedBox(height: 8),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12),
            child: ShimmerLoading(
              // حامل مكان السعر
              width: width * 0.5,
              height: 14,
              borderRadius: 4,
              animationDuration: const Duration(milliseconds: 1400),
            ),
          ),
          const SizedBox(height: 12),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12),
            child: Row(
              children: [
                ShimmerLoading(
                  // حامل مكان التقييم
                  width: 80,
                  height: 16,
                  borderRadius: 4,
                  animationDuration: const Duration(milliseconds: 1600),
                ),
                const Spacer(),
                ShimmerLoading(
                  // حامل مكان الأيقونة
                  width: 24,
                  height: 24,
                  shape: BoxShape.circle,
                  animationDuration: const Duration(milliseconds: 1700),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

// كائن لشبكة منتجات أثناء التحميل
class ShimmerProductGrid extends StatelessWidget {
  final int crossAxisCount; // عدد الأعمدة
  final int itemCount; // عدد العناصر
  final double childAspectRatio; // نسبة العرض إلى الارتفاع
  final EdgeInsets padding;
  final double crossAxisSpacing; // المسافة الأفقية
  final double mainAxisSpacing; // المسافة الرأسية

  const ShimmerProductGrid({
    super.key,
    this.crossAxisCount = 2,
    this.itemCount = 6,
    this.childAspectRatio = 0.7,
    this.padding = const EdgeInsets.all(16),
    this.crossAxisSpacing = 16,
    this.mainAxisSpacing = 16,
  });

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      padding: padding,
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        childAspectRatio: childAspectRatio,
        crossAxisSpacing: crossAxisSpacing,
        mainAxisSpacing: mainAxisSpacing,
      ),
      itemCount: itemCount,
      itemBuilder: (context, index) {
        final staggeredDuration = Duration(
          // تأخير متدرج للرسوم
          milliseconds: 1200 + (index * 100 % 500),
        );

        return ShimmerProductCard(animationDuration: staggeredDuration);
      },
    );
  }
}

// كائن لبطاقة تفاصيل متقدمة أثناء التحميل
class ShimmerDetailCard extends StatelessWidget {
  final double height;
  final EdgeInsets padding;
  final BorderRadius borderRadius;

  const ShimmerDetailCard({
    super.key,
    this.height = 120,
    this.padding = const EdgeInsets.all(16),
    this.borderRadius = const BorderRadius.all(Radius.circular(16)),
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding,
      child: Container(
        height: height,
        decoration: BoxDecoration(
          borderRadius: borderRadius,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 8,
              spreadRadius: 1,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                ShimmerLoading(
                  // حامل مكان الصورة الرمزية
                  width: 50,
                  height: 50,
                  shape: BoxShape.circle,
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ShimmerLoading(
                        // حامل مكان العنوان
                        width: 140,
                        height: 16,
                        borderRadius: 4,
                      ),
                      const SizedBox(height: 8),
                      ShimmerLoading(
                        // حامل مكان النص الفرعي
                        width: 100,
                        height: 12,
                        borderRadius: 4,
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ShimmerLoading(
              // حامل مكان النص الأول
              width: double.infinity,
              height: 12,
              borderRadius: 4,
            ),
            const SizedBox(height: 8),
            ShimmerLoading(
              // حامل مكان النص الثاني
              width: double.infinity * 0.7,
              height: 12,
              borderRadius: 4,
            ),
          ],
        ),
      ),
    );
  }
}

// ========================================
// مؤشرات التحميل المدمجة من loading_indicators.dart
// ========================================

/// مؤشر تحميل أساسي محسن
class AppLoadingIndicator extends StatelessWidget {
  final String? message;
  final bool showMessage;
  final Color? color;
  final double size;

  const AppLoadingIndicator({
    super.key,
    this.message,
    this.showMessage = true,
    this.color,
    this.size = 40.0,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: size,
            height: size,
            child: CircularProgressIndicator(
              color: color ?? AppTheme.primaryColor,
              strokeWidth: 3.0,
            ),
          ),
          if (showMessage) ...[
            const SizedBox(height: 16),
            Text(
              message ?? 'جاري التحميل...',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }
}

/// مؤشر تحميل صغير للأزرار
class ButtonLoadingIndicator extends StatelessWidget {
  final Color? color;
  final double size;

  const ButtonLoadingIndicator({super.key, this.color, this.size = 20.0});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: size,
      height: size,
      child: CircularProgressIndicator(
        color: color ?? Colors.white,
        strokeWidth: 2.0,
      ),
    );
  }
}

/// مؤشر تحميل مع رسوم متحركة مخصصة
class CustomLoadingIndicator extends StatefulWidget {
  final String? message;
  final Color? color;
  final double size;

  const CustomLoadingIndicator({
    super.key,
    this.message,
    this.color,
    this.size = 60.0,
  });

  @override
  State<CustomLoadingIndicator> createState() => _CustomLoadingIndicatorState();
}

class _CustomLoadingIndicatorState extends State<CustomLoadingIndicator>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();
    _animation = Tween<double>(begin: 0, end: 1).animate(_controller);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          AnimatedBuilder(
            animation: _animation,
            builder: (context, child) {
              return Transform.rotate(
                angle: _animation.value * 2 * 3.14159,
                child: Container(
                  width: widget.size,
                  height: widget.size,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: LinearGradient(
                      colors: [
                        widget.color ?? AppTheme.primaryColor,
                        (widget.color ?? AppTheme.primaryColor).withOpacity(
                          0.3,
                        ),
                      ],
                    ),
                  ),
                  child: const Icon(Icons.motorcycle, color: Colors.white),
                ),
              );
            },
          ),
          if (widget.message != null) ...[
            const SizedBox(height: 16),
            Text(
              widget.message!,
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }
}
