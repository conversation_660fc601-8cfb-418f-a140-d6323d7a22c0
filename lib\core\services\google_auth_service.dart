import 'package:flutter/foundation.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:motorcycle_parts_shop/core/services/auth_supabase_service.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class GoogleAuthService extends ChangeNotifier {
  final AuthSupabaseService _authService;
  late GoogleSignIn _googleSignIn;

  GoogleSignInAccount? _currentUser;
  bool _isSigningIn = false;
  String? _errorMessage;

  GoogleAuthService(this._authService) {
    _initializeGoogleSignIn();
  }

  // Getters
  GoogleSignInAccount? get currentUser => _currentUser;
  bool get isSigningIn => _isSigningIn;
  String? get errorMessage => _errorMessage;
  bool get isSignedIn => _currentUser != null;

  /// تهيئة Google Sign In
  void _initializeGoogleSignIn() {
    try {
      _googleSignIn = GoogleSignIn(
        scopes: ['email', 'profile'],
        // إضافة clientId للويب إذا لزم الأمر
        clientId:
            kIsWeb
                ? '************-zyxwvutsrqponmlkjihgfedcba654321.apps.googleusercontent.com'
                : null,
      );

      // الاستماع لتغييرات حالة تسجيل الدخول
      _googleSignIn.onCurrentUserChanged.listen((GoogleSignInAccount? account) {
        _currentUser = account;
        notifyListeners();
        debugPrint('Google Sign-In state changed: ${account?.email ?? 'null'}');
      });

      // محاولة تسجيل الدخول التلقائي
      _googleSignIn.signInSilently().catchError((error) {
        debugPrint('Silent sign-in failed: $error');
        return null;
      });

      debugPrint('Google Sign-In initialized successfully');
    } catch (e) {
      debugPrint('Failed to initialize Google Sign-In: $e');
      _setError('فشل في تهيئة خدمة Google Sign-In');
    }
  }

  /// تسجيل الدخول باستخدام Google
  Future<AuthResponse?> signInWithGoogle() async {
    try {
      _setSigningIn(true);
      _clearError();

      debugPrint('بدء عملية تسجيل الدخول مع Google...');

      // التحقق من تهيئة الخدمة
      if (!_authService.isInitialized) {
        throw Exception('خدمة المصادقة غير مهيأة');
      }

      // تسجيل الدخول مع Google
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();

      if (googleUser == null) {
        // المستخدم ألغى عملية تسجيل الدخول
        debugPrint('المستخدم ألغى عملية تسجيل الدخول');
        _setSigningIn(false);
        return null;
      }

      debugPrint('تم تسجيل الدخول مع Google: ${googleUser.email}');

      // الحصول على بيانات المصادقة
      final GoogleSignInAuthentication googleAuth =
          await googleUser.authentication;

      if (googleAuth.accessToken == null || googleAuth.idToken == null) {
        throw Exception('فشل في الحصول على رموز المصادقة من Google');
      }

      debugPrint('تم الحصول على رموز المصادقة من Google');

      // تسجيل الدخول في Supabase باستخدام Google
      final AuthResponse response = await _authService.client.auth
          .signInWithIdToken(
            provider: OAuthProvider.google,
            idToken: googleAuth.idToken!,
            accessToken: googleAuth.accessToken!,
          );

      if (response.user == null) {
        throw Exception('فشل في تسجيل الدخول في Supabase');
      }

      debugPrint('تم تسجيل الدخول في Supabase: ${response.user!.id}');

      _currentUser = googleUser;

      // تحديث معلومات المستخدم في قاعدة البيانات
      final isNewUser = await _updateUserProfile(googleUser, response.user!);

      // إضافة معلومة إذا كان المستخدم جديد
      if (response.user!.userMetadata != null) {
        response.user!.userMetadata!['is_new_user'] = isNewUser;
      }

      debugPrint('تم تسجيل الدخول بنجاح مع Google: ${googleUser.email}');
      _setSigningIn(false);
      return response;
    } on Exception catch (e) {
      _setSigningIn(false);
      final errorMessage = 'فشل في تسجيل الدخول مع Google: ${e.toString()}';
      _setError(errorMessage);
      debugPrint('Google Sign In Error: $e');
      return null;
    } catch (e) {
      _setSigningIn(false);
      final errorMessage =
          'خطأ غير متوقع في تسجيل الدخول مع Google: ${e.toString()}';
      _setError(errorMessage);
      debugPrint('Unexpected Google Sign In Error: $e');
      return null;
    }
  }

  /// تحديث معلومات المستخدم في قاعدة البيانات
  Future<bool> _updateUserProfile(
    GoogleSignInAccount googleUser,
    User supabaseUser,
  ) async {
    try {
      debugPrint('تحديث ملف المستخدم: ${googleUser.email}');

      // التحقق من وجود الملف الشخصي
      final existingProfile =
          await _authService.client
              .from('profiles')
              .select()
              .eq('id', supabaseUser.id)
              .maybeSingle();

      final profileData = {
        'id': supabaseUser.id,
        'email': googleUser.email,
        'name': googleUser.displayName ?? 'مستخدم Google',
        'profile_type': 'customer',
        'updated_at': DateTime.now().toIso8601String(),
        'last_login_at': DateTime.now().toIso8601String(),
        'raw_user_meta_data': {
          'provider': 'google',
          'name': googleUser.displayName,
          'email': googleUser.email,
          'provider_id': googleUser.id,
          'photo_url': googleUser.photoUrl,
        },
      };

      if (existingProfile == null) {
        debugPrint('إنشاء ملف شخصي جديد للمستخدم');

        // إنشاء ملف تعريف جديد (بدون البيانات الإضافية)
        profileData['created_at'] = DateTime.now().toIso8601String();
        profileData['unread_notifications_count'] = 0;
        profileData['total_notifications_count'] = 0;
        profileData['order_count'] = 0;
        profileData['wishlist_count'] = 0;
        profileData['average_rating'] = 0.0;
        profileData['total_spent'] = 0.0;
        profileData['permissions'] = ['customer'];
        // لا نضع phone, address, governorate, birth_date هنا
        // سيتم إضافتها في شاشة إكمال البيانات

        await _authService.client.from('profiles').insert(profileData);
        debugPrint('تم إنشاء الملف الشخصي بنجاح');

        // إنشاء إعدادات إشعارات افتراضية
        try {
          await _authService.client.from('notification_settings').insert({
            'user_id': supabaseUser.id,
            'email_notifications': true,
            'push_notifications': true,
            'sms_notifications': false,
            'marketing_notifications': true,
            'created_at': DateTime.now().toIso8601String(),
            'updated_at': DateTime.now().toIso8601String(),
          });
          debugPrint('تم إنشاء إعدادات الإشعارات');
        } catch (e) {
          debugPrint('فشل في إنشاء إعدادات الإشعارات: $e');
          // لا نرمي خطأ هنا
        }

        // إرجاع true للإشارة إلى أن هذا مستخدم جديد يحتاج لإكمال البيانات
        return true;
      } else {
        debugPrint('تحديث الملف الشخصي الموجود');

        // تحديث الملف الشخصي الموجود (فقط البيانات الأساسية)
        final updateData = {
          'name': profileData['name'],
          'updated_at': profileData['updated_at'],
          'last_login_at': profileData['last_login_at'],
          'raw_user_meta_data': profileData['raw_user_meta_data'],
        };

        await _authService.client
            .from('profiles')
            .update(updateData)
            .eq('id', supabaseUser.id);

        debugPrint('تم تحديث الملف الشخصي بنجاح');

        // إرجاع false للإشارة إلى أن المستخدم موجود بالفعل
        return false;
      }
    } catch (e) {
      debugPrint('خطأ في تحديث ملف المستخدم: $e');
      // لا نرمي خطأ هنا لأن تسجيل الدخول نجح
      return false;
    }
  }

  /// تسجيل الخروج
  Future<void> signOut() async {
    try {
      // تسجيل الخروج من Google
      await _googleSignIn.signOut();

      // تسجيل الخروج من Supabase
      await _authService.signOut();

      _currentUser = null;
      _clearError();
      notifyListeners();

      debugPrint('تم تسجيل الخروج بنجاح');
    } catch (e) {
      _setError('فشل في تسجيل الخروج: ${e.toString()}');
      debugPrint('Sign out error: $e');
    }
  }

  /// قطع الاتصال مع Google (إلغاء الإذن)
  Future<void> disconnect() async {
    try {
      await _googleSignIn.disconnect();
      await _authService.signOut();

      _currentUser = null;
      _clearError();
      notifyListeners();

      debugPrint('تم قطع الاتصال مع Google بنجاح');
    } catch (e) {
      _setError('فشل في قطع الاتصال: ${e.toString()}');
      debugPrint('Disconnect error: $e');
    }
  }

  /// التحقق من حالة تسجيل الدخول
  Future<bool> isSignedInSilently() async {
    try {
      final account = await _googleSignIn.signInSilently();
      _currentUser = account;
      notifyListeners();
      return account != null;
    } catch (e) {
      debugPrint('Silent sign in error: $e');
      return false;
    }
  }

  /// الحصول على معلومات المستخدم الحالي
  Map<String, dynamic>? getCurrentUserInfo() {
    if (_currentUser == null) return null;

    return {
      'id': _currentUser!.id,
      'email': _currentUser!.email,
      'displayName': _currentUser!.displayName,
      'photoUrl': _currentUser!.photoUrl,
      'serverAuthCode': _currentUser!.serverAuthCode,
    };
  }

  /// تحديث حالة تسجيل الدخول
  void _setSigningIn(bool value) {
    _isSigningIn = value;
    notifyListeners();
  }

  /// تحديد رسالة خطأ
  void _setError(String message) {
    _errorMessage = message;
    notifyListeners();
  }

  /// مسح رسالة الخطأ
  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  @override
  void dispose() {
    // تنظيف الموارد
    super.dispose();
  }
}
