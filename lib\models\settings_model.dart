import 'package:flutter/foundation.dart';

class SettingsModel extends ChangeNotifier {
  String _supportEmail = '<EMAIL>';
  String _whatsappNumber = '01207075721';

  String get supportEmail => _supportEmail;
  String get whatsappNumber => _whatsappNumber;

  void updateSupportEmail(String newEmail) {
    _supportEmail = newEmail;
    notifyListeners();
  }

  void updateWhatsappNumber(String newNumber) {
    _whatsappNumber = newNumber;
    notifyListeners();
  }
}
