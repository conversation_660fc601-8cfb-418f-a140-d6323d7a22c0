-- ===================================================================
-- ملف الدوال والمحفزات المفقودة من ملفات قاعدة البيانات
-- هذا الملف يحتوي على جميع الدوال والمحفزات الموجودة في قاعدة البيانات
-- ولكن غير موجودة في ملفات الهيكلة
-- ===================================================================

-- ===================================================================
-- دوال مزامنة البيانات الوصفية (تم إضافتها مؤخراً)
-- ===================================================================

-- دالة مزامنة البيانات الوصفية عند التحديث
CREATE OR REPLACE FUNCTION sync_user_metadata()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    -- عند تحديث بيانات auth.users
    IF TG_OP = 'UPDATE' THEN
        -- تحديث البيانات الوصفية في profiles
        UPDATE profiles
        SET
            raw_user_meta_data = NEW.raw_user_meta_data,
            email = COALESCE(NEW.email, email),
            phone = COALESCE(NEW.raw_user_meta_data->>'phone', NEW.phone, phone),
            updated_at = NOW()
        WHERE id = NEW.id;

        -- تحديث الاسم إذا كان متوفراً في البيانات الوصفية
        UPDATE profiles
        SET
            name = COALESCE(
                NEW.raw_user_meta_data->>'name',
                NEW.raw_user_meta_data->>'full_name',
                NEW.raw_user_meta_data->>'display_name',
                name
            ),
            updated_at = NOW()
        WHERE id = NEW.id
        AND (
            NEW.raw_user_meta_data->>'name' IS NOT NULL OR
            NEW.raw_user_meta_data->>'full_name' IS NOT NULL OR
            NEW.raw_user_meta_data->>'display_name' IS NOT NULL
        );

        RETURN NEW;
    END IF;

    RETURN NULL;
END;
$$;

-- دالة مزامنة جميع المستخدمين
CREATE OR REPLACE FUNCTION sync_all_user_metadata()
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    user_record RECORD;
    updated_count INTEGER := 0;
BEGIN
    -- مزامنة جميع المستخدمين
    FOR user_record IN
        SELECT
            au.id,
            au.email,
            au.phone,
            au.raw_user_meta_data
        FROM auth.users au
        JOIN profiles p ON au.id = p.id
    LOOP
        -- تحديث البيانات الوصفية
        UPDATE profiles
        SET
            raw_user_meta_data = user_record.raw_user_meta_data,
            email = COALESCE(user_record.email, email),
            phone = COALESCE(
                user_record.raw_user_meta_data->>'phone',
                user_record.phone,
                phone
            ),
            name = COALESCE(
                user_record.raw_user_meta_data->>'name',
                user_record.raw_user_meta_data->>'full_name',
                user_record.raw_user_meta_data->>'display_name',
                name
            ),
            updated_at = NOW()
        WHERE id = user_record.id;

        updated_count := updated_count + 1;
    END LOOP;

    RETURN 'تم تحديث ' || updated_count || ' مستخدم';
END;
$$;

-- محفز مزامنة البيانات الوصفية عند التحديث
DROP TRIGGER IF EXISTS sync_user_metadata_trigger ON auth.users;
CREATE TRIGGER sync_user_metadata_trigger
    AFTER UPDATE ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION sync_user_metadata();

-- ===================================================================
-- دالة تحديث إحصائيات المستخدم (محسنة وآمنة)
-- ===================================================================

CREATE OR REPLACE FUNCTION update_user_stats()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        IF TG_TABLE_NAME = 'orders' AND NEW.status = 'delivered' THEN
            UPDATE profiles SET
                order_count = order_count + 1,
                total_spent = total_spent + NEW.total_amount
            WHERE id = NEW.user_id;
        END IF;

        IF TG_TABLE_NAME = 'wishlists' THEN
            UPDATE profiles SET wishlist_count = wishlist_count + 1 WHERE id = NEW.user_id;
        END IF;
        RETURN NEW;
    END IF;

    IF TG_OP = 'UPDATE' THEN
        IF TG_TABLE_NAME = 'orders' THEN
            IF OLD.status <> 'delivered' AND NEW.status = 'delivered' THEN
                UPDATE profiles SET
                    order_count = order_count + 1,
                    total_spent = total_spent + NEW.total_amount
                WHERE id = NEW.user_id;
            ELSIF OLD.status = 'delivered' AND NEW.status <> 'delivered' THEN
                UPDATE profiles SET
                    order_count = order_count - 1,
                    total_spent = total_spent - NEW.total_amount
                WHERE id = NEW.user_id;
            END IF;
        END IF;
        RETURN NEW;
    END IF;

    IF TG_OP = 'DELETE' THEN
        IF TG_TABLE_NAME = 'wishlists' THEN
            UPDATE profiles SET wishlist_count = wishlist_count - 1 WHERE id = OLD.user_id;
        END IF;
        RETURN OLD;
    END IF;

    RETURN NULL;
END;
$$;

-- ===================================================================
-- دوال الأمان والصلاحيات (محسنة)
-- ===================================================================

-- دالة فحص صلاحيات الإدارة
CREATE OR REPLACE FUNCTION is_admin(user_id UUID DEFAULT auth.uid())
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    IF user_id IS NULL THEN
        RETURN FALSE;
    END IF;

    RETURN EXISTS (
        SELECT 1 FROM profiles
        WHERE id = user_id
        AND profile_type = 'admin'
    );
END;
$$;

-- دالة فحص الوصول للطلبات
CREATE OR REPLACE FUNCTION can_access_order(order_id UUID, user_id UUID DEFAULT auth.uid())
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    IF user_id IS NULL THEN
        RETURN FALSE;
    END IF;

    -- المدير يمكنه الوصول لجميع الطلبات
    IF is_admin(user_id) THEN
        RETURN TRUE;
    END IF;

    -- المستخدم يمكنه الوصول لطلباته فقط
    RETURN EXISTS (
        SELECT 1 FROM orders
        WHERE id = order_id
        AND user_id = can_access_order.user_id
    );
END;
$$;

-- دالة فحص الوصول لتحليلات البحث
CREATE OR REPLACE FUNCTION can_access_search_analytics(user_id UUID DEFAULT auth.uid())
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    RETURN is_admin(user_id);
END;
$$;

-- دالة تعيين سياق المستخدم
CREATE OR REPLACE FUNCTION set_user_context()
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    -- تعيين نوع المستخدم الحالي
    IF auth.uid() IS NOT NULL THEN
        PERFORM set_config(
            'app.current_user_type',
            COALESCE(
                (SELECT profile_type FROM profiles WHERE id = auth.uid()),
                'customer'
            ),
            true
        );
    ELSE
        PERFORM set_config('app.current_user_type', 'anonymous', true);
    END IF;
END;
$$;

-- دالة تعيين سياق الإدارة
CREATE OR REPLACE FUNCTION set_admin_context(user_id UUID)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    IF is_admin(user_id) THEN
        PERFORM set_config('app.current_user_type', 'admin', true);
        PERFORM set_config('app.admin_user_id', user_id::TEXT, true);
    ELSE
        RAISE EXCEPTION 'المستخدم ليس مديراً';
    END IF;
END;
$$;

-- ===================================================================
-- رسالة نجاح
-- ===================================================================

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '✅ تم إضافة الدوال والمحفزات المفقودة بنجاح!';
    RAISE NOTICE '';
    RAISE NOTICE '🔄 الدوال المضافة:';
    RAISE NOTICE '   • sync_user_metadata - مزامنة البيانات الوصفية';
    RAISE NOTICE '   • sync_all_user_metadata - مزامنة جميع المستخدمين';
    RAISE NOTICE '   • update_user_stats - تحديث إحصائيات المستخدم';
    RAISE NOTICE '';
    RAISE NOTICE '🎯 المحفزات المضافة:';
    RAISE NOTICE '   • sync_user_metadata_trigger - مزامنة تلقائية للبيانات الوصفية';
    RAISE NOTICE '';
    RAISE NOTICE '📝 ملاحظة: هذا الملف يحتوي على الدوال والمحفزات المفقودة فقط';
    RAISE NOTICE '   باقي الدوال موجودة في ملفات أخرى أو تم إنشاؤها تلقائياً';
    RAISE NOTICE '';
END $$;
