import 'package:flutter/material.dart';

/// مؤشر التحميل
/// يستخدم لعرض حالة التحميل في التطبيق
class LoadingIndicator extends StatelessWidget {
  final String? text;
  final Color? color;
  final double size;

  const LoadingIndicator({super.key, this.text, this.color, this.size = 24.0});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SizedBox(
          width: size,
          height: size,
          child: CircularProgressIndicator(
            valueColor:
                color != null ? AlwaysStoppedAnimation<Color>(color!) : null,
            strokeWidth: 2.0,
          ),
        ),
        if (text != null) ...[
          const SizedBox(height: 16.0),
          Text(
            text!,
            style: TextStyle(color: color, fontSize: 16.0),
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );
  }
}
