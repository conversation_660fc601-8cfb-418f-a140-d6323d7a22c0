import 'dart:math' as math;
import 'package:flutter/material.dart';

/// مساعد التصميم المتجاوب للتطبيق
class ResponsiveHelper {
  // نقاط التوقف للشاشات
  static const double mobileBreakpoint = 600;
  static const double tabletBreakpoint = 1024;
  static const double desktopBreakpoint = 1440;

  /// التحقق من نوع الجهاز
  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < mobileBreakpoint;
  }

  static bool isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= mobileBreakpoint && width < tabletBreakpoint;
  }

  static bool isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= tabletBreakpoint;
  }

  /// الحصول على نوع الجهاز
  static DeviceType getDeviceType(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    if (width < mobileBreakpoint) return DeviceType.mobile;
    if (width < tabletBreakpoint) return DeviceType.tablet;
    return DeviceType.desktop;
  }

  /// الحصول على المساحة المناسبة حسب نوع الجهاز
  static double getPadding(
    BuildContext context, {
    double mobile = 16.0,
    double tablet = 24.0,
    double desktop = 32.0,
  }) {
    if (isMobile(context)) return mobile;
    if (isTablet(context)) return tablet;
    return desktop;
  }

  /// الحصول على حجم الخط المناسب
  static double getFontSize(
    BuildContext context,
    double baseSize, {
    double mobileScale = 1.0,
    double tabletScale = 1.1,
    double desktopScale = 1.2,
  }) {
    if (isMobile(context)) return baseSize * mobileScale;
    if (isTablet(context)) return baseSize * tabletScale;
    return baseSize * desktopScale;
  }

  /// الحصول على عدد الأعمدة في الشبكة
  static int getGridColumns(
    BuildContext context, {
    int mobileColumns = 2,
    int tabletColumns = 3,
    int desktopColumns = 4,
    int? maxColumns,
  }) {
    int columns;
    if (isMobile(context)) {
      columns = mobileColumns;
    } else if (isTablet(context)) {
      columns = tabletColumns;
    } else {
      columns = desktopColumns;
    }

    if (maxColumns != null && columns > maxColumns) {
      columns = maxColumns;
    }

    return columns;
  }

  /// الحصول على نسبة العرض إلى الارتفاع
  static double getAspectRatio(
    BuildContext context, {
    double mobile = 0.8,
    double tablet = 0.9,
    double desktop = 1.0,
  }) {
    if (isMobile(context)) return mobile;
    if (isTablet(context)) return tablet;
    return desktop;
  }

  /// الحصول على عرض البطاقة المناسب
  static double getCardWidth(
    BuildContext context, {
    double minWidth = 150,
    double maxWidth = 300,
    double? fixedWidth,
  }) {
    if (fixedWidth != null) return fixedWidth;

    final screenWidth = MediaQuery.of(context).size.width;
    final padding = getPadding(context);
    final availableWidth = screenWidth - (padding * 2);

    if (isMobile(context)) {
      return math.min(maxWidth, math.max(minWidth, availableWidth * 0.45));
    } else if (isTablet(context)) {
      return math.min(maxWidth, math.max(minWidth, availableWidth * 0.3));
    } else {
      return math.min(maxWidth, math.max(minWidth, availableWidth * 0.25));
    }
  }

  /// الحصول على ارتفاع شريط التطبيق
  static double getAppBarHeight(BuildContext context) {
    if (isMobile(context)) return 120.0;
    if (isTablet(context)) return 140.0;
    return 160.0;
  }

  /// الحصول على ارتفاع الزر
  static double getButtonHeight(BuildContext context) {
    if (isMobile(context)) return 48.0;
    if (isTablet(context)) return 52.0;
    return 56.0;
  }

  /// الحصول على حجم الأيقونة
  static double getIconSize(
    BuildContext context, {
    double mobile = 24.0,
    double tablet = 28.0,
    double desktop = 32.0,
  }) {
    if (isMobile(context)) return mobile;
    if (isTablet(context)) return tablet;
    return desktop;
  }

  /// الحصول على المساحة بين العناصر
  static double getSpacing(
    BuildContext context, {
    double mobile = 8.0,
    double tablet = 12.0,
    double desktop = 16.0,
  }) {
    if (isMobile(context)) return mobile;
    if (isTablet(context)) return tablet;
    return desktop;
  }

  /// الحصول على نصف قطر الحواف
  static double getBorderRadius(
    BuildContext context, {
    double mobile = 8.0,
    double tablet = 10.0,
    double desktop = 12.0,
  }) {
    if (isMobile(context)) return mobile;
    if (isTablet(context)) return tablet;
    return desktop;
  }

  /// الحصول على حجم الصورة
  static double getImageSize(
    BuildContext context, {
    double mobile = 80.0,
    double tablet = 100.0,
    double desktop = 120.0,
  }) {
    if (isMobile(context)) return mobile;
    if (isTablet(context)) return tablet;
    return desktop;
  }

  /// الحصول على عرض الحاوية الرئيسية
  static double getMaxContentWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (isDesktop(context)) {
      return math.min(screenWidth * 0.8, 1200);
    }
    return screenWidth;
  }

  /// الحصول على المساحة الآمنة
  static EdgeInsets getSafeAreaPadding(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    return EdgeInsets.only(
      top: mediaQuery.padding.top,
      bottom: mediaQuery.padding.bottom,
      left: mediaQuery.padding.left,
      right: mediaQuery.padding.right,
    );
  }

  /// التحقق من الاتجاه
  static bool isLandscape(BuildContext context) {
    return MediaQuery.of(context).orientation == Orientation.landscape;
  }

  static bool isPortrait(BuildContext context) {
    return MediaQuery.of(context).orientation == Orientation.portrait;
  }

  /// الحصول على ارتفاع الشاشة المتاح
  static double getAvailableHeight(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    return mediaQuery.size.height -
        mediaQuery.padding.top -
        mediaQuery.padding.bottom -
        kToolbarHeight;
  }

  /// الحصول على عرض الشاشة المتاح
  static double getAvailableWidth(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    return mediaQuery.size.width -
        mediaQuery.padding.left -
        mediaQuery.padding.right;
  }

  /// الحصول على ارتفاع عنصر القائمة
  static double getListItemHeight(BuildContext context) {
    if (isMobile(context)) return 280.0;
    if (isTablet(context)) return 320.0;
    return 360.0;
  }
}

/// أنواع الأجهزة
enum DeviceType { mobile, tablet, desktop }

/// ويجت بناء متجاوب
class ResponsiveBuilder extends StatelessWidget {
  final Widget Function(BuildContext context, BoxConstraints constraints)
  builder;
  final Widget? mobile;
  final Widget? tablet;
  final Widget? desktop;

  const ResponsiveBuilder({
    super.key,
    required this.builder,
    this.mobile,
    this.tablet,
    this.desktop,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // إذا تم توفير ويجت محدد لنوع الجهاز
        if (ResponsiveHelper.isMobile(context) && mobile != null) {
          return mobile!;
        }
        if (ResponsiveHelper.isTablet(context) && tablet != null) {
          return tablet!;
        }
        if (ResponsiveHelper.isDesktop(context) && desktop != null) {
          return desktop!;
        }

        // استخدام البناء المخصص
        return builder(context, constraints);
      },
    );
  }
}

/// ويجت للتخطيط المتجاوب
class ResponsiveLayout extends StatelessWidget {
  final Widget mobile;
  final Widget? tablet;
  final Widget? desktop;

  const ResponsiveLayout({
    super.key,
    required this.mobile,
    this.tablet,
    this.desktop,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, constraints) => mobile,
      mobile: mobile,
      tablet: tablet ?? mobile,
      desktop: desktop ?? tablet ?? mobile,
    );
  }
}
