import 'package:flutter_dotenv/flutter_dotenv.dart';

/// 🧠 تكوين Clarifai المتقدم والاحترافي
/// 
/// ملف تكوين شامل لجميع إعدادات Clarifai AI والخدمات المرتبطة
/// يدعم التخصيص المتقدم والنماذج المختلفة
class ClarifaiConfig {
  // 🔑 إعدادات المصادقة الأساسية
  static String get apiKey => dotenv.env['CLARIFAI_API_KEY'] ?? '';
  static String get userId => dotenv.env['CLARIFAI_USER_ID'] ?? '';
  static String get appId => dotenv.env['CLARIFAI_APP_ID'] ?? '';
  static String get baseUrl => 'https://api.clarifai.com';

  // 🌍 إعدادات الاتصال
  static const Duration defaultTimeout = Duration(seconds: 30);
  static const Duration uploadTimeout = Duration(minutes: 2);
  static const Duration analysisTimeout = Duration(minutes: 5);
  static const int maxRetries = 3;
  static const int maxConcurrentRequests = 5;

  // 🎯 نماذج Clarifai المدعومة
  static const Map<String, ModelConfig> supportedModels = {
    // نماذج التعرف العامة
    'general': ModelConfig(
      id: 'aaa03c23b3724a16a56b629203edc62c',
      name: 'General',
      type: ModelType.classification,
      description: 'تعرف عام على الكائنات والمفاهيم',
      maxImageSize: 20, // MB
      supportedFormats: ['jpg', 'jpeg', 'png', 'webp'],
    ),
    
    // نماذج كشف الكائنات
    'object_detection': ModelConfig(
      id: 'a403429f2ddf4b49b307e318f00e528b',
      name: 'General Detection',
      type: ModelType.detection,
      description: 'كشف وتحديد موقع الكائنات في الصور',
      maxImageSize: 20,
      supportedFormats: ['jpg', 'jpeg', 'png', 'webp'],
    ),

    // نماذج التعرف على الوجوه
    'face_detection': ModelConfig(
      id: 'a403429f2ddf4b49b307e318f00e528b',
      name: 'Face Detection',
      type: ModelType.faceDetection,
      description: 'كشف الوجوه والتعرف على المشاعر',
      maxImageSize: 10,
      supportedFormats: ['jpg', 'jpeg', 'png'],
    ),

    // نماذج تحليل النص
    'text_detection': ModelConfig(
      id: 'c386b7a870114f4a87477c0824499348',
      name: 'OCR',
      type: ModelType.textDetection,
      description: 'استخراج النص من الصور',
      maxImageSize: 15,
      supportedFormats: ['jpg', 'jpeg', 'png', 'pdf'],
    ),

    // نماذج تصنيف المحتوى
    'moderation': ModelConfig(
      id: 'e9576d86d2004ed1a38ba0cf39ecb4b1',
      name: 'Moderation',
      type: ModelType.moderation,
      description: 'كشف المحتوى غير المناسب',
      maxImageSize: 20,
      supportedFormats: ['jpg', 'jpeg', 'png', 'webp'],
    ),

    // نماذج تحليل الألوان
    'color_analysis': ModelConfig(
      id: 'eeed0b6733a644cea07cf4c60f87ebb7',
      name: 'Color',
      type: ModelType.colorAnalysis,
      description: 'تحليل الألوان السائدة في الصور',
      maxImageSize: 20,
      supportedFormats: ['jpg', 'jpeg', 'png', 'webp'],
    ),

    // نماذج مخصصة للمنتجات
    'product_recognition': ModelConfig(
      id: 'custom_product_model',
      name: 'Product Recognition',
      type: ModelType.custom,
      description: 'تعرف على منتجات قطع غيار الدراجات',
      maxImageSize: 25,
      supportedFormats: ['jpg', 'jpeg', 'png', 'webp'],
    ),
  };

  // 🎨 إعدادات معالجة الصور
  static const ImageProcessingConfig imageProcessing = ImageProcessingConfig(
    autoResize: true,
    maxWidth: 2048,
    maxHeight: 2048,
    quality: 85,
    autoRotate: true,
    removeExif: true,
  );

  // 📊 إعدادات التحليل
  static const AnalysisConfig analysisConfig = AnalysisConfig(
    minConfidence: 0.7,
    maxConcepts: 20,
    enableHierarchy: true,
    enableRegions: true,
    enableColors: true,
    enableFaces: false, // معطل للخصوصية
  );

  // 💾 إعدادات التخزين المؤقت
  static const Duration cacheExpiry = Duration(hours: 24);
  static const int maxCacheSize = 1000; // عدد النتائج المحفوظة
  static const int maxCacheSizeMB = 50; // حجم الذاكرة المؤقتة

  // 🔄 إعدادات المعالجة المتوازية
  static const int batchSize = 10;
  static const Duration batchDelay = Duration(milliseconds: 100);
  static const int maxQueueSize = 100;

  // 🛡️ إعدادات الأمان والخصوصية
  static const SecurityConfig security = SecurityConfig(
    enableEncryption: true,
    enableDataMinimization: true,
    enableAnonymization: true,
    dataRetentionDays: 30,
    enableAuditLog: true,
  );

  // 📈 إعدادات المراقبة والتحليلات
  static const MonitoringConfig monitoring = MonitoringConfig(
    enableMetrics: true,
    enableErrorTracking: true,
    enablePerformanceMonitoring: true,
    metricsInterval: Duration(minutes: 5),
  );

  // 🌍 إعدادات البيئة
  static Environment getCurrentEnvironment() {
    final env = dotenv.env['ENVIRONMENT'] ?? 'development';
    switch (env.toLowerCase()) {
      case 'production':
        return Environment.production;
      case 'staging':
        return Environment.staging;
      case 'testing':
        return Environment.testing;
      default:
        return Environment.development;
    }
  }

  // ⚙️ إعدادات مخصصة للبيئة
  static Map<String, dynamic> getEnvironmentConfig() {
    final env = getCurrentEnvironment();
    
    switch (env) {
      case Environment.production:
        return {
          'max_requests_per_minute': 1000,
          'enable_debug': false,
          'cache_duration_hours': 48,
          'min_confidence': 0.8,
          'enable_detailed_logging': false,
        };
      case Environment.staging:
        return {
          'max_requests_per_minute': 500,
          'enable_debug': true,
          'cache_duration_hours': 24,
          'min_confidence': 0.7,
          'enable_detailed_logging': true,
        };
      case Environment.testing:
        return {
          'max_requests_per_minute': 100,
          'enable_debug': true,
          'cache_duration_hours': 1,
          'min_confidence': 0.5,
          'enable_detailed_logging': true,
        };
      default: // development
        return {
          'max_requests_per_minute': 200,
          'enable_debug': true,
          'cache_duration_hours': 12,
          'min_confidence': 0.6,
          'enable_detailed_logging': true,
        };
    }
  }

  // 🔍 التحقق من صحة التكوين
  static bool validateConfig() {
    final errors = <String>[];

    if (apiKey.isEmpty) errors.add('CLARIFAI_API_KEY مطلوب');
    if (userId.isEmpty) errors.add('CLARIFAI_USER_ID مطلوب');
    if (appId.isEmpty) errors.add('CLARIFAI_APP_ID مطلوب');

    if (errors.isNotEmpty) {
      throw Exception('أخطاء في تكوين Clarifai: ${errors.join(', ')}');
    }

    return true;
  }

  // 🎯 الحصول على تكوين نموذج محدد
  static ModelConfig? getModelConfig(String modelKey) {
    return supportedModels[modelKey];
  }

  // 📋 الحصول على قائمة النماذج حسب النوع
  static List<ModelConfig> getModelsByType(ModelType type) {
    return supportedModels.values
        .where((model) => model.type == type)
        .toList();
  }
}

/// 🎯 تكوين النموذج
class ModelConfig {
  final String id;
  final String name;
  final ModelType type;
  final String description;
  final int maxImageSize; // MB
  final List<String> supportedFormats;

  const ModelConfig({
    required this.id,
    required this.name,
    required this.type,
    required this.description,
    required this.maxImageSize,
    required this.supportedFormats,
  });
}

/// 🏷️ أنواع النماذج
enum ModelType {
  classification,
  detection,
  faceDetection,
  textDetection,
  moderation,
  colorAnalysis,
  custom,
}

/// 🎨 تكوين معالجة الصور
class ImageProcessingConfig {
  final bool autoResize;
  final int maxWidth;
  final int maxHeight;
  final int quality;
  final bool autoRotate;
  final bool removeExif;

  const ImageProcessingConfig({
    required this.autoResize,
    required this.maxWidth,
    required this.maxHeight,
    required this.quality,
    required this.autoRotate,
    required this.removeExif,
  });
}

/// 📊 تكوين التحليل
class AnalysisConfig {
  final double minConfidence;
  final int maxConcepts;
  final bool enableHierarchy;
  final bool enableRegions;
  final bool enableColors;
  final bool enableFaces;

  const AnalysisConfig({
    required this.minConfidence,
    required this.maxConcepts,
    required this.enableHierarchy,
    required this.enableRegions,
    required this.enableColors,
    required this.enableFaces,
  });
}

/// 🛡️ تكوين الأمان
class SecurityConfig {
  final bool enableEncryption;
  final bool enableDataMinimization;
  final bool enableAnonymization;
  final int dataRetentionDays;
  final bool enableAuditLog;

  const SecurityConfig({
    required this.enableEncryption,
    required this.enableDataMinimization,
    required this.enableAnonymization,
    required this.dataRetentionDays,
    required this.enableAuditLog,
  });
}

/// 📈 تكوين المراقبة
class MonitoringConfig {
  final bool enableMetrics;
  final bool enableErrorTracking;
  final bool enablePerformanceMonitoring;
  final Duration metricsInterval;

  const MonitoringConfig({
    required this.enableMetrics,
    required this.enableErrorTracking,
    required this.enablePerformanceMonitoring,
    required this.metricsInterval,
  });
}

/// 🌍 بيئات التشغيل
enum Environment {
  development,
  testing,
  staging,
  production,
}
