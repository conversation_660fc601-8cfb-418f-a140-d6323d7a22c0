
// نموذج بيانات المستخدم

/// نتيجة التحقق من صحة البيانات
class ValidationResult {
  final bool isValid;
  final List<String> errors;
  final List<String> warnings;

  const ValidationResult({
    required this.isValid,
    required this.errors,
    required this.warnings,
  });

  bool get hasErrors => errors.isNotEmpty;
  bool get hasWarnings => warnings.isNotEmpty;

  String get errorMessage => errors.join(', ');
  String get warningMessage => warnings.join(', ');
}

class UserModel {
  // معرف المستخدم الفريد (UUID)
  final String id;

  // عدد الطلبات (اختياري)
  final int? orderCount;

  // عدد العناصر في قائمة الرغبات (اختياري)
  final int? wishlistCount;

  // متوسط التقييم (اختياري)
  final double? averageRating;

  // إجمالي المشتريات (اختياري)
  final double? totalSpent;

  // اسم المستخدم
  final String name;

  // البريد الإلكتروني للمستخدم
  final String email;

  // عنوان المستخدم (اختياري) - للتوافق الخلفي
  final String? address;

  // رقم الهاتف (اختياري) - للتوافق الخلفي
  final String? phone;

  // تم إزالة الحقول المشفرة كجزء من تنظيف المشروع

  // تم حذف رابط صورة المستخدم

  // نوع الملف الشخصي (customer أو admin)
  final String profileType;

  // صلاحيات المستخدم (مصفوفة نصوص)
  // final List<String>? permissions;

  // تاريخ الميلاد (اختياري)
  final DateTime? birthDate;

  // المحافظة (اختياري)
  final String? governorate;

  // المركز (اختياري)
  final String? center;

  // تاريخ آخر تسجيل دخول (اختياري)
  final DateTime? lastLoginAt;

  // تاريخ إنشاء المستخدم
  final DateTime createdAt;

  // تاريخ آخر تحديث للمستخدم
  final DateTime updatedAt;

  // بيانات وصفية خام للمستخدم (اختياري)
  final Map<String, dynamic>? rawUserMetaData;

  // الحقول الجديدة المضافة للتوافق مع قاعدة البيانات
  final int? unreadNotificationsCount;
  final int? totalNotificationsCount;
  final DateTime? lastNotificationCheck;
  final bool isActive;

  // حالة التحقق من البريد الإلكتروني
  // final bool emailVerified;

  // تم دمج بيانات المستخدم الإضافية مباشرة في النموذج الرئيسي

  // بناء كائن UserModel
  UserModel({
    required this.id,
    required this.name,
    required this.email,
    this.address,
    this.phone,
    required this.profileType,
    this.birthDate,
    this.governorate,
    this.center,
    this.lastLoginAt,
    required this.createdAt,
    required this.updatedAt,
    this.orderCount,
    this.wishlistCount,
    this.averageRating,
    this.totalSpent,
    this.rawUserMetaData,
    this.unreadNotificationsCount,
    this.totalNotificationsCount,
    this.lastNotificationCheck,
    this.isActive = true,
  }) : assert(
         profileType == 'customer' || profileType == 'admin',
         'profileType يجب أن يكون إما "customer" أو "admin"',
       );

  // إنشاء كائن UserModel من JSON
  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'] as String,
      name: json['name'] as String,
      email: json['email'] as String,
      address: json['address'] as String?,
      phone: json['phone'] as String?,
      profileType: json['profile_type'] as String,
      // permissions: (json['permissions'] as List<dynamic>?)?.cast<String>(),
      birthDate:
          json['birth_date'] != null
              ? DateTime.parse(json['birth_date'] as String)
              : null,
      governorate: json['governorate'] as String?,
      center: json['center'] as String?,
      lastLoginAt:
          json['last_login_at'] != null
              ? DateTime.parse(json['last_login_at'] as String)
              : null,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      orderCount: json['order_count'] as int?,
      wishlistCount: json['wishlist_count'] as int?,
      averageRating: (json['average_rating'] as num?)?.toDouble(),
      totalSpent: (json['total_spent'] as num?)?.toDouble(),
      rawUserMetaData:
          json['raw_user_meta_data'] != null
              ? Map<String, dynamic>.from(json['raw_user_meta_data'])
              : null,
      unreadNotificationsCount: json['unread_notifications_count'] as int?,
      totalNotificationsCount: json['total_notifications_count'] as int?,
      lastNotificationCheck:
          json['last_notification_check'] != null
              ? DateTime.parse(json['last_notification_check'] as String)
              : null,
      isActive: json['is_active'] as bool? ?? true,
    );
  }

  // تحويل كائن UserModel إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'address': address,
      'phone': phone,
      'profile_type': profileType,
      'birth_date': birthDate?.toIso8601String(),
      'governorate': governorate,
      'center': center,
      'last_login_at': lastLoginAt?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'order_count': orderCount,
      'wishlist_count': wishlistCount,
      'average_rating': averageRating,
      'total_spent': totalSpent,
      'raw_user_meta_data': rawUserMetaData,
      'unread_notifications_count': unreadNotificationsCount,
      'total_notifications_count': totalNotificationsCount,
      'last_notification_check': lastNotificationCheck?.toIso8601String(),
      'is_active': isActive,
    };
  }

  // إنشاء نسخة معدلة من الكائن مع إمكانية تغيير بعض القيم
  UserModel copyWith({
    String? id,
    String? name,
    String? email,
    String? address,
    String? phone,
    String? profileType,
    DateTime? birthDate,
    String? governorate,
    String? center,
    DateTime? lastLoginAt,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? orderCount,
    int? wishlistCount,
    double? averageRating,
    double? totalSpent,
    Map<String, dynamic>? rawUserMetaData,
    int? unreadNotificationsCount,
    int? totalNotificationsCount,
    DateTime? lastNotificationCheck,
    bool? isActive,
  }) {
    return UserModel(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      address: address ?? this.address,
      phone: phone ?? this.phone,
      profileType: profileType ?? this.profileType,
      birthDate: birthDate ?? this.birthDate,
      governorate: governorate ?? this.governorate,
      center: center ?? this.center,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      orderCount: orderCount ?? this.orderCount,
      wishlistCount: wishlistCount ?? this.wishlistCount,
      averageRating: averageRating ?? this.averageRating,
      totalSpent: totalSpent ?? this.totalSpent,
      rawUserMetaData: rawUserMetaData ?? this.rawUserMetaData,
      unreadNotificationsCount:
          unreadNotificationsCount ?? this.unreadNotificationsCount,
      totalNotificationsCount:
          totalNotificationsCount ?? this.totalNotificationsCount,
      lastNotificationCheck:
          lastNotificationCheck ?? this.lastNotificationCheck,
      isActive: isActive ?? this.isActive,
    );
  }

  // التحقق مما إذا كان المستخدم هو مدير
  bool get isAdmin => profileType == 'admin';

  // التحقق مما إذا كان المستخدم هو عميل
  bool get isCustomer => profileType == 'customer';

  // التحقق من وجود إشعارات غير مقروءة
  bool get hasUnreadNotifications => (unreadNotificationsCount ?? 0) > 0;

  // الحصول على عدد الإشعارات غير المقروءة
  int get unreadCount => unreadNotificationsCount ?? 0;

  // الحصول على إجمالي عدد الإشعارات
  int get totalNotifications => totalNotificationsCount ?? 0;

  /// التحقق من صحة بيانات المستخدم
  ValidationResult validateUser() {
    final errors = <String>[];
    final warnings = <String>[];

    // التحقق من الحقول الأساسية
    if (id.isEmpty) errors.add('معرف المستخدم مطلوب');
    if (name.isEmpty) errors.add('اسم المستخدم مطلوب');
    if (name.length < 2) warnings.add('اسم المستخدم قصير جداً');
    if (name.length > 100) errors.add('اسم المستخدم طويل جداً');

    // التحقق من البريد الإلكتروني
    if (email.isEmpty) errors.add('البريد الإلكتروني مطلوب');
    if (!_isValidEmail(email)) errors.add('البريد الإلكتروني غير صالح');

    // التحقق من رقم الهاتف
    if (phone != null && phone!.isNotEmpty && !_isValidPhone(phone!)) {
      warnings.add('رقم الهاتف قد يكون غير صالح');
    }

    // التحقق من نوع الملف الشخصي
    if (!['customer', 'admin'].contains(profileType)) {
      errors.add('نوع الملف الشخصي غير صالح');
    }

    // التحقق من تاريخ الميلاد
    if (birthDate != null) {
      final now = DateTime.now();
      final age = now.difference(birthDate!).inDays ~/ 365;
      if (age < 13) warnings.add('العمر صغير جداً');
      if (age > 120) warnings.add('العمر كبير جداً');
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  /// التحقق من صحة البريد الإلكتروني
  bool _isValidEmail(String email) {
    return RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    ).hasMatch(email);
  }

  /// التحقق من صحة رقم الهاتف
  bool _isValidPhone(String phone) {
    // التحقق من الأرقام المصرية
    return RegExp(r'^(\+20|0)?1[0-9]{9}$').hasMatch(phone.replaceAll(' ', ''));
  }
}
