import 'package:flutter/material.dart';

class WatermarkConfig {
  // الخصائص الافتراضية للعلامة المائية
  static const String _defaultWatermarkText = 'Motorcycle Parts Shop';
  static const double _defaultWatermarkOpacity = 0.3;
  static const double _defaultWatermarkRotation = -45.0; // بالدرجات
  static const double _defaultWatermarkFontSize = 20.0;
  static const Color _defaultWatermarkColor = Colors.black;
  static const FontWeight _defaultWatermarkFontWeight = FontWeight.bold;
  static const String _defaultWatermarkFontFamily = 'Cairo';

  // الخصائص القابلة للتخصيص
  final String watermarkText;
  final double watermarkOpacity;
  final double watermarkRotation;
  final double watermarkFontSize;
  final Color watermarkColor;
  final FontWeight watermarkFontWeight;
  final String watermarkFontFamily;

  // مُنشئ افتراضي مع قيم اختيارية
  const WatermarkConfig({
    this.watermarkText = _defaultWatermarkText,
    this.watermarkOpacity = _defaultWatermarkOpacity,
    this.watermarkRotation = _defaultWatermarkRotation,
    this.watermarkFontSize = _defaultWatermarkFontSize,
    this.watermarkColor = _defaultWatermarkColor,
    this.watermarkFontWeight = _defaultWatermarkFontWeight,
    this.watermarkFontFamily = _defaultWatermarkFontFamily,
  });

  // نسخة مخصصة مع إمكانية التعديل
  WatermarkConfig copyWith({
    String? watermarkText,
    double? watermarkOpacity,
    double? watermarkRotation,
    double? watermarkFontSize,
    Color? watermarkColor,
    FontWeight? watermarkFontWeight,
    String? watermarkFontFamily,
  }) {
    return WatermarkConfig(
      watermarkText: watermarkText ?? this.watermarkText,
      watermarkOpacity: watermarkOpacity ?? this.watermarkOpacity,
      watermarkRotation: watermarkRotation ?? this.watermarkRotation,
      watermarkFontSize: watermarkFontSize ?? this.watermarkFontSize,
      watermarkColor: watermarkColor ?? this.watermarkColor,
      watermarkFontWeight: watermarkFontWeight ?? this.watermarkFontWeight,
      watermarkFontFamily: watermarkFontFamily ?? this.watermarkFontFamily,
    );
  }

  // إنشاء نمط النص بناءً على التكوين
  TextStyle get watermarkTextStyle => TextStyle(
    color: watermarkColor.withOpacity(watermarkOpacity),
    fontSize: watermarkFontSize,
    fontWeight: watermarkFontWeight,
    fontFamily: watermarkFontFamily,
  );

  // تحويل الزاوية إلى راديان للاستخدام في الرسومات
  double get watermarkRotationInRadians =>
      watermarkRotation * (3.14159 / 180.0);

  // تكوين افتراضي كمثيل ثابت
  static const WatermarkConfig defaultConfig = WatermarkConfig();

  // تكوين مخصص للسمات الداكنة
  static const WatermarkConfig darkThemeConfig = WatermarkConfig(
    watermarkColor: Colors.white,
    watermarkOpacity: 0.2,
  );
}
