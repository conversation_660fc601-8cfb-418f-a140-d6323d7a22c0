
class PaymentMethodModel {
  final String id;
  final String name;
  final String? description;
  final String type;
  final bool isActive;
  final Map<String, dynamic>? config;
  final double? minAmount;
  final double? maxAmount;
  final DateTime createdAt;
  final DateTime updatedAt;

  PaymentMethodModel({
    required this.id,
    required this.name,
    this.description,
    required this.type,
    required this.isActive,
    this.config,
    this.minAmount,
    this.maxAmount,
    required this.createdAt,
    required this.updatedAt,
  }) : assert(
         type == 'cash' ||
             type == 'wallet' ||
             type == 'instapay' ||
             type == 'credit_card' ||
             type == 'bank_transfer',
         'نوع طريقة الدفع يجب أن يكون cash أو wallet أو instapay أو credit_card أو bank_transfer',
       );

  factory PaymentMethodModel.fromJson(Map<String, dynamic> json) {
    return PaymentMethodModel(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      type: json['type'] as String,
      isActive: json['is_active'] as bool? ?? false,
      config:
          json['config'] != null
              ? Map<String, dynamic>.from(json['config'] as Map)
              : null,
      minAmount:
          json['min_amount'] != null
              ? (json['min_amount'] as num).toDouble()
              : null,
      maxAmount:
          json['max_amount'] != null
              ? (json['max_amount'] as num).toDouble()
              : null,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'type': type,
      'is_active': isActive,
      'config': config,
      'min_amount': minAmount,
      'max_amount': maxAmount,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  PaymentMethodModel copyWith({
    String? id,
    String? name,
    String? description,
    String? type,
    bool? isActive,
    Map<String, dynamic>? config,
    double? minAmount,
    double? maxAmount,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return PaymentMethodModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      type: type ?? this.type,
      isActive: isActive ?? this.isActive,
      config: config ?? this.config,
      minAmount: minAmount ?? this.minAmount,
      maxAmount: maxAmount ?? this.maxAmount,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  bool get isCash => type == 'cash';
  bool get isWallet => type == 'wallet';
  bool get isInstapay => type == 'instapay';
  bool get requiresConfig => !isCash;

  String get displayName {
    switch (type) {
      case 'cash':
        return 'الدفع عند الاستلام';
      case 'wallet':
        return 'محفظة إلكترونية';
      case 'instapay':
        return 'انستا باي';
      default:
        return name;
    }
  }

  String? get phoneNumber => config?['phone_number'] as String?;
}
