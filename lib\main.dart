import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:motorcycle_parts_shop/core/services/accessibility_service.dart';
import 'package:motorcycle_parts_shop/core/services/address_service.dart';
import 'package:motorcycle_parts_shop/core/services/admin_service.dart';
import 'package:motorcycle_parts_shop/core/services/advanced_search_service.dart';
import 'package:motorcycle_parts_shop/core/services/advertisement_service.dart';
import 'package:motorcycle_parts_shop/core/services/ai_services.dart';
import 'package:motorcycle_parts_shop/core/services/analytics_service.dart';
import 'package:motorcycle_parts_shop/core/services/backup_service.dart';
import 'package:motorcycle_parts_shop/core/services/cart_service.dart';
import 'package:motorcycle_parts_shop/core/services/chatbot_service.dart';
import 'package:motorcycle_parts_shop/core/services/connectivity_service.dart';
import 'package:motorcycle_parts_shop/core/services/coupon_service.dart';
import 'package:motorcycle_parts_shop/core/services/currency_service.dart';
import 'package:motorcycle_parts_shop/core/services/data_integrity_service.dart';
import 'package:motorcycle_parts_shop/core/services/device_capability_service.dart';
import 'package:motorcycle_parts_shop/core/services/favorites_service.dart';
import 'package:motorcycle_parts_shop/core/services/google_auth_service.dart';
import 'package:motorcycle_parts_shop/core/services/image_recognition_service.dart';
import 'package:motorcycle_parts_shop/core/services/inventory_service.dart';
import 'package:motorcycle_parts_shop/core/services/local_storage_service.dart';
import 'package:motorcycle_parts_shop/core/services/localization_service.dart';
import 'package:motorcycle_parts_shop/core/services/navigation_service.dart';
import 'package:motorcycle_parts_shop/core/services/notification_service.dart';
import 'package:motorcycle_parts_shop/core/services/offline_mode_service.dart';
import 'package:motorcycle_parts_shop/core/services/order_service.dart';
import 'package:motorcycle_parts_shop/core/services/product_service.dart';
import 'package:motorcycle_parts_shop/core/services/sync_manager.dart';
import 'package:motorcycle_parts_shop/core/services/theme_service.dart';
import 'package:motorcycle_parts_shop/core/services/unified_storage_service.dart';
import 'package:motorcycle_parts_shop/core/services/user_interaction_service.dart';
import 'package:motorcycle_parts_shop/core/utils/performance_optimizer.dart';
import 'package:motorcycle_parts_shop/models/product_model.dart';
import 'package:motorcycle_parts_shop/screens/admin/add_edit_product_screen.dart';
import 'package:motorcycle_parts_shop/screens/admin/inventory_management_screen.dart';
import 'package:motorcycle_parts_shop/screens/auth/change_password_screen.dart';
import 'package:motorcycle_parts_shop/screens/auth/login_screen.dart';
import 'package:motorcycle_parts_shop/screens/auth/register_screen.dart';
import 'package:motorcycle_parts_shop/screens/cart/cart_screen.dart';
import 'package:motorcycle_parts_shop/screens/home/<USER>';
import 'package:motorcycle_parts_shop/screens/onboarding/onboarding_screen.dart';
import 'package:motorcycle_parts_shop/screens/product/product_details_screen.dart';
import 'package:motorcycle_parts_shop/screens/profile/notification_settings_screen.dart';
import 'package:motorcycle_parts_shop/screens/settings/security_activity_screen.dart';
import 'package:motorcycle_parts_shop/screens/welcome_screen.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  await PerformanceOptimizer.initialize();

  Provider.debugCheckInvalidValueType = null;

  try {
    await dotenv.load(fileName: ".env");

    final supabaseUrl = dotenv.env['SUPABASE_URL'];
    final supabaseAnonKey = dotenv.env['SUPABASE_ANON_KEY'];

    if (supabaseUrl == null || supabaseAnonKey == null) {
      throw Exception('المتغيرات البيئية المطلوبة غير موجودة');
    }

    // تهيئة SharedPreferences والخدمات الأساسية بالتوازي
    final initFutures = await Future.wait([
      SharedPreferences.getInstance(),
      _initializeSupabase(supabaseUrl, supabaseAnonKey),
    ]);

    final prefs = initFutures[0] as SharedPreferences;

    final services = await _initializeEssentialServices(prefs);

    // تعيين اتجاه الشاشة
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);

    // تشغيل التطبيق فوراً
    runApp(MyApp(prefs: prefs, services: services));

    // تهيئة الخدمات الإضافية في الخلفية
    _initializeAdditionalServicesInBackground(services);
  } catch (e) {
    debugPrint('خطأ في تهيئة التطبيق: $e');
    runApp(const ErrorApp());
  }
}

/// تهيئة Supabase بسرعة
Future<void> _initializeSupabase(String url, String anonKey) async {
  try {
    await Supabase.initialize(
      url: url,
      anonKey: anonKey,
      authOptions: const FlutterAuthClientOptions(autoRefreshToken: true),
    );
  } catch (e) {
    debugPrint('خطأ في تهيئة Supabase: $e');
    // لا نرمي استثناء للسماح للتطبيق بالعمل
  }
}

/// تهيئة الخدمات الأساسية فقط للسرعة
Future<AppServices> _initializeEssentialServices(
  SharedPreferences prefs,
) async {
  final supabaseClient = Supabase.instance.client;

  // إنشاء الخدمات الأساسية فقط
  final unifiedStorageService = UnifiedStorageService();
  final localStorageService = LocalStorageService();
  final supabaseService = AuthSupabaseService();
  final googleAuthService = GoogleAuthService(supabaseService);
  final themeService = ThemeService(storageService: unifiedStorageService);
  final cartService = CartService();
  final favoritesService = FavoritesService();
  final navigationService = NavigationService();
  final connectivityService = ConnectivityService();
  final productService = ProductService(supabaseClient);
  final analyticsService = AnalyticsService(
    client: supabaseClient,
    storageService: unifiedStorageService,
    prefs: prefs,
  );
  final localizationService = LocalizationService();

  // تهيئة الخدمات الأساسية بالتوازي
  await Future.wait([
    unifiedStorageService.initialize(),
    localStorageService.initialize(),
    themeService.initialize(),
    cartService.initialize(),
    favoritesService.initialize(),
    connectivityService.initialize(),
    analyticsService.initialize(),
    localizationService.initialize(),
  ]);

  // تهيئة خدمة المصادقة بدون انتظار
  supabaseService.initialize().catchError((e) {
    debugPrint('خطأ في تهيئة خدمة المصادقة: $e');
  });

  return AppServices(
    unifiedStorageService: unifiedStorageService,
    localStorageService: localStorageService,
    supabaseService: supabaseService,
    googleAuthService: googleAuthService,
    themeService: themeService,
    cartService: cartService,
    favoritesService: favoritesService,
    navigationService: navigationService,

    connectivityService: connectivityService,
    productService: productService,
    analyticsService: analyticsService,
    localizationService: localizationService,
    aiServicesManager: AIServicesManager(),
    chatbotService: ChatbotService(supabaseService),
    syncManager: SyncManager(
      supabase: supabaseClient,
      cartService: cartService,
      favoritesService: favoritesService,
      localStorageService: unifiedStorageService,
    ),

    accessibilityService: AccessibilityService(),
    // خدمات افتراضية للباقي
    addressService: AddressService(),
    adminService: AdminService(client: supabaseClient),
    advancedSearchService: AdvancedSearchService(),
    advertisementService: AdvertisementService(),
    backupService: BackupService(supabaseClient),
    couponService: CouponService(supabaseClient),
    currencyService: CurrencyService(),
    dataIntegrityService: DataIntegrityService(),
    deviceCapabilityService: DeviceCapabilityService(),

    imageRecognitionService: ImageRecognitionService(productService),
    inventoryService: InventoryService(),
    notificationService: NotificationService(),
    offlineModeService: OfflineModeService(),
    orderService: OrderService(),

    userInteractionService: UserInteractionService(supabaseClient),
  );
}

/// تهيئة الخدمات الإضافية في الخلفية
void _initializeAdditionalServicesInBackground(AppServices services) {
  Future.delayed(const Duration(milliseconds: 500), () async {
    try {
      // تهيئة الخدمات الإضافية بدون انتظار
      final futures = [
        services.aiServicesManager.initializeAll(),
        services.chatbotService.initSpeech(),
        services.syncManager.initialize(),
        services.accessibilityService.initialize(),
        services.dataIntegrityService.initialize(),
        services.inventoryService.initialize(),
        services.notificationService.initialize(),
        services.orderService.initialize(),
        services.deviceCapabilityService.initialize(),
        services.offlineModeService.initialize(),
        services.imageRecognitionService.initialize(),
      ];

      // تهيئة الخدمات بالتوازي مع تجاهل الأخطاء
      await Future.wait(
        futures.map(
          (future) =>
              future.catchError((e) => debugPrint('خطأ في تهيئة خدمة: $e')),
        ),
      );

      debugPrint('تم تهيئة الخدمات الإضافية في الخلفية');
    } catch (e) {
      debugPrint('خطأ في تهيئة الخدمات الإضافية: $e');
    }
  });
}

class AppServices {
  final UnifiedStorageService unifiedStorageService;
  final LocalStorageService localStorageService;
  final AuthSupabaseService supabaseService;
  final GoogleAuthService googleAuthService;
  final ThemeService themeService;
  final CartService cartService;
  final FavoritesService favoritesService;
  final NavigationService navigationService;
  final ConnectivityService connectivityService;
  final ProductService productService;
  final AnalyticsService analyticsService;
  final LocalizationService localizationService;
  final AIServicesManager aiServicesManager;
  final ChatbotService chatbotService;
  final SyncManager syncManager;
  final AccessibilityService accessibilityService;

  // الخدمات الإضافية
  final AddressService addressService;
  final AdminService adminService;
  final AdvancedSearchService advancedSearchService;
  final AdvertisementService advertisementService;
  final BackupService backupService;
  final CouponService couponService;
  final CurrencyService currencyService;
  final DataIntegrityService dataIntegrityService;
  final DeviceCapabilityService deviceCapabilityService;
  final ImageRecognitionService imageRecognitionService;
  final InventoryService inventoryService;
  final NotificationService notificationService;
  final OfflineModeService offlineModeService;
  final OrderService orderService;
  final UserInteractionService userInteractionService;

  AppServices({
    required this.unifiedStorageService,
    required this.localStorageService,
    required this.supabaseService,
    required this.googleAuthService,
    required this.themeService,
    required this.cartService,
    required this.favoritesService,
    required this.navigationService,
    required this.connectivityService,
    required this.productService,
    required this.analyticsService,
    required this.localizationService,
    required this.aiServicesManager,
    required this.chatbotService,
    required this.syncManager,
    required this.accessibilityService,
    // الخدمات الإضافية
    required this.addressService,
    required this.adminService,
    required this.advancedSearchService,
    required this.advertisementService,
    required this.backupService,
    required this.couponService,
    required this.currencyService,
    required this.dataIntegrityService,
    required this.deviceCapabilityService,
    required this.imageRecognitionService,
    required this.inventoryService,
    required this.notificationService,
    required this.offlineModeService,
    required this.orderService,
    required this.userInteractionService,
  });
}

class MyApp extends StatefulWidget {
  final SharedPreferences prefs;
  final AppServices services;

  const MyApp({super.key, required this.prefs, required this.services});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  late bool _onboardingComplete;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _onboardingComplete = widget.prefs.getBool('onboarding_complete') ?? false;
    _isInitialized = true;
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) {
      return const MaterialApp(
        home: Scaffold(body: Center(child: CircularProgressIndicator())),
      );
    }

    return MultiProvider(
      providers: [
        Provider.value(value: widget.services.unifiedStorageService),
        Provider.value(value: widget.services.localStorageService),
        Provider.value(value: widget.services.supabaseService),
        ChangeNotifierProvider.value(value: widget.services.googleAuthService),
        ChangeNotifierProvider.value(value: widget.services.themeService),
        ChangeNotifierProvider.value(value: widget.services.cartService),
        Provider.value(value: widget.services.favoritesService),
        ChangeNotifierProvider.value(value: widget.services.analyticsService),
        ChangeNotifierProvider.value(
          value: widget.services.localizationService,
        ),
        Provider.value(value: widget.services.connectivityService),
        Provider.value(value: widget.services.accessibilityService),
        Provider.value(value: widget.services.navigationService),
        Provider.value(value: widget.services.productService),
        Provider.value(value: widget.services.aiServicesManager),
        Provider.value(value: widget.services.syncManager),

        Provider.value(value: widget.services.chatbotService),
      ],
      child: Consumer<ThemeService>(
        builder: (context, themeService, _) {
          final themeData =
              themeService.isDarkMode(context)
                  ? themeService.getDarkTheme()
                  : themeService.getLightTheme();

          return MaterialApp(
            title: 'متجر قطع غيار الدراجات النارية',
            theme: themeData,
            home: _getInitialScreen(),
            navigatorKey: widget.services.navigationService.navigatorKey,
            localizationsDelegates: const [
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            supportedLocales: const [Locale('ar', '')],
            locale: const Locale('ar', ''),
            routes: {
              '/home': (context) => const HomeScreen(),

              '/welcome': (context) => const WelcomeScreen(),
              '/login': (context) => const LoginScreen(),
              '/register': (context) => const RegisterScreen(),
              '/notification_settings':
                  (context) => const NotificationSettingsScreen(),

              '/product_details':
                  (context) => ProductDetailsScreen(
                    product:
                        ModalRoute.of(context)!.settings.arguments
                            as ProductModel,
                  ),
              '/cart': (context) => const CartScreen(),
              '/admin/add-product': (context) => const AddEditProductScreen(),
              '/admin/inventory':
                  (context) => const InventoryManagementScreen(),
              '/change-password': (context) => const ChangePasswordScreen(),
              '/security-activity': (context) => const SecurityActivityScreen(),
            },
          );
        },
      ),
    );
  }

  Widget _getInitialScreen() {
    if (!_onboardingComplete) {
      return const OnboardingScreen();
    }

    try {
      if (widget.services.supabaseService.isInitialized &&
          widget.services.supabaseService.isAuthenticated) {
        return const HomeScreen();
      } else {
        return const WelcomeScreen();
      }
    } catch (e) {
      debugPrint('خطأ في التحقق من حالة المصادقة: $e');
      return const WelcomeScreen();
    }
  }
}

class ErrorApp extends StatelessWidget {
  const ErrorApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      home: Scaffold(
        body: Center(
          child: Text(
            'حدث خطأ في تهيئة التطبيق',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(color: Colors.red, fontSize: 24),
          ),
        ),
      ),
    );
  }
}
