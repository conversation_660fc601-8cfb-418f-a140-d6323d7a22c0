import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:motorcycle_parts_shop/core/services/analytics_service.dart';
import 'package:motorcycle_parts_shop/core/services/auth_supabase_service.dart';
import 'package:motorcycle_parts_shop/core/services/inventory_service.dart';
import 'package:motorcycle_parts_shop/core/services/product_service.dart';
import 'package:motorcycle_parts_shop/core/theme/app_theme.dart'; // Import AppTheme
import 'package:motorcycle_parts_shop/core/utils/responsive_helper.dart';
import 'package:motorcycle_parts_shop/screens/admin/real_stats_screen.dart';
import 'package:motorcycle_parts_shop/screens/admin/widgets/dashboard_charts.dart';
import 'package:open_file/open_file.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:provider/provider.dart' as provider;

class AdminDashboardScreen extends ConsumerStatefulWidget {
  static const routeName = '/admin-dashboard';

  const AdminDashboardScreen({super.key});

  @override
  ConsumerState<AdminDashboardScreen> createState() =>
      _AdminDashboardScreenState();
}

class _AdminDashboardScreenState extends ConsumerState<AdminDashboardScreen>
    with SingleTickerProviderStateMixin {
  // Add SingleTickerProviderStateMixin
  int _selectedIndex = 0;
  bool _isLoading = true;
  late ProductService _productService;
  late InventoryService _inventoryService;
  late AnalyticsService _analyticsService;
  late AuthSupabaseService _authService;

  List<_DashboardItem> _dashboardItems = [];

  // Animation controller for dashboard items
  late AnimationController _animationController;

  final List<_NavigationItem> _navigationItems = [
    _NavigationItem(title: 'لوحة التحكم', icon: Icons.dashboard_rounded),
    _NavigationItem(title: 'المنتجات', icon: Icons.inventory_2_rounded),
    _NavigationItem(title: 'الطلبات', icon: Icons.shopping_cart_rounded),
    _NavigationItem(title: 'المستخدمين', icon: Icons.people_alt_rounded),
    _NavigationItem(title: 'التقارير', icon: Icons.bar_chart_rounded),
    _NavigationItem(title: 'التحليلات', icon: Icons.analytics_rounded),
    _NavigationItem(title: 'الإحصائيات الفعلية', icon: Icons.storage_rounded),
    _NavigationItem(
      title: 'الإشعارات',
      icon: Icons.notifications_active_rounded,
    ),
    _NavigationItem(title: 'الإعدادات', icon: Icons.settings_rounded),
  ];

  @override
  void initState() {
    super.initState();
    _initializeServices();

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 700),
    );
  }

  @override
  void dispose() {
    _animationController.dispose(); // Dispose animation controller
    super.dispose();
  }

  /// معالجة اختيار عنصر التنقل
  void _handleNavigationSelection(int index) {
    switch (index) {
      case 0: // لوحة التحكم
        // البقاء في الصفحة الحالية
        break;
      case 1: // المنتجات
        Navigator.pushNamed(context, '/admin/products');
        break;
      case 2: // الطلبات
        Navigator.pushNamed(context, '/admin/orders');
        break;
      case 3: // المستخدمين
        Navigator.pushNamed(context, '/admin/users');
        break;
      case 4: // التقارير
        Navigator.pushNamed(context, '/admin/reports');
        break;
      case 5: // التحليلات
        Navigator.pushNamed(context, '/admin/analytics');
        break;
      case 6: // الإحصائيات الفعلية
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const RealStatsScreen()),
        );
        break;
      case 7: // الإشعارات
        Navigator.pushNamed(context, '/admin/notifications');
        break;
      case 8: // الإعدادات
        Navigator.pushNamed(context, '/admin/settings');
        break;
    }
  }

  Future<void> _initializeServices() async {
    try {
      _authService = provider.Provider.of<AuthSupabaseService>(
        context,
        listen: false,
      );
      _productService = ProductService(_authService.client);
      _inventoryService = InventoryService();
      _analyticsService = provider.Provider.of<AnalyticsService>(
        context,
        listen: false,
      );

      await _inventoryService.initialize();
      await _loadDashboardData();
    } catch (e, stackTrace) {
      debugPrint('خطأ في تهيئة الخدمات في AdminDashboardScreen: $e');
      debugPrint(stackTrace.toString());
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ في تحميل البيانات: $e'),
            backgroundColor: AppTheme.errorColor,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  Future<void> _loadDashboardData() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // جلب عدد المنتجات
      final productsCount = await _productService.getProductsCount();

      // جلب إحصائيات المستخدمين
      final userStats = await _analyticsService.getGlobalMetrics();

      // تحديث عناصر لوحة التحكم بالبيانات الفعلية
      _dashboardItems = [
        _DashboardItem(
          title: 'المنتجات',
          icon: Icons.inventory_2_outlined,
          color: AppTheme.primaryColor,
          count: productsCount.toString(),
          subtitle: 'إجمالي المنتجات',
          trend: 0,
          trendType: TrendType.up,
        ),
        _DashboardItem(
          title: 'الطلبات',
          icon: Icons.shopping_cart_checkout_rounded,
          color: Colors.orangeAccent,
          count: userStats['total_orders']?.toString() ?? '0',
          subtitle: 'طلبات اليوم',
          trend: 0,
          trendType: TrendType.up,
        ),
        _DashboardItem(
          title: 'المستخدمين',
          icon: Icons.group_add_outlined,
          color: Colors.greenAccent.shade700,
          count: userStats['total_users']?.toString() ?? '0',
          subtitle: 'إجمالي المستخدمين',
          trend: 0,
          trendType: TrendType.up,
        ),
        _DashboardItem(
          title: 'الإيرادات',
          icon: Icons.monetization_on_outlined,
          color: Colors.purpleAccent,
          count:
              '${userStats['total_revenue']?.toStringAsFixed(2) ?? '0.00'} ج.م',
          subtitle: 'إيرادات الشهر',
          trend: 0,
          trendType: TrendType.up,
        ),
        _DashboardItem(
          title: 'معدل التحويل',
          icon: Icons.transform_rounded,
          color: Colors.amberAccent,
          count:
              '${userStats['conversion_rate']?.toStringAsFixed(1) ?? '0.0'}%',
          subtitle: 'متوسط الشهر',
          trend: 0,
          trendType: TrendType.up,
        ),
        _DashboardItem(
          title: 'التقييمات',
          icon: Icons.star_border_purple500_outlined,
          color: Colors.deepOrangeAccent,
          count: userStats['average_rating']?.toStringAsFixed(1) ?? '0.0',
          subtitle: 'متوسط التقييمات',
          trend: 0,
          trendType: TrendType.up,
        ),
        _DashboardItem(
          title: 'الزيارات',
          icon: Icons.visibility_outlined,
          color: Colors.tealAccent.shade700,
          count: userStats['total_visits']?.toString() ?? '0',
          subtitle: 'زيارات اليوم',
          trend: 0,
          trendType: TrendType.up,
        ),
      ];

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        _animationController.forward(); // Start animation after data is loaded
      }
    } catch (e, stackTrace) {
      debugPrint('خطأ في تحميل بيانات لوحة التحكم في AdminDashboardScreen: $e');
      debugPrint(stackTrace.toString());
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ في تحميل البيانات: $e'),
            backgroundColor: AppTheme.errorColor,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor, // Use AppTheme background
      appBar: AppBar(
        title: Text(
          'لوحة تحكم المسؤول',
          style: AppTheme.lightTheme().textTheme.displayLarge?.copyWith(
            color: AppTheme.textLightColor,
          ),
        ),
        backgroundColor: AppTheme.primaryColor,
        elevation: 0, // Remove shadow for a flatter design
        actions: [
          IconButton(
            icon: Icon(Icons.refresh_rounded, color: AppTheme.textLightColor),
            onPressed: _loadDashboardData,
            tooltip: 'تحديث البيانات',
          ),
          IconButton(
            icon: const Icon(
              Icons.picture_as_pdf_rounded,
              color: AppTheme.textLightColor,
            ),
            onPressed: () async {
              // Generate PDF report
              final pdf = pw.Document();
              pdf.addPage(
                pw.Page(
                  build:
                      (pw.Context context) =>
                          pw.Center(child: pw.Text('Sales Report')),
                ),
              );

              // Save the PDF file
              final output = await getTemporaryDirectory();
              final file = File('${output.path}/report.pdf');
              await file.writeAsBytes(await pdf.save());

              // Open the PDF file
              OpenFile.open(file.path);
            },
            tooltip: 'طباعة تقرير PDF',
          ),
          IconButton(
            icon: Icon(Icons.logout_rounded, color: AppTheme.textLightColor),
            onPressed: () async {
              await _authService.signOut();
              if (mounted) {
                Navigator.of(
                  context,
                ).pushReplacementNamed('/login'); // Navigate to login screen
              }
            },
            tooltip: 'تسجيل الخروج',
          ),
        ],
      ),
      body: Row(
        children: [
          // Navigation Rail
          NavigationRail(
            selectedIndex: _selectedIndex,
            onDestinationSelected: (int index) {
              setState(() {
                _selectedIndex = index;
              });
              _handleNavigationSelection(index);
            },
            labelType:
                NavigationRailLabelType
                    .selected, // Show label only for selected item
            backgroundColor:
                AppTheme.textLightColor, // Use AppTheme white color
            selectedIconTheme: IconThemeData(
              color: AppTheme.primaryColor,
              size: 28,
            ),
            unselectedIconTheme: IconThemeData(
              color: AppTheme.textSecondaryColor.withOpacity(0.7),
              size: 24,
            ),
            selectedLabelTextStyle: AppTheme.lightTheme().textTheme.titleLarge!
                .copyWith(color: AppTheme.primaryColor),
            unselectedLabelTextStyle: AppTheme.lightTheme()
                .textTheme
                .bodyMedium!
                .copyWith(color: AppTheme.textSecondaryColor),
            elevation: 4,
            indicatorColor: AppTheme.primaryColor.withOpacity(0.1),
            destinations:
                _navigationItems
                    .map(
                      (item) => NavigationRailDestination(
                        icon: Icon(item.icon),
                        selectedIcon: Icon(
                          item.icon,
                          size: 28,
                        ), // Larger icon when selected
                        label: Text(item.title),
                        padding: const EdgeInsets.symmetric(
                          vertical: 8,
                        ), // Add padding
                      ),
                    )
                    .toList(),
          ),
          // Main Content
          Expanded(
            child: ResponsiveBuilder(
              builder: (context, constraints) {
                final padding = ResponsiveHelper.getPadding(context);

                return SingleChildScrollView(
                  child: Padding(
                    padding: EdgeInsets.all(padding),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Header
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text(
                              'لوحة التحكم',
                              style: TextStyle(
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Row(
                              children: [
                                ElevatedButton.icon(
                                  onPressed: () {
                                    Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                        builder:
                                            (context) =>
                                                const RealStatsScreen(),
                                      ),
                                    );
                                  },
                                  icon: const Icon(
                                    Icons.storage_rounded,
                                    size: 18,
                                  ),
                                  label: const Text('الإحصائيات الفعلية'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.blue[600],
                                    foregroundColor: Colors.white,
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 16,
                                      vertical: 8,
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                IconButton(
                                  icon: const Icon(Icons.notifications),
                                  onPressed: () {
                                    Navigator.pushNamed(
                                      context,
                                      '/admin/notifications',
                                    );
                                  },
                                ),
                              ],
                            ),
                          ],
                        ),
                        const SizedBox(height: 24),
                        // Dashboard Cards
                        _isLoading
                            ? const Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  CircularProgressIndicator(),
                                  SizedBox(height: 16),
                                  Text('جاري تحميل بيانات لوحة التحكم...'),
                                ],
                              ),
                            )
                            : GridView.builder(
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              gridDelegate:
                                  SliverGridDelegateWithFixedCrossAxisCount(
                                    crossAxisCount:
                                        ResponsiveHelper.isMobile(context)
                                            ? 2
                                            : ResponsiveHelper.isTablet(context)
                                            ? 3
                                            : 4,
                                    crossAxisSpacing: 16,
                                    mainAxisSpacing: 16,
                                    childAspectRatio: 1.5,
                                  ),
                              itemCount: _dashboardItems.length,
                              itemBuilder: (context, index) {
                                final item = _dashboardItems[index];
                                return Card(
                                  elevation: 2,
                                  child: Padding(
                                    padding: const EdgeInsets.all(16.0),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Icon(
                                              item.icon,
                                              color: item.color,
                                              size: 32,
                                            ),
                                            Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.end,
                                              children: [
                                                Text(
                                                  item.count,
                                                  style: const TextStyle(
                                                    fontSize: 24,
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                ),
                                                if (item.trend != null)
                                                  Row(
                                                    children: [
                                                      Icon(
                                                        item.trendType ==
                                                                TrendType.up
                                                            ? Icons.arrow_upward
                                                            : Icons
                                                                .arrow_downward,
                                                        color:
                                                            item.trendType ==
                                                                    TrendType.up
                                                                ? Colors.green
                                                                : Colors.red,
                                                        size: 14,
                                                      ),
                                                      Text(
                                                        '${item.trend}%',
                                                        style: TextStyle(
                                                          fontSize: 12,
                                                          color:
                                                              item.trendType ==
                                                                      TrendType
                                                                          .up
                                                                  ? Colors.green
                                                                  : Colors.red,
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                              ],
                                            ),
                                          ],
                                        ),
                                        const Spacer(),
                                        Text(
                                          item.title,
                                          style: const TextStyle(
                                            fontSize: 16,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                        const SizedBox(height: 4),
                                        Text(
                                          item.subtitle,
                                          style: TextStyle(
                                            fontSize: 12,
                                            color: Colors.grey[600],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                );
                              },
                            ),
                        const SizedBox(height: 24),
                        // Analytics Section - استخدام مكون DashboardCharts بدلاً من تكرار الكود
                        _isLoading
                            ? const Center(child: CircularProgressIndicator())
                            : const DashboardCharts(),
                        const SizedBox(height: 24),
                        // Recent Orders Section
                        Row(
                          children: [
                            Expanded(
                              flex: 2,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const Text(
                                    'الطلبات الحديثة',
                                    style: TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  const SizedBox(height: 16),
                                  Card(
                                    elevation: 2,
                                    child: SizedBox(
                                      height: 300,
                                      child: ListView.builder(
                                        itemCount: 5,
                                        itemBuilder: (context, index) {
                                          return ListTile(
                                            leading: const CircleAvatar(
                                              child: Icon(Icons.shopping_bag),
                                            ),
                                            title: Text('طلب #${1000 + index}'),
                                            subtitle: Text(
                                              'العميل: أحمد محمد - ${150 + index * 50} ج.م',
                                            ),
                                            trailing: Chip(
                                              label: const Text('قيد التوصيل'),
                                              backgroundColor:
                                                  Colors.orange[100],
                                              labelStyle: TextStyle(
                                                color: Colors.orange[900],
                                              ),
                                            ),
                                          );
                                        },
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const Text(
                                    'التنبيهات المهمة',
                                    style: TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  const SizedBox(height: 16),
                                  Card(
                                    elevation: 2,
                                    child: SizedBox(
                                      height: 300,
                                      child: ListView(
                                        children: [
                                          ListTile(
                                            leading: const CircleAvatar(
                                              backgroundColor: Colors.red,
                                              child: Icon(
                                                Icons.warning,
                                                color: Colors.white,
                                              ),
                                            ),
                                            title: const Text('مخزون منخفض'),
                                            subtitle: Text(
                                              '${_isLoading ? '...' : _dashboardItems[4].count} منتجات تحتاج للتجديد',
                                            ),
                                            trailing: TextButton(
                                              onPressed: () {
                                                Navigator.pushNamed(
                                                  context,
                                                  '/admin/inventory',
                                                );
                                              },
                                              child: const Text('عرض'),
                                            ),
                                          ),
                                          ListTile(
                                            leading: const CircleAvatar(
                                              backgroundColor: Colors.orange,
                                              child: Icon(
                                                Icons.access_time,
                                                color: Colors.white,
                                              ),
                                            ),
                                            title: const Text('طلبات معلقة'),
                                            subtitle: Text(
                                              '${_isLoading ? '...' : _dashboardItems[1].count} طلبات تحتاج للمراجعة',
                                            ),
                                            trailing: TextButton(
                                              onPressed: () {
                                                Navigator.pushNamed(
                                                  context,
                                                  '/admin/orders',
                                                );
                                              },
                                              child: const Text('عرض'),
                                            ),
                                          ),
                                          ListTile(
                                            leading: const CircleAvatar(
                                              backgroundColor: Colors.green,
                                              child: Icon(
                                                Icons.thumb_up,
                                                color: Colors.white,
                                              ),
                                            ),
                                            title: const Text('تقييمات جديدة'),
                                            subtitle: const Text(
                                              '8 تقييمات تحتاج للرد',
                                            ),
                                            trailing: TextButton(
                                              onPressed: () {
                                                Navigator.pushNamed(
                                                  context,
                                                  '/admin/reviews',
                                                );
                                              },
                                              child: const Text('عرض'),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 24),
                        // Export & Print Section
                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            ElevatedButton.icon(
                              onPressed: () async {
                                showDialog(
                                  context: context,
                                  barrierDismissible: false,
                                  builder:
                                      (context) => const Center(
                                        child: CircularProgressIndicator(),
                                      ),
                                );
                                final pdf = pw.Document();
                                pdf.addPage(
                                  pw.Page(
                                    build: (pw.Context context) {
                                      return pw.Column(
                                        children: [
                                          pw.Header(
                                            level: 0,
                                            child: pw.Text('تقرير لوحة التحكم'),
                                          ),
                                          pw.SizedBox(height: 20),
                                          pw.Table.fromTextArray(
                                            context: context,
                                            data: <List<String>>[
                                              <String>[
                                                'العنوان',
                                                'القيمة',
                                                'التغيير',
                                              ],
                                              ..._dashboardItems.map(
                                                (item) => [
                                                  item.title,
                                                  item.count,
                                                  '${item.trend}%',
                                                ],
                                              ),
                                            ],
                                          ),
                                        ],
                                      );
                                    },
                                  ),
                                );
                                await Printing.sharePdf(
                                  bytes: await pdf.save(),
                                  filename: 'تقرير_لوحة_التحكم.pdf',
                                );
                                Navigator.pop(context); // إغلاق مؤشر التحميل
                              },
                              icon: const Icon(Icons.print),
                              label: const Text('طباعة التقرير'),
                            ),
                          ],
                        ),
                        // Charts
                        const DashboardCharts(),
                        const SizedBox(height: 24),
                        // Recent Orders
                        Card(
                          child: Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    const Text(
                                      'آخر الطلبات',
                                      style: TextStyle(
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    TextButton(
                                      onPressed: () {
                                        Navigator.pushNamed(
                                          context,
                                          '/admin/inventory',
                                        );
                                      },
                                      child: const Text('عرض'),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 16),
                                SingleChildScrollView(
                                  scrollDirection: Axis.horizontal,
                                  child: DataTable(
                                    columns: const [
                                      DataColumn(label: Text('رقم الطلب')),
                                      DataColumn(label: Text('العميل')),
                                      DataColumn(label: Text('المنتجات')),
                                      DataColumn(label: Text('المبلغ')),
                                      DataColumn(label: Text('الحالة')),
                                      DataColumn(label: Text('التاريخ')),
                                    ],
                                    rows: List.generate(
                                      5,
                                      (index) => DataRow(
                                        cells: [
                                          DataCell(Text('#${1000 + index}')),
                                          DataCell(Text('عميل ${index + 1}')),
                                          DataCell(Text('${2 + index} منتجات')),
                                          DataCell(
                                            Text('${1500 + index * 100} ج.م'),
                                          ),
                                          DataCell(
                                            Container(
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                    horizontal: 8,
                                                    vertical: 4,
                                                  ),
                                              decoration: BoxDecoration(
                                                color:
                                                    index % 3 == 0
                                                        ? Colors.green[100]
                                                        : index % 3 == 1
                                                        ? Colors.orange[100]
                                                        : Colors.blue[100],
                                                borderRadius:
                                                    BorderRadius.circular(12),
                                              ),
                                              child: Text(
                                                index % 3 == 0
                                                    ? 'مكتمل'
                                                    : index % 3 == 1
                                                    ? 'قيد المعالجة'
                                                    : 'قيد الشحن',
                                                style: TextStyle(
                                                  color:
                                                      index % 3 == 0
                                                          ? Colors.green[900]
                                                          : index % 3 == 1
                                                          ? Colors.orange[900]
                                                          : Colors.blue[900],
                                                ),
                                              ),
                                            ),
                                          ),
                                          DataCell(
                                            Text('2024/1/${20 - index}'),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}

enum TrendType { up, down, neutral }

class _DashboardItem {
  final String title;
  final IconData icon;
  final Color color;
  final String count;
  final String subtitle;
  final double? trend;
  final TrendType? trendType;

  _DashboardItem({
    required this.title,
    required this.icon,
    required this.color,
    required this.count,
    required this.subtitle,
    this.trend,
    this.trendType,
  });
}

class _NavigationItem {
  final String title;
  final IconData icon;

  _NavigationItem({required this.title, required this.icon});
}
