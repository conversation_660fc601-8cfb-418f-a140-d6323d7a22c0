import '../../core/services/auth_supabase_service.dart';
import '../../core/services/real_stats_service.dart';
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class RealStatsScreen extends StatefulWidget {
  const RealStatsScreen({super.key});

  @override
  State<RealStatsScreen> createState() => _RealStatsScreenState();
}

class _RealStatsScreenState extends State<RealStatsScreen> {
  final RealStatsService _statsService = RealStatsService();
  Map<String, dynamic>? _stats;
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _checkAdminAccess();
  }

  /// فحص صلاحيات المدير
  Future<void> _checkAdminAccess() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // التحقق من تسجيل الدخول
      final user = Supabase.instance.client.auth.currentUser;
      if (user == null) {
        setState(() {
          _error = 'يجب تسجيل الدخول أولاً';
          _isLoading = false;
        });
        return;
      }

      // جلب بيانات المستخدم من جدول profiles
      final response =
          await Supabase.instance.client
              .from('profiles')
              .select('profile_type')
              .eq('id', user.id)
              .single();

      final profileType = response['profile_type'] as String?;

      if (profileType == 'admin') {
        // تحميل الإحصائيات إذا كان مدير
        await _loadRealStats();
      } else {
        setState(() {
          _error = 'غير مسموح - هذه الصفحة للمديرين فقط';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _error = 'خطأ في التحقق من الصلاحيات: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _loadRealStats() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final stats = await _statsService.getAllRealStats();
      setState(() {
        _stats = stats;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('📊 الإحصائيات الفعلية من قاعدة البيانات'),
        backgroundColor: Colors.blue[800],
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadRealStats,
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('جاري التحقق من الصلاحيات...'),
          ],
        ),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              _error!.contains('غير مسموح') ? Icons.security : Icons.error,
              size: 64,
              color:
                  _error!.contains('غير مسموح')
                      ? Colors.orange[400]
                      : Colors.red[400],
            ),
            const SizedBox(height: 16),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32),
              child: Text(
                _error!,
                textAlign: TextAlign.center,
                style: const TextStyle(fontSize: 16),
              ),
            ),
            const SizedBox(height: 24),
            if (!_error!.contains('غير مسموح'))
              ElevatedButton(
                onPressed: _checkAdminAccess,
                child: const Text('إعادة المحاولة'),
              ),
            if (_error!.contains('غير مسموح'))
              Column(
                children: [
                  Container(
                    padding: const EdgeInsets.all(16),
                    margin: const EdgeInsets.symmetric(horizontal: 32),
                    decoration: BoxDecoration(
                      color: Colors.orange[50],
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.orange[200]!),
                    ),
                    child: const Column(
                      children: [
                        Icon(
                          Icons.admin_panel_settings,
                          size: 48,
                          color: Colors.orange,
                        ),
                        SizedBox(height: 12),
                        Text(
                          'صفحة الإحصائيات الفعلية',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        SizedBox(height: 8),
                        Text(
                          'هذه الصفحة تحتوي على بيانات حساسة من قاعدة البيانات ومتاحة للمديرين فقط',
                          textAlign: TextAlign.center,
                          style: TextStyle(color: Colors.grey),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton.icon(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.arrow_back),
                    label: const Text('العودة'),
                  ),
                ],
              ),
          ],
        ),
      );
    }

    if (_stats == null) {
      return const Center(child: Text('لا توجد بيانات'));
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          const SizedBox(height: 20),
          _buildStatsGrid(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    final timestamp = _stats?['timestamp'];
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '📊 الإحصائيات الفعلية من قاعدة البيانات',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text(
              'آخر تحديث: ${timestamp ?? 'غير محدد'}',
              style: TextStyle(color: Colors.grey[600]),
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.green[100],
                borderRadius: BorderRadius.circular(20),
              ),
              child: const Text(
                '✅ بيانات حقيقية 100%',
                style: TextStyle(
                  color: Colors.green,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatsGrid() {
    return Column(
      children: [
        _buildStatsSection('📦 المنتجات', _stats?['products'], Colors.blue),
        _buildStatsSection('👥 المستخدمين', _stats?['users'], Colors.green),
        _buildStatsSection('🛒 الطلبات', _stats?['orders'], Colors.orange),
        _buildStatsSection('📂 الفئات', _stats?['categories'], Colors.purple),
        _buildStatsSection('🏢 الشركات', _stats?['companies'], Colors.teal),
        _buildStatsSection('⭐ المراجعات', _stats?['reviews'], Colors.amber),
        _buildStatsSection(
          '❤️ قوائم الرغبات',
          _stats?['wishlists'],
          Colors.red,
        ),
        _buildStatsSection('🛍️ سلة التسوق', _stats?['cart'], Colors.indigo),
        _buildStatsSection(
          '🔔 الإشعارات',
          _stats?['notifications'],
          Colors.cyan,
        ),
        _buildStatsSection(
          '💳 طرق الدفع',
          _stats?['payment_methods'],
          Colors.pink,
        ),
        _buildStatsSection(
          '🚚 طرق الشحن',
          _stats?['shipping_methods'],
          Colors.brown,
        ),
        _buildStatsSection('📍 العناوين', _stats?['addresses'], Colors.lime),
        _buildStatsSection(
          '🎫 الكوبونات',
          _stats?['coupons'],
          Colors.deepOrange,
        ),
        _buildStatsSection('📊 المخزون', _stats?['inventory'], Colors.blueGrey),
        _buildStatsSection(
          '📈 التحليلات',
          _stats?['analytics'],
          Colors.deepPurple,
        ),
      ],
    );
  }

  Widget _buildStatsSection(
    String title,
    Map<String, dynamic>? data,
    Color color,
  ) {
    if (data == null) return const SizedBox.shrink();

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(width: 4, height: 24, color: color),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (data.containsKey('error'))
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red[200]!),
                ),
                child: Row(
                  children: [
                    Icon(Icons.error, color: Colors.red[600]),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'خطأ: ${data['error']}',
                        style: TextStyle(color: Colors.red[600]),
                      ),
                    ),
                  ],
                ),
              )
            else
              Wrap(
                spacing: 16,
                runSpacing: 12,
                children:
                    data.entries.map((entry) {
                      return _buildStatItem(entry.key, entry.value, color);
                    }).toList(),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String key, dynamic value, Color color) {
    String displayKey = _getDisplayName(key);
    String displayValue = _formatValue(value);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            displayKey,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            displayValue,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color.withOpacity(0.8),
            ),
          ),
        ],
      ),
    );
  }

  String _getDisplayName(String key) {
    final names = {
      'total_products': 'إجمالي المنتجات',
      'available_products': 'المنتجات المتاحة',
      'featured_products': 'المنتجات المميزة',
      'average_rating': 'متوسط التقييم',
      'total_stock_quantity': 'إجمالي المخزون',
      'out_of_stock': 'نفد المخزون',
      'low_stock': 'مخزون منخفض',
      'total_users': 'إجمالي المستخدمين',
      'active_users': 'المستخدمين النشطين',
      'admin_users': 'المديرين',
      'customer_users': 'العملاء',
      'new_users_last_30_days': 'مستخدمين جدد (30 يوم)',
      'total_orders': 'إجمالي الطلبات',
      'pending_orders': 'طلبات معلقة',
      'completed_orders': 'طلبات مكتملة',
      'cancelled_orders': 'طلبات ملغية',
      'total_revenue': 'إجمالي المبيعات',
      'average_order_value': 'متوسط قيمة الطلب',
      'total_categories': 'إجمالي الفئات',
      'active_categories': 'الفئات النشطة',
      'total_companies': 'إجمالي الشركات',
      'active_companies': 'الشركات النشطة',
      'total_reviews': 'إجمالي المراجعات',
      'approved_reviews': 'المراجعات المعتمدة',
      'total_wishlist_items': 'عناصر قوائم الرغبات',
      'total_cart_items': 'عناصر سلة التسوق',
      'total_notifications': 'إجمالي الإشعارات',
      'total_payment_methods': 'طرق الدفع',
      'total_shipping_methods': 'طرق الشحن',
      'total_addresses': 'العناوين',
      'total_coupons': 'الكوبونات',
      'total_inventory_movements': 'حركات المخزون',
      'total_analytics_events': 'أحداث التحليلات',
    };
    return names[key] ?? key;
  }

  String _formatValue(dynamic value) {
    if (value is double) {
      if (value == value.toInt()) {
        return value.toInt().toString();
      }
      return value.toStringAsFixed(2);
    }
    return value.toString();
  }
}
