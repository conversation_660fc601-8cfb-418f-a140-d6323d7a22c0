import 'dart:async';
import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';

// مستويات قدرة الجهاز
enum DeviceCapabilityLevel {
  low, // أجهزة ذات موارد محدودة
  medium, // أجهزة متوسطة
  high, // أجهزة عالية الأداء
}

/// خدمة التكيف مع قدرات الجهاز
/// توفر هذه الخدمة آليات لتحديد قدرات الجهاز وتكييف سلوك التطبيق وفقًا لذلك
/// لتحسين الأداء على الأجهزة ذات الموارد المحدودة
class DeviceCapabilityService extends ChangeNotifier {
  static final DeviceCapabilityService _instance =
      DeviceCapabilityService._internal();
  final DeviceInfoPlugin _deviceInfo = DeviceInfoPlugin();

  // مستوى قدرة الجهاز الحالي
  static DeviceCapabilityLevel _capabilityLevel = DeviceCapabilityLevel.medium;

  // إعدادات التخزين المؤقت حسب مستوى قدرة الجهاز
  static final Map<DeviceCapabilityLevel, CacheSettings> _cacheSettings = {
    DeviceCapabilityLevel.low: CacheSettings(
      maxCacheSize: 100,
      cacheExpirationHours: 48,
      prefetchLimit: 5,
    ),
    DeviceCapabilityLevel.medium: CacheSettings(
      maxCacheSize: 300,
      cacheExpirationHours: 24,
      prefetchLimit: 10,
    ),
    DeviceCapabilityLevel.high: CacheSettings(
      maxCacheSize: 500,
      cacheExpirationHours: 12,
      prefetchLimit: 20,
    ),
  };

  // إعدادات العرض حسب مستوى قدرة الجهاز
  static final Map<DeviceCapabilityLevel, DisplaySettings> _displaySettings = {
    DeviceCapabilityLevel.low: DisplaySettings(
      enableAnimations: false,
      enableParallaxEffects: false,
      enableBlurEffects: false,
      imageQuality: 0.7,
      maxImagesPerPage: 5,
    ),
    DeviceCapabilityLevel.medium: DisplaySettings(
      enableAnimations: true,
      enableParallaxEffects: false,
      enableBlurEffects: true,
      imageQuality: 0.85,
      maxImagesPerPage: 10,
    ),
    DeviceCapabilityLevel.high: DisplaySettings(
      enableAnimations: true,
      enableParallaxEffects: true,
      enableBlurEffects: true,
      imageQuality: 1.0,
      maxImagesPerPage: 20,
    ),
  };

  // تطبيق نمط Singleton لإنشاء كائن واحد فقط من هذه الخدمة
  factory DeviceCapabilityService() {
    return _instance;
  }

  DeviceCapabilityService._internal();

  /// تهيئة خدمة التكيف مع قدرات الجهاز
  Future<void> initialize() async {
    try {
      await detectDeviceCapability();
      debugPrint('تم تهيئة خدمة التكيف مع قدرات الجهاز بنجاح');
      notifyListeners();
    } catch (e) {
      debugPrint('خطأ أثناء تهيئة خدمة التكيف مع قدرات الجهاز: $e');
      rethrow;
    }
  }

  /// تحديد مستوى قدرة الجهاز
  Future<void> detectDeviceCapability() async {
    try {
      // الحصول على معلومات الجهاز
      final deviceInfo = await _getDeviceInfo();

      // تحليل قدرات الجهاز
      final totalMemoryMB = deviceInfo['totalMemoryMB'] as double? ?? 0;
      final processorCores = deviceInfo['processorCores'] as int? ?? 0;

      // تحديد مستوى قدرة الجهاز بناءً على المواصفات
      if (totalMemoryMB < 1500 || processorCores <= 2) {
        _capabilityLevel = DeviceCapabilityLevel.low;
      } else if (totalMemoryMB < 3000 || processorCores <= 4) {
        _capabilityLevel = DeviceCapabilityLevel.medium;
      } else {
        _capabilityLevel = DeviceCapabilityLevel.high;
      }

      debugPrint('تم تحديد مستوى قدرة الجهاز: $_capabilityLevel');
      debugPrint(
        'الذاكرة الكلية: ${totalMemoryMB.toStringAsFixed(0)} ميجابايت',
      );
      debugPrint('عدد أنوية المعالج: $processorCores');
    } catch (e) {
      debugPrint('خطأ في تحديد قدرات الجهاز: $e');
      // استخدام المستوى المتوسط كقيمة افتراضية
      _capabilityLevel = DeviceCapabilityLevel.medium;
    }
  }

  /// الحصول على معلومات الجهاز
  Future<Map<String, dynamic>> _getDeviceInfo() async {
    try {
      if (Platform.isAndroid) {
        final androidInfo = await _deviceInfo.androidInfo;
        // تقدير الذاكرة الكلية بناءً على طراز الجهاز وإصدار Android
        // حيث أن خاصية totalPhysicalMemory غير متوفرة في الإصدار الحالي من المكتبة
        double totalMem = 2048.0; // قيمة افتراضية 2GB

        // يمكن تقدير الذاكرة بناءً على طراز الجهاز وإصدار Android
        final String model = androidInfo.model;
        final int sdkVersion = androidInfo.version.sdkInt;

        if (sdkVersion >= 29) {
          // Android 10 وما فوق
          if (model.contains('Samsung') || model.contains('Galaxy')) {
            totalMem = 4096.0; // تقدير 4GB للأجهزة الحديثة
          } else if (model.contains('Pixel')) {
            totalMem = 6144.0; // تقدير 6GB لأجهزة Pixel
          }
        }

        return {
          'totalMemoryMB': totalMem,
          'processorCores': androidInfo.supportedAbis.length,
          'model': androidInfo.model,
          'manufacturer': androidInfo.manufacturer,
          'androidVersion': androidInfo.version.release,
          'sdkVersion': androidInfo.version.sdkInt,
        };
      } else if (Platform.isIOS) {
        final iosInfo = await _deviceInfo.iosInfo;
        // تقدير تقريبي لمواصفات أجهزة iOS
        int estimatedCores = 2;
        double estimatedMemory = 2048.0;

        // تقدير عدد الأنوية والذاكرة بناءً على طراز الجهاز
        final model = iosInfo.model;
        if (model.contains('iPhone')) {
          if (model.contains('iPhone X') ||
              model.contains('iPhone 11') ||
              model.contains('iPhone 12') ||
              model.contains('iPhone 13') ||
              model.contains('iPhone 14') ||
              model.contains('iPhone 15')) {
            estimatedCores = 6;
            estimatedMemory = 4096.0;
          } else if (model.contains('iPhone 8') ||
              model.contains('iPhone 7') ||
              model.contains('iPhone 6s')) {
            estimatedCores = 2;
            estimatedMemory = 2048.0;
          } else {
            estimatedCores = 2;
            estimatedMemory = 1536.0;
          }
        } else if (model.contains('iPad')) {
          if (model.contains('iPad Pro')) {
            estimatedCores = 8;
            estimatedMemory = 6144.0;
          } else if (model.contains('iPad Air')) {
            estimatedCores = 6;
            estimatedMemory = 4096.0;
          } else {
            estimatedCores = 4;
            estimatedMemory = 3072.0;
          }
        }

        return {
          'totalMemoryMB': estimatedMemory,
          'processorCores': estimatedCores,
          'model': iosInfo.model,
          'systemName': iosInfo.systemName,
          'systemVersion': iosInfo.systemVersion,
          'name': iosInfo.name,
        };
      } else {
        // قيم افتراضية للأنظمة الأخرى
        return {
          'totalMemoryMB': 2048.0,
          'processorCores': 4,
          'platform': Platform.operatingSystem,
        };
      }
    } catch (e) {
      debugPrint('خطأ أثناء الحصول على معلومات الجهاز: $e');
      // قيم افتراضية في حالة حدوث خطأ
      return {
        'totalMemoryMB': 2048.0,
        'processorCores': 4,
        'error': e.toString(),
      };
    }
  }

  /// الحصول على إعدادات التخزين المؤقت المناسبة لقدرات الجهاز
  static CacheSettings getCacheSettings() {
    return _cacheSettings[_capabilityLevel]!;
  }

  /// الحصول على إعدادات العرض المناسبة لقدرات الجهاز
  static DisplaySettings getDisplaySettings() {
    return _displaySettings[_capabilityLevel]!;
  }

  /// الحصول على مستوى قدرة الجهاز
  static DeviceCapabilityLevel getCapabilityLevel() {
    return _capabilityLevel;
  }

  /// تعيين مستوى قدرة الجهاز يدويًا (للاختبار)
  void setCapabilityLevel(DeviceCapabilityLevel level) {
    _capabilityLevel = level;
    debugPrint('تم تعيين مستوى قدرة الجهاز يدويًا إلى: $_capabilityLevel');
    notifyListeners();
  }

  /// التحقق مما إذا كان الجهاز ذو موارد محدودة
  static bool isLowEndDevice() {
    return _capabilityLevel == DeviceCapabilityLevel.low;
  }

  /// التحقق مما إذا كان الجهاز ذو أداء عالي
  static bool isHighEndDevice() {
    return _capabilityLevel == DeviceCapabilityLevel.high;
  }

  /// التخلص من الموارد عند إنهاء الخدمة
  @override
  void dispose() {
    super.dispose();
  }
}

/// إعدادات التخزين المؤقت
class CacheSettings {
  final int maxCacheSize;
  final int cacheExpirationHours;
  final int prefetchLimit;

  CacheSettings({
    required this.maxCacheSize,
    required this.cacheExpirationHours,
    required this.prefetchLimit,
  });
}

/// إعدادات العرض
class DisplaySettings {
  final bool enableAnimations;
  final bool enableParallaxEffects;
  final bool enableBlurEffects;
  final double imageQuality;
  final int maxImagesPerPage;

  DisplaySettings({
    required this.enableAnimations,
    required this.enableParallaxEffects,
    required this.enableBlurEffects,
    required this.imageQuality,
    required this.maxImagesPerPage,
  });
}
