import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:motorcycle_parts_shop/core/constants/app_constants.dart';
import 'package:motorcycle_parts_shop/core/services/unified_storage_service.dart';
import 'package:motorcycle_parts_shop/core/utils/common_operations.dart';
import 'package:motorcycle_parts_shop/models/address_model.dart';
import 'package:motorcycle_parts_shop/models/category_model.dart';
import 'package:motorcycle_parts_shop/models/product_model.dart';
import 'package:motorcycle_parts_shop/models/user_model.dart';
import 'package:supabase_flutter/supabase_flutter.dart'

    show
        AuthChangeEvent,
        AuthException,
        GoTrueClientSignInProvider,
        OAuthProvider,
        OtpType,
        PostgrestException,
        Supabase,
        SupabaseClient,
        User,
        UserAttributes;
import 'package:shared_preferences/shared_preferences.dart';

/// خدمة المصادقة باستخدام Supabase لإدارة عمليات تسجيل الدخول، التسجيل، وإدارة بيانات المستخدم.
class AuthSupabaseService extends ChangeNotifier {
  static final AuthSupabaseService _instance = AuthSupabaseService._internal();
  late final SupabaseClient _client;
  final UnifiedStorageService _storageService = UnifiedStorageService();

  bool _isInitialized = false;
  bool _isAuthenticated = false;
  bool _isLoading = false;
  String? _error;
  UserModel? _currentUser;
  DateTime? _lastAuthCheck;
  static const Duration _authCheckInterval = Duration(minutes: 5);
  static const Duration _cacheDuration = Duration(minutes: 5);
  static const Duration _sessionRefreshInterval = Duration(minutes: 4);

  final Map<String, dynamic> _cache = {};
  Timer? _sessionRefreshTimer;
  Timer? _connectionCheckTimer;

  factory AuthSupabaseService() => _instance;

  AuthSupabaseService._internal() {
    _client = Supabase.instance.client;
  }

  bool get isInitialized => _isInitialized;
  bool get isAuthenticated => _isAuthenticated;
  bool get isLoading => _isLoading;
  String? get error => _error;
  UserModel? get currentUser => _currentUser;
  SupabaseClient get client => _client;

  /// تهيئة خدمة المصادقة وتكوين الاتصال بـ Supabase.
  Future<void> initialize() async {
    if (_isInitialized) {
      debugPrint('خدمة المصادقة مهيأة بالفعل');
      return;
    }

    try {
      await _storageService.initialize();
      debugPrint('تم تهيئة خدمة التخزين المحلي بنجاح');

      // محاولة الاتصال بخادم Supabase مع إعادة المحاولة
      int retryCount = 0;
      const maxRetries = 3;
      const retryDelay = Duration(seconds: 2);

      while (retryCount < maxRetries) {
        try {
          await _checkConnection();
          break; // إذا نجح الاتصال، نخرج من الحلقة
        } catch (e) {
          retryCount++;
          if (retryCount >= maxRetries) {
            debugPrint('فشل الاتصال بخادم Supabase بعد $maxRetries محاولات');
            // نستمر في العملية حتى مع عدم وجود اتصال
            break;
          }
          debugPrint(
            'محاولة الاتصال $retryCount/$maxRetries فشلت، إعادة المحاولة بعد ${retryDelay.inSeconds} ثواني',
          );
          await Future.delayed(retryDelay);
        }
      }

      // محاولة جلب بيانات المستخدم الحالي
      try {
        final authUser = _client.auth.currentUser;
        if (authUser != null) {
          _currentUser = await _fetchUserModel(authUser);
          _isAuthenticated = true;
        }
      } catch (e) {
        debugPrint('فشل جلب بيانات المستخدم: $e');
        // نستمر في العملية حتى مع فشل جلب بيانات المستخدم
      }

      _isInitialized = true;
      notifyListeners();
      debugPrint('تم تهيئة خدمة المصادقة بنجاح');

      // إعداد مراقبة تغييرات حالة المصادقة
      _client.auth.onAuthStateChange.listen(
        (state) => _handleAuthStateChange(state.event),
        onError: (e) => debugPrint('خطأ في مراقبة تغييرات المصادقة: $e'),
      );

      _setupSessionRefresh();
      _setupConnectionCheck();
    } catch (e) {
      _error = 'فشل في تهيئة خدمة المصادقة: $e';
      debugPrint(_error);
      // لا نرمي استثناء هنا للسماح للتطبيق بالعمل في وضع عدم الاتصال
      _isInitialized = true;
      notifyListeners();
    }
  }

  /// التحقق من اتصال الشبكة بخادم Supabase.
  Future<void> _checkConnection() async {
    try {
      // التحقق من وجود جلسة حالية
      final session = _client.auth.currentSession;
      if (session == null) {
        // إذا لم تكن هناك جلسة، نحاول فقط التحقق من الاتصال بالخادم
        await _client
            .from('products')
            .select('count')
            .limit(1)
            .timeout(const Duration(seconds: 5));
        return;
      }

      // إذا كانت هناك جلسة، نتحقق من صلاحيتها
      await _client.auth.getUser().timeout(const Duration(seconds: 5));
    } catch (e) {
      debugPrint('خطأ في الاتصال بخادم Supabase: $e');
      // لا نرمي استثناء هنا للسماح للتطبيق بالعمل في وضع عدم الاتصال
      return;
    }
  }

  /// إعداد فحص دوري للاتصال بالخادم.
  void _setupConnectionCheck() {
    _connectionCheckTimer?.cancel();
    _connectionCheckTimer = Timer.periodic(const Duration(minutes: 1), (
      _,
    ) async {
      if (_isAuthenticated) {
        // فقط نتحقق من الاتصال إذا كان المستخدم مصادقاً
        try {
          await _checkConnection();
        } catch (e) {
          debugPrint('خطأ في التحقق من الاتصال: $e');
        }
      }
    });
  }

  /// جلب بيانات المستخدم من قاعدة البيانات بناءً على بيانات المصادقة.
  Future<UserModel> _fetchUserModel(User authUser) async {
    try {
      final cachedUser = _getCachedUser(authUser.id);
      if (cachedUser != null) {
        return cachedUser;
      }

      final query = _client
          .from(AppConstants.profilesTable)
          .select()
          .eq('id', authUser.id);

      final response = await query.maybeSingle();

      if (response == null) {
        debugPrint('لم يتم العثور على ملف شخصي للمستخدم: ${authUser.id}');
        throw Exception(
          'لم يتم العثور على ملف شخصي. قد يكون هناك مشكلة في إنشاء الملف الشخصي.',
        );
      }

      final user = UserModel.fromJson(response);
      _cacheUser(user);
      return user;
    } catch (e) {
      debugPrint('خطأ في جلب بيانات المستخدم: $e');
      rethrow;
    }
  }

  /// استرجاع بيانات المستخدم من المخبأ إذا كانت صالحة.
  UserModel? _getCachedUser(String userId) {
    final cacheKey = 'user_$userId';
    final cachedData = _cache[cacheKey];

    if (cachedData != null &&
        cachedData['timestamp'] != null &&
        DateTime.now().difference(cachedData['timestamp']) < _cacheDuration) {
      return cachedData['user'];
    }
    return null;
  }

  /// تخزين بيانات المستخدم في المخبأ.
  void _cacheUser(UserModel user) {
    final cacheKey = 'user_${user.id}';
    _cache[cacheKey] = {'user': user, 'timestamp': DateTime.now()};
  }

  /// معالجة تغييرات حالة المصادقة.
  void _handleAuthStateChange(AuthChangeEvent event) async {
    try {
      switch (event) {
        case AuthChangeEvent.signedIn:
          final authUser = _client.auth.currentUser;
          if (authUser != null) {
            _currentUser = await _fetchUserModel(authUser);
            _isAuthenticated = true;
            _setupSessionRefresh();
          }
          break;
        case AuthChangeEvent.signedOut:
          _currentUser = null;
          _isAuthenticated = false;
          _sessionRefreshTimer?.cancel();
          break;
        case AuthChangeEvent.tokenRefreshed:
          debugPrint('تم تجديد رمز الجلسة');
          final authUser = _client.auth.currentUser;
          if (authUser != null) {
            _currentUser = await _fetchUserModel(authUser);
          }
          break;
        case AuthChangeEvent.passwordRecovery:
          debugPrint('بدأت عملية استعادة كلمة المرور');
          break;
        default:
          debugPrint('حدث تغيير حالة غير مدعوم: $event');
      }
      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في معالجة تغيير حالة المصادقة: $e');
    }
  }

  /// التحقق من صلاحية الجلسة وتجديدها إذا لزم الأمر.
  Future<bool> _validateSession() async {
    final session = _client.auth.currentSession;
    if (session == null) return false;

    if (_lastAuthCheck != null &&
        DateTime.now().difference(_lastAuthCheck!) < _authCheckInterval) {
      return true;
    }

    try {
      if (session.isExpired) {
        final refreshedSession = await _client.auth.refreshSession();
        if (refreshedSession.session == null) return false;
        _lastAuthCheck = DateTime.now();
      }
      _lastAuthCheck = DateTime.now();
      return true;
    } catch (e) {
      debugPrint('خطأ في تجديد الجلسة: $e');
      return false;
    }
  }

  /// إعداد تجديد دوري للجلسة.
  void _setupSessionRefresh() {
    _sessionRefreshTimer?.cancel();
    _sessionRefreshTimer = Timer.periodic(_sessionRefreshInterval, (_) async {
      if (_isAuthenticated) {
        final isValid = await _validateSession();
        if (!isValid) {
          _isAuthenticated = false;
          _currentUser = null;
          notifyListeners();
        }
      }
    });
  }

  /// معالجة الطلبات مع التعامل مع الأخطاء.
  Future<T> _handleRequest<T>(
    Future<T> Function() request, {
    String? customError,
  }) async {
    try {
      return await request();
    } on TimeoutException {
      _error = 'انتهت مهلة الطلب. حاول مرة أخرى.';
      throw Exception(_error);
    } on AuthException catch (e) {
      _error = _handleAuthError(e);
      throw Exception(_error);
    } on PostgrestException catch (e) {
      _error = _handlePostgrestError(e);
      throw Exception(_error);
    } catch (e) {
      _error = customError ?? 'حدث خطأ غير متوقع: $e';
      throw Exception(_error);
    }
  }

  /// التحقق من صلاحية البريد الإلكتروني.
  bool _isVerEmail(String email) {
    if (email.isEmpty) return false;
    return RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    ).hasMatch(email);
  }

  /// التحقق من صلاحية رقم الهاتف.
  bool _isValidPhone(String? phone) {
    if (phone == null || phone.isEmpty) return true;
    final cleanPhone = phone.replaceAll(RegExp(r'[\s\-()]'), '');
    return RegExp(r'^\+?[0-9]{8,15}$').hasMatch(cleanPhone);
  }

  /// التحقق من صلاحية كلمة المرور.
  bool _isValidPassword(String password) {
    // كلمة المرور يجب أن تكون 6 أحرف على الأقل
    if (password.length < 6) return false;
    return true;
  }

  /// جلب بيانات المستخدم الحالي.
  Future<void> _fetchUserData() async {
    final authUser = _client.auth.currentUser;
    if (authUser == null) {
      _isAuthenticated = false;
      _error = 'لا يوجد مستخدم حالي';
      return;
    }

    // استخدام getUserById لجلب بيانات المستخدم
    _currentUser = await getUserById(authUser.id);
    if (_currentUser != null) {
      _isAuthenticated = true;
      await _storageService.saveUser(_currentUser!);
    } else {
      _isAuthenticated = false;
      _error = 'لم يتم العثور على ملف شخصي لهذا المستخدم';
    }
  }

  /// تسجيل الدخول باستخدام البريد الإلكتروني وكلمة المرور.
  Future<bool> signInWithEmail(String email, String password) async {
    debugPrint('بدء عملية تسجيل الدخول في خدمة المصادقة');
    debugPrint('البريد الإلكتروني: $email');
    debugPrint('طول كلمة المرور: ${password.length}');

    if (!isInitialized) {
      debugPrint('خدمة المصادقة غير مهيأة');
      _error = 'خدمة المصادقة غير مهيأة';
      return false;
    }

    if (email.isEmpty || password.isEmpty) {
      debugPrint('البريد الإلكتروني أو كلمة المرور فارغة');
      _error = 'جميع الحقول مطلوبة';
      return false;
    }

    try {
      debugPrint('محاولة تسجيل الدخول في Supabase Auth...');
      debugPrint('التحقق من حالة الاتصال بـ Supabase...');

      // التحقق من حالة الاتصال
      try {
        await _client.from('profiles').select('count').limit(1);
        debugPrint('الاتصال بـ Supabase يعمل بشكل صحيح');
      } catch (e) {
        debugPrint('خطأ في الاتصال بـ Supabase: $e');
        _error = 'خطأ في الاتصال بالخادم';
        return false;
      }

      // محاولة تسجيل الدخول في Auth أولاً
      final authResponse = await _client.auth.signInWithPassword(
        email: email,
        password: password,
      );

      if (authResponse.user == null) {
        debugPrint('فشل تسجيل الدخول في Auth');
        _error = 'فشل تسجيل الدخول';
        return false;
      }

      debugPrint('تم تسجيل الدخول بنجاح في Auth');
      debugPrint('معرف المستخدم: ${authResponse.user!.id}');

      // التحقق من وجود الملف الشخصي
      debugPrint('التحقق من وجود الملف الشخصي...');
      final profileResponse =
          await _client
              .from(AppConstants.profilesTable)
              .select()
              .eq('id', authResponse.user!.id)
              .maybeSingle();

      if (profileResponse == null) {
        debugPrint('المستخدم غير موجود في جدول الملفات الشخصية');
        _error = 'البريد الإلكتروني غير مسجل';
        return false;
      }

      debugPrint('تم العثور على الملف الشخصي');
      _currentUser = UserModel.fromJson(profileResponse);
      _isAuthenticated = true;
      notifyListeners();

      return true;
    } catch (e) {
      debugPrint('حدث خطأ أثناء تسجيل الدخول: $e');
      if (e.toString().contains('Invalid login credentials')) {
        _error = 'البريد الإلكتروني أو كلمة المرور غير صحيحة';
      } else if (e.toString().contains('Email not confirmed')) {
        _error = 'يرجى تأكيد البريد الإلكتروني أولاً';
      } else {
        _error = 'حدث خطأ أثناء تسجيل الدخول: $e';
      }
      return false;
    }
  }

  /// إرسال رمز OTP إلى البريد الإلكتروني.
  Future<bool> sendEmailOtp(String email, {bool isRegistration = false}) async {
    if (!_isInitialized) throw Exception('الخدمة لم تُهيأ بعد');
    _isLoading = true;
    _error = null;

    try {
      debugPrint('محاولة إرسال رمز OTP إلى: $email');

      if (!_isVerEmail(email)) {
        _error = 'البريد الإلكتروني غير صالح';
        debugPrint(_error);
        return false;
      }

      if (!isRegistration) {
        final emailExists = await checkEmailExists(email);
        if (!emailExists) {
          _error = 'البريد الإلكتروني غير مسجل';
          debugPrint(_error);
          return false;
        }
      } else {
        final emailExists = await checkEmailExists(email);
        if (emailExists) {
          _error = 'البريد الإلكتروني مسجل بالفعل';
          debugPrint(_error);
          return false;
        }
      }

      await _client.auth.signInWithOtp(
        email: email,
        emailRedirectTo:
            kIsWeb
                ? '${Uri.base.origin}/auth/callback'
                : 'io.supabase.motorcycleparts://login-callback/',
      );

      debugPrint('تم إرسال رمز التحقق بنجاح إلى: $email');
      return true;
    } on AuthException catch (e) {
      _error = _handleAuthError(e);
      if (e.statusCode == '429') {
        _error = '${_error ?? ''}، يرجى الانتظار قليلاً قبل المحاولة مرة أخرى';
      }
      debugPrint('خطأ مصادقة في إرسال رمز التحقق: $_error');
      return false;
    } catch (e) {
      _error = 'فشل إرسال رمز التحقق: $e';
      debugPrint('خطأ غير متوقع في إرسال رمز التحقق: $_error');
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// التحقق من رمز OTP المرسل إلى البريد الإلكتروني.
  Future<bool> verifyEmailOtp(String email, String token) async {
    if (!_isInitialized) throw Exception('الخدمة لم تُهيأ بعد');
    _isLoading = true;
    _error = null;

    try {
      // التحقق من صحة البيانات
      if (!_isVerEmail(email)) {
        _error = 'البريد الإلكتروني غير صالح';
        debugPrint(_error);
        return false;
      }
      if (!RegExp(r'^\d{6}$').hasMatch(token)) {
        _error = 'رمز التحقق يجب أن يكون 6 أرقام';
        debugPrint(_error);
        return false;
      }

      debugPrint('التحقق من رمز OTP للبريد: $email، الرمز: $token');

      // التحقق من الرمز
      final response = await _handleRequest(
        () => _client.auth.verifyOTP(
          email: email,
          token: token,
          type: OtpType.email,
        ),
        customError: 'فشل التحقق من الرمز',
      );

      debugPrint(
        'نتيجة التحقق من الرمز: ${response.session != null ? 'ناجح' : 'فاشل'}',
      );

      if (response.session != null) {
        // تحديث بيانات المستخدم
        await _fetchUserData();
        if (_currentUser != null) {
          // تحديث آخر تسجيل دخول
          await _updateLastLogin(_currentUser!.id);
          // حفظ رمز الوصول
          await _storageService.saveToken(response.session!.accessToken);
          // تحديث حالة المصادقة
          _isAuthenticated = true;
          // إعداد تحديث الجلسة
          _setupSessionRefresh();
          debugPrint('تم التحقق من OTP بنجاح لـ: $email');
          return true;
        } else {
          _error = 'فشل تحميل بيانات المستخدم بعد التحقق';
          debugPrint(_error);
        }
      } else {
        _error = 'فشل التحقق من الرمز: الجلسة غير متوفرة';
        debugPrint(_error);
      }
      return false;
    } catch (e) {
      _error = e.toString();
      debugPrint('خطأ في التحقق من OTP: $_error');
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// تحليل أخطاء المصادقة العامة بشكل مفصل.
  String _getAuthErrorMessage(dynamic error) {
    final errorStr = error.toString().toLowerCase();
    debugPrint('تحليل خطأ المصادقة: $errorStr');

    if (errorStr.contains('invalid credentials') ||
        errorStr.contains('invalid login credentials')) {
      debugPrint('نوع الخطأ: بيانات تسجيل دخول غير صحيحة');
      return 'بيانات تسجيل الدخول غير صحيحة. يرجى التحقق من البريد الإلكتروني وكلمة المرور';
    } else if (errorStr.contains('invalid email')) {
      debugPrint('نوع الخطأ: بريد إلكتروني غير صالح');
      return 'البريد الإلكتروني غير صالح. يرجى التأكد من كتابة البريد الإلكتروني بشكل صحيح';
    } else if (errorStr.contains('email already exists') ||
        errorStr.contains('unique constraint')) {
      debugPrint('نوع الخطأ: البريد الإلكتروني مستخدم بالفعل');
      return 'البريد الإلكتروني مستخدم بالفعل. يرجى تسجيل الدخول أو استخدام بريد إلكتروني آخر';
    } else if (errorStr.contains('weak password')) {
      debugPrint('نوع الخطأ: كلمة مرور ضعيفة');
      return 'كلمة المرور ضعيفة جداً. يجب أن تحتوي على 6 أحرف على الأقل';
    } else if (errorStr.contains('network') ||
        errorStr.contains('connection')) {
      debugPrint('نوع الخطأ: مشكلة في الشبكة');
      return 'خطأ في الاتصال بالشبكة. يرجى التحقق من اتصالك بالإنترنت والمحاولة مرة أخرى';
    } else if (errorStr.contains('rate limit')) {
      debugPrint('نوع الخطأ: تجاوز الحد المسموح من المحاولات');
      return 'تم تجاوز عدد المحاولات المسموح بها. يرجى الانتظار لمدة دقيقة ثم المحاولة مرة أخرى';
    } else if (errorStr.contains('user not found')) {
      debugPrint('نوع الخطأ: المستخدم غير موجود');
      return 'البريد الإلكتروني غير مسجل في النظام. يرجى التأكد من البريد الإلكتروني أو إنشاء حساب جديد';
    } else if (errorStr.contains('timeout')) {
      debugPrint('نوع الخطأ: انتهاء مهلة الاتصال');
      return 'انتهت مهلة الاتصال بالخادم. يرجى التحقق من اتصالك بالإنترنت والمحاولة مرة أخرى';
    } else if (errorStr.contains('email not confirmed')) {
      debugPrint('نوع الخطأ: البريد الإلكتروني غير مؤكد');
      return 'لم يتم تأكيد البريد الإلكتروني بعد. يرجى التحقق من بريدك الإلكتروني وتأكيد حسابك';
    } else if (errorStr.contains('invalid password')) {
      debugPrint('نوع الخطأ: كلمة مرور غير صحيحة');
      return 'كلمة المرور غير صحيحة. يرجى التأكد من كلمة المرور والمحاولة مرة أخرى';
    } else if (errorStr.contains('auth session')) {
      debugPrint('نوع الخطأ: مشكلة في جلسة المصادقة');
      return 'حدثت مشكلة في جلسة المصادقة. يرجى تسجيل الخروج وإعادة تسجيل الدخول';
    } else if (errorStr.contains('server')) {
      debugPrint('نوع الخطأ: مشكلة في الخادم');
      return 'حدثت مشكلة في الخادم. يرجى المحاولة مرة أخرى لاحقاً';
    } else {
      // تنظيف رسالة الخطأ من أي استثناءات إضافية
      String cleanError = error.toString();
      if (cleanError.contains('Exception:')) {
        cleanError = cleanError.split('Exception:').last.trim();
      }
      debugPrint('نوع الخطأ: خطأ غير معروف - $cleanError');
      return 'حدث خطأ غير متوقع: $cleanError';
    }
  }

  /// إنشاء حساب جديد للمستخدم.
  Future<bool> signUp({
    required String email,
    required String password,
    required String name,
    String? address,
    DateTime? birthDate,
    String? phone,
    List<String>? permissions,
    String? governorate,

    String? profileType,
  }) async {
    if (!_isInitialized) {
      _error = 'الخدمة لم تُهيأ بعد';
      return false;
    }

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // التحقق من صحة البيانات
      if (!_isVerEmail(email)) {
        _error = 'البريد الإلكتروني غير صالح';
        return false;
      }
      if (!_isValidPassword(password)) {
        _error = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
        return false;
      }
      if (name.trim().isEmpty) {
        _error = 'الاسم مطلوب';
        return false;
      }
      if (phone != null && !_isValidPhone(phone)) {
        _error = 'رقم الهاتف غير صالح';
        return false;
      }
      if (birthDate != null && birthDate.isAfter(DateTime.now())) {
        _error = 'تاريخ الميلاد غير صالح';
        return false;
      }
      if (governorate != null && governorate.trim().isEmpty) {
        _error = 'المحافظة غير صالحة';
        return false;
      }
      if (address != null && address.trim().isEmpty) {
        _error = 'العنوان غير صالح';
        return false;
      }

      // التحقق من عدم وجود البريد الإلكتروني
      final emailExists = await checkEmailExists(email);
      if (emailExists) {
        _error = 'البريد الإلكتروني مستخدم بالفعل';
        return false;
      }

      final userProfileType = profileType ?? AppConstants.profileTypeCustomer;
      if (![
        AppConstants.profileTypeCustomer,
        AppConstants.profileTypeAdmin,
      ].contains(userProfileType)) {
        _error = 'نوع الملف الشخصي غير صالح';
        return false;
      }

      debugPrint('بدء عملية إنشاء حساب جديد للبريد: $email');

      // إنشاء الحساب في Supabase Auth
      final authResponse = await _handleRequest(
        () => _client.auth.signUp(
          email: email,
          password: password,
          data: {
            "name": name,
            "address": address,
            "birth_date": birthDate?.toIso8601String(),
            "phone": phone,
            "governorate": governorate,

            "profile_type": userProfileType,
            "permissions": permissions ?? ['customer'],
          },
        ),
        customError: 'فشل في إنشاء الحساب',
      );

      if (authResponse.user == null) {
        _error = 'فشل في إنشاء الحساب';
        return false;
      }

      // إنشاء ملف شخصي للمستخدم
      try {
        await _createUserProfile(
          userId: authResponse.user!.id,
          name: name,
          email: email,
          phone: phone,
          address: address,
          birthDate: birthDate,
          governorate: governorate,
          center: null, // سيتم تحديثه لاحقاً من صفحة التحقق
          profileType: userProfileType,
          permissions: permissions,
        );

        // تحديث المستخدم الحالي
        _currentUser = await _fetchUserModel(authResponse.user!);
        _isAuthenticated = true;

        // حفظ رمز الوصول
        if (authResponse.session != null) {
          await _storageService.saveToken(authResponse.session!.accessToken);
        }

        // إعداد تحديث الجلسة
        _setupSessionRefresh();

        debugPrint('تم إنشاء الحساب والملف الشخصي بنجاح');
        return true;
      } catch (e) {
        // في حالة فشل إنشاء الملف الشخصي، نحذف الحساب
        try {
          await _client.auth.admin.deleteUser(authResponse.user!.id);
          debugPrint('تم حذف حساب المستخدم بعد فشل إنشاء الملف الشخصي');
        } catch (deleteError) {
          debugPrint('خطأ في حذف حساب المستخدم: $deleteError');
        }
        throw Exception('فشل في إنشاء الملف الشخصي: $e');
      }
    } catch (e) {
      _error = _getAuthErrorMessage(e);
      debugPrint('خطأ في التسجيل: $_error');
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// التحقق من نجاح إنشاء الملف الشخصي
  Future<bool> _verifyProfileCreation(String userId) async {
    try {
      final response =
          await _client.from('profiles').select().eq('id', userId).single();

      return response != null;
    } catch (e) {
      debugPrint('خطأ في التحقق من إنشاء الملف الشخصي: $e');
      return false;
    }
  }

  /// إنشاء ملف شخصي للمستخدم
  Future<void> _createUserProfile({
    required String userId,
    required String name,
    required String email,
    String? phone,
    String? address,
    DateTime? birthDate,
    String? governorate,
    String? center,
    required String profileType,
    List<String>? permissions,
  }) async {
    try {
      debugPrint('بدء إنشاء ملف شخصي للمستخدم: $userId');
      debugPrint('البريد الإلكتروني: $email');
      debugPrint('نوع الملف الشخصي: $profileType');

      final profileData = {
        'id': userId,
        'name': name,
        'email': email,
        'phone': phone,
        'address': address,
        'birth_date': birthDate?.toIso8601String(),
        'governorate': governorate,
        'center': center,
        'profile_type': profileType,
        'permissions': permissions ?? ['customer'],
        'order_count': 0,
        'wishlist_count': 0,
        'average_rating': 0.0,
        'total_spent': 0.0,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
        'last_login_at': DateTime.now().toIso8601String(),
        'unread_notifications_count': 0,
        'total_notifications_count': 0,
      };

      debugPrint('بيانات الملف الشخصي: $profileData');

      // التحقق من وجود البيانات المطلوبة
      if (name.isEmpty || email.isEmpty) {
        throw Exception('الاسم والبريد الإلكتروني مطلوبان');
      }

      // التحقق من وجود المستخدم في جدول الملفات الشخصية قبل الإنشاء
      final existingProfile =
          await _client
              .from(AppConstants.profilesTable)
              .select()
              .eq('id', userId)
              .maybeSingle();

      if (existingProfile != null) {
        debugPrint('الملف الشخصي موجود بالفعل للمستخدم: $userId');
        return;
      }

      // محاولة إنشاء الملف الشخصي
      debugPrint('محاولة إنشاء الملف الشخصي...');
      final response =
          await _client
              .from(AppConstants.profilesTable)
              .insert(profileData)
              .select()
              .single();

      if (response == null) {
        throw Exception(
          'فشل في إنشاء الملف الشخصي: لم يتم استلام استجابة من الخادم',
        );
      }

      // التحقق من نجاح إنشاء الملف الشخصي
      final isProfileCreated = await _verifyProfileCreation(userId);
      if (!isProfileCreated) {
        throw Exception('فشل في التحقق من إنشاء الملف الشخصي');
      }

      debugPrint('تم إنشاء الملف الشخصي بنجاح');
      debugPrint('تفاصيل الملف الشخصي المنشأ: $response');
    } catch (e) {
      debugPrint('خطأ في إنشاء الملف الشخصي: $e');
      throw Exception('فشل في إنشاء الملف الشخصي: $e');
    }
  }

  /// تسجيل الدخول باستخدام Google (للويب فقط).
  /// للتطبيقات المحمولة، استخدم GoogleAuthService
  Future<bool> signInWithGoogle() async {
    try {
      debugPrint('بدء تسجيل الدخول باستخدام Google (OAuth)');

      if (!_isInitialized) {
        throw Exception('لم يتم تهيئة خدمة المصادقة');
      }

      if (!kIsWeb) {
        throw Exception(
          'هذه الطريقة مخصصة للويب فقط. استخدم GoogleAuthService للتطبيقات المحمولة',
        );
      }

      final redirectUrl = '${Uri.base.origin}/auth/callback';
      debugPrint('URL إعادة التوجيه: $redirectUrl');

      final response = await _handleRequest(
        () => _client.auth.signInWithOAuth(
          OAuthProvider.google,
          redirectTo: redirectUrl,
          queryParams: {'access_type': 'offline', 'prompt': 'consent'},
        ),
        customError: 'فشل تسجيل الدخول باستخدام Google',
      );

      debugPrint('تم استلام الاستجابة من Google: $response');

      final session = _client.auth.currentSession;
      if (session != null) {
        debugPrint('تم تسجيل الدخول بنجاح');
        await _saveAccessToken(session.accessToken);
        await _fetchUserData();
        _isAuthenticated = true;
        _setupSessionRefresh();

        await _storageService.saveLastAuthenticationTime(
          DateTime.now().millisecondsSinceEpoch,
        );
        return true;
      } else {
        throw Exception('لم يتم العثور على جلسة صالحة');
      }
    } on AuthException catch (e) {
      debugPrint('خطأ مصادقة: ${e.message}');
      _error = 'خطأ في المصادقة: ${e.message}';
      return false;
    } catch (e) {
      debugPrint('خطأ غير متوقع: $e');
      _error = 'حدث خطأ غير متوقع أثناء تسجيل الدخول باستخدام Google: $e';
      return false;
    }
  }

  /// حفظ رمز الوصول في التخزين المحلي.
  Future<void> _saveAccessToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('access_token', token);
  }

  /// حذف منتج من قاعدة البيانات
  Future<bool> deleteProduct(String productId) async {
    if (!_isInitialized) throw Exception('الخدمة لم تُهيأ بعد');
    if (!_isAuthenticated) throw Exception('المستخدم غير مصرح له');

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // التحقق من وجود المنتج

      // حذف المنتج
      await _client.from('products').delete().eq('id', productId);

      debugPrint('تم حذف المنتج بنجاح: $productId');
      return true;
    } catch (e) {
      _error = 'فشل حذف المنتج: $e';
      debugPrint(_error);
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// إعادة تعيين كلمة المرور.
  Future<bool> resetPassword(String email) async {
    if (!_isInitialized) throw Exception('الخدمة لم تُهيأ بعد');
    _isLoading = true;
    _error = null;

    try {
      if (!_isVerEmail(email)) throw Exception('البريد الإلكتروني غير صالح');

      // تحديد رابط إعادة التوجيه بناءً على المنصة
      final redirectTo =
          kIsWeb
              ? '${Uri.base.origin}/reset-password'
              : 'io.supabase.motorcycleparts://reset-password';

      await _handleRequest(
        () => _client.auth.resetPasswordForEmail(email, redirectTo: redirectTo),
        customError: 'فشل إعادة تعيين كلمة المرور',
      );

      debugPrint('تم إرسال رابط إعادة تعيين كلمة المرور إلى: $email');
      debugPrint('رابط إعادة التوجيه: $redirectTo');
      return true;
    } catch (e) {
      _error = _getAuthErrorMessage(e);
      debugPrint('خطأ في إعادة تعيين كلمة المرور: $_error');
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// جلب جميع المنتجات مع خيارات التصفية والترتيب المحسنة.
  Future<List<ProductModel>> getAllProducts({
    int limit = 20,
    int offset = 0,
    String? searchQuery,
    String? categoryId,
    String? sortBy,
    bool ascending = true,
    bool onlyAvailable = true,
  }) async {
    return await _handleRequest(() async {
      // تحسين الاستعلام لتقليل البيانات المنقولة
      const selectColumns = '''
        id, name, description, price, discount_price, original_price,
        category_id, company_id, image_urls, specifications,
        stock_quantity, average_rating, review_count, view_count, is_featured,
        is_best_selling, created_at, updated_at, sku, brand, is_available
      ''';

      dynamic query = _client
          .from(AppConstants.productsTable)
          .select(selectColumns);

      // تطبيق فلتر التوفر أولاً لتحسين الأداء
      if (onlyAvailable) {
        query = query.eq('is_available', true);
      }

      // البحث النصي المحسن
      if (searchQuery != null && searchQuery.isNotEmpty) {
        query = query.or(
          'name.ilike.%$searchQuery%,description.ilike.%$searchQuery%,brand.ilike.%$searchQuery%',
        );
      }

      // فلتر الفئة
      if (categoryId != null && categoryId.isNotEmpty) {
        query = query.eq('category_id', categoryId);
      }

      // ترتيب محسن مع فهارس
      if (sortBy != null && sortBy.isNotEmpty) {
        switch (sortBy) {
          case 'name':
            query = query.order('name', ascending: ascending);
            break;
          case 'price':
            query = query.order('price', ascending: ascending);
            break;
          case 'rating':
            query = query.order('average_rating', ascending: !ascending);
            break;
          case 'newest':
            query = query.order('created_at', ascending: !ascending);
            break;
          case 'popular':
            query = query.order('view_count', ascending: !ascending);
            break;
          default:
            query = query.order('created_at', ascending: false);
        }
      } else {
        query = query.order('created_at', ascending: false);
      }

      // تطبيق pagination مع timeout
      final response = await query
          .range(offset, offset + limit - 1)
          .timeout(const Duration(seconds: 10));

      return response.map((json) => ProductModel.fromJson(json)).toList();
    }, customError: 'فشل في جلب المنتجات');
  }

  /// تسجيل الخروج ومسح البيانات المحلية.
  ///
  /// تقوم هذه الدالة بتسجيل خروج المستخدم الحالي وتنظيف جميع البيانات المحلية المرتبطة به.
  /// كما تقوم بتسجيل نشاط تسجيل الخروج وإلغاء جميع المؤقتات والاشتراكات.
  ///
  /// المعلمات:
  /// * [forceSignOut] - إذا كانت القيمة `true`، سيتم تنفيذ تسجيل الخروج بشكل إجباري حتى في حالة حدوث أخطاء.
  /// * [clearCache] - إذا كانت القيمة `true`، سيتم مسح ذاكرة التخزين المؤقت بالكامل (افتراضيًا `true`).
  /// * [notifyUser] - إذا كانت القيمة `true`، سيتم إشعار المستخدم بنجاح تسجيل الخروج (افتراضيًا `false`).
  ///
  /// تُرجع:
  /// * `Future<bool>` - `true` إذا تم تسجيل الخروج بنجاح، و`false` إذا فشلت العملية.
  Future<bool> signOut({
    bool forceSignOut = false,
    bool clearCache = true,
    bool notifyUser = false,
  }) async {
    if (!_isInitialized) {
      _error = 'الخدمة لم تُهيأ بعد';
      debugPrint('محاولة تسجيل الخروج قبل تهيئة الخدمة');
      return false;
    }

    _isLoading = true;
    _error = null;
    notifyListeners();

    // تسجيل وقت بدء عملية تسجيل الخروج للقياس
    final startTime = DateTime.now();

    // حفظ معلومات المستخدم قبل تسجيل الخروج لاستخدامها في تسجيل النشاط
    final userId = _currentUser?.id;
    final userEmail = _currentUser?.email;
    final deviceInfo = await _getDeviceInfo();
    bool logoutSuccessful = false;

    try {
      // التحقق من وجود اتصال بالإنترنت قبل محاولة تسجيل الخروج من الخادم
      try {
        await _checkConnection();
      } catch (connectionError) {
        debugPrint(
          'تحذير: لا يوجد اتصال بالإنترنت، سيتم تسجيل الخروج محليًا فقط',
        );
        // نستمر في العملية حتى مع عدم وجود اتصال
      }

      // تسجيل نشاط تسجيل الخروج إذا كان المستخدم مسجل الدخول
      if (_isAuthenticated && userId != null) {
        try {
          final logData = {
            'user_id': userId,
            'email': userEmail,
            'activity_type': 'logout',
            'device_info': deviceInfo,
            'ip_address': await _getIpAddress(),
            'created_at': DateTime.now().toIso8601String(),
            'status': 'success',
          };

          await _client.from('user_activity_logs').insert(logData);
          debugPrint('تم تسجيل نشاط تسجيل الخروج بنجاح');
        } catch (logError) {
          // لا نريد إيقاف عملية تسجيل الخروج إذا فشل تسجيل النشاط
          debugPrint('فشل تسجيل نشاط تسجيل الخروج: $logError');
        }
      }

      // تنفيذ تسجيل الخروج من Supabase
      await _handleRequest(
        () => _client.auth.signOut(),
        customError: 'فشل تسجيل الخروج من الخادم',
      );

      // تنظيف البيانات المحلية
      if (clearCache) {
        await _cleanupLocalData();
      }

      // إعادة تعيين حالة المصادقة
      _isAuthenticated = false;
      _currentUser = null;
      _lastAuthCheck = null;

      // إلغاء المؤقتات
      _sessionRefreshTimer?.cancel();
      _connectionCheckTimer?.cancel();

      // حساب الوقت المستغرق لتسجيل الخروج
      final duration = DateTime.now().difference(startTime);
      debugPrint('تم تسجيل الخروج بنجاح (${duration.inMilliseconds}ms)');
      logoutSuccessful = true;
      return true;
    } catch (e) {
      _error = 'خطأ أثناء تسجيل الخروج: $e';
      debugPrint(_error);

      // إذا كان التسجيل إجباري، نقوم بتنظيف البيانات المحلية بغض النظر عن الخطأ
      if (forceSignOut) {
        debugPrint('تنفيذ تسجيل الخروج الإجباري بسبب الخطأ: $e');

        if (clearCache) {
          await _cleanupLocalData();
        }

        _isAuthenticated = false;
        _currentUser = null;
        _lastAuthCheck = null;
        _sessionRefreshTimer?.cancel();
        _connectionCheckTimer?.cancel();

        debugPrint('تم تنفيذ تسجيل الخروج الإجباري بنجاح');
        logoutSuccessful = true;
        return true;
      }

      // محاولة تسجيل الخطأ في قاعدة البيانات إذا كان المستخدم لا يزال مصادقًا
      if (_isAuthenticated && userId != null) {
        try {
          await _client.from('user_activity_logs').insert({
            'user_id': userId,
            'email': userEmail,
            'activity_type': 'logout_failed',
            'device_info': deviceInfo,
            'ip_address': await _getIpAddress(),
            'created_at': DateTime.now().toIso8601String(),
            'status': 'failed',
            'error_details': e.toString(),
          });
        } catch (logError) {
          debugPrint('فشل تسجيل خطأ تسجيل الخروج: $logError');
        }
      }

      return false;
    } finally {
      _isLoading = false;
      notifyListeners();

      // إشعار المستخدم بنتيجة تسجيل الخروج إذا كان مطلوبًا
      if (notifyUser) {
        // يمكن تنفيذ آلية الإشعار هنا، مثل استخدام خدمة الإشعارات
        debugPrint(
          'إشعار المستخدم: ${logoutSuccessful ? 'تم تسجيل الخروج بنجاح' : 'فشل تسجيل الخروج'}',
        );
      }
    }
  }

  /// تنظيف البيانات المحلية المرتبطة بالمستخدم
  Future<void> _cleanupLocalData() async {
    try {
      // مسح جميع البيانات المخزنة محلياً
      await _storageService.clearAll();
      await _storageService.saveLastAuthenticationTime(null);

      // مسح ذاكرة التخزين المؤقت
      _cache.clear();

      // مسح أي بيانات مؤقتة أخرى
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('access_token');
      await prefs.remove('refresh_token');

      debugPrint('تم تنظيف البيانات المحلية بنجاح');
    } catch (e) {
      debugPrint('خطأ أثناء تنظيف البيانات المحلية: $e');
    }
  }

  /// الحصول على معلومات الجهاز
  Future<Map<String, dynamic>> _getDeviceInfo() async {
    try {
      final Map<String, dynamic> deviceData = {
        'platform': kIsWeb ? 'web' : Platform.operatingSystem,
        'timestamp': DateTime.now().toIso8601String(),
      };

      return deviceData;
    } catch (e) {
      debugPrint('خطأ في الحصول على معلومات الجهاز: $e');
      return {'platform': 'unknown', 'error': e.toString()};
    }
  }

  /// الحصول على عنوان IP
  Future<String> _getIpAddress() async {
    try {
      // هذه طريقة مبسطة، يمكن استخدام خدمة خارجية للحصول على عنوان IP
      return 'unknown'; // في التطبيق الحقيقي، يمكن استخدام خدمة مثل ipify
    } catch (e) {
      return 'unknown';
    }
  }

  /// تحديث ملف المستخدم.
  Future<void> updateProfile(UserModel user) async {
    if (!_isInitialized) throw Exception('الخدمة لم تُهيأ بعد');
    if (_currentUser == null || user.id != _currentUser!.id) {
      throw Exception('لا يمكنك تحديث بيانات مستخدم آخر');
    }

    _isLoading = true;
    _error = null;

    try {
      // تم إزالة معالجة صورة المستخدم

      final updatedUser = user.copyWith(
        updatedAt: DateTime.now(),
        // تم إزالة الحقول المشفرة كجزء من تنظيف المشروع
      );

      await _handleRequest(
        () => _client
            .from('profiles')
            .update(updatedUser.toJson())
            .eq('id', user.id),
        customError: 'فشل تحديث الملف الشخصي',
      );

      _currentUser = updatedUser;
      await _storageService.saveUser(updatedUser);
      _cacheUser(updatedUser);
    } catch (e) {
      throw Exception(_error ?? 'فشل تحديث الملف الشخصي: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // ✅ updateUserProfile مع تحقق إضافي:
  Future<bool> updateUserProfile({
    required String name,
    String? phone,
    String? address,
    DateTime? birthDate,
    String? governorate,
    String? center,
  }) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('لم يتم العثور على مستخدم مسجل الدخول');
      }

      // إنشاء خريطة البيانات للتحديث
      final Map<String, dynamic> updateData = {
        'name': name,
        'phone': phone,
        'address': address,
        'birth_date': birthDate?.toIso8601String(),
        'governorate': governorate,
        'center': center,
        'updated_at': DateTime.now().toIso8601String(),
      };

      // إزالة القيم الفارغة
      updateData.removeWhere((key, value) => value == null);

      // تحديث الملف الشخصي
      final response =
          await _client
              .from(AppConstants.profilesTable)
              .update(updateData)
              .eq('id', userId)
              .select()
              .single();

      if (response == null) {
        throw Exception('فشل تحديث الملف الشخصي');
      }

      // تحديث بيانات المستخدم الحالي
      _currentUser = UserModel.fromJson(response);
      notifyListeners();

      return true;
    } catch (e) {
      _error = e.toString();
      debugPrint('خطأ في تحديث الملف الشخصي: $e');
      return false;
    }
  }

  /// تخزين رابط صورة Cloudinary في قاعدة البيانات.
  Future<bool> storeCloudinaryImageUrl({
    required String imageUrl,
    required String tableName,
    required String columnName,
    required String recordId,
  }) async {
    try {
      await _handleRequest(
        () => _client
            .from(tableName)
            .update({columnName: imageUrl})
            .eq('id', recordId),
        customError: 'فشل تخزين رابط الصورة',
      );
      return true;
    } catch (e) {
      return false;
    }
  }

  /// تخزين رابط صورة المنتج.
  Future<bool> storeProductImageUrl({
    required String imageUrl,
    required String productId,
    bool isMainImage = true,
  }) async {
    try {
      if (isMainImage) {
        return await storeCloudinaryImageUrl(
          imageUrl: imageUrl,
          tableName: AppConstants.productsTable,
          columnName: 'image_url',
          recordId: productId,
        );
      } else {
        final response =
            await _client
                .from(AppConstants.productsTable)
                .select('additional_images')
                .eq('id', productId)
                .single();
        List<String> additionalImages = List<String>.from(
          response['additional_images'] ?? [],
        );
        if (!additionalImages.contains(imageUrl)) {
          additionalImages.add(imageUrl);
        }
        await _handleRequest(
          () => _client
              .from(AppConstants.productsTable)
              .update({'additional_images': additionalImages})
              .eq('id', productId),
          customError: 'فشل إضافة صورة إضافية للمنتج',
        );
        return true;
      }
    } catch (e) {
      return false;
    }
  }

  /// تخزين رابط صورة الفئة.
  Future<bool> storeCategoryImageUrl({
    required String imageUrl,
    required String categoryId,
  }) async {
    return storeCloudinaryImageUrl(
      imageUrl: imageUrl,
      tableName: AppConstants.categoriesTable,
      columnName: 'image_url',
      recordId: categoryId,
    );
  }

  // تم إزالة دوال التشفير وفك التشفير

  /// جلب بيانات المستخدم بناءً على معرفه.
  Future<UserModel?> getUserById(String userId) async {
    try {
      debugPrint('جلب بيانات المستخدم بالمعرف: $userId');
      final query = _client
          .from(AppConstants.profilesTable)
          .select('*')
          .eq('id', userId);

      final response = await query.maybeSingle();

      if (response == null) {
        debugPrint('لم يتم العثور على ملف شخصي للمستخدم: $userId');
        return null;
      }

      // استخدام البيانات مباشرة بدون تشفير
      // تم إزالة جميع عمليات التشفير من المشروع

      debugPrint('تم العثور على ملف المستخدم: ${response['email']}');
      return UserModel.fromJson(response);
    } catch (e) {
      _error = 'فشل في جلب بيانات المستخدم: $e';
      debugPrint(_error);
      return null;
    }
  }

  /// جلب جميع عناوين المستخدم.
  Future<List<AddressModel>> getAddresses() async {
    return await _fetchWithCache('user_addresses', () async {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('المستخدم غير مصادق عليه');
      final query = _client
          .from(AppConstants.addressesTable)
          .select('*')
          .eq('user_id', userId);

      final response = await query
          .order('is_default', ascending: false)
          .order('created_at', ascending: false);
      return response.map((address) => AddressModel.fromJson(address)).toList();
    });
  }

  /// إضافة عنوان جديد للمستخدم.
  Future<void> addAddress(AddressModel address) async {
    try {
      await _client.from('addresses').insert({
        'title': address.title,
        'full_name': address.fullName,
        'street_address': address.streetAddress,
        'city': address.city,
        'state': address.state,
        'phone_number': address.phoneNumber,
        'is_default': address.isDefault,
        'user_id': _currentUser!.id,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      });
      _cache.remove('user_addresses');
    } catch (e) {
      throw Exception('فشل في إضافة العنوان: $e');
    }
  }

  /// تحديث عنوان موجود.
  Future<void> updateAddress(AddressModel address) async {
    try {
      await _client
          .from('addresses')
          .update({
            'title': address.title,
            'full_name': address.fullName,
            'street_address': address.streetAddress,
            'city': address.city,
            'state': address.state,
            'phone_number': address.phoneNumber,
            'is_default': address.isDefault,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', address.id);
      _cache.remove('user_addresses');
    } catch (e) {
      throw Exception('فشل في تحديث العنوان: $e');
    }
  }

  /// جلب جميع الفئات النشطة.
  Future<List<CategoryModel>> getCategories() async {
    return await _fetchWithCache('categories', () async {
      final query = _client
          .from(AppConstants.categoriesTable)
          .select('*')
          .eq('is_active', true);

      final response = await query.order('name');
      return response.map((json) => CategoryModel.fromJson(json)).toList();
    });
  }

  /// جلب المنتجات بناءً على معرف الفئة.
  Future<List<ProductModel>> getProductsByCategory(String categoryId) async {
    return await _fetchWithCache('products_category_$categoryId', () async {
      final query = _client
          .from(AppConstants.productsTable)
          .select('*')
          .eq('category_id', categoryId)
          .eq('is_available', true);

      final response = await query.order('name');
      return response.map((json) => ProductModel.fromJson(json)).toList();
    });
  }

  /// جلب البيانات مع التخزين المؤقت.
  Future<T> _fetchWithCache<T>(
    String cacheKey,
    Future<T> Function() fetchData,
  ) async {
    final cachedData = _cache[cacheKey];
    if (cachedData != null &&
        cachedData['timestamp'] != null &&
        DateTime.now().difference(cachedData['timestamp']) < _cacheDuration) {
      return cachedData['data'] as T;
    }

    final data = await _handleRequest(
      fetchData,
      customError: 'فشل جلب البيانات',
    );
    _cache[cacheKey] = {'data': data, 'timestamp': DateTime.now()};
    return data;
  }

  /// جلب المنتجات الأكثر مبيعًا.
  Future<List<Map<String, dynamic>>> getBestSellingProducts() async {
    const cacheKey = 'best_selling_products';
    if (_cache.containsKey(cacheKey) &&
        (_cache[cacheKey]['timestamp'] as DateTime).isAfter(
          DateTime.now().subtract(_cacheDuration),
        )) {
      return _cache[cacheKey]['data'] as List<Map<String, dynamic>>;
    }

    try {
      final query = _client
          .from(AppConstants.productsTable)
          .select()
          .eq('is_best_selling', true);

      final response = await query
          .order('rating', ascending: false)
          .timeout(const Duration(seconds: 15));
      final data = List<Map<String, dynamic>>.from(response);
      _cache[cacheKey] = {'data': data, 'timestamp': DateTime.now()};
      return data;
    } catch (e) {
      debugPrint('خطأ في جلب المنتجات الأكثر مبيعاً: $e');
      throw Exception('فشل جلب المنتجات الأكثر مبيعاً: $e');
    }
  }

  /// جلب العروض.
  Future<List<Map<String, dynamic>>> getOffers() async {
    const cacheKey = 'offers';
    if (_cache.containsKey(cacheKey) &&
        (_cache[cacheKey]['timestamp'] as DateTime).isAfter(
          DateTime.now().subtract(_cacheDuration),
        )) {
      return _cache[cacheKey]['data'] as List<Map<String, dynamic>>;
    }

    try {
      final query = _client
          .from(AppConstants.productsTable)
          .select()
          .not('discount_price', 'is', null);

      final response = await query
          .order('discount_price')
          .timeout(const Duration(seconds: 15));
      final data = List<Map<String, dynamic>>.from(response);
      _cache[cacheKey] = {'data': data, 'timestamp': DateTime.now()};
      return data;
    } catch (e) {
      debugPrint('خطأ في جلب العروض: $e');
      throw Exception('فشل جلب العروض: $e');
    }
  }

  /// جلب المنتجات المقترحة.
  Future<List<Map<String, dynamic>>> getSuggestedProducts() async {
    const cacheKey = 'suggested_products';
    if (_cache.containsKey(cacheKey) &&
        (_cache[cacheKey]['timestamp'] as DateTime).isAfter(
          DateTime.now().subtract(_cacheDuration),
        )) {
      return _cache[cacheKey]['data'] as List<Map<String, dynamic>>;
    }

    try {
      final query = _client
          .from(AppConstants.productsTable)
          .select()
          .eq('is_available', true);

      final response = await query
          .order('rating', ascending: false)
          .limit(10)
          .timeout(const Duration(seconds: 15));
      final data = List<Map<String, dynamic>>.from(response);
      _cache[cacheKey] = {'data': data, 'timestamp': DateTime.now()};
      return data;
    } catch (e) {
      debugPrint('خطأ في جلب المنتجات المقترحة: $e');
      throw Exception('فشل جلب المنتجات المقترحة: $e');
    }
  }

  /// حذف عنوان بناءً على معرفه.
  Future<void> deleteAddress(String addressId) async {
    if (addressId.isEmpty) {
      throw Exception('معرف العنوان مطلوب');
    }

    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('المستخدم غير مصادق عليه');

      await _client
          .from(AppConstants.addressesTable)
          .delete()
          .eq('id', addressId)
          .eq('user_id', userId)
          .timeout(const Duration(seconds: 10));
      _cache.remove('user_addresses');
    } catch (e) {
      debugPrint('خطأ في حذف العنوان: $e');
      throw Exception('فشل حذف العنوان: $e');
    }
  }

  /// تعيين عنوان كافتراضي.
  Future<void> setDefaultAddress(String id) async {
    if (id.isEmpty) {
      throw Exception('معرف العنوان مطلوب');
    }

    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('المستخدم غير مصادق عليه');

      await _client
          .from(AppConstants.addressesTable)
          .update({'is_default': false})
          .eq('user_id', userId)
          .timeout(const Duration(seconds: 10));

      await _client
          .from(AppConstants.addressesTable)
          .update({'is_default': true})
          .eq('id', id)
          .eq('user_id', userId)
          .timeout(const Duration(seconds: 10));
      _cache.remove('user_addresses');
    } catch (e) {
      debugPrint('خطأ في تعيين العنوان الافتراضي: $e');
      throw Exception('فشل تعيين الع عنوان الافتراضي: $e');
    }
  }

  /// البحث عن المنتجات بناءً على نص البحث.
  Future<List<Map<String, dynamic>>> searchProducts(String query) async {
    if (query.isEmpty) {
      throw Exception('نص البحث مطلوب');
    }

    try {
      final response = await _client
          .from(AppConstants.productsTable)
          .select()
          .ilike('name', '%$query%')
          .order('name')
          .timeout(const Duration(seconds: 15));
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      debugPrint('خطأ في البحث عن المنتجات: $e');
      throw Exception('فشل البحث عن المنتجات: $e');
    }
  }

  /// التحقق من وجود بريد إلكتروني في قاعدة البيانات.
  Future<bool> checkEmailExists(String email) async {
    try {
      debugPrint('التحقق من وجود البريد الإلكتروني: $email');
      final response =
          await _client
              .from(AppConstants.profilesTable)
              .select('*')
              .eq('email', email)
              .maybeSingle();

      final exists = response != null;
      debugPrint(
        'نتيجة التحقق من البريد $email: ${exists ? "موجود" : "غير موجود"}',
      );
      return exists;
    } catch (e) {
      _error = 'فشل في التحقق من وجود البريد الإلكتروني: $e';
      debugPrint(_error);
      return false;
    }
  }

  /// تحديث وقت آخر تسجيل دخول للمستخدم.
  Future<bool> _updateLastLogin(String userId) async {
    try {
      debugPrint('تحديث وقت آخر تسجيل دخول للمستخدم: $userId');
      final now = DateTime.now().toIso8601String();

      await _client
          .from(AppConstants.profilesTable)
          .update({'last_login_at': now})
          .eq('id', userId);

      final updatedUser = await getUserById(userId);
      if (updatedUser?.lastLoginAt != null) {
        debugPrint(
          'تم تحديث وقت آخر تسجيل دخول بنجاح: ${updatedUser?.lastLoginAt}',
        );
        return true;
      }
      debugPrint('تم إرسال طلب التحديث ولكن لم يتم التأكد من نجاحه');
      return false;
    } catch (e) {
      debugPrint('فشل في تحديث وقت آخر تسجيل دخول: $e');
      return false;
    }
  }

  /// تجديد جلسة المستخدم باستخدام رمز الجلسة.
  Future<void> refreshSession(String token) async {
    if (!_isInitialized) throw Exception('الخدمة لم تُهيأ بعد');
    if (token.isEmpty) throw Exception('رمز الجلسة مطلوب');

    _isLoading = true;
    _error = null;

    try {
      await _handleRequest(() async {
        final response = await _client.auth.setSession(token);
        if (response.session == null) throw Exception('فشل تهيئة الجلسة');
        await _fetchUserData();
        _isAuthenticated = _currentUser != null;
      }, customError: 'فشل تهيئة جلسة المستخدم');
    } catch (e) {
      _isAuthenticated = false;
      throw Exception(_error ?? 'فشل تهيئة الجلسة: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// تنظيف الموارد عند التخلص من الكائن.
  @override
  void dispose() {
    _sessionRefreshTimer?.cancel();
    _connectionCheckTimer?.cancel();
    _client.auth.onAuthStateChange.drain();
    _cache.clear();
    super.dispose();
  }

  /// معالجة أخطاء Postgrest.
  String _handlePostgrestError(PostgrestException e) {
    switch (e.code) {
      case '42501':
        return 'لا يمكن تنفيذ العملية. تواصل مع الدعم الفني.';
      case '23505':
        return 'البريد الإلكتروني مستخدم بالفعل.';
      case '23503':
        return 'خطأ في قاعدة البيانات. تواصل مع الدعم.';
      default:
        return 'خطأ في قاعدة البيانات: ${e.message}';
    }
  }

  /// معالجة أخطاء المصادقة.
  String _handleAuthError(AuthException e) {
    debugPrint('رمز الخطأ: ${e.statusCode}, رسالة الخطأ: ${e.message}');
    switch (e.statusCode) {
      case '400':
        if (e.message.toLowerCase().contains('invalid login credentials')) {
          return 'بيانات تسجيل الدخول غير صحيحة. تأكد من البريد الإلكتروني وكلمة المرور';
        } else if (e.message.toLowerCase().contains('email not confirmed')) {
          return 'يرجى تأكيد البريد الإلكتروني أولاً';
        }
        return 'بيانات غير صالحة. الرجاء التحقق من المعلومات المدخلة';
      case '401':
        if (e.message.toLowerCase().contains('invalid token')) {
          return 'انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى';
        }
        return 'غير مصرح لك بالدخول. تأكد من بيانات تسجيل الدخول';
      case '403':
        return 'ممنوع الوصول. ليس لديك صلاحية للوصول';
      case '404':
        return 'المستخدم غير موجود. الرجاء التأكد من البريد الإلكتروني';
      case '409':
        return 'البريد الإلكتروني مستخدم بالفعل';
      case '422':
        if (e.message.toLowerCase().contains('password')) {
          return 'كلمة المرور غير صالحة. يجب أن تكون 6 أحرف على الأقل';
        } else if (e.message.toLowerCase().contains('email')) {
          return 'البريد الإلكتروني غير صالح. الرجاء التحقق من صيغة البريد';
        }
        return 'بيانات غير صالحة. الرجاء التحقق من المعلومات المدخلة';
      case '429':
        return 'تم تجاوز عدد المحاولات المسموح بها. الرجاء المحاولة لاحقاً';
      case '500':
        return 'خطأ في الخادم. الرجاء المحاولة لاحقاً';
      default:
        if (e.message.toLowerCase().contains('network')) {
          return 'خطأ في الاتصال بالشبكة. يرجى التحقق من اتصالك بالإنترنت';
        } else if (e.message.toLowerCase().contains('timeout')) {
          return 'انتهت مهلة الاتصال. يرجى المحاولة مرة أخرى';
        }
        return 'حدث خطأ غير متوقع: ${e.message}';
    }
  }

  /// التحقق مما إذا كان المستخدم مسؤولاً.
  Future<bool> isAdmin() async {
    try {
      if (!_isInitialized || !_isAuthenticated || _currentUser == null) {
        return false;
      }
      return _currentUser!.profileType == 'admin';
    } catch (e) {
      debugPrint('خطأ في التحقق من صلاحيات المسؤول: $e');
      return false;
    }
  }

  /// تغيير كلمة المرور للمستخدم الحالي
  Future<bool> changePassword(
    String currentPassword,
    String newPassword,
  ) async {
    if (!_isInitialized || !_isAuthenticated) {
      _error = 'المستخدم غير مسجل الدخول';
      return false;
    }

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // التحقق من صحة كلمة المرور الحالية
      final email = _currentUser?.email;
      if (email == null) {
        _error = 'لم يتم العثور على بريد إلكتروني للمستخدم';
        return false;
      }

      // حفظ الجلسة الحالية
      final currentSession = _client.auth.currentSession;

      // التحقق من كلمة المرور الحالية عن طريق محاولة تسجيل الدخول في عميل منفصل
      try {
        final tempClient = SupabaseClient(
          AppConstants.baseUrl, // استخدام baseUrl من AppConstants
          AppConstants.apiKey,
        );

        await tempClient.auth.signInWithPassword(
          email: email,
          password: currentPassword,
        );

        // تسجيل الخروج من العميل المؤقت
        await tempClient.auth.signOut();
      } catch (e) {
        _error = 'كلمة المرور الحالية غير صحيحة';
        return false;
      }

      // استعادة الجلسة الحالية إذا لزم الأمر
      if (currentSession != null && _client.auth.currentSession == null) {
        await _client.auth.setSession(currentSession.accessToken);
      }

      // التحقق من صحة كلمة المرور الجديدة
      if (!_isValidPassword(newPassword)) {
        _error = 'كلمة المرور الجديدة يجب أن تكون 6 أحرف على الأقل';
        return false;
      }

      // تغيير كلمة المرور
      await _client.auth.updateUser(UserAttributes(password: newPassword));

      debugPrint('تم تغيير كلمة المرور بنجاح');
      return true;
    } catch (e) {
      _error = 'فشل تغيير كلمة المرور: $e';
      debugPrint(_error);
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// حذف حساب المستخدم الحالي
  Future<bool> deleteAccount(String password) async {
    if (!_isInitialized || !_isAuthenticated || _currentUser == null) {
      _error = 'المستخدم غير مسجل الدخول';
      return false;
    }

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final userId = _currentUser!.id;
      final email = _currentUser!.email;

      // التحقق من كلمة المرور عن طريق محاولة تسجيل الدخول
      try {
        await _client.auth.signInWithPassword(email: email, password: password);
      } catch (e) {
        _error = 'كلمة المرور غير صحيحة';
        return false;
      }

      // حذف بيانات المستخدم من جداول قاعدة البيانات
      // 1. حذف العناوين
      await _client
          .from(AppConstants.addressesTable)
          .delete()
          .eq('user_id', userId);

      // 2. حذف الملف الشخصي
      await _client.from(AppConstants.profilesTable).delete().eq('id', userId);

      // 3. حذف حساب المصادقة
      await _client.auth.admin.deleteUser(userId);

      // تسجيل الخروج وتنظيف البيانات المحلية
      await signOut();

      debugPrint('تم حذف الحساب بنجاح');
      return true;
    } catch (e) {
      _error = 'فشل حذف الحساب: $e';
      debugPrint(_error);
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// تحديث عدد الطلبات للمستخدم
  Future<bool> updateOrderCount(String userId, {bool increment = true}) async {
    try {
      if (_currentUser == null) {
        throw Exception('المستخدم غير موجود');
      }

      final currentCount = _currentUser!.orderCount ?? 0;
      final newCount = increment ? currentCount + 1 : currentCount - 1;

      final response =
          await _client
              .from(AppConstants.profilesTable)
              .update({
                'order_count': newCount,
                'updated_at': DateTime.now().toIso8601String(),
              })
              .eq('id', userId)
              .select()
              .single();

      if (response != null) {
        _currentUser = UserModel.fromJson(response);
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('خطأ في تحديث عدد الطلبات: $e');
      return false;
    }
  }

  /// تحديث عدد المنتجات في قائمة الرغبات
  Future<bool> updateWishlistCount(
    String userId, {
    bool increment = true,
  }) async {
    try {
      if (_currentUser == null) {
        throw Exception('المستخدم غير موجود');
      }

      final currentCount = _currentUser!.wishlistCount ?? 0;
      final newCount = increment ? currentCount + 1 : currentCount - 1;

      final response =
          await _client
              .from(AppConstants.profilesTable)
              .update({
                'wishlist_count': newCount,
                'updated_at': DateTime.now().toIso8601String(),
              })
              .eq('id', userId)
              .select()
              .single();

      if (response != null) {
        _currentUser = UserModel.fromJson(response);
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('خطأ في تحديث عدد المنتجات في قائمة الرغبات: $e');
      return false;
    }
  }

  /// تحديث متوسط التقييم
  Future<bool> updateAverageRating(String userId, double newRating) async {
    try {
      final response =
          await _client
              .from(AppConstants.profilesTable)
              .update({
                'average_rating': newRating,
                'updated_at': DateTime.now().toIso8601String(),
              })
              .eq('id', userId)
              .select()
              .single();

      if (response != null) {
        _currentUser = UserModel.fromJson(response);
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('خطأ في تحديث متوسط التقييم: $e');
      return false;
    }
  }

  /// تحديث إجمالي المبلغ المنفق
  Future<bool> updateTotalSpent(String userId, double amount) async {
    try {
      if (_currentUser == null) {
        throw Exception('المستخدم غير موجود');
      }

      final currentTotal = _currentUser!.totalSpent ?? 0.0;
      final newTotal = currentTotal + amount;

      final response =
          await _client
              .from(AppConstants.profilesTable)
              .update({
                'total_spent': newTotal,
                'updated_at': DateTime.now().toIso8601String(),
              })
              .eq('id', userId)
              .select()
              .single();

      if (response != null) {
        _currentUser = UserModel.fromJson(response);
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('خطأ في تحديث إجمالي المبلغ المنفق: $e');
      return false;
    }
  }

  /// تحديث جميع إحصائيات المستخدم
  Future<bool> updateUserStats({
    required String userId,
    int? orderCount,
    int? wishlistCount,
    double? averageRating,
    double? totalSpent,
  }) async {
    try {
      final Map<String, dynamic> updateData = {
        'updated_at': DateTime.now().toIso8601String(),
      };

      if (orderCount != null) updateData['order_count'] = orderCount;
      if (wishlistCount != null) updateData['wishlist_count'] = wishlistCount;
      if (averageRating != null) updateData['average_rating'] = averageRating;
      if (totalSpent != null) updateData['total_spent'] = totalSpent;

      final response =
          await _client
              .from(AppConstants.profilesTable)
              .update(updateData)
              .eq('id', userId)
              .select()
              .single();

      if (response != null) {
        _currentUser = UserModel.fromJson(response);
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('خطأ في تحديث إحصائيات المستخدم: $e');
      return false;
    }
  }

  /// إعادة إرسال رمز التحقق
  Future<bool> resendOTP(String email) async {
    if (!_isInitialized) {
      _error = 'الخدمة لم تُهيأ بعد';
      return false;
    }

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      debugPrint('إعادة إرسال رمز التحقق إلى: $email');

      final response = await _handleRequest(
        () => _client.auth.resend(type: OtpType.email, email: email),
        customError: 'فشل في إعادة إرسال رمز التحقق',
      );

      if (response != null) {
        debugPrint('تم إعادة إرسال رمز التحقق بنجاح');
        return true;
      } else {
        _error = 'فشل في إعادة إرسال رمز التحقق';
        return false;
      }
    } catch (e) {
      _error = _getAuthErrorMessage(e);
      debugPrint('خطأ في إعادة إرسال رمز التحقق: $_error');
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // ========================================
  // امتداد إعدادات الأمان (مدمج من auth_supabase_service_extension.dart)
  // ========================================

  /// تحديث إعدادات الأمان للمستخدم
  Future<void> updateSecuritySettings(String setting, bool value) async {
    if (!_isInitialized) throw Exception('الخدمة لم تُهيأ بعد');
    if (!_isAuthenticated) throw Exception('المستخدم غير مسجل الدخول');

    final userId = _client.auth.currentUser?.id;
    if (userId == null) throw Exception('المستخدم غير مصادق عليه');

    try {
      await _client.from('user_security_settings').upsert({
        'user_id': userId,
        'setting_name': setting,
        'setting_value': value,
        'updated_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      throw Exception('فشل في تحديث إعدادات الأمان: $e');
    }
  }

  /// حذف جميع بيانات المستخدم نهائياً
  Future<void> deleteAllUserData() async {
    if (!_isInitialized) throw Exception('الخدمة لم تُهيأ بعد');
    if (!_isAuthenticated) throw Exception('المستخدم غير مسجل الدخول');

    final userId = _client.auth.currentUser?.id;
    if (userId == null) throw Exception('المستخدم غير مصادق عليه');

    try {
      // حذف البيانات من جميع الجداول المرتبطة
      await Future.wait([
        _client.from('profiles').delete().eq('id', userId),
        _client.from('orders').delete().eq('user_id', userId),
        _client.from('cart_items').delete().eq('user_id', userId),
        _client.from('favorites').delete().eq('user_id', userId),
        _client.from('user_interactions').delete().eq('user_id', userId),
        _client.from('user_security_settings').delete().eq('user_id', userId),
      ]);

      // حذف الحساب من المصادقة
      await _client.auth.admin.deleteUser(userId);

      // تنظيف البيانات المحلية
      _currentUser = null;
      _isAuthenticated = false;
      _error = null;
      _cache.clear();
      await _storageService.clearAll();
      notifyListeners();
    } catch (e) {
      throw Exception('فشل في حذف بيانات المستخدم: $e');
    }
  }
}
