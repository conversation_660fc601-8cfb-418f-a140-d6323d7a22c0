
class AddressModel {
  final String id;
  final String userId;
  final String title;
  final String fullName;
  final String streetAddress;
  final String city;
  final String state;
  final String phoneNumber;
  final bool isDefault;
  final DateTime createdAt;
  final DateTime updatedAt;

  AddressModel({
    required this.id,
    required this.userId,
    required this.title,
    required this.fullName,
    required this.streetAddress,
    required this.city,
    required this.state,
    required this.phoneNumber,
    this.isDefault = false,
    required this.createdAt,
    required this.updatedAt,
  });

  factory AddressModel.fromJson(Map<String, dynamic> json) {
    return AddressModel(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      title: json['title'] as String,
      fullName: json['full_name'] as String,
      streetAddress: json['street_address'] as String,
      city: json['city'] as String,
      state: json['state'] as String,
      phoneNumber: json['phone_number'] as String,
      isDefault: json['is_default'] as bool? ?? false,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'title': title,
      'full_name': fullName,
      'street_address': streetAddress,
      'city': city,
      'state': state,
      'phone_number': phoneNumber,
      'is_default': isDefault,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  AddressModel copyWith({
    String? id,
    String? userId,
    String? title,
    String? fullName,
    String? streetAddress,
    String? city,
    String? state,
    String? phoneNumber,
    bool? isDefault,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return AddressModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      title: title ?? this.title,
      fullName: fullName ?? this.fullName,
      streetAddress: streetAddress ?? this.streetAddress,
      city: city ?? this.city,
      state: state ?? this.state,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      isDefault: isDefault ?? this.isDefault,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Getter for label (alias for title)
  String get label => title;

  // Getter for fullAddress
  String get fullAddress => '$streetAddress, $city, $state';
}
