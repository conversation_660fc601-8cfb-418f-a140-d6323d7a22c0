import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:crypto/crypto.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:image/image.dart' as img;
import 'package:motorcycle_parts_shop/core/config/clarifai_config.dart';
import 'package:motorcycle_parts_shop/core/exceptions/clarifai_exception.dart';
import 'package:motorcycle_parts_shop/models/clarifai_request.dart';
import 'package:motorcycle_parts_shop/models/clarifai_result.dart';

/// 🧠 خدمة Clarifai الاحترافية والمتقدمة
///
/// خدمة شاملة ومتطورة للذكاء الاصطناعي وتحليل الصور
/// تدعم جميع نماذج Clarifai مع ميزات متقدمة
///
/// 🎯 الميزات الرئيسية:
/// - 🔍 تحليل ذكي للصور والمحتوى
/// - 🎯 كشف الكائنات والمفاهيم
/// - 📝 استخراج النص (OCR)
/// - 🎨 تحليل الألوان والتركيب
/// - 🛡️ فلترة المحتوى غير المناسب
/// - 📊 تحليلات مفصلة وإحصائيات
/// - 💾 تخزين مؤقت ذكي
/// - 🔄 معالجة متوازية ومتقدمة
class ClarifaiService extends ChangeNotifier {
  static final ClarifaiService _instance = ClarifaiService._internal();
  factory ClarifaiService() => _instance;
  ClarifaiService._internal();

  // 🔧 إعدادات الخدمة
  bool _isInitialized = false;
  late http.Client _httpClient;

  // 💾 تخزين مؤقت متقدم
  final Map<String, ClarifaiResult> _resultCache = {};
  final Map<String, DateTime> _cacheTimestamps = {};

  // 📊 إحصائيات الأداء
  int _totalRequests = 0;
  int _successfulRequests = 0;
  int _failedRequests = 0;
  int _cacheHits = 0;
  int _cacheMisses = 0;
  double _averageResponseTime = 0.0;

  // 🔄 إدارة الطلبات
  final Map<String, ClarifaiRequest> _activeRequests = {};
  final List<ClarifaiRequest> _requestQueue = [];

  // 📊 Getters للإحصائيات
  bool get isInitialized => _isInitialized;
  int get totalRequests => _totalRequests;
  int get successfulRequests => _successfulRequests;
  int get failedRequests => _failedRequests;
  double get successRate =>
      _totalRequests > 0 ? (_successfulRequests / _totalRequests) * 100 : 0.0;
  int get cacheHits => _cacheHits;
  int get cacheMisses => _cacheMisses;
  double get cacheHitRate =>
      (_cacheHits + _cacheMisses) > 0
          ? (_cacheHits / (_cacheHits + _cacheMisses)) * 100
          : 0.0;
  double get averageResponseTime => _averageResponseTime;
  int get activeRequestsCount => _activeRequests.length;
  int get queuedRequestsCount => _requestQueue.length;

  /// 🚀 تهيئة خدمة Clarifai
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // التحقق من صحة التكوين (مع معالجة أفضل للأخطاء)
      try {
        ClarifaiConfig.validateConfig();
      } catch (e) {
        debugPrint('⚠️ تكوين Clarifai غير مكتمل: $e');
        debugPrint('الخدمة ستعمل في وضع محدود');
        _isInitialized = true;
        return;
      }

      // إنشاء HTTP client مع إعدادات محسنة
      _httpClient = http.Client();

      // اختبار الاتصال (اختياري)
      try {
        await _testConnection();
      } catch (e) {
        debugPrint('⚠️ فشل اختبار الاتصال مع Clarifai: $e');
        // لا نرمي استثناء هنا للسماح للتطبيق بالعمل
      }

      _isInitialized = true;
      notifyListeners();
      debugPrint('✅ تم تهيئة خدمة Clarifai بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة خدمة Clarifai: $e');
      rethrow;
    }
  }

  /// 🔍 تحليل صورة شامل
  Future<ClarifaiResult> analyzeImage({
    required File imageFile,
    required String modelKey,
    double? minConfidence,
    int? maxConcepts,
    Map<String, dynamic>? options,
  }) async {
    if (!_isInitialized) await initialize();

    final startTime = DateTime.now();
    final requestId = _generateRequestId();

    try {
      // التحقق من الذاكرة المؤقتة
      final cacheKey = await _generateCacheKey(imageFile, modelKey, options);
      final cachedResult = _getCachedResult(cacheKey);
      if (cachedResult != null) {
        _cacheHits++;
        notifyListeners();
        return cachedResult;
      }
      _cacheMisses++;

      // التحقق من صحة النموذج
      final modelConfig = ClarifaiConfig.getModelConfig(modelKey);
      if (modelConfig == null) {
        throw ClarifaiException('نموذج غير مدعوم: $modelKey');
      }

      // التحقق من صحة الملف
      await _validateImageFile(imageFile, modelConfig);

      // إنشاء طلب
      final request = ClarifaiRequest(
        id: requestId,
        type: ClarifaiRequestType.imageAnalysis,
        modelKey: modelKey,
        imageFile: imageFile,
        options: options ?? {},
        startTime: startTime,
      );

      _activeRequests[requestId] = request;
      _totalRequests++;

      // معالجة الصورة
      final processedImageData = await _processImage(imageFile, modelConfig);

      // إرسال الطلب لـ Clarifai
      final response = await _sendAnalysisRequest(
        modelConfig: modelConfig,
        imageData: processedImageData,
        minConfidence:
            minConfidence ?? ClarifaiConfig.analysisConfig.minConfidence,
        maxConcepts: maxConcepts ?? ClarifaiConfig.analysisConfig.maxConcepts,
        options: options,
      );

      // معالجة النتيجة
      final result = _processAnalysisResponse(response, modelConfig, request);

      // حفظ في الذاكرة المؤقتة
      _cacheResult(cacheKey, result);

      _successfulRequests++;
      _updateAverageResponseTime(startTime);
      _activeRequests.remove(requestId);

      notifyListeners();
      return result;
    } catch (e) {
      _failedRequests++;
      _activeRequests.remove(requestId);

      throw ClarifaiException('فشل في تحليل الصورة: ${e.toString()}');
    }
  }

  /// 🔍 تحليل متعدد الصور
  Future<List<ClarifaiResult>> analyzeMultipleImages({
    required List<File> imageFiles,
    required String modelKey,
    double? minConfidence,
    int? maxConcepts,
    Function(int current, int total)? onProgress,
  }) async {
    if (!_isInitialized) await initialize();

    final results = <ClarifaiResult>[];
    int completed = 0;

    // معالجة متوازية مع حد أقصى
    for (
      int i = 0;
      i < imageFiles.length;
      i += ClarifaiConfig.maxConcurrentRequests
    ) {
      final batch =
          imageFiles
              .skip(i)
              .take(ClarifaiConfig.maxConcurrentRequests)
              .toList();

      final batchFutures = batch.map((imageFile) async {
        try {
          final result = await analyzeImage(
            imageFile: imageFile,
            modelKey: modelKey,
            minConfidence: minConfidence,
            maxConcepts: maxConcepts,
          );

          completed++;
          onProgress?.call(completed, imageFiles.length);

          return result;
        } catch (e) {
          completed++;
          onProgress?.call(completed, imageFiles.length);

          return ClarifaiResult(
            success: false,
            error: e.toString(),
            processingTime: 0,
            modelUsed: modelKey,
          );
        }
      });

      final batchResults = await Future.wait(batchFutures);
      results.addAll(batchResults);

      // تأخير قصير بين المجموعات
      if (i + ClarifaiConfig.maxConcurrentRequests < imageFiles.length) {
        await Future.delayed(ClarifaiConfig.batchDelay);
      }
    }

    return results;
  }

  /// 🎯 كشف الكائنات في الصورة
  Future<ObjectDetectionResult> detectObjects({
    required File imageFile,
    double minConfidence = 0.7,
  }) async {
    final result = await analyzeImage(
      imageFile: imageFile,
      modelKey: 'object_detection',
      minConfidence: minConfidence,
    );

    return ObjectDetectionResult.fromClarifaiResult(result);
  }

  /// 📝 استخراج النص من الصورة (OCR)
  Future<TextExtractionResult> extractText({
    required File imageFile,
    String language = 'ar',
  }) async {
    final result = await analyzeImage(
      imageFile: imageFile,
      modelKey: 'text_detection',
      options: {'language': language},
    );

    return TextExtractionResult.fromClarifaiResult(result);
  }

  /// 🎨 تحليل الألوان في الصورة
  Future<ColorAnalysisResult> analyzeColors({
    required File imageFile,
    int maxColors = 10,
  }) async {
    final result = await analyzeImage(
      imageFile: imageFile,
      modelKey: 'color_analysis',
      maxConcepts: maxColors,
    );

    return ColorAnalysisResult.fromClarifaiResult(result);
  }

  /// 🛡️ فحص المحتوى غير المناسب
  Future<ModerationResult> moderateContent({
    required File imageFile,
    double threshold = 0.8,
  }) async {
    final result = await analyzeImage(
      imageFile: imageFile,
      modelKey: 'moderation',
      minConfidence: threshold,
    );

    return ModerationResult.fromClarifaiResult(result);
  }

  /// 🔧 دوال مساعدة خاصة

  /// اختبار الاتصال (محسن ومرن)
  Future<void> _testConnection() async {
    try {
      // اختبار بسيط للاتصال مع Clarifai
      final response = await _httpClient
          .get(
            Uri.parse('${ClarifaiConfig.baseUrl}/v2/models'),
            headers: _getHeaders(),
          )
          .timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        debugPrint('✅ اختبار الاتصال مع Clarifai نجح');
      } else if (response.statusCode == 401) {
        throw Exception('مفتاح API غير صالح');
      } else {
        throw Exception('خطأ في الاتصال: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('⚠️ فشل اختبار الاتصال مع Clarifai: $e');
      throw Exception('فشل في اختبار الاتصال: $e');
    }
  }

  /// التحقق من صحة ملف الصورة
  Future<void> _validateImageFile(
    File imageFile,
    ModelConfig modelConfig,
  ) async {
    if (!await imageFile.exists()) {
      throw ClarifaiException('ملف الصورة غير موجود');
    }

    final fileSizeBytes = await imageFile.length();
    final fileSizeMB = fileSizeBytes / (1024 * 1024);

    if (fileSizeMB > modelConfig.maxImageSize) {
      throw ClarifaiException(
        'حجم الملف كبير جداً (${fileSizeMB.toStringAsFixed(2)} MB). الحد الأقصى: ${modelConfig.maxImageSize} MB',
      );
    }

    final extension = imageFile.path.split('.').last.toLowerCase();
    if (!modelConfig.supportedFormats.contains(extension)) {
      throw ClarifaiException(
        'نوع الملف غير مدعوم. الأنواع المدعومة: ${modelConfig.supportedFormats.join(', ')}',
      );
    }
  }

  /// معالجة الصورة قبل الإرسال
  Future<Uint8List> _processImage(
    File imageFile,
    ModelConfig modelConfig,
  ) async {
    try {
      final imageBytes = await imageFile.readAsBytes();

      if (!ClarifaiConfig.imageProcessing.autoResize) {
        return imageBytes;
      }

      final image = img.decodeImage(imageBytes);
      if (image == null) {
        throw Exception('فشل في قراءة الصورة');
      }

      // تغيير الحجم إذا لزم الأمر
      img.Image processedImage = image;
      if (image.width > ClarifaiConfig.imageProcessing.maxWidth ||
          image.height > ClarifaiConfig.imageProcessing.maxHeight) {
        processedImage = img.copyResize(
          image,
          width: ClarifaiConfig.imageProcessing.maxWidth,
          height: ClarifaiConfig.imageProcessing.maxHeight,
          interpolation: img.Interpolation.linear,
        );
      }

      // إزالة بيانات EXIF إذا مطلوب
      if (ClarifaiConfig.imageProcessing.removeExif) {
        // تتم إزالة بيانات EXIF تلقائياً عند إعادة التشفير
      }

      // تشفير الصورة المعدلة
      final processedBytes = img.encodeJpg(
        processedImage,
        quality: ClarifaiConfig.imageProcessing.quality,
      );

      return Uint8List.fromList(processedBytes);
    } catch (e) {
      debugPrint('خطأ في معالجة الصورة: $e');
      return await imageFile.readAsBytes(); // إرجاع الصورة الأصلية
    }
  }

  /// إرسال طلب التحليل
  Future<Map<String, dynamic>> _sendAnalysisRequest({
    required ModelConfig modelConfig,
    required Uint8List imageData,
    required double minConfidence,
    required int maxConcepts,
    Map<String, dynamic>? options,
  }) async {
    final url = Uri.parse(
      '${ClarifaiConfig.baseUrl}/v2/models/${modelConfig.id}/outputs',
    );

    final requestBody = {
      'inputs': [
        {
          'data': {
            'image': {'base64': base64Encode(imageData)},
          },
        },
      ],
      'model': {
        'output_info': {
          'output_config': {
            'min_value': minConfidence,
            'max_concepts': maxConcepts,
            'select_concepts': options?['concepts'] ?? [],
          },
        },
      },
    };

    final response = await _httpClient
        .post(url, headers: _getHeaders(), body: json.encode(requestBody))
        .timeout(ClarifaiConfig.analysisTimeout);

    if (response.statusCode != 200) {
      throw ClarifaiException(
        'خطأ في API: ${response.statusCode} - ${response.body}',
      );
    }

    return json.decode(response.body);
  }

  /// معالجة استجابة التحليل
  ClarifaiResult _processAnalysisResponse(
    Map<String, dynamic> response,
    ModelConfig modelConfig,
    ClarifaiRequest request,
  ) {
    try {
      final outputs = response['outputs'] as List?;
      if (outputs == null || outputs.isEmpty) {
        throw Exception('لا توجد نتائج في الاستجابة');
      }

      final output = outputs.first as Map<String, dynamic>;
      final data = output['data'] as Map<String, dynamic>?;

      if (data == null) {
        throw Exception('بيانات غير صالحة في الاستجابة');
      }

      return ClarifaiResult(
        success: true,
        modelUsed: modelConfig.name,
        processingTime:
            DateTime.now().difference(request.startTime).inMilliseconds,
        concepts: _extractConcepts(data),
        regions: _extractRegions(data),
        colors: _extractColors(data),
        textResults: _extractText(data),
        rawResponse: response,
      );
    } catch (e) {
      throw ClarifaiException('فشل في معالجة الاستجابة: $e');
    }
  }

  /// استخراج المفاهيم
  List<ConceptResult> _extractConcepts(Map<String, dynamic> data) {
    final concepts = data['concepts'] as List?;
    if (concepts == null) return [];

    return concepts
        .map(
          (concept) => ConceptResult(
            id: concept['id'] ?? '',
            name: concept['name'] ?? '',
            confidence: (concept['value'] ?? 0.0).toDouble(),
          ),
        )
        .toList();
  }

  /// استخراج المناطق
  List<RegionResult> _extractRegions(Map<String, dynamic> data) {
    final regions = data['regions'] as List?;
    if (regions == null) return [];

    return regions.map((region) {
      final regionInfo = region['region_info'] as Map<String, dynamic>?;
      final boundingBox = regionInfo?['bounding_box'] as Map<String, dynamic>?;

      return RegionResult(
        boundingBox:
            boundingBox != null
                ? BoundingBox(
                  topRow: (boundingBox['top_row'] ?? 0.0).toDouble(),
                  leftCol: (boundingBox['left_col'] ?? 0.0).toDouble(),
                  bottomRow: (boundingBox['bottom_row'] ?? 0.0).toDouble(),
                  rightCol: (boundingBox['right_col'] ?? 0.0).toDouble(),
                )
                : null,
        concepts: _extractConcepts(region['data'] ?? {}),
      );
    }).toList();
  }

  /// استخراج الألوان
  List<ColorResult> _extractColors(Map<String, dynamic> data) {
    final colors = data['colors'] as List?;
    if (colors == null) return [];

    return colors
        .map(
          (color) => ColorResult(
            hex: color['hex'] ?? '',
            webSafeHex: color['web_safe_hex'] ?? '',
            webSafeColorName: color['web_safe_color_name'] ?? '',
            percentage: (color['value'] ?? 0.0).toDouble(),
          ),
        )
        .toList();
  }

  /// استخراج النص
  List<TextResult> _extractText(Map<String, dynamic> data) {
    final textAnnotations = data['text'] as List?;
    if (textAnnotations == null) return [];

    return textAnnotations
        .map(
          (text) => TextResult(
            text: text['text'] ?? '',
            confidence: (text['confidence'] ?? 0.0).toDouble(),
            boundingBox: null, // يمكن إضافة معلومات الموقع لاحقاً
          ),
        )
        .toList();
  }

  /// إنشاء headers للطلبات
  Map<String, String> _getHeaders() {
    return {
      'Authorization': 'Key ${ClarifaiConfig.apiKey}',
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };
  }

  /// إنشاء مفتاح الذاكرة المؤقتة
  Future<String> _generateCacheKey(
    File imageFile,
    String modelKey,
    Map<String, dynamic>? options,
  ) async {
    final imageBytes = await imageFile.readAsBytes();
    final imageHash = sha256.convert(imageBytes).toString();
    final optionsHash =
        sha256.convert(utf8.encode(json.encode(options ?? {}))).toString();

    return '${modelKey}_${imageHash}_$optionsHash';
  }

  /// الحصول من الذاكرة المؤقتة
  ClarifaiResult? _getCachedResult(String cacheKey) {
    final timestamp = _cacheTimestamps[cacheKey];
    if (timestamp == null) return null;

    final now = DateTime.now();
    if (now.difference(timestamp) > ClarifaiConfig.cacheExpiry) {
      _resultCache.remove(cacheKey);
      _cacheTimestamps.remove(cacheKey);
      return null;
    }

    return _resultCache[cacheKey];
  }

  /// حفظ في الذاكرة المؤقتة
  void _cacheResult(String cacheKey, ClarifaiResult result) {
    _resultCache[cacheKey] = result;
    _cacheTimestamps[cacheKey] = DateTime.now();

    // تنظيف الذاكرة المؤقتة إذا تجاوزت الحد الأقصى
    if (_resultCache.length > ClarifaiConfig.maxCacheSize) {
      _cleanupCache();
    }
  }

  /// تنظيف الذاكرة المؤقتة
  void _cleanupCache() {
    final now = DateTime.now();
    final keysToRemove = <String>[];

    _cacheTimestamps.forEach((key, timestamp) {
      if (now.difference(timestamp) > ClarifaiConfig.cacheExpiry) {
        keysToRemove.add(key);
      }
    });

    for (final key in keysToRemove) {
      _resultCache.remove(key);
      _cacheTimestamps.remove(key);
    }
  }

  /// إنشاء معرف طلب
  String _generateRequestId() {
    return 'req_${DateTime.now().millisecondsSinceEpoch}_$_totalRequests';
  }

  /// تحديث متوسط وقت الاستجابة
  void _updateAverageResponseTime(DateTime startTime) {
    final responseTime = DateTime.now().difference(startTime).inMilliseconds;
    _averageResponseTime =
        ((_averageResponseTime * (_successfulRequests - 1)) + responseTime) /
        _successfulRequests;
  }

  /// 📊 الحصول على إحصائيات مفصلة
  Map<String, dynamic> getDetailedStats() {
    return {
      'service_stats': {
        'is_initialized': _isInitialized,
        'total_requests': _totalRequests,
        'successful_requests': _successfulRequests,
        'failed_requests': _failedRequests,
        'success_rate': successRate,
        'average_response_time_ms': _averageResponseTime,
      },
      'cache_stats': {
        'cache_hits': _cacheHits,
        'cache_misses': _cacheMisses,
        'cache_hit_rate': cacheHitRate,
        'cache_size': _resultCache.length,
        'max_cache_size': ClarifaiConfig.maxCacheSize,
      },
      'request_stats': {
        'active_requests': activeRequestsCount,
        'queued_requests': queuedRequestsCount,
        'max_concurrent_requests': ClarifaiConfig.maxConcurrentRequests,
      },
    };
  }

  /// 🧹 تنظيف وصيانة
  void performMaintenance() {
    _cleanupCache();
    debugPrint('🧹 تم تنظيف خدمة Clarifai');
    notifyListeners();
  }

  /// 🔄 إعادة تعيين الإحصائيات
  void resetStatistics() {
    _totalRequests = 0;
    _successfulRequests = 0;
    _failedRequests = 0;
    _cacheHits = 0;
    _cacheMisses = 0;
    _averageResponseTime = 0.0;
    notifyListeners();
  }

  /// 🧹 مسح الذاكرة المؤقتة
  void clearCache() {
    _resultCache.clear();
    _cacheTimestamps.clear();
    notifyListeners();
  }

  @override
  void dispose() {
    _httpClient.close();
    super.dispose();
  }
}
