import 'package:flutter/material.dart';
import 'package:motorcycle_parts_shop/core/services/notification_service.dart';
import 'package:motorcycle_parts_shop/core/theme/app_theme.dart';
import 'package:provider/provider.dart';

class NotificationSettingsScreen extends StatefulWidget {
  const NotificationSettingsScreen({super.key});

  @override
  State<NotificationSettingsScreen> createState() =>
      _NotificationSettingsScreenState();
}

class _NotificationSettingsScreenState
    extends State<NotificationSettingsScreen> {
  late NotificationService _notificationService;
  bool _isLoading = true;
  late Map<String, bool> _settings;

  @override
  void initState() {
    super.initState();
    _initializeSettings();
  }

  Future<void> _initializeSettings() async {
    _notificationService = Provider.of<NotificationService>(
      context,
      listen: false,
    );

    if (!_notificationService.isInitialized) {
      await _notificationService.initialize();
    }

    // Create a copy of the settings to work with
    _settings = Map<String, bool>.from(
      _notificationService.notificationSettings,
    );

    if (mounted) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _saveSettings() async {
    setState(() {
      _isLoading = true;
    });

    await _notificationService.updateNotificationSettings(_settings);

    if (mounted) {
      setState(() {
        _isLoading = false;
      });

      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('تم حفظ الإعدادات بنجاح')));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(80),
        child: Container(
          decoration: BoxDecoration(
            gradient: AppTheme.primaryGradient,
            boxShadow: AppTheme.cardShadow,
          ),
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Row(
                children: [
                  // زر الرجوع المحسن
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: IconButton(
                      icon: const Icon(
                        Icons.arrow_back_ios_rounded,
                        color: AppTheme.textLightColor,
                      ),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ),

                  const SizedBox(width: 16),

                  // أيقونة الإشعارات والعنوان
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      Icons.notifications_rounded,
                      color: AppTheme.textLightColor,
                      size: 24,
                    ),
                  ),

                  const SizedBox(width: 12),

                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          'إعدادات الإشعارات',
                          style: AppTheme.cardTitle.copyWith(
                            color: AppTheme.textLightColor,
                            fontSize: 18,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                        Text(
                          'تخصيص تفضيلاتك',
                          style: AppTheme.cardSubtitle.copyWith(
                            color: AppTheme.textLightColor.withOpacity(0.8),
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // زر الحفظ المحسن
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.15),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: Colors.white.withOpacity(0.2),
                        width: 1,
                      ),
                    ),
                    child: IconButton(
                      icon: Icon(
                        Icons.save_rounded,
                        color: AppTheme.textLightColor,
                        size: 22,
                      ),
                      onPressed: _isLoading ? null : _saveSettings,
                      tooltip: 'حفظ الإعدادات',
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : SingleChildScrollView(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'أنواع الإشعارات',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    _buildNotificationTypeSwitch(
                      'تحديثات الطلبات',
                      'order_updates',
                      'إشعارات عن تغييرات حالة طلباتك',
                      Icons.local_shipping,
                    ),
                    _buildNotificationTypeSwitch(
                      'إعادة توفر المنتجات',
                      'product_restocks',
                      'إشعارات عندما تتوفر المنتجات التي نفدت من المخزون',
                      Icons.inventory,
                    ),
                    _buildNotificationTypeSwitch(
                      'انخفاض الأسعار',
                      'price_drops',
                      'إشعارات عن تخفيضات الأسعار للمنتجات التي تهتم بها',
                      Icons.trending_down,
                    ),
                    _buildNotificationTypeSwitch(
                      'العروض الترويجية',
                      'promotions',
                      'إشعارات عن العروض والخصومات الجديدة',
                      Icons.discount,
                    ),
                    _buildNotificationTypeSwitch(
                      'رسائل النظام',
                      'system_messages',
                      'إشعارات مهمة من النظام',
                      Icons.info,
                    ),
                    const Divider(height: 32),
                    const Text(
                      'طرق الإشعار',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    _buildNotificationTypeSwitch(
                      'إشعارات البريد الإلكتروني',
                      'email_notifications',
                      'استلام الإشعارات عبر البريد الإلكتروني',
                      Icons.email,
                    ),
                    _buildNotificationTypeSwitch(
                      'إشعارات التطبيق',
                      'push_notifications',
                      'استلام إشعارات مباشرة على جهازك',
                      Icons.notifications,
                    ),
                    _buildNotificationTypeSwitch(
                      'تفعيل الاهتزاز',
                      'vibration_enabled',
                      'تفعيل الاهتزاز مع الإشعارات',
                      Icons.vibration,
                    ),
                  ],
                ),
              ),
    );
  }

  Widget _buildNotificationTypeSwitch(
    String title,
    String settingKey,
    String description,
    IconData icon,
  ) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8.0),
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Row(
          children: [
            Icon(icon, color: Theme.of(context).primaryColor),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  Text(
                    description,
                    style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                  ),
                ],
              ),
            ),
            Switch(
              value: _settings[settingKey] ?? true,
              onChanged: (value) {
                if (mounted) {
                  setState(() {
                    _settings[settingKey] = value;
                  });
                }
              },
              activeColor: Theme.of(context).primaryColor,
            ),
          ],
        ),
      ),
    );
  }
}
