import 'dart:io';
import 'package:flutter/material.dart';
import 'package:motorcycle_parts_shop/core/services/auth_supabase_service.dart';
import 'package:motorcycle_parts_shop/core/services/clarifai_service.dart';
import 'package:motorcycle_parts_shop/models/clarifai_result.dart';

/// 🔍 محلل المنتجات الذكي والمتقدم
///
/// خدمة متخصصة لتحليل صور قطع غيار الدراجات النارية
/// باستخدام الذكاء الاصطناعي من Clarifai
///
/// 🎯 الميزات الرئيسية:
/// - 🔍 تعرف ذكي على قطع الغيار
/// - 🏷️ تصنيف تلقائي للمنتجات
/// - 📝 استخراج معلومات المنتج
/// - 🎨 تحليل الألوان والحالة
/// - 📊 تقييم جودة الصورة
/// - 🛡️ فحص المحتوى المناسب
/// - 📈 تحليلات وإحصائيات مفصلة
class SmartProductAnalyzer extends ChangeNotifier {
  static final SmartProductAnalyzer _instance =
      SmartProductAnalyzer._internal();
  factory SmartProductAnalyzer() => _instance;
  SmartProductAnalyzer._internal();

  final ClarifaiService _clarifaiService = ClarifaiService();
  final AuthSupabaseService _supabaseService = AuthSupabaseService();

  // 📊 إحصائيات التحليل
  int _totalAnalyses = 0;
  int _successfulAnalyses = 0;
  int _failedAnalyses = 0;
  final Map<String, int> _partTypeStats = {};
  final Map<String, int> _brandStats = {};
  final Map<String, double> _qualityScores = {};

  bool _isInitialized = false;

  // 📊 Getters للإحصائيات
  bool get isInitialized => _isInitialized;
  int get totalAnalyses => _totalAnalyses;
  int get successfulAnalyses => _successfulAnalyses;
  int get failedAnalyses => _failedAnalyses;
  double get successRate =>
      _totalAnalyses > 0 ? (_successfulAnalyses / _totalAnalyses) * 100 : 0.0;
  Map<String, int> get partTypeStats => Map.unmodifiable(_partTypeStats);
  Map<String, int> get brandStats => Map.unmodifiable(_brandStats);

  /// 🚀 تهيئة المحلل الذكي
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // تهيئة خدمة Clarifai
      if (!_clarifaiService.isInitialized) {
        await _clarifaiService.initialize();
      }

      // تهيئة خدمة Supabase
      if (!_supabaseService.isInitialized) {
        await _supabaseService.initialize();
      }

      _isInitialized = true;
      notifyListeners();
      debugPrint('✅ تم تهيئة محلل المنتجات الذكي بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة محلل المنتجات الذكي: $e');
      rethrow;
    }
  }

  /// 🔍 تحليل شامل لصورة المنتج
  Future<ProductAnalysisResult> analyzeProduct({
    required File imageFile,
    String? productId,
    bool enableAdvancedAnalysis = true,
    Function(String stage, double progress)? onProgress,
  }) async {
    if (!_isInitialized) await initialize();

    final startTime = DateTime.now();
    _totalAnalyses++;

    try {
      onProgress?.call('بدء التحليل...', 0.1);

      // 1. التحليل العام للصورة
      onProgress?.call('تحليل المحتوى العام...', 0.2);
      final generalAnalysis = await _clarifaiService.analyzeImage(
        imageFile: imageFile,
        modelKey: 'general',
        minConfidence: 0.6,
        maxConcepts: 20,
      );

      // 2. كشف الكائنات
      onProgress?.call('كشف الكائنات...', 0.4);
      final objectDetection = await _clarifaiService.detectObjects(
        imageFile: imageFile,
        minConfidence: 0.7,
      );

      // 3. تحليل الألوان
      onProgress?.call('تحليل الألوان...', 0.6);
      final colorAnalysis = await _clarifaiService.analyzeColors(
        imageFile: imageFile,
        maxColors: 5,
      );

      // 4. فحص المحتوى
      onProgress?.call('فحص المحتوى...', 0.7);
      final moderation = await _clarifaiService.moderateContent(
        imageFile: imageFile,
        threshold: 0.8,
      );

      // 5. تحليل متقدم (إذا مطلوب)
      ProductClassification? classification;
      QualityAssessment? quality;

      if (enableAdvancedAnalysis) {
        onProgress?.call('التحليل المتقدم...', 0.8);
        classification = await _classifyMotorcyclePart(
          generalAnalysis,
          objectDetection,
        );
        quality = await _assessImageQuality(imageFile, generalAnalysis);
      }

      // 6. استخراج النص (إذا وجد)
      onProgress?.call('استخراج النص...', 0.9);
      final textExtraction = await _extractProductText(imageFile);

      // 7. تجميع النتائج
      onProgress?.call('تجميع النتائج...', 1.0);
      final result = ProductAnalysisResult(
        success: true,
        productId: productId,
        generalAnalysis: generalAnalysis,
        objectDetection: objectDetection,
        colorAnalysis: colorAnalysis,
        moderation: moderation,
        classification: classification,
        quality: quality,
        textExtraction: textExtraction,
        processingTime: DateTime.now().difference(startTime).inMilliseconds,
        timestamp: DateTime.now(),
      );

      // 8. حفظ النتائج في قاعدة البيانات
      if (productId != null) {
        await _saveAnalysisResults(productId, result);
      }

      // 9. تحديث الإحصائيات
      _updateAnalysisStats(result);

      _successfulAnalyses++;
      notifyListeners();
      return result;
    } catch (e) {
      _failedAnalyses++;
      notifyListeners();

      return ProductAnalysisResult(
        success: false,
        error: e.toString(),
        processingTime: DateTime.now().difference(startTime).inMilliseconds,
        timestamp: DateTime.now(),
      );
    }
  }

  /// 🏷️ تصنيف قطعة غيار الدراجة النارية
  Future<ProductClassification> _classifyMotorcyclePart(
    ClarifaiResult generalAnalysis,
    ObjectDetectionResult objectDetection,
  ) async {
    // قاموس تصنيف قطع الغيار
    final partKeywords = {
      'engine': [
        'engine',
        'motor',
        'cylinder',
        'piston',
        'carburetor',
        'محرك',
        'أسطوانة',
      ],
      'brake': ['brake', 'disc', 'pad', 'caliper', 'فرامل', 'قرص', 'فحمات'],
      'tire': ['tire', 'wheel', 'rim', 'إطار', 'عجلة', 'جنط'],
      'exhaust': ['exhaust', 'muffler', 'pipe', 'عادم', 'شكمان', 'أنبوب'],
      'electrical': [
        'battery',
        'light',
        'wire',
        'spark',
        'بطارية',
        'ضوء',
        'سلك',
        'شمعة',
      ],
      'body': [
        'fairing',
        'tank',
        'seat',
        'mirror',
        'هيكل',
        'خزان',
        'مقعد',
        'مرآة',
      ],
      'suspension': ['shock', 'fork', 'spring', 'مساعد', 'شوكة', 'نابض'],
      'transmission': [
        'chain',
        'sprocket',
        'gear',
        'clutch',
        'سلسلة',
        'ترس',
        'دبرياج',
      ],
    };

    // تحليل المفاهيم المكتشفة
    final detectedConcepts =
        generalAnalysis.concepts.map((c) => c.name.toLowerCase()).toList();
    final detectedObjects =
        objectDetection.objects
            .expand((o) => o.concepts.map((c) => c.name.toLowerCase()))
            .toList();
    final allDetections = [...detectedConcepts, ...detectedObjects];

    // تسجيل النقاط لكل فئة
    final categoryScores = <String, double>{};

    for (final category in partKeywords.keys) {
      double score = 0.0;
      final keywords = partKeywords[category]!;

      for (final keyword in keywords) {
        for (final detection in allDetections) {
          if (detection.contains(keyword.toLowerCase())) {
            // إضافة نقاط بناءً على الثقة
            final concept = generalAnalysis.concepts.firstWhere(
              (c) => c.name.toLowerCase() == detection,
              orElse: () => ConceptResult(id: '', name: '', confidence: 0.5),
            );
            score += concept.confidence;
          }
        }
      }

      if (score > 0) {
        categoryScores[category] = score;
      }
    }

    // تحديد الفئة الأكثر احتمالاً
    String primaryCategory = 'unknown';
    double maxScore = 0.0;

    categoryScores.forEach((category, score) {
      if (score > maxScore) {
        maxScore = score;
        primaryCategory = category;
      }
    });

    // تحديد العلامة التجارية المحتملة
    final brandKeywords = [
      'honda',
      'yamaha',
      'suzuki',
      'kawasaki',
      'bmw',
      'ducati',
      'harley',
    ];
    String? detectedBrand;

    for (final brand in brandKeywords) {
      if (allDetections.any((detection) => detection.contains(brand))) {
        detectedBrand = brand;
        break;
      }
    }

    return ProductClassification(
      primaryCategory: primaryCategory,
      confidence: maxScore,
      secondaryCategories:
          categoryScores.entries
              .where((e) => e.key != primaryCategory && e.value > 0.3)
              .map((e) => e.key)
              .toList(),
      detectedBrand: detectedBrand,
      suggestedTags: _generateSuggestedTags(allDetections, primaryCategory),
    );
  }

  /// 📊 تقييم جودة الصورة
  Future<QualityAssessment> _assessImageQuality(
    File imageFile,
    ClarifaiResult analysis,
  ) async {
    // تحليل جودة الصورة بناءً على عوامل متعددة
    double qualityScore = 0.0;
    final issues = <String>[];
    final suggestions = <String>[];

    // 1. وضوح الصورة (بناءً على عدد المفاهيم المكتشفة)
    final conceptCount = analysis.concepts.length;
    final avgConfidence =
        analysis.concepts.isNotEmpty
            ? analysis.concepts
                    .map((c) => c.confidence)
                    .reduce((a, b) => a + b) /
                conceptCount
            : 0.0;

    if (avgConfidence > 0.8) {
      qualityScore += 30;
    } else if (avgConfidence > 0.6) {
      qualityScore += 20;
    } else {
      qualityScore += 10;
      issues.add('وضوح منخفض');
      suggestions.add('استخدم إضاءة أفضل أو كاميرا عالية الجودة');
    }

    // 2. تنوع المحتوى
    if (conceptCount > 10) {
      qualityScore += 25;
    } else if (conceptCount > 5) {
      qualityScore += 15;
    } else {
      qualityScore += 5;
      issues.add('تفاصيل قليلة');
      suggestions.add('التقط صورة أقرب أو من زاوية أفضل');
    }

    // 3. حجم الملف (مؤشر على الجودة)
    final fileSize = await imageFile.length();
    final fileSizeMB = fileSize / (1024 * 1024);

    if (fileSizeMB > 2) {
      qualityScore += 20;
    } else if (fileSizeMB > 1) {
      qualityScore += 15;
    } else {
      qualityScore += 10;
      issues.add('حجم ملف صغير');
      suggestions.add('استخدم جودة أعلى في الكاميرا');
    }

    // 4. تحليل الألوان (تنوع الألوان يشير لجودة أفضل)
    final colorAnalysis = await _clarifaiService.analyzeColors(
      imageFile: imageFile,
      maxColors: 10,
    );

    if (colorAnalysis.colors.length > 5) {
      qualityScore += 15;
    } else if (colorAnalysis.colors.length > 3) {
      qualityScore += 10;
    } else {
      qualityScore += 5;
      issues.add('ألوان محدودة');
      suggestions.add('تأكد من الإضاءة الجيدة والخلفية المناسبة');
    }

    // 5. مكافأة إضافية للصور الواضحة جداً
    if (analysis.concepts.any((c) => c.confidence > 0.95)) {
      qualityScore += 10;
    }

    // تحديد التقييم النهائي
    QualityLevel level;
    if (qualityScore >= 80) {
      level = QualityLevel.excellent;
    } else if (qualityScore >= 60) {
      level = QualityLevel.good;
    } else if (qualityScore >= 40) {
      level = QualityLevel.fair;
    } else {
      level = QualityLevel.poor;
    }

    return QualityAssessment(
      overallScore: qualityScore.clamp(0, 100),
      level: level,
      issues: issues,
      suggestions: suggestions,
      metrics: {
        'concept_count': conceptCount,
        'avg_confidence': avgConfidence,
        'file_size_mb': fileSizeMB,
        'color_count': colorAnalysis.colors.length,
      },
    );
  }

  /// 📝 استخراج النص من صورة المنتج
  Future<TextExtractionResult?> _extractProductText(File imageFile) async {
    try {
      return await _clarifaiService.extractText(
        imageFile: imageFile,
        language: 'ar',
      );
    } catch (e) {
      debugPrint('فشل في استخراج النص: $e');
      return null;
    }
  }

  /// 🏷️ إنشاء علامات مقترحة
  List<String> _generateSuggestedTags(
    List<String> detections,
    String primaryCategory,
  ) {
    final tags = <String>{};

    // إضافة الفئة الأساسية
    tags.add(primaryCategory);

    // إضافة المفاهيم عالية الثقة
    tags.addAll(detections.take(5));

    // إضافة علامات عامة للدراجات النارية
    tags.addAll(['motorcycle', 'bike', 'part', 'دراجة نارية', 'قطعة غيار']);

    return tags.toList();
  }

  /// 💾 حفظ نتائج التحليل في قاعدة البيانات
  Future<void> _saveAnalysisResults(
    String productId,
    ProductAnalysisResult result,
  ) async {
    try {
      final analysisData = {
        'product_id': productId,
        'analysis_result': result.toJson(),
        'created_at': DateTime.now().toIso8601String(),
      };

      await _supabaseService.client
          .from('product_analysis')
          .insert(analysisData);
    } catch (e) {
      debugPrint('فشل في حفظ نتائج التحليل: $e');
    }
  }

  /// 📊 تحديث إحصائيات التحليل
  void _updateAnalysisStats(ProductAnalysisResult result) {
    if (result.classification != null) {
      final category = result.classification!.primaryCategory;
      _partTypeStats[category] = (_partTypeStats[category] ?? 0) + 1;

      if (result.classification!.detectedBrand != null) {
        final brand = result.classification!.detectedBrand!;
        _brandStats[brand] = (_brandStats[brand] ?? 0) + 1;
      }
    }

    if (result.quality != null) {
      final category = result.classification?.primaryCategory ?? 'unknown';
      _qualityScores[category] = result.quality!.overallScore;
    }
  }

  /// 📊 الحصول على إحصائيات مفصلة
  Map<String, dynamic> getDetailedStats() {
    return {
      'analysis_stats': {
        'total_analyses': _totalAnalyses,
        'successful_analyses': _successfulAnalyses,
        'failed_analyses': _failedAnalyses,
        'success_rate': successRate,
      },
      'part_type_distribution': _partTypeStats,
      'brand_distribution': _brandStats,
      'average_quality_scores': _qualityScores,
      'clarifai_stats': _clarifaiService.getDetailedStats(),
    };
  }

  /// 🧹 تنظيف وصيانة
  void performMaintenance() {
    _clarifaiService.performMaintenance();
    debugPrint('🧹 تم تنظيف محلل المنتجات الذكي');
    notifyListeners();
  }

  /// 🔄 إعادة تعيين الإحصائيات
  void resetStatistics() {
    _totalAnalyses = 0;
    _successfulAnalyses = 0;
    _failedAnalyses = 0;
    _partTypeStats.clear();
    _brandStats.clear();
    _qualityScores.clear();
    _clarifaiService.resetStatistics();
    notifyListeners();
  }
}

/// 📊 نتيجة تحليل المنتج الشاملة
class ProductAnalysisResult {
  final bool success;
  final String? error;
  final String? productId;
  final ClarifaiResult? generalAnalysis;
  final ObjectDetectionResult? objectDetection;
  final ColorAnalysisResult? colorAnalysis;
  final ModerationResult? moderation;
  final ProductClassification? classification;
  final QualityAssessment? quality;
  final TextExtractionResult? textExtraction;
  final int processingTime;
  final DateTime timestamp;

  ProductAnalysisResult({
    required this.success,
    this.error,
    this.productId,
    this.generalAnalysis,
    this.objectDetection,
    this.colorAnalysis,
    this.moderation,
    this.classification,
    this.quality,
    this.textExtraction,
    required this.processingTime,
    required this.timestamp,
  });

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'error': error,
      'product_id': productId,
      'general_analysis': generalAnalysis?.toJson(),
      'object_detection':
          objectDetection != null
              ? {
                'success': objectDetection!.success,
                'objects_count': objectDetection!.objects.length,
              }
              : null,
      'color_analysis':
          colorAnalysis != null
              ? {
                'success': colorAnalysis!.success,
                'colors_count': colorAnalysis!.colors.length,
              }
              : null,
      'moderation':
          moderation != null
              ? {
                'is_appropriate': moderation!.isAppropriate,
                'confidence': moderation!.confidence,
              }
              : null,
      'classification': classification?.toJson(),
      'quality': quality?.toJson(),
      'text_extraction':
          textExtraction != null
              ? {
                'success': textExtraction!.success,
                'texts_count': textExtraction!.texts.length,
              }
              : null,
      'processing_time_ms': processingTime,
      'timestamp': timestamp.toIso8601String(),
    };
  }
}

/// 🏷️ تصنيف المنتج
class ProductClassification {
  final String primaryCategory;
  final double confidence;
  final List<String> secondaryCategories;
  final String? detectedBrand;
  final List<String> suggestedTags;

  ProductClassification({
    required this.primaryCategory,
    required this.confidence,
    required this.secondaryCategories,
    this.detectedBrand,
    required this.suggestedTags,
  });

  Map<String, dynamic> toJson() {
    return {
      'primary_category': primaryCategory,
      'confidence': confidence,
      'secondary_categories': secondaryCategories,
      'detected_brand': detectedBrand,
      'suggested_tags': suggestedTags,
    };
  }
}

/// 📊 تقييم الجودة
class QualityAssessment {
  final double overallScore;
  final QualityLevel level;
  final List<String> issues;
  final List<String> suggestions;
  final Map<String, dynamic> metrics;

  QualityAssessment({
    required this.overallScore,
    required this.level,
    required this.issues,
    required this.suggestions,
    required this.metrics,
  });

  Map<String, dynamic> toJson() {
    return {
      'overall_score': overallScore,
      'level': level.toString(),
      'issues': issues,
      'suggestions': suggestions,
      'metrics': metrics,
    };
  }
}

/// 📊 مستويات الجودة
enum QualityLevel { poor, fair, good, excellent }
