import 'package:flutter/material.dart';
import 'package:motorcycle_parts_shop/core/theme/app_theme.dart';
import 'package:motorcycle_parts_shop/core/utils/responsive_helper.dart';
import 'package:motorcycle_parts_shop/models/settings_model.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';

class SupportScreen extends StatelessWidget {
  const SupportScreen({super.key});

  void _showEditDialog(
    BuildContext context,
    String currentValue,
    String title,
    Function(String) onSave,
  ) {
    final controller = TextEditingController(text: currentValue);
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text('تعديل $title'),
            content: TextField(
              controller: controller,
              decoration: InputDecoration(hintText: 'أدخل $title الجديد'),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              TextButton(
                onPressed: () {
                  onSave(controller.text);
                  Navigator.pop(context);
                },
                child: const Text('حفظ'),
              ),
            ],
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(
          ResponsiveHelper.isMobile(context) ? 70 : 80,
        ),
        child: Container(
          decoration: BoxDecoration(
            gradient: AppTheme.primaryGradient,
            boxShadow: AppTheme.cardShadow,
          ),
          child: SafeArea(
            child: Padding(
              padding: EdgeInsets.symmetric(
                horizontal: ResponsiveHelper.getPadding(
                  context,
                  mobile: 12,
                  tablet: 16,
                  desktop: 20,
                ),
                vertical: 8,
              ),
              child: Row(
                children: [
                  // زر الرجوع المحسن
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: IconButton(
                      icon: const Icon(
                        Icons.arrow_back_ios_rounded,
                        color: AppTheme.textLightColor,
                      ),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ),

                  const SizedBox(width: 16),

                  // أيقونة الدعم والعنوان
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      Icons.support_agent_rounded,
                      color: AppTheme.textLightColor,
                      size: 24,
                    ),
                  ),

                  const SizedBox(width: 12),

                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          'الدعم الفني',
                          style: AppTheme.cardTitle.copyWith(
                            color: AppTheme.textLightColor,
                            fontSize: 18,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                        Text(
                          'نحن هنا لمساعدتك',
                          style: AppTheme.cardSubtitle.copyWith(
                            color: AppTheme.textLightColor.withOpacity(0.8),
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
      body: ResponsiveBuilder(
        builder: (context, constraints) {
          final padding = ResponsiveHelper.getPadding(context);

          return ListView(
            padding: EdgeInsets.all(padding),
            children: [
              ListTile(
                leading: const CircleAvatar(
                  backgroundColor: AppTheme.primaryColor,
                  child: Icon(Icons.email, color: Colors.white),
                ),
                title: const Text('البريد الإلكتروني'),
                subtitle: Consumer<SettingsModel>(
                  builder:
                      (context, settings, child) => Text(settings.supportEmail),
                ),
                trailing: const Icon(Icons.edit),
                onTap: () async {
                  final settings = Provider.of<SettingsModel>(
                    context,
                    listen: false,
                  );
                  _showEditDialog(
                    context,
                    settings.supportEmail,
                    'البريد الإلكتروني',
                    settings.updateSupportEmail,
                  );
                  final Uri emailLaunchUri = Uri(
                    scheme: 'mailto',
                    path: settings.supportEmail,
                    queryParameters: {'subject': 'طلب دعم فني'},
                  );
                  if (await canLaunchUrl(emailLaunchUri)) {
                    await launchUrl(emailLaunchUri);
                  }
                },
              ),
              const Divider(),
              ListTile(
                leading: const CircleAvatar(
                  backgroundColor: AppTheme.primaryColor,
                  child: Icon(Icons.chat, color: Colors.white),
                ),
                title: const Text('واتساب'),
                subtitle: Consumer<SettingsModel>(
                  builder:
                      (context, settings, child) =>
                          Text('+${settings.whatsappNumber}'),
                ),
                trailing: const Icon(Icons.edit),
                onTap: () async {
                  final settings = Provider.of<SettingsModel>(
                    context,
                    listen: false,
                  );
                  _showEditDialog(
                    context,
                    settings.whatsappNumber,
                    'رقم الواتساب',
                    settings.updateWhatsappNumber,
                  );
                  final url =
                      'https://wa.me/${settings.whatsappNumber}?text=مرحبا، أريد المساعدة بخصوص...';
                  if (await canLaunchUrl(Uri.parse(url))) {
                    await launchUrl(Uri.parse(url));
                  }
                },
              ),
            ],
          );
        },
      ),
    );
  }
}
