-- ===================================================================
-- جداول المراجعات والتسويق
-- متجر قطع غيار الدراجات النارية - Supabase Database
-- ===================================================================

-- ===================================================================
-- 1. جدول المراجعات والتقييمات
-- ===================================================================
CREATE TABLE IF NOT EXISTS reviews (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    order_id UUID REFERENCES orders(id) ON DELETE SET NULL,
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    title VARCHAR(200),
    comment TEXT,
    images JSONB DEFAULT '[]'::jsonb,
    verified_purchase BOOLEAN DEFAULT false,
    is_approved BOOLEAN DEFAULT false,
    helpful_count INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(user_id, product_id, order_id)
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_reviews_user_id ON reviews(user_id);
CREATE INDEX IF NOT EXISTS idx_reviews_product_id ON reviews(product_id);
CREATE INDEX IF NOT EXISTS idx_reviews_rating ON reviews(rating);
CREATE INDEX IF NOT EXISTS idx_reviews_is_approved ON reviews(is_approved);
CREATE INDEX IF NOT EXISTS idx_reviews_created_at ON reviews(created_at);

-- ===================================================================
-- 2. جدول العروض والخصومات
-- ===================================================================
CREATE TABLE IF NOT EXISTS offers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(200) NOT NULL,
    description TEXT,
    discount_percentage DECIMAL(5,2) NOT NULL CHECK (discount_percentage >= 0 AND discount_percentage <= 100),
    discount_amount DECIMAL(12,2),
    min_purchase_amount DECIMAL(12,2),
    max_discount_amount DECIMAL(12,2),
    start_date TIMESTAMPTZ NOT NULL,
    end_date TIMESTAMPTZ NOT NULL,
    usage_limit INTEGER,
    usage_count INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    applies_to VARCHAR(20) DEFAULT 'products' CHECK (applies_to IN ('products', 'categories', 'all')),
    target_ids JSONB DEFAULT '[]'::jsonb, -- معرفات المنتجات أو الفئات
    created_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    CHECK (end_date > start_date)
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_offers_is_active ON offers(is_active);
CREATE INDEX IF NOT EXISTS idx_offers_start_date ON offers(start_date);
CREATE INDEX IF NOT EXISTS idx_offers_end_date ON offers(end_date);

-- ===================================================================
-- 3. جدول الكوبونات
-- ===================================================================
CREATE TABLE IF NOT EXISTS coupons (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    code VARCHAR(50) UNIQUE NOT NULL,
    description TEXT NOT NULL,
    discount_type VARCHAR(20) NOT NULL CHECK (discount_type IN ('percentage', 'fixed')),
    discount_value DECIMAL(12,2) NOT NULL CHECK (discount_value > 0),
    min_purchase_amount DECIMAL(12,2),
    max_discount_amount DECIMAL(12,2),
    start_date TIMESTAMPTZ NOT NULL,
    end_date TIMESTAMPTZ NOT NULL,
    usage_limit INTEGER,
    usage_count INTEGER DEFAULT 0,
    user_usage_limit INTEGER DEFAULT 1,
    is_active BOOLEAN DEFAULT true,
    created_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    CHECK (end_date > start_date)
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_coupons_code ON coupons(code);
CREATE INDEX IF NOT EXISTS idx_coupons_is_active ON coupons(is_active);
CREATE INDEX IF NOT EXISTS idx_coupons_start_date ON coupons(start_date);
CREATE INDEX IF NOT EXISTS idx_coupons_end_date ON coupons(end_date);

-- ===================================================================
-- 4. جدول استخدام الكوبونات
-- ===================================================================
CREATE TABLE IF NOT EXISTS coupon_usage (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    coupon_id UUID NOT NULL REFERENCES coupons(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    order_id UUID NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
    discount_amount DECIMAL(12,2) NOT NULL,
    used_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(coupon_id, user_id, order_id)
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_coupon_usage_coupon_id ON coupon_usage(coupon_id);
CREATE INDEX IF NOT EXISTS idx_coupon_usage_user_id ON coupon_usage(user_id);
CREATE INDEX IF NOT EXISTS idx_coupon_usage_order_id ON coupon_usage(order_id);

-- ===================================================================
-- 5. جدول الإعلانات
-- ===================================================================
CREATE TABLE IF NOT EXISTS advertisements (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(200) NOT NULL,
    description TEXT,
    image_url TEXT,
    link_url TEXT,
    position VARCHAR(50) NOT NULL, -- 'banner', 'sidebar', 'popup', 'inline'
    target_audience JSONB DEFAULT '{}'::jsonb, -- معايير الاستهداف
    start_date TIMESTAMPTZ NOT NULL,
    end_date TIMESTAMPTZ NOT NULL,
    click_count INTEGER DEFAULT 0,
    view_count INTEGER DEFAULT 0,
    budget DECIMAL(10,2),
    cost_per_click DECIMAL(6,2),
    is_active BOOLEAN DEFAULT true,
    created_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    CHECK (end_date > start_date)
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_advertisements_position ON advertisements(position);
CREATE INDEX IF NOT EXISTS idx_advertisements_is_active ON advertisements(is_active);
CREATE INDEX IF NOT EXISTS idx_advertisements_start_date ON advertisements(start_date);
CREATE INDEX IF NOT EXISTS idx_advertisements_end_date ON advertisements(end_date);

-- ===================================================================
-- الدوال المساعدة
-- ===================================================================

-- دالة تحديث إحصائيات المنتج عند إضافة مراجعة
CREATE OR REPLACE FUNCTION update_product_review_stats()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public, extensions
AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE products SET
            review_count = (SELECT COUNT(*) FROM reviews WHERE product_id = NEW.product_id AND is_approved = true),
            average_rating = (SELECT COALESCE(AVG(rating), 0) FROM reviews WHERE product_id = NEW.product_id AND is_approved = true)
        WHERE id = NEW.product_id;
        RETURN NEW;
    END IF;
    
    IF TG_OP = 'UPDATE' THEN
        UPDATE products SET
            review_count = (SELECT COUNT(*) FROM reviews WHERE product_id = NEW.product_id AND is_approved = true),
            average_rating = (SELECT COALESCE(AVG(rating), 0) FROM reviews WHERE product_id = NEW.product_id AND is_approved = true)
        WHERE id = NEW.product_id;
        RETURN NEW;
    END IF;
    
    IF TG_OP = 'DELETE' THEN
        UPDATE products SET
            review_count = (SELECT COUNT(*) FROM reviews WHERE product_id = OLD.product_id AND is_approved = true),
            average_rating = (SELECT COALESCE(AVG(rating), 0) FROM reviews WHERE product_id = OLD.product_id AND is_approved = true)
        WHERE id = OLD.product_id;
        RETURN OLD;
    END IF;
    
    RETURN NULL;
END;
$$;

-- ===================================================================
-- المحفزات
-- ===================================================================

-- محفزات تحديث الطوابع الزمنية
CREATE TRIGGER update_reviews_updated_at 
    BEFORE UPDATE ON reviews 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_offers_updated_at 
    BEFORE UPDATE ON offers 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_coupons_updated_at 
    BEFORE UPDATE ON coupons 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_advertisements_updated_at 
    BEFORE UPDATE ON advertisements 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- محفز تحديث إحصائيات المنتج
CREATE TRIGGER update_product_stats_on_review 
    AFTER INSERT OR UPDATE OR DELETE ON reviews 
    FOR EACH ROW 
    EXECUTE FUNCTION update_product_review_stats();

-- ===================================================================
-- سياسات الأمان (RLS)
-- ===================================================================

-- تفعيل RLS
ALTER TABLE reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE offers ENABLE ROW LEVEL SECURITY;
ALTER TABLE coupons ENABLE ROW LEVEL SECURITY;
ALTER TABLE coupon_usage ENABLE ROW LEVEL SECURITY;
ALTER TABLE advertisements ENABLE ROW LEVEL SECURITY;

-- سياسات للمراجعات
CREATE POLICY "Users can view approved reviews" ON reviews FOR SELECT USING (is_approved = true);
CREATE POLICY "Users can manage own reviews" ON reviews FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Admins can manage all reviews" ON reviews FOR ALL USING (
    EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND profile_type = 'admin')
);

-- سياسات للعروض
CREATE POLICY "Users can view active offers" ON offers FOR SELECT USING (
    is_active = true AND NOW() BETWEEN start_date AND end_date
);
CREATE POLICY "Admins can manage offers" ON offers FOR ALL USING (
    EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND profile_type = 'admin')
);

-- سياسات للكوبونات
CREATE POLICY "Users can view active coupons" ON coupons FOR SELECT USING (
    is_active = true AND NOW() BETWEEN start_date AND end_date
);
CREATE POLICY "Admins can manage coupons" ON coupons FOR ALL USING (
    EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND profile_type = 'admin')
);

-- سياسات لاستخدام الكوبونات
CREATE POLICY "Users can view own coupon usage" ON coupon_usage FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can create coupon usage" ON coupon_usage FOR INSERT WITH CHECK (auth.uid() = user_id);

-- سياسات للإعلانات
CREATE POLICY "Users can view active ads" ON advertisements FOR SELECT USING (
    is_active = true AND NOW() BETWEEN start_date AND end_date
);
CREATE POLICY "Admins can manage ads" ON advertisements FOR ALL USING (
    EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND profile_type = 'admin')
);

-- ===================================================================
-- رسالة نجاح
-- ===================================================================

DO $$
BEGIN
    RAISE NOTICE '✅ تم إنشاء جداول المراجعات والتسويق بنجاح!';
    RAISE NOTICE '⭐ جدول المراجعات والتقييمات (reviews)';
    RAISE NOTICE '🎯 جدول العروض والخصومات (offers)';
    RAISE NOTICE '🎫 جدول الكوبونات (coupons)';
    RAISE NOTICE '📊 جدول استخدام الكوبونات (coupon_usage)';
    RAISE NOTICE '📢 جدول الإعلانات (advertisements)';
    RAISE NOTICE '🔒 تم تطبيق سياسات الأمان';
END $$;
