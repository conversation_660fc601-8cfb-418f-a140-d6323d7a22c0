import 'package:flutter/foundation.dart';
import 'package:motorcycle_parts_shop/core/services/unified_storage_service.dart';
import 'package:motorcycle_parts_shop/models/product_model.dart';

/// نموذج عنصر السلة المحسن
class CartItemModel {
  final String id;
  final ProductModel product;
  int quantity;
  final DateTime addedAt;
  final Map<String, dynamic>? selectedOptions;
  bool selected;

  CartItemModel({
    required this.id,
    required this.product,
    required this.quantity,
    required this.addedAt,
    this.selectedOptions,
    this.selected = false,
  });

  double get totalPrice => product.price * quantity;

  Map<String, dynamic> toJson() => {
    'id': id,
    'product': product.toJson(),
    'quantity': quantity,
    'addedAt': addedAt.toIso8601String(),
    'selectedOptions': selectedOptions,
    'selected': selected,
  };

  factory CartItemModel.fromJson(Map<String, dynamic> json) => CartItemModel(
    id: json['id'],
    product: ProductModel.fromJson(json['product']),
    quantity: json['quantity'],
    addedAt: DateTime.parse(json['addedAt']),
    selectedOptions: json['selectedOptions'],
    selected: json['selected'] ?? false,
  );
}

/// خدمة السلة المحسنة والسريعة
class CartService extends ChangeNotifier {
  static final CartService _instance = CartService._internal();
  factory CartService() => _instance;
  CartService._internal();

  final UnifiedStorageService _storage = UnifiedStorageService();
  final List<CartItemModel> _items = [];
  bool _isInitialized = false;

  // Getters
  List<CartItemModel> get items => List.unmodifiable(_items);
  int get itemCount => _items.length;
  int get totalQuantity => _items.fold(0, (sum, item) => sum + item.quantity);
  double get totalPrice =>
      _items.fold(0.0, (sum, item) => sum + item.totalPrice);
  bool get isEmpty => _items.isEmpty;
  bool get isNotEmpty => _items.isNotEmpty;
  bool get isInitialized => _isInitialized;

  /// تهيئة الخدمة
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _loadCartFromStorage();
      _isInitialized = true;
      debugPrint('✅ تم تهيئة خدمة السلة بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة خدمة السلة: $e');
      _isInitialized = true; // تهيئة حتى مع وجود خطأ
    }
  }

  /// تحميل السلة من التخزين
  Future<void> _loadCartFromStorage() async {
    try {
      final cartData = await _storage.getData('cart_items');
      if (cartData != null && cartData is List) {
        _items.clear();
        for (final item in cartData) {
          if (item is Map<String, dynamic>) {
            _items.add(CartItemModel.fromJson(item));
          }
        }
        debugPrint('📦 تم تحميل ${_items.length} عنصر من السلة');
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحميل السلة: $e');
    }
  }

  /// حفظ السلة في التخزين
  Future<void> _saveCartToStorage() async {
    try {
      final cartData = _items.map((item) => item.toJson()).toList();
      await _storage.saveData('cart_items', cartData);
    } catch (e) {
      debugPrint('❌ خطأ في حفظ السلة: $e');
    }
  }

  /// إضافة منتج للسلة
  Future<void> addToCart(
    ProductModel product, {
    int quantity = 1,
    Map<String, dynamic>? options,
  }) async {
    try {
      // البحث عن المنتج في السلة
      final existingIndex = _items.indexWhere(
        (item) => item.product.id == product.id,
      );

      if (existingIndex != -1) {
        // تحديث الكمية إذا كان المنتج موجود
        _items[existingIndex].quantity += quantity;
      } else {
        // إضافة منتج جديد
        final cartItem = CartItemModel(
          id: '${product.id}_${DateTime.now().millisecondsSinceEpoch}',
          product: product,
          quantity: quantity,
          addedAt: DateTime.now(),
          selectedOptions: options,
        );
        _items.add(cartItem);
      }

      await _saveCartToStorage();
      notifyListeners();
      debugPrint('✅ تم إضافة ${product.name} للسلة');
    } catch (e) {
      debugPrint('❌ خطأ في إضافة المنتج للسلة: $e');
      throw Exception('فشل في إضافة المنتج للسلة');
    }
  }

  /// تحديث كمية المنتج
  Future<void> updateQuantity(String itemId, int newQuantity) async {
    try {
      if (newQuantity <= 0) {
        await removeFromCart(itemId);
        return;
      }

      final index = _items.indexWhere((item) => item.id == itemId);
      if (index != -1) {
        _items[index].quantity = newQuantity;
        await _saveCartToStorage();
        notifyListeners();
        debugPrint('✅ تم تحديث الكمية');
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحديث الكمية: $e');
      throw Exception('فشل في تحديث الكمية');
    }
  }

  /// حذف منتج من السلة
  Future<void> removeFromCart(String itemId) async {
    try {
      final index = _items.indexWhere((item) => item.id == itemId);
      if (index != -1) {
        final removedItem = _items.removeAt(index);
        await _saveCartToStorage();
        notifyListeners();
        debugPrint('✅ تم حذف ${removedItem.product.name} من السلة');
      }
    } catch (e) {
      debugPrint('❌ خطأ في حذف المنتج: $e');
      throw Exception('فشل في حذف المنتج');
    }
  }

  /// مسح السلة بالكامل
  Future<void> clearCart() async {
    try {
      _items.clear();
      await _saveCartToStorage();
      notifyListeners();
      debugPrint('✅ تم مسح السلة بالكامل');
    } catch (e) {
      debugPrint('❌ خطأ في مسح السلة: $e');
      throw Exception('فشل في مسح السلة');
    }
  }

  /// التحقق من وجود منتج في السلة
  bool isInCart(String productId) {
    return _items.any((item) => item.product.id == productId);
  }

  /// الحصول على كمية منتج معين
  int getProductQuantity(String productId) {
    final item = _items.firstWhere(
      (item) => item.product.id == productId,
      orElse:
          () => CartItemModel(
            id: '',
            product: ProductModel.empty(),
            quantity: 0,
            addedAt: DateTime.now(),
          ),
    );
    return item.quantity;
  }

  /// الحصول على عنصر من السلة
  CartItemModel? getCartItem(String itemId) {
    try {
      return _items.firstWhere((item) => item.id == itemId);
    } catch (e) {
      return null;
    }
  }

  /// تطبيق كوبون خصم
  double applyDiscount(String couponCode) {
    // يمكن تطوير هذه الدالة لاحقاً
    switch (couponCode.toUpperCase()) {
      case 'SAVE10':
        return totalPrice * 0.1;
      case 'SAVE20':
        return totalPrice * 0.2;
      default:
        return 0.0;
    }
  }

  /// حساب الضرائب
  double calculateTax() {
    return totalPrice * 0.15; // 15% ضريبة
  }

  /// حساب رسوم الشحن
  double calculateShipping() {
    if (totalPrice > 500) return 0.0; // شحن مجاني فوق 500
    return 50.0; // رسوم شحن ثابتة
  }

  /// المجموع النهائي
  double getFinalTotal({String? couponCode}) {
    double total = totalPrice;

    if (couponCode != null) {
      total -= applyDiscount(couponCode);
    }

    total += calculateTax();
    total += calculateShipping();

    return total;
  }

  /// إحصائيات السلة
  Map<String, dynamic> getCartStatistics() {
    return {
      'itemCount': itemCount,
      'totalQuantity': totalQuantity,
      'totalPrice': totalPrice,
      'averageItemPrice': itemCount > 0 ? totalPrice / itemCount : 0.0,
      'oldestItem':
          _items.isNotEmpty
              ? _items
                  .reduce((a, b) => a.addedAt.isBefore(b.addedAt) ? a : b)
                  .addedAt
              : null,
      'newestItem':
          _items.isNotEmpty
              ? _items
                  .reduce((a, b) => a.addedAt.isAfter(b.addedAt) ? a : b)
                  .addedAt
              : null,
    };
  }

  /// تصدير السلة كـ JSON
  Map<String, dynamic> exportCart() {
    return {
      'items': _items.map((item) => item.toJson()).toList(),
      'statistics': getCartStatistics(),
      'exportedAt': DateTime.now().toIso8601String(),
    };
  }

  /// استيراد السلة من JSON
  Future<void> importCart(Map<String, dynamic> cartData) async {
    try {
      _items.clear();

      if (cartData['items'] != null) {
        for (final item in cartData['items']) {
          _items.add(CartItemModel.fromJson(item));
        }
      }

      await _saveCartToStorage();
      notifyListeners();
      debugPrint('✅ تم استيراد السلة بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في استيراد السلة: $e');
      throw Exception('فشل في استيراد السلة');
    }
  }

  /// تنظيف السلة من المنتجات المنتهية الصلاحية
  Future<void> cleanupExpiredItems() async {
    try {
      final now = DateTime.now();
      final expiredDuration = const Duration(days: 30); // 30 يوم

      _items.removeWhere((item) {
        return now.difference(item.addedAt) > expiredDuration;
      });

      await _saveCartToStorage();
      notifyListeners();
      debugPrint('✅ تم تنظيف السلة من العناصر المنتهية الصلاحية');
    } catch (e) {
      debugPrint('❌ خطأ في تنظيف السلة: $e');
    }
  }

  /// مزامنة السلة مع قاعدة البيانات
  Future<void> syncCartWithDatabase() async {
    try {
      // يمكن تطوير هذه الدالة لاحقاً للمزامنة مع Supabase
      debugPrint('🔄 تم مزامنة السلة مع قاعدة البيانات');
    } catch (e) {
      debugPrint('❌ خطأ في مزامنة السلة: $e');
    }
  }

  /// تبديل تحديد جميع العناصر
  void toggleSelectAll() {
    // يمكن تطوير هذه الدالة لاحقاً
    notifyListeners();
  }

  /// تبديل تحديد عنصر معين
  void toggleItemSelection(String itemId) {
    // يمكن تطوير هذه الدالة لاحقاً
    notifyListeners();
  }

  /// حذف العناصر المحددة
  Future<void> removeSelectedItems() async {
    // يمكن تطوير هذه الدالة لاحقاً
    await _saveCartToStorage();
    notifyListeners();
  }

  /// المجموع (alias للتوافق)
  double get total => totalPrice;

  @override
  void dispose() {
    _items.clear();
    super.dispose();
  }
}
