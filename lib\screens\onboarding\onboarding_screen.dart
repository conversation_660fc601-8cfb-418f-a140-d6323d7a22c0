import 'dart:async';
import 'dart:math' as math;
import 'package:confetti/confetti.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:lottie/lottie.dart';
import 'package:motorcycle_parts_shop/core/theme/app_theme.dart';
import 'package:motorcycle_parts_shop/core/theme/gradients.dart';
import 'package:motorcycle_parts_shop/core/utils/responsive_helper.dart';
import 'package:motorcycle_parts_shop/screens/auth/login_screen.dart';
import 'package:shared_preferences/shared_preferences.dart';

class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({super.key});

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen>
    with TickerProviderStateMixin {
  final PageController _pageController = PageController();
  late final AnimationController _animationController;
  late final Animation<double> _fadeAnimation;
  late final AnimationController _scaleController;
  late final AnimationController _rotationController;
  late final AnimationController _pulseController;
  late final ConfettiController _confettiController;
  int _currentPage = 0;
  bool _isAutoPlaying = true;
  Timer? _autoPlayTimer;

  late List<AnimationController> _pageAnimationControllers;
  late List<Animation<Offset>> _pageSlideAnimations;
  late List<Animation<double>> _pageFadeAnimations;
  late List<Animation<double>> _pageScaleAnimations;
  late Animation<double> _rotationAnimation;
  late Animation<double> _pulseAnimation;

  final List<OnboardingItem> _onboardingItems = [
    OnboardingItem(
      title: 'مرحباً بك في متجر قطع الغيار',
      description:
          'اكتشف تشكيلة واسعة من قطع الغيار الأصلية لجميع أنواع الدراجات النارية مع ضمان الجودة والأصالة',
      image: 'assets/images/motorcycle.png',
      lottieAsset: 'assets/animations/motorcycle.json',
      icon: Icons.motorcycle,
      backgroundColor: AppTheme.primaryColor,
      gradient: AppGradients.blueWaveGradient,
      features: ['قطع غيار أصلية', 'ضمان الجودة', 'تشكيلة واسعة'],
    ),
    OnboardingItem(
      title: 'جودة عالية وأسعار منافسة',
      description:
          'نوفر لك أفضل قطع الغيار بأسعار تنافسية مع خدمة عملاء متميزة على مدار الساعة',
      image: 'assets/images/quality.png',
      lottieAsset: 'assets/animations/quality.json',
      icon: Icons.verified_outlined,
      backgroundColor: const Color(0xFF1E3A8A),
      gradient: AppGradients.turquoiseWaveGradient,
      features: ['أسعار منافسة', 'خدمة عملاء 24/7', 'ضمان شامل'],
    ),
    OnboardingItem(
      title: 'توصيل سريع وآمن',
      description:
          'نضمن وصول طلبك بسرعة وأمان إلى أي مكان في مصر مع إمكانية تتبع الشحنة',
      image: 'assets/images/delivery.png',
      lottieAsset: 'assets/animations/delivery.json',
      icon: Icons.local_shipping_outlined,
      backgroundColor: AppTheme.secondaryColor,
      gradient: AppGradients.goldenWaveGradient,
      features: ['توصيل سريع', 'تتبع الشحنة', 'تغطية شاملة'],
    ),
    OnboardingItem(
      title: 'ذكاء اصطناعي متقدم',
      description:
          'استفد من تقنيات الذكاء الاصطناعي للحصول على توصيات مخصصة وتجربة تسوق فريدة',
      image: 'assets/images/ai.png',
      lottieAsset: 'assets/animations/ai.json',
      icon: Icons.psychology_outlined,
      backgroundColor: const Color(0xFF7C3AED),
      gradient: AppGradients.purpleWaveGradient,
      features: ['توصيات ذكية', 'بحث بالصوت', 'تحليل الصور'],
    ),
  ];

  @override
  void initState() {
    super.initState();
    _confettiController = ConfettiController(
      duration: const Duration(seconds: 2),
    );

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1200),
    );

    _scaleController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    _rotationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 20),
    );

    _pulseController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _rotationAnimation = Tween<double>(begin: 0.0, end: 2 * math.pi).animate(
      CurvedAnimation(parent: _rotationController, curve: Curves.linear),
    );

    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.1).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    _pageAnimationControllers = List.generate(
      _onboardingItems.length,
      (index) => AnimationController(
        vsync: this,
        duration: const Duration(milliseconds: 800),
      ),
    );

    _pageSlideAnimations = List.generate(
      _onboardingItems.length,
      (index) =>
          Tween<Offset>(begin: const Offset(0, 0.3), end: Offset.zero).animate(
            CurvedAnimation(
              parent: _pageAnimationControllers[index],
              curve: Curves.easeOutBack,
            ),
          ),
    );

    _pageFadeAnimations = List.generate(
      _onboardingItems.length,
      (index) => Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(
          parent: _pageAnimationControllers[index],
          curve: Curves.easeInOut,
        ),
      ),
    );

    _pageScaleAnimations = List.generate(
      _onboardingItems.length,
      (index) => Tween<double>(begin: 0.8, end: 1.0).animate(
        CurvedAnimation(
          parent: _pageAnimationControllers[index],
          curve: Curves.elasticOut,
        ),
      ),
    );

    _animationController.forward();
    _scaleController.forward();
    _rotationController.repeat();
    _pulseController.repeat(reverse: true);
    _pageAnimationControllers[0].forward();

    SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);

    // بدء التشغيل التلقائي
    _startAutoPlay();
  }

  void _startAutoPlay() {
    if (_isAutoPlaying) {
      _autoPlayTimer = Timer.periodic(const Duration(seconds: 4), (timer) {
        if (_currentPage < _onboardingItems.length - 1) {
          _nextPage();
        } else {
          _stopAutoPlay();
        }
      });
    }
  }

  void _stopAutoPlay() {
    _autoPlayTimer?.cancel();
    _autoPlayTimer = null;
  }

  void _toggleAutoPlay() {
    setState(() {
      _isAutoPlaying = !_isAutoPlaying;
    });

    if (_isAutoPlaying) {
      _startAutoPlay();
    } else {
      _stopAutoPlay();
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    _animationController.dispose();
    _scaleController.dispose();
    _rotationController.dispose();
    _pulseController.dispose();
    _confettiController.dispose();
    _autoPlayTimer?.cancel();
    for (var controller in _pageAnimationControllers) {
      controller.dispose();
    }
    SystemChrome.setEnabledSystemUIMode(
      SystemUiMode.manual,
      overlays: SystemUiOverlay.values,
    );
    super.dispose();
  }

  void _markOnboardingComplete() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('onboarding_complete', true);
  }

  void _nextPage() {
    _stopAutoPlay(); // إيقاف التشغيل التلقائي عند التفاعل اليدوي

    if (_currentPage < _onboardingItems.length - 1) {
      _pageAnimationControllers[_currentPage].reset();
      _pageController
          .nextPage(
            duration: const Duration(milliseconds: 700),
            curve: Curves.easeInOutCubic,
          )
          .then((_) {
            _pageAnimationControllers[_currentPage].forward();
          });
    } else {
      _confettiController.play();
      _markOnboardingComplete();
      Future.delayed(const Duration(milliseconds: 1500), () {
        if (mounted) {
          Navigator.of(context).pushReplacement(
            PageRouteBuilder(
              pageBuilder:
                  (context, animation, secondaryAnimation) =>
                      const LoginScreen(),
              transitionsBuilder: (
                context,
                animation,
                secondaryAnimation,
                child,
              ) {
                const begin = Offset(1.0, 0.0);
                const end = Offset.zero;
                const curve = Curves.easeOutQuint;
                var tween = Tween(
                  begin: begin,
                  end: end,
                ).chain(CurveTween(curve: curve));
                var offsetAnimation = animation.drive(tween);
                return SlideTransition(position: offsetAnimation, child: child);
              },
              transitionDuration: const Duration(milliseconds: 1000),
            ),
          );
        }
      });
    }
  }

  void _skipOnboarding() {
    _markOnboardingComplete();
    if (mounted) {
      Navigator.of(context).pushReplacement(
        PageRouteBuilder(
          pageBuilder:
              (context, animation, secondaryAnimation) => const LoginScreen(),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return FadeTransition(
              opacity: animation,
              child: ScaleTransition(
                scale: Tween<double>(begin: 0.9, end: 1.0).animate(
                  CurvedAnimation(parent: animation, curve: Curves.easeOutBack),
                ),
                child: child,
              ),
            );
          },
          transitionDuration: const Duration(milliseconds: 800),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      body: GestureDetector(
        onHorizontalDragEnd: (details) {
          if (details.primaryVelocity! > 0) {
            // Swipe right to left
            _nextPage();
          } else if (details.primaryVelocity! < 0) {
            // Swipe left to right
            if (_currentPage > 0) {
              _pageController.previousPage(
                duration: const Duration(milliseconds: 500),
                curve: Curves.easeInOutBack,
              );
            }
          }
        },
        child: Stack(
          children: [
            ScaleTransition(
              scale: Tween<double>(begin: 0.95, end: 1.0).animate(
                CurvedAnimation(
                  parent: _scaleController,
                  curve: Curves.easeOutBack,
                ),
              ),
              child: FadeTransition(
                opacity: _fadeAnimation,
                child: PageView.builder(
                  controller: _pageController,
                  itemCount: _onboardingItems.length,
                  onPageChanged: (index) {
                    setState(() {
                      _currentPage = index;
                    });
                    _pageAnimationControllers[index].forward();
                    HapticFeedback.lightImpact();
                  },
                  physics: const BouncingScrollPhysics(),
                  reverse: true,
                  itemBuilder: (context, index) {
                    return _buildOnboardingPage(_onboardingItems[index], index);
                  },
                ),
              ),
            ),

            Align(
              alignment: Alignment.topCenter,
              child: ConfettiWidget(
                confettiController: _confettiController,
                blastDirectionality: BlastDirectionality.explosive,
                shouldLoop: false,
                colors: const [
                  Colors.blue,
                  Colors.yellow,
                  Colors.red,
                  Colors.green,
                  Colors.purple,
                ],
              ),
            ),

            // مؤشر التقدم
            Positioned(
              top: 50,
              left: 20,
              right: 80,
              child: Container(
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(2),
                ),
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  width:
                      MediaQuery.of(context).size.width *
                      ((_currentPage + 1) / _onboardingItems.length),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(2),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.white.withOpacity(0.5),
                        blurRadius: 4,
                        spreadRadius: 1,
                      ),
                    ],
                  ),
                ),
              ),
            ),

            // زر التحكم في التشغيل التلقائي
            Positioned(
              top: 60,
              right: 20,
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(25),
                  border: Border.all(
                    color: Colors.white.withOpacity(0.3),
                    width: 1,
                  ),
                ),
                child: IconButton(
                  onPressed: _toggleAutoPlay,
                  icon: Icon(
                    _isAutoPlaying ? Icons.pause : Icons.play_arrow,
                    color: Colors.white,
                    size: 24,
                  ),
                  tooltip:
                      _isAutoPlaying
                          ? 'إيقاف التشغيل التلقائي'
                          : 'تشغيل تلقائي',
                ),
              ),
            ),

            Positioned(
              bottom: ResponsiveHelper.isMobile(context) ? 40 : 50,
              left: 0,
              right: 0,
              child: ResponsiveBuilder(
                builder: (context, constraints) {
                  final padding = ResponsiveHelper.getPadding(context);

                  return Column(
                    children: [
                      AnimatedSwitcher(
                        duration: const Duration(milliseconds: 300),
                        child: Row(
                          key: ValueKey<int>(_currentPage),
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: List.generate(
                            _onboardingItems.length,
                            (index) => _buildDotIndicator(index),
                          ),
                        ),
                      ),

                      SizedBox(
                        height: ResponsiveHelper.isMobile(context) ? 20 : 30,
                      ),
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: padding),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            AnimatedSwitcher(
                              duration: const Duration(milliseconds: 400),
                              transitionBuilder: (child, animation) {
                                return ScaleTransition(
                                  scale: animation,
                                  child: child,
                                );
                              },
                              child:
                                  _currentPage < _onboardingItems.length - 1
                                      ? _buildNextButton()
                                      : _buildGetStartedButton(),
                            ),

                            AnimatedSwitcher(
                              duration: const Duration(milliseconds: 300),
                              child:
                                  _currentPage < _onboardingItems.length - 1
                                      ? _buildSkipButton()
                                      : _buildRestartButton(),
                            ),
                          ],
                        ),
                      ),
                    ],
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOnboardingPage(OnboardingItem item, int pageIndex) {
    return ResponsiveBuilder(
      builder: (context, constraints) {
        final padding = ResponsiveHelper.getPadding(context);
        final imageHeight = ResponsiveHelper.isMobile(context) ? 200.0 : 250.0;
        final iconSize = ResponsiveHelper.isMobile(context) ? 50.0 : 60.0;

        return Container(
          decoration: BoxDecoration(gradient: item.gradient),
          child: Stack(
            children: [
              // خلفية متحركة
              Positioned.fill(
                child: AnimatedBuilder(
                  animation: _rotationAnimation,
                  builder: (context, child) {
                    return Transform.rotate(
                      angle: _rotationAnimation.value * 0.1,
                      child: Container(
                        decoration: BoxDecoration(
                          gradient: RadialGradient(
                            center: Alignment.topRight,
                            radius: 2.0,
                            colors: [
                              Colors.white.withOpacity(0.1),
                              Colors.transparent,
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),

              // المحتوى الرئيسي
              SafeArea(
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: padding),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Spacer(),

                      // الأيقونة والصورة
                      ScaleTransition(
                        scale: _pageScaleAnimations[pageIndex],
                        child: SlideTransition(
                          position: _pageSlideAnimations[pageIndex],
                          child: FadeTransition(
                            opacity: _pageFadeAnimations[pageIndex],
                            child: Column(
                              children: [
                                // الأيقونة مع تأثير النبض
                                AnimatedBuilder(
                                  animation: _pulseAnimation,
                                  builder: (context, child) {
                                    return Transform.scale(
                                      scale: _pulseAnimation.value,
                                      child: Container(
                                        padding: const EdgeInsets.all(20),
                                        decoration: BoxDecoration(
                                          shape: BoxShape.circle,
                                          color: Colors.white.withOpacity(0.2),
                                          border: Border.all(
                                            color: Colors.white.withOpacity(
                                              0.3,
                                            ),
                                            width: 2,
                                          ),
                                        ),
                                        child: Icon(
                                          item.icon,
                                          size: iconSize,
                                          color: Colors.white,
                                        ),
                                      ),
                                    );
                                  },
                                ),

                                const SizedBox(height: 30),

                                // الصورة أو Lottie Animation
                                Hero(
                                  tag: 'onboarding-image-$pageIndex',
                                  child:
                                      item.lottieAsset != null
                                          ? Lottie.asset(
                                            item.lottieAsset!,
                                            height: imageHeight,
                                            fit: BoxFit.contain,
                                            repeat: true,
                                            animate: true,
                                          )
                                          : Image.asset(
                                            item.image,
                                            height: imageHeight,
                                            fit: BoxFit.contain,
                                          ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),

                      const SizedBox(height: 40),

                      // النص والوصف
                      SlideTransition(
                        position: _pageSlideAnimations[pageIndex],
                        child: FadeTransition(
                          opacity: _pageFadeAnimations[pageIndex],
                          child: Column(
                            children: [
                              Text(
                                item.title,
                                style: Theme.of(
                                  context,
                                ).textTheme.headlineMedium?.copyWith(
                                  color: Colors.white,
                                  fontSize: ResponsiveHelper.getFontSize(
                                    context,
                                    ResponsiveHelper.isMobile(context)
                                        ? 24
                                        : 28,
                                  ),
                                  fontWeight: FontWeight.bold,
                                  shadows: [
                                    Shadow(
                                      blurRadius: 8,
                                      color: Colors.black.withOpacity(0.3),
                                      offset: const Offset(2, 2),
                                    ),
                                  ],
                                ),
                                textAlign: TextAlign.center,
                              ),

                              const SizedBox(height: 16),

                              Padding(
                                padding: EdgeInsets.symmetric(
                                  horizontal: padding * 0.5,
                                ),
                                child: Text(
                                  item.description,
                                  style: Theme.of(
                                    context,
                                  ).textTheme.bodyLarge?.copyWith(
                                    color: Colors.white.withOpacity(0.9),
                                    fontSize: ResponsiveHelper.getFontSize(
                                      context,
                                      16,
                                    ),
                                    height: 1.6,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),

                              // المميزات
                              if (item.features.isNotEmpty) ...[
                                const SizedBox(height: 24),
                                _buildFeaturesList(item.features),
                              ],
                            ],
                          ),
                        ),
                      ),

                      const Spacer(flex: 2),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildFeaturesList(List<String> features) {
    return Wrap(
      alignment: WrapAlignment.center,
      spacing: 12,
      runSpacing: 8,
      children:
          features.map((feature) {
            return Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.2),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: Colors.white.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.check_circle_outline,
                    size: 16,
                    color: Colors.white,
                  ),
                  const SizedBox(width: 6),
                  Text(
                    feature,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
    );
  }

  Widget _buildDotIndicator(int index) {
    return GestureDetector(
      onTap: () {
        _pageController.animateToPage(
          index,
          duration: const Duration(milliseconds: 700),
          curve: Curves.easeInOut,
        );
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 400),
        curve: Curves.easeInOut,
        margin: const EdgeInsets.symmetric(horizontal: 8),
        height: _currentPage == index ? 12 : 8,
        width: _currentPage == index ? 24 : 8,
        decoration: BoxDecoration(
          color:
              _currentPage == index
                  ? Colors.white
                  : Colors.white.withOpacity(0.5),
          borderRadius: BorderRadius.circular(10),
          boxShadow:
              _currentPage == index
                  ? [
                    BoxShadow(
                      color: Colors.white.withOpacity(0.3),
                      blurRadius: 8,
                      spreadRadius: 2,
                    ),
                  ]
                  : null,
        ),
      ),
    );
  }

  Widget _buildNextButton() {
    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: 1.0 + (_pulseAnimation.value - 1.0) * 0.05,
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(25),
              gradient: LinearGradient(
                colors: [Colors.white, Colors.white.withOpacity(0.9)],
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.white.withOpacity(0.3),
                  blurRadius: 15,
                  spreadRadius: 2,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: ElevatedButton(
              onPressed: () {
                HapticFeedback.mediumImpact();
                _nextPage();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.transparent,
                foregroundColor: AppTheme.primaryColor,
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(25),
                ),
                padding: const EdgeInsets.symmetric(
                  horizontal: 32,
                  vertical: 16,
                ),
              ),
              child: const Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'التالي',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  SizedBox(width: 8),
                  Icon(Icons.arrow_forward_ios, size: 18),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildGetStartedButton() {
    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: 1.0 + (_pulseAnimation.value - 1.0) * 0.1,
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(30),
              gradient: LinearGradient(
                colors: [
                  AppTheme.secondaryColor,
                  AppTheme.secondaryColor.withOpacity(0.8),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              boxShadow: [
                BoxShadow(
                  color: AppTheme.secondaryColor.withOpacity(0.4),
                  blurRadius: 20,
                  spreadRadius: 3,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: ElevatedButton(
              onPressed: () {
                HapticFeedback.heavyImpact();
                _confettiController.play();
                _nextPage();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.transparent,
                foregroundColor: Colors.white,
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(30),
                ),
                padding: const EdgeInsets.symmetric(
                  horizontal: 40,
                  vertical: 18,
                ),
              ),
              child: const Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'ابدأ الآن',
                    style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                  ),
                  SizedBox(width: 12),
                  Icon(Icons.rocket_launch, size: 24),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildSkipButton() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: Colors.white.withOpacity(0.3), width: 1.5),
        color: Colors.white.withOpacity(0.1),
      ),
      child: TextButton(
        onPressed: () {
          HapticFeedback.selectionClick();
          _skipOnboarding();
        },
        style: TextButton.styleFrom(
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
        ),
        child: const Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'تخطي',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
            SizedBox(width: 8),
            Icon(Icons.skip_next, size: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildRestartButton() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: Colors.white.withOpacity(0.3), width: 1.5),
        color: Colors.white.withOpacity(0.1),
      ),
      child: TextButton(
        onPressed: () {
          HapticFeedback.selectionClick();
          _pageController.animateToPage(
            0,
            duration: const Duration(milliseconds: 800),
            curve: Curves.easeInOutBack,
          );
        },
        style: TextButton.styleFrom(
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
        ),
        child: const Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.refresh, size: 20),
            SizedBox(width: 8),
            Text(
              'إعادة',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
          ],
        ),
      ),
    );
  }
}

class OnboardingItem {
  final String title;
  final String description;
  final String image;
  final String? lottieAsset;
  final IconData icon;
  final Color backgroundColor;
  final LinearGradient gradient;
  final List<String> features;

  const OnboardingItem({
    required this.title,
    required this.description,
    required this.image,
    this.lottieAsset,
    required this.icon,
    required this.backgroundColor,
    required this.gradient,
    this.features = const [],
  });
}
