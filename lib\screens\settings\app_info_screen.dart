import 'package:flutter/material.dart';
import 'package:motorcycle_parts_shop/core/theme/app_theme.dart';
import 'package:package_info_plus/package_info_plus.dart';

class AppInfoScreen extends StatefulWidget {
  const AppInfoScreen({super.key});

  @override
  State<AppInfoScreen> createState() => _AppInfoScreenState();
}

class _AppInfoScreenState extends State<AppInfoScreen> {
  PackageInfo _packageInfo = PackageInfo(
    appName: 'متجر قطع غيار الدراجات النارية',
    packageName: 'motorcycle_parts_shop',
    version: '1.0.0',
    buildNumber: '1',
  );
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _initPackageInfo();
  }

  Future<void> _initPackageInfo() async {
    try {
      final info = await PackageInfo.fromPlatform();
      setState(() {
        _packageInfo = info;
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('خطأ في الحصول على معلومات التطبيق: $e');
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('معلومات التطبيق'),
        centerTitle: true,
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : ListView(
                padding: const EdgeInsets.all(16.0),
                children: [
                  _buildAppIcon(),
                  const SizedBox(height: 24),
                  _buildInfoSection('اسم التطبيق', _packageInfo.appName),
                  _buildInfoSection(
                    'الإصدار',
                    '${_packageInfo.version} (${_packageInfo.buildNumber})',
                  ),
                  _buildInfoSection('معرف الحزمة', _packageInfo.packageName),
                  const Divider(),
                  _buildSectionHeader('معلومات قانونية'),
                  _buildInfoCard(
                    'سياسة الخصوصية',
                    'تعرف على كيفية جمع واستخدام بياناتك',
                    Icons.privacy_tip,
                    () => _showLegalInfo(
                      'سياسة الخصوصية',
                      'نص سياسة الخصوصية يظهر هنا...',
                    ),
                  ),
                  _buildInfoCard(
                    'شروط الاستخدام',
                    'الشروط والأحكام لاستخدام التطبيق',
                    Icons.description,
                    () => _showLegalInfo(
                      'شروط الاستخدام',
                      'نص شروط الاستخدام يظهر هنا...',
                    ),
                  ),
               
                  const Divider(),
                  _buildSectionHeader('المطورون'),
                  _buildInfoCard(
                    'فريق التطوير',
                    'معلومات عن مطوري التطبيق',
                    Icons.people,
                    () => _showDevelopersInfo(),
                  ),
                ],
              ),
    );
  }

  Widget _buildAppIcon() {
    return Center(
      child: Container(
        width: 100,
        height: 100,
        decoration: BoxDecoration(
          color: AppTheme.primaryColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Icon(Icons.motorcycle, size: 60, color: AppTheme.primaryColor),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 12.0),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: AppTheme.primaryColor,
        ),
      ),
    );
  }

  Widget _buildInfoSection(String title, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(title, style: const TextStyle(fontWeight: FontWeight.bold)),
          Text(value),
        ],
      ),
    );
  }

  Widget _buildInfoCard(
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap,
  ) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8.0),
      elevation: 2,
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: AppTheme.primaryColor,
          child: Icon(icon, color: Colors.white, size: 20),
        ),
        title: Text(title),
        subtitle: Text(subtitle),
        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: onTap,
      ),
    );
  }

  void _showLegalInfo(String title, String content) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(title),
            content: SingleChildScrollView(child: Text(content)),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إغلاق'),
              ),
            ],
          ),
    );
  }


  void _showDevelopersInfo() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('فريق التطوير'),
            content: const Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('تم تطوير هذا التطبيق بواسطة:'),
                SizedBox(height: 8),
                Text('• فريق متجر قطع غيار الدراجات النارية'),
                SizedBox(height: 16),
                Text('للتواصل:'),
                Text('البريد الإلكتروني: <EMAIL>'),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إغلاق'),
              ),
            ],
          ),
    );
  }
}
