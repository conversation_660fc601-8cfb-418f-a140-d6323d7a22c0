import 'package:motorcycle_parts_shop/core/services/auth_supabase_service.dart';
import 'package:motorcycle_parts_shop/models/address_model.dart';

class AddressService {
  final AuthSupabaseService _authService;

  AddressService({AuthSupabaseService? authService})
    : _authService = authService ?? AuthSupabaseService();

  Future<List<AddressModel>> getUserAddresses() async {
    try {
      final userId = _authService.currentUser?.id;
      if (userId == null) {
        throw Exception('المستخدم غير مسجل الدخول');
      }

      final response = await _authService.client
          .from('addresses')
          .select()
          .eq('user_id', userId)
          .order('is_default', ascending: false);

      return (response as List)
          .map((address) => AddressModel.fromJson(address))
          .toList();
    } catch (e) {
      throw Exception('فشل في جلب العناوين: $e');
    }
  }

  Future<void> addAddress(AddressModel address) async {
    try {
      await _authService.client.from('addresses').insert({
        'title': address.title,
        'full_name': address.fullName,
        'street_address': address.streetAddress,
        'city': address.city,
        'state': address.state,
        'phone_number': address.phoneNumber,
        'is_default': address.isDefault,
        'user_id': _authService.currentUser!.id,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      throw Exception('فشل في إضافة العنوان: $e');
    }
  }

  Future<void> updateAddress(AddressModel address) async {
    try {
      await _authService.client
          .from('addresses')
          .update({
            'title': address.title,
            'full_name': address.fullName,
            'street_address': address.streetAddress,
            'city': address.city,
            'state': address.state,
            'phone_number': address.phoneNumber,
            'is_default': address.isDefault,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', address.id);
    } catch (e) {
      throw Exception('فشل في تحديث العنوان: $e');
    }
  }

  Future<void> deleteAddress(String addressId) async {
    try {
      await _authService.client.from('addresses').delete().eq('id', addressId);
    } catch (e) {
      throw Exception('فشل في حذف العنوان: $e');
    }
  }

  Future<void> setDefaultAddress(String addressId) async {
    try {
      final userId = _authService.currentUser?.id;
      if (userId == null) {
        throw Exception('المستخدم غير مسجل الدخول');
      }

      // إلغاء تعيين العنوان الافتراضي الحالي
      await _authService.client
          .from('addresses')
          .update({'is_default': false})
          .eq('user_id', userId)
          .eq('is_default', true);

      // تعيين العنوان الجديد كافتراضي
      await _authService.client
          .from('addresses')
          .update({'is_default': true})
          .eq('id', addressId);
    } catch (e) {
      throw Exception('فشل في تعيين العنوان الافتراضي: $e');
    }
  }
}
