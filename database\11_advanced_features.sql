-- ===================================================================
-- الميزات المتقدمة لمتجر قطع غيار الدراجات النارية
-- متجر قطع غيار الدراجات النارية - Supabase Database
-- ===================================================================

-- ===================================================================
-- 1. إعدادات المنتجات الجديدة
-- ===================================================================

-- جدول إعدادات المنتجات الجديدة
CREATE TABLE IF NOT EXISTS new_product_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    setting_name VARCHAR(100) UNIQUE NOT NULL,
    setting_value INTEGER NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- إدراج الإعدادات الافتراضية
INSERT INTO new_product_settings (setting_name, setting_value, description) VALUES
    ('new_product_duration_days', 10, 'عدد الأيام التي يظل فيها المنتج معلماً كـ "جديد"'),
    ('auto_cleanup_enabled', 1, 'تفعيل التنظيف التلقائي للمنتجات الجديدة (1 = مفعل، 0 = معطل)'),
    ('cleanup_interval_hours', 24, 'فترة تشغيل التنظيف التلقائي بالساعات')
ON CONFLICT (setting_name) DO NOTHING;

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_new_product_settings_name ON new_product_settings(setting_name);
CREATE INDEX IF NOT EXISTS idx_new_product_settings_active ON new_product_settings(is_active);

-- جدول سجلات النظام
CREATE TABLE IF NOT EXISTS system_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    log_type VARCHAR(50) NOT NULL,
    message TEXT NOT NULL,
    details JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_system_logs_type ON system_logs(log_type);
CREATE INDEX IF NOT EXISTS idx_system_logs_created_at ON system_logs(created_at);

-- ===================================================================
-- 2. نظام المحفظة والنقاط
-- ===================================================================

-- جدول محفظة المستخدم الرقمية
CREATE TABLE IF NOT EXISTS user_wallet (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    balance DECIMAL(12,2) DEFAULT 0.00 CHECK (balance >= 0),
    pending_balance DECIMAL(12,2) DEFAULT 0.00,
    total_earned DECIMAL(12,2) DEFAULT 0.00,
    total_spent DECIMAL(12,2) DEFAULT 0.00,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(user_id)
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_user_wallet_user_id ON user_wallet(user_id);
CREATE INDEX IF NOT EXISTS idx_user_wallet_balance ON user_wallet(balance);

-- جدول معاملات المحفظة
CREATE TABLE IF NOT EXISTS wallet_transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    transaction_type VARCHAR(20) NOT NULL CHECK (transaction_type IN ('credit', 'debit', 'refund', 'bonus')),
    amount DECIMAL(12,2) NOT NULL CHECK (amount > 0),
    balance_before DECIMAL(12,2) NOT NULL,
    balance_after DECIMAL(12,2) NOT NULL,
    reference_type VARCHAR(20) CHECK (reference_type IN ('order', 'refund', 'bonus', 'cashback', 'referral')),
    reference_id UUID,
    description TEXT,
    status VARCHAR(20) DEFAULT 'completed' CHECK (status IN ('pending', 'completed', 'failed', 'cancelled')),
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_wallet_transactions_user_id ON wallet_transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_wallet_transactions_type ON wallet_transactions(transaction_type);
CREATE INDEX IF NOT EXISTS idx_wallet_transactions_created_at ON wallet_transactions(created_at);

-- جدول نقاط الولاء
CREATE TABLE IF NOT EXISTS loyalty_points (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    points_balance INTEGER DEFAULT 0 CHECK (points_balance >= 0),
    total_earned INTEGER DEFAULT 0,
    total_redeemed INTEGER DEFAULT 0,
    tier_level VARCHAR(20) DEFAULT 'bronze' CHECK (tier_level IN ('bronze', 'silver', 'gold', 'platinum')),
    next_tier_points INTEGER DEFAULT 100,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(user_id)
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_loyalty_points_user_id ON loyalty_points(user_id);
CREATE INDEX IF NOT EXISTS idx_loyalty_points_tier ON loyalty_points(tier_level);

-- جدول معاملات النقاط
CREATE TABLE IF NOT EXISTS points_transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    transaction_type VARCHAR(20) NOT NULL CHECK (transaction_type IN ('earned', 'redeemed', 'expired', 'bonus')),
    points INTEGER NOT NULL,
    balance_before INTEGER NOT NULL,
    balance_after INTEGER NOT NULL,
    reference_type VARCHAR(20) CHECK (reference_type IN ('order', 'review', 'referral', 'bonus', 'signup')),
    reference_id UUID,
    description TEXT,
    expires_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_points_transactions_user_id ON points_transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_points_transactions_expires_at ON points_transactions(expires_at);

-- ===================================================================
-- 2. نظام الإحالة والمكافآت
-- ===================================================================

-- جدول كوبونات الإحالة
CREATE TABLE IF NOT EXISTS referral_codes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    code VARCHAR(20) UNIQUE NOT NULL,
    usage_count INTEGER DEFAULT 0,
    max_usage INTEGER DEFAULT 10,
    reward_type VARCHAR(20) DEFAULT 'points' CHECK (reward_type IN ('points', 'discount', 'cashback')),
    reward_value DECIMAL(8,2) NOT NULL CHECK (reward_value > 0),
    is_active BOOLEAN DEFAULT true,
    expires_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_referral_codes_user_id ON referral_codes(user_id);
CREATE INDEX IF NOT EXISTS idx_referral_codes_code ON referral_codes(code);
CREATE INDEX IF NOT EXISTS idx_referral_codes_active ON referral_codes(is_active);

-- جدول الإحالات
CREATE TABLE IF NOT EXISTS referrals (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    referrer_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    referred_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    referral_code VARCHAR(20) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'rewarded')),
    reward_amount DECIMAL(8,2),
    reward_points INTEGER,
    completed_at TIMESTAMPTZ,
    rewarded_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(referred_id)
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_referrals_referrer_id ON referrals(referrer_id);
CREATE INDEX IF NOT EXISTS idx_referrals_referred_id ON referrals(referred_id);
CREATE INDEX IF NOT EXISTS idx_referrals_status ON referrals(status);

-- ===================================================================
-- 3. إدارة المخزون المتقدمة
-- ===================================================================

-- جدول تنبيهات المخزون
CREATE TABLE IF NOT EXISTS inventory_alerts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    alert_type VARCHAR(20) NOT NULL CHECK (alert_type IN ('low_stock', 'out_of_stock', 'overstock')),
    threshold_value INTEGER,
    current_value INTEGER,
    is_resolved BOOLEAN DEFAULT false,
    resolved_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_inventory_alerts_product_id ON inventory_alerts(product_id);
CREATE INDEX IF NOT EXISTS idx_inventory_alerts_type ON inventory_alerts(alert_type);
CREATE INDEX IF NOT EXISTS idx_inventory_alerts_resolved ON inventory_alerts(is_resolved);

-- جدول طلبات إعادة التخزين
CREATE TABLE IF NOT EXISTS restock_requests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    requested_quantity INTEGER NOT NULL CHECK (requested_quantity > 0),
    current_stock INTEGER NOT NULL,
    priority VARCHAR(10) DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'ordered', 'received', 'cancelled')),
    estimated_cost DECIMAL(10,2),
    supplier_info JSONB DEFAULT '{}'::jsonb,
    requested_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
    approved_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_restock_requests_product_id ON restock_requests(product_id);
CREATE INDEX IF NOT EXISTS idx_restock_requests_status ON restock_requests(status);
CREATE INDEX IF NOT EXISTS idx_restock_requests_priority ON restock_requests(priority);

-- ===================================================================
-- 4. نظام التسويق المتقدم
-- ===================================================================

-- جدول شرائح العملاء
CREATE TABLE IF NOT EXISTS customer_segments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    criteria JSONB NOT NULL DEFAULT '{}'::jsonb,
    customer_count INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_customer_segments_name ON customer_segments(name);
CREATE INDEX IF NOT EXISTS idx_customer_segments_active ON customer_segments(is_active);

-- جدول عضوية العملاء في الشرائح
CREATE TABLE IF NOT EXISTS customer_segment_members (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    segment_id UUID NOT NULL REFERENCES customer_segments(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    added_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(segment_id, user_id)
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_segment_members_segment_id ON customer_segment_members(segment_id);
CREATE INDEX IF NOT EXISTS idx_segment_members_user_id ON customer_segment_members(user_id);

-- ===================================================================
-- 5. ميزات التطبيق المتقدمة
-- ===================================================================

-- جدول قوائم المفضلات المتعددة
CREATE TABLE IF NOT EXISTS wishlist_collections (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    is_public BOOLEAN DEFAULT false,
    is_default BOOLEAN DEFAULT false,
    items_count INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_wishlist_collections_user_id ON wishlist_collections(user_id);
CREATE INDEX IF NOT EXISTS idx_wishlist_collections_public ON wishlist_collections(is_public);

-- جدول عناصر قوائم المفضلات
CREATE TABLE IF NOT EXISTS wishlist_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    collection_id UUID NOT NULL REFERENCES wishlist_collections(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    notes TEXT,
    priority INTEGER DEFAULT 1,
    added_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(collection_id, product_id)
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_wishlist_items_collection_id ON wishlist_items(collection_id);
CREATE INDEX IF NOT EXISTS idx_wishlist_items_product_id ON wishlist_items(product_id);

-- جدول مقارنة المنتجات
CREATE TABLE IF NOT EXISTS product_comparisons (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
    session_id VARCHAR(100),
    name VARCHAR(100),
    product_ids JSONB NOT NULL DEFAULT '[]'::jsonb,
    comparison_data JSONB DEFAULT '{}'::jsonb,
    is_saved BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_product_comparisons_user_id ON product_comparisons(user_id);
CREATE INDEX IF NOT EXISTS idx_product_comparisons_session_id ON product_comparisons(session_id);

-- ===================================================================
-- الدوال المساعدة للميزات المتقدمة
-- ===================================================================

-- دالة إضافة رصيد للمحفظة
CREATE OR REPLACE FUNCTION add_wallet_balance(
    p_user_id UUID,
    p_amount DECIMAL,
    p_transaction_type VARCHAR DEFAULT 'credit',
    p_reference_type VARCHAR DEFAULT NULL,
    p_reference_id UUID DEFAULT NULL,
    p_description TEXT DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    v_wallet_record RECORD;
    v_transaction_id UUID;
    v_new_balance DECIMAL;
BEGIN
    -- الحصول على محفظة المستخدم أو إنشاؤها
    SELECT * INTO v_wallet_record FROM user_wallet WHERE user_id = p_user_id;

    IF NOT FOUND THEN
        INSERT INTO user_wallet (user_id, balance) VALUES (p_user_id, 0.00);
        SELECT * INTO v_wallet_record FROM user_wallet WHERE user_id = p_user_id;
    END IF;

    -- حساب الرصيد الجديد
    IF p_transaction_type = 'credit' THEN
        v_new_balance := v_wallet_record.balance + p_amount;
    ELSE
        v_new_balance := v_wallet_record.balance - p_amount;
        IF v_new_balance < 0 THEN
            RAISE EXCEPTION 'رصيد المحفظة غير كافي';
        END IF;
    END IF;

    -- إنشاء معاملة المحفظة
    INSERT INTO wallet_transactions (
        user_id, transaction_type, amount, balance_before, balance_after,
        reference_type, reference_id, description
    ) VALUES (
        p_user_id, p_transaction_type, p_amount, v_wallet_record.balance, v_new_balance,
        p_reference_type, p_reference_id, p_description
    ) RETURNING id INTO v_transaction_id;

    -- تحديث رصيد المحفظة
    UPDATE user_wallet SET
        balance = v_new_balance,
        total_earned = CASE WHEN p_transaction_type = 'credit' THEN total_earned + p_amount ELSE total_earned END,
        total_spent = CASE WHEN p_transaction_type = 'debit' THEN total_spent + p_amount ELSE total_spent END,
        updated_at = NOW()
    WHERE user_id = p_user_id;

    RETURN v_transaction_id;
END;
$$;

-- دالة إضافة نقاط الولاء
CREATE OR REPLACE FUNCTION add_loyalty_points(
    p_user_id UUID,
    p_points INTEGER,
    p_transaction_type VARCHAR DEFAULT 'earned',
    p_reference_type VARCHAR DEFAULT NULL,
    p_reference_id UUID DEFAULT NULL,
    p_description TEXT DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    v_loyalty_record RECORD;
    v_transaction_id UUID;
    v_new_balance INTEGER;
    v_new_tier VARCHAR;
BEGIN
    -- الحصول على نقاط المستخدم أو إنشاؤها
    SELECT * INTO v_loyalty_record FROM loyalty_points WHERE user_id = p_user_id;

    IF NOT FOUND THEN
        INSERT INTO loyalty_points (user_id, points_balance) VALUES (p_user_id, 0);
        SELECT * INTO v_loyalty_record FROM loyalty_points WHERE user_id = p_user_id;
    END IF;

    -- حساب الرصيد الجديد
    IF p_transaction_type = 'earned' THEN
        v_new_balance := v_loyalty_record.points_balance + p_points;
    ELSE
        v_new_balance := v_loyalty_record.points_balance - p_points;
        IF v_new_balance < 0 THEN
            RAISE EXCEPTION 'نقاط الولاء غير كافية';
        END IF;
    END IF;

    -- تحديد المستوى الجديد
    v_new_tier := CASE
        WHEN v_new_balance >= 10000 THEN 'platinum'
        WHEN v_new_balance >= 5000 THEN 'gold'
        WHEN v_new_balance >= 1000 THEN 'silver'
        ELSE 'bronze'
    END;

    -- إنشاء معاملة النقاط
    INSERT INTO points_transactions (
        user_id, transaction_type, points, balance_before, balance_after,
        reference_type, reference_id, description
    ) VALUES (
        p_user_id, p_transaction_type, p_points, v_loyalty_record.points_balance, v_new_balance,
        p_reference_type, p_reference_id, p_description
    ) RETURNING id INTO v_transaction_id;

    -- تحديث نقاط الولاء
    UPDATE loyalty_points SET
        points_balance = v_new_balance,
        total_earned = CASE WHEN p_transaction_type = 'earned' THEN total_earned + p_points ELSE total_earned END,
        total_redeemed = CASE WHEN p_transaction_type = 'redeemed' THEN total_redeemed + p_points ELSE total_redeemed END,
        tier_level = v_new_tier,
        updated_at = NOW()
    WHERE user_id = p_user_id;

    RETURN v_transaction_id;
END;
$$;

-- دالة إنشاء كود إحالة
CREATE OR REPLACE FUNCTION generate_referral_code(
    p_user_id UUID,
    p_reward_value DECIMAL DEFAULT 50.00,
    p_reward_type VARCHAR DEFAULT 'points'
)
RETURNS VARCHAR
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    v_code VARCHAR;
    v_user_name VARCHAR;
BEGIN
    -- الحصول على اسم المستخدم
    SELECT name INTO v_user_name FROM profiles WHERE id = p_user_id;

    -- إنشاء كود فريد
    v_code := UPPER(LEFT(v_user_name, 3)) || LPAD(EXTRACT(EPOCH FROM NOW())::TEXT, 8, '0');

    -- التأكد من عدم وجود الكود
    WHILE EXISTS (SELECT 1 FROM referral_codes WHERE code = v_code) LOOP
        v_code := v_code || FLOOR(RANDOM() * 10)::TEXT;
    END LOOP;

    -- إنشاء كود الإحالة
    INSERT INTO referral_codes (
        user_id, code, reward_type, reward_value, expires_at
    ) VALUES (
        p_user_id, v_code, p_reward_type, p_reward_value, NOW() + INTERVAL '1 year'
    );

    RETURN v_code;
END;
$$;

-- دالة الحصول على إعدادات المنتجات الجديدة
CREATE OR REPLACE FUNCTION get_new_product_setting(
    p_setting_name VARCHAR
)
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    v_setting_value INTEGER;
BEGIN
    SELECT setting_value INTO v_setting_value
    FROM new_product_settings
    WHERE setting_name = p_setting_name AND is_active = true;

    -- إرجاع القيمة الافتراضية إذا لم توجد
    IF v_setting_value IS NULL THEN
        CASE p_setting_name
            WHEN 'new_product_duration_days' THEN v_setting_value := 10;
            WHEN 'auto_cleanup_enabled' THEN v_setting_value := 1;
            WHEN 'cleanup_interval_hours' THEN v_setting_value := 24;
            ELSE v_setting_value := 0;
        END CASE;
    END IF;

    RETURN v_setting_value;
END;
$$;

-- دالة تحديث إعدادات المنتجات الجديدة
CREATE OR REPLACE FUNCTION update_new_product_setting(
    p_setting_name VARCHAR,
    p_setting_value INTEGER
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    UPDATE new_product_settings
    SET setting_value = p_setting_value, updated_at = NOW()
    WHERE setting_name = p_setting_name;

    IF NOT FOUND THEN
        INSERT INTO new_product_settings (setting_name, setting_value)
        VALUES (p_setting_name, p_setting_value);
    END IF;

    RETURN true;
END;
$$;

-- دالة إنشاء منتج جديد مع إعدادات مخصصة
CREATE OR REPLACE FUNCTION create_new_product(
    p_product_data JSONB,
    p_custom_duration_days INTEGER DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    v_product_id UUID;
    v_duration_days INTEGER;
    v_new_until TIMESTAMPTZ;
BEGIN
    -- الحصول على مدة المنتج الجديد
    IF p_custom_duration_days IS NOT NULL THEN
        v_duration_days := p_custom_duration_days;
    ELSE
        v_duration_days := get_new_product_setting('new_product_duration_days');
    END IF;

    -- حساب تاريخ انتهاء كون المنتج جديد
    v_new_until := NOW() + (v_duration_days || ' days')::INTERVAL;

    -- إضافة الحقول المطلوبة للبيانات
    p_product_data := p_product_data || jsonb_build_object(
        'is_new', true,
        'new_until', v_new_until,
        'created_at', NOW(),
        'updated_at', NOW()
    );

    -- إدراج المنتج (هذا مثال - يجب تخصيصه حسب هيكل جدول المنتجات)
    INSERT INTO products (
        name, description, price, category_id, company_id,
        is_new, new_until, created_at, updated_at
    )
    SELECT
        (p_product_data->>'name')::VARCHAR,
        (p_product_data->>'description')::TEXT,
        (p_product_data->>'price')::DECIMAL,
        (p_product_data->>'category_id')::UUID,
        (p_product_data->>'company_id')::UUID,
        true,
        v_new_until,
        NOW(),
        NOW()
    RETURNING id INTO v_product_id;

    RETURN v_product_id;
END;
$$;

-- دالة تطبيق الإحالة
CREATE OR REPLACE FUNCTION apply_referral(
    p_referral_code VARCHAR,
    p_referred_user_id UUID
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    v_referral_record RECORD;
    v_referrer_id UUID;
BEGIN
    -- التحقق من صحة كود الإحالة
    SELECT * INTO v_referral_record FROM referral_codes
    WHERE code = p_referral_code AND is_active = true
    AND (expires_at IS NULL OR expires_at > NOW())
    AND usage_count < max_usage;

    IF NOT FOUND THEN
        RETURN false;
    END IF;

    v_referrer_id := v_referral_record.user_id;

    -- التأكد من عدم إحالة المستخدم لنفسه
    IF v_referrer_id = p_referred_user_id THEN
        RETURN false;
    END IF;

    -- التأكد من عدم وجود إحالة سابقة
    IF EXISTS (SELECT 1 FROM referrals WHERE referred_id = p_referred_user_id) THEN
        RETURN false;
    END IF;

    -- إنشاء الإحالة
    INSERT INTO referrals (
        referrer_id, referred_id, referral_code, status
    ) VALUES (
        v_referrer_id, p_referred_user_id, p_referral_code, 'pending'
    );

    -- تحديث عداد الاستخدام
    UPDATE referral_codes SET
        usage_count = usage_count + 1,
        updated_at = NOW()
    WHERE code = p_referral_code;

    RETURN true;
END;
$$;

-- ===================================================================
-- المحفزات للتحديث التلقائي
-- ===================================================================

-- محفز تحديث الطوابع الزمنية
CREATE TRIGGER update_user_wallet_updated_at
    BEFORE UPDATE ON user_wallet
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_loyalty_points_updated_at
    BEFORE UPDATE ON loyalty_points
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_referral_codes_updated_at
    BEFORE UPDATE ON referral_codes
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_restock_requests_updated_at
    BEFORE UPDATE ON restock_requests
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_customer_segments_updated_at
    BEFORE UPDATE ON customer_segments
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_wishlist_collections_updated_at
    BEFORE UPDATE ON wishlist_collections
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_product_comparisons_updated_at
    BEFORE UPDATE ON product_comparisons
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- ===================================================================
-- سياسات الأمان (RLS)
-- ===================================================================

-- تفعيل RLS
ALTER TABLE user_wallet ENABLE ROW LEVEL SECURITY;
ALTER TABLE wallet_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE loyalty_points ENABLE ROW LEVEL SECURITY;
ALTER TABLE points_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE referral_codes ENABLE ROW LEVEL SECURITY;
ALTER TABLE referrals ENABLE ROW LEVEL SECURITY;
ALTER TABLE inventory_alerts ENABLE ROW LEVEL SECURITY;
ALTER TABLE restock_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE customer_segments ENABLE ROW LEVEL SECURITY;
ALTER TABLE customer_segment_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE wishlist_collections ENABLE ROW LEVEL SECURITY;
ALTER TABLE wishlist_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE product_comparisons ENABLE ROW LEVEL SECURITY;
ALTER TABLE new_product_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE system_logs ENABLE ROW LEVEL SECURITY;


-- سياسات المحفظة والنقاط
CREATE POLICY "Users can view own wallet" ON user_wallet FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can view own wallet transactions" ON wallet_transactions FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can view own loyalty points" ON loyalty_points FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can view own points transactions" ON points_transactions FOR SELECT USING (auth.uid() = user_id);

-- سياسات الإحالة
CREATE POLICY "Users can manage own referral codes" ON referral_codes FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Users can view own referrals" ON referrals FOR SELECT USING (auth.uid() = referrer_id OR auth.uid() = referred_id);

-- سياسات المخزون (للمشرفين فقط)
CREATE POLICY "Admins can manage inventory alerts" ON inventory_alerts FOR ALL USING (
    EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND profile_type = 'admin')
);
CREATE POLICY "Admins can manage restock requests" ON restock_requests FOR ALL USING (
    EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND profile_type = 'admin')
);

-- سياسات شرائح العملاء (للمشرفين فقط)
CREATE POLICY "Admins can manage customer segments" ON customer_segments FOR ALL USING (
    EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND profile_type = 'admin')
);
CREATE POLICY "Admins can view segment members" ON customer_segment_members FOR SELECT USING (
    EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND profile_type = 'admin')
);

-- سياسات المفضلات والمقارنات
CREATE POLICY "Users can manage own wishlist collections" ON wishlist_collections FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Users can view public wishlist collections" ON wishlist_collections FOR SELECT USING (is_public = true);
CREATE POLICY "Users can manage own wishlist items" ON wishlist_items FOR ALL USING (
    EXISTS (SELECT 1 FROM wishlist_collections WHERE id = collection_id AND user_id = auth.uid())
);
CREATE POLICY "Users can manage own product comparisons" ON product_comparisons FOR ALL USING (auth.uid() = user_id);


-- سياسات أمان لجدول إعدادات المنتجات الجديدة
CREATE POLICY "Admins can manage new product settings" ON new_product_settings 
FOR ALL USING (
    EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND profile_type = 'admin')
);

CREATE POLICY "Read-only access to active settings" ON new_product_settings 
FOR SELECT USING (is_active = true);

-- سياسات أمان لجدول سجلات النظام
CREATE POLICY "Admins can view all system logs" ON system_logs 
FOR SELECT USING (
    EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND profile_type = 'admin')
);

CREATE POLICY "System can insert logs" ON system_logs 
FOR INSERT WITH CHECK (true);

CREATE POLICY "Admins can delete old logs" ON system_logs 
FOR DELETE USING (
    created_at < NOW() - INTERVAL '30 days' AND
    EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND profile_type = 'admin')
);

-- ===================================================================
-- رسالة نجاح
-- ===================================================================

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '✅ تم إنشاء جداول الميزات المتقدمة بنجاح!';
    RAISE NOTICE '';
    RAISE NOTICE '💰 نظام المحفظة والنقاط:';
    RAISE NOTICE '   • user_wallet - محفظة المستخدم';
    RAISE NOTICE '   • wallet_transactions - معاملات المحفظة';
    RAISE NOTICE '   • loyalty_points - نقاط الولاء';
    RAISE NOTICE '   • points_transactions - معاملات النقاط';
    RAISE NOTICE '';
    RAISE NOTICE '🎁 نظام الإحالة والمكافآت:';
    RAISE NOTICE '   • referral_codes - كوبونات الإحالة';
    RAISE NOTICE '   • referrals - الإحالات';
    RAISE NOTICE '';
    RAISE NOTICE '📦 إدارة المخزون المتقدمة:';
    RAISE NOTICE '   • inventory_alerts - تنبيهات المخزون';
    RAISE NOTICE '   • restock_requests - طلبات إعادة التخزين';
    RAISE NOTICE '';
    RAISE NOTICE '🎯 نظام التسويق المتقدم:';
    RAISE NOTICE '   • customer_segments - شرائح العملاء';
    RAISE NOTICE '   • customer_segment_members - عضوية الشرائح';
    RAISE NOTICE '';
    RAISE NOTICE '⭐ ميزات التطبيق المتقدمة:';
    RAISE NOTICE '   • wishlist_collections - قوائم المفضلات';
    RAISE NOTICE '   • wishlist_items - عناصر المفضلات';
    RAISE NOTICE '   • product_comparisons - مقارنة المنتجات';
    RAISE NOTICE '';
    RAISE NOTICE '🔄 تم إنشاء المحفزات التلقائية';
END $$;
