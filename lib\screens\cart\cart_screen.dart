import 'package:flutter/material.dart';
import 'package:motorcycle_parts_shop/core/services/cart_service.dart'

    as cart_service;
import 'package:provider/provider.dart';
import 'package:motorcycle_parts_shop/core/theme/app_theme.dart';
import 'package:motorcycle_parts_shop/core/utils/responsive_helper.dart';
import 'package:motorcycle_parts_shop/core/services/cart_service.dart';
import 'package:motorcycle_parts_shop/core/services/currency_service.dart';

class CartScreen extends StatefulWidget {
  const CartScreen({super.key});

  @override
  State<CartScreen> createState() => _CartScreenState();
}

class _CartScreenState extends State<CartScreen> {
  final bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    final cartService = Provider.of<CartService>(context);
    final cartItems = cartService.items;

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(
          ResponsiveHelper.isMobile(context) ? 70 : 80,
        ),
        child: Container(
          decoration: BoxDecoration(
            gradient: AppTheme.primaryGradient,
            boxShadow: AppTheme.cardShadow,
          ),
          child: SafeArea(
            child: Padding(
              padding: EdgeInsets.symmetric(
                horizontal: ResponsiveHelper.getPadding(
                  context,
                  mobile: 12,
                  tablet: 16,
                  desktop: 20,
                ),
                vertical: 8,
              ),
              child: Row(
                children: [
                  // زر الرجوع المحسن
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: IconButton(
                      icon: const Icon(
                        Icons.arrow_back_ios_rounded,
                        color: AppTheme.textLightColor,
                      ),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ),

                  const SizedBox(width: 16),

                  // أيقونة السلة والعنوان
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      Icons.shopping_cart_rounded,
                      color: AppTheme.textLightColor,
                      size: 24,
                    ),
                  ),

                  const SizedBox(width: 12),

                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          'سلة التسوق',
                          style: AppTheme.cardTitle.copyWith(
                            color: AppTheme.textLightColor,
                            fontSize: ResponsiveHelper.getFontSize(context, 18),
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                        if (cartItems.isNotEmpty)
                          Text(
                            '${cartItems.length} منتج',
                            style: AppTheme.cardSubtitle.copyWith(
                              color: AppTheme.textLightColor.withOpacity(0.8),
                              fontSize: 12,
                            ),
                          ),
                      ],
                    ),
                  ),

                  // أزرار الإجراءات
                  if (cartItems.isNotEmpty) ...[
                    if (cartItems.any((item) => item.selected))
                      _buildHeaderButton(
                        icon: Icons.delete_rounded,
                        onPressed:
                            () => _showDeleteConfirmationDialog(
                              context,
                              cartService,
                            ),
                        tooltip: 'حذف المحدد',
                        color: AppTheme.errorColor,
                      ),
                    const SizedBox(width: 8),
                    _buildHeaderButton(
                      icon:
                          cartItems.every((item) => item.selected)
                              ? Icons.deselect_rounded
                              : Icons.select_all_rounded,
                      onPressed: () => cartService.toggleSelectAll(),
                      tooltip:
                          cartItems.every((item) => item.selected)
                              ? 'إلغاء تحديد الكل'
                              : 'تحديد الكل',
                    ),
                  ],
                ],
              ),
            ),
          ),
        ),
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : cartItems.isEmpty
              ? _buildEmptyCartView(context)
              : _buildCartView(context, cartService),
    );
  }

  Widget _buildEmptyCartView(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, constraints) {
        final padding = ResponsiveHelper.getPadding(
          context,
          mobile: 24,
          tablet: 32,
          desktop: 40,
        );
        final iconSize = ResponsiveHelper.isMobile(context) ? 60.0 : 80.0;

        return Center(
          child: Padding(
            padding: EdgeInsets.all(padding),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // أيقونة السلة الفارغة المحسنة
                Container(
                  padding: const EdgeInsets.all(32),
                  decoration: BoxDecoration(
                    gradient: AppTheme.cardGradient,
                    shape: BoxShape.circle,
                    boxShadow: AppTheme.cardShadow,
                  ),
                  child: Icon(
                    Icons.shopping_cart_outlined,
                    size: iconSize,
                    color: AppTheme.textTertiaryColor,
                  ),
                ),

                const SizedBox(height: 32),

                // النص الرئيسي
                Text(
                  'سلة التسوق فارغة',
                  style: AppTheme.heroTitle.copyWith(
                    fontSize: ResponsiveHelper.getFontSize(context, 24),
                    color: AppTheme.textPrimaryColor,
                  ),
                ),

                const SizedBox(height: 12),

                // النص الفرعي
                Text(
                  'ابدأ بإضافة منتجات رائعة إلى سلتك\nواستمتع بتجربة تسوق مميزة',
                  style: AppTheme.cardSubtitle.copyWith(
                    fontSize: ResponsiveHelper.getFontSize(context, 16),
                    height: 1.5,
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 40),

                // زر التصفح المحسن
                Container(
                  decoration: BoxDecoration(
                    gradient: AppTheme.primaryGradient,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: AppTheme.cardShadow,
                  ),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: () => Navigator.pushNamed(context, '/home'),
                      borderRadius: BorderRadius.circular(16),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 32,
                          vertical: 16,
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.shopping_bag_rounded,
                              color: AppTheme.textLightColor,
                              size: 24,
                            ),
                            const SizedBox(width: 12),
                            Text(
                              'تصفح المنتجات',
                              style: AppTheme.buttonText.copyWith(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),

                const SizedBox(height: 24),

                // زر الفئات
                Container(
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: AppTheme.primaryColor.withOpacity(0.3),
                      width: 1,
                    ),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: () => Navigator.pushNamed(context, '/categories'),
                      borderRadius: BorderRadius.circular(12),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 24,
                          vertical: 12,
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.category_rounded,
                              color: AppTheme.primaryColor,
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'تصفح الفئات',
                              style: AppTheme.cardTitle.copyWith(
                                color: AppTheme.primaryColor,
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildCartView(BuildContext context, CartService cartService) {
    final cartItems = cartService.items;

    return Column(
      children: [
        Expanded(
          child: ListView.builder(
            padding: EdgeInsets.all(ResponsiveHelper.getPadding(context)),
            itemCount: cartItems.length,
            itemBuilder: (context, index) {
              final item = cartItems[index];
              return _buildCartItem(item, cartService);
            },
          ),
        ),
        _buildCheckoutSection(cartService),
      ],
    );
  }

  Widget _buildCartItem(CartItemModel item, CartService cartService) {
    return ResponsiveBuilder(
      builder: (context, constraints) {
        final imageSize = ResponsiveHelper.isMobile(context) ? 60.0 : 80.0;
        final margin = ResponsiveHelper.getPadding(
          context,
          mobile: 8,
          tablet: 12,
          desktop: 16,
        );
        final padding = ResponsiveHelper.getPadding(
          context,
          mobile: 8,
          tablet: 12,
          desktop: 16,
        );

        return Card(
          margin: EdgeInsets.only(bottom: margin),
          child: Padding(
            padding: EdgeInsets.all(padding),
            child: ListTile(
              selected: item.selected,
              onLongPress: () => cartService.toggleItemSelection(item.id),
              leading: ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Image.network(
                  item.product.imageUrl ?? '',
                  width: imageSize,
                  height: imageSize,
                  fit: BoxFit.cover,
                  errorBuilder:
                      (context, error, stackTrace) => Container(
                        width: imageSize,
                        height: imageSize,
                        color: Colors.grey[200],
                        child: const Icon(Icons.error),
                      ),
                ),
              ),
              title: Text(
                item.product.name,
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              subtitle: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 4),
                  Text(
                    '${item.product.price} ج.م',
                    style: const TextStyle(
                      color: AppTheme.primaryColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      IconButton(
                        icon: const Icon(Icons.remove),
                        onPressed: () {
                          if (item.quantity > 1) {
                            cartService.updateQuantity(
                              item.id,
                              item.quantity - 1,
                            );
                          }
                        },
                      ),
                      Text(
                        '${item.quantity}',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.add),
                        onPressed: () {
                          cartService.updateQuantity(
                            item.id,
                            item.quantity + 1,
                          );
                        },
                      ),
                    ],
                  ),
                ],
              ),
              trailing: IconButton(
                icon: const Icon(Icons.delete, color: AppTheme.errorColor),
                onPressed:
                    () => _showDeleteConfirmationDialog(
                      context,
                      cartService,
                      item.id,
                    ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildCheckoutSection(CartService cartService) {
    return ResponsiveBuilder(
      builder: (context, constraints) {
        final padding = ResponsiveHelper.getPadding(context);
        final buttonPadding = ResponsiveHelper.getPadding(
          context,
          mobile: 12,
          tablet: 16,
          desktop: 20,
        );

        return Container(
          padding: EdgeInsets.all(padding),
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.2),
                spreadRadius: 1,
                blurRadius: 5,
                offset: const Offset(0, -3),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'المجموع:',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  Text(
                    '${cartService.total} جم',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.primaryColor,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    _proceedToCheckout(context, cartService);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.primaryColor,
                    foregroundColor: Colors.white,
                    padding: EdgeInsets.all(buttonPadding),
                  ),
                  child: const Text('متابعة الشراء'),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  void _showDeleteConfirmationDialog(
    BuildContext context,
    CartService cartService, [
    String? productId,
  ]) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('حذف العناصر المحددة'),
          content: const Text('هل أنت متأكد أنك تريد حذف العناصر المحددة؟'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context); // إغلاق مربع الحوار
              },
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () {
                if (productId != null) {
                  cartService.removeFromCart(productId);
                } else {
                  cartService.removeSelectedItems();
                }
                setState(() {}); // تحديث الواجهة
                Navigator.pop(context); // إغلاق مربع الحوار
              },
              child: const Text('حذف'),
            ),
          ],
        );
      },
    );
  }

  /// الانتقال إلى شاشة الدفع
  void _proceedToCheckout(BuildContext context, CartService cartService) {
    if (cartService.items.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('السلة فارغة، أضف منتجات للمتابعة')),
      );
      return;
    }

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('تأكيد الطلب'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('المجموع: ${cartService.total} جم'),
                const SizedBox(height: 8),
                const Text('هل تريد المتابعة للدفع؟'),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  // سيتم استبدال هذا بالانتقال إلى شاشة الدفع عند إنشائها
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text(
                        'تم تأكيد الطلب، جاري الانتقال إلى صفحة الدفع...',
                      ),
                      duration: Duration(seconds: 2),
                    ),
                  );

                  // محاكاة الانتقال إلى شاشة الدفع بعد فترة قصيرة
                  Future.delayed(const Duration(seconds: 2), () {
                    // سيتم استبدال هذا بالانتقال الفعلي إلى شاشة الدفع
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('تم إنشاء الطلب بنجاح!'),
                        backgroundColor: Colors.green,
                      ),
                    );

                    // تفريغ السلة بعد إتمام الطلب
                    cartService.removeSelectedItems();
                  });
                },
                child: const Text('متابعة'),
              ),
            ],
          ),
    );
  }

  // 🎨 دالة بناء أزرار الهيدر المتطورة
  Widget _buildHeaderButton({
    required IconData icon,
    required VoidCallback onPressed,
    required String tooltip,
    Color? color,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.15),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withOpacity(0.2), width: 1),
      ),
      child: IconButton(
        icon: Icon(icon, color: color ?? AppTheme.textLightColor, size: 22),
        onPressed: onPressed,
        tooltip: tooltip,
        splashRadius: 20,
        padding: const EdgeInsets.all(8),
      ),
    );
  }
}
