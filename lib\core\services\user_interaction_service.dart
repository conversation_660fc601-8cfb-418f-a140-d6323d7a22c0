import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// خدمة تفاعل المستخدم
/// تستخدم لتسجيل تفاعلات المستخدم مع المنتجات
class UserInteractionService {
  final SupabaseClient _client;

  UserInteractionService(this._client);

  /// تسجيل مشاهدة منتج
  Future<void> recordProductView({
    required String productId,
    required String userId,
  }) async {
    try {
      await _client.from('user_interactions').insert({
        'user_id': userId,
        'product_id': productId,
        'interaction_type': 'view',
        'created_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      debugPrint('خطأ في تسجيل مشاهدة المنتج: $e');
    }
  }

  /// تسجيل إضافة منتج للسلة
  Future<void> recordAddToCart({
    required String productId,
    required String userId,
    required int quantity,
  }) async {
    try {
      await _client.from('user_interactions').insert({
        'user_id': userId,
        'product_id': productId,
        'interaction_type': 'add_to_cart',
        'metadata': {'quantity': quantity},
        'created_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      debugPrint('خطأ في تسجيل إضافة المنتج للسلة: $e');
    }
  }

  /// تسجيل إضافة منتج للمفضلة
  Future<void> recordAddToWishlist({
    required String productId,
    required String userId,
  }) async {
    try {
      await _client.from('user_interactions').insert({
        'user_id': userId,
        'product_id': productId,
        'interaction_type': 'add_to_wishlist',
        'created_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      debugPrint('خطأ في تسجيل إضافة المنتج للمفضلة: $e');
    }
  }

  /// تسجيل شراء منتج
  Future<void> recordPurchase({
    required String productId,
    required String userId,
    required double price,
    required int quantity,
  }) async {
    try {
      await _client.from('user_interactions').insert({
        'user_id': userId,
        'product_id': productId,
        'interaction_type': 'purchase',
        'metadata': {
          'price': price,
          'quantity': quantity,
        },
        'created_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      debugPrint('خطأ في تسجيل شراء المنتج: $e');
    }
  }

  /// تسجيل بحث المستخدم
  Future<void> recordSearch({
    required String userId,
    required String searchQuery,
    int? resultsCount,
  }) async {
    try {
      await _client.from('user_interactions').insert({
        'user_id': userId,
        'interaction_type': 'search',
        'metadata': {
          'search_query': searchQuery,
          'results_count': resultsCount,
        },
        'created_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      debugPrint('خطأ في تسجيل البحث: $e');
    }
  }

  /// الحصول على تفاعلات المستخدم مع منتج معين
  Future<List<Map<String, dynamic>>> getUserProductInteractions({
    required String userId,
    required String productId,
  }) async {
    try {
      final response = await _client
          .from('user_interactions')
          .select()
          .eq('user_id', userId)
          .eq('product_id', productId)
          .order('created_at', ascending: false);

      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      debugPrint('خطأ في جلب تفاعلات المستخدم: $e');
      return [];
    }
  }

  /// الحصول على المنتجات الأكثر مشاهدة للمستخدم
  Future<List<String>> getMostViewedProducts({
    required String userId,
    int limit = 10,
  }) async {
    try {
      final response = await _client
          .from('user_interactions')
          .select('product_id')
          .eq('user_id', userId)
          .eq('interaction_type', 'view')
          .order('created_at', ascending: false)
          .limit(limit);

      return response
          .map<String>((item) => item['product_id'] as String)
          .toList();
    } catch (e) {
      debugPrint('خطأ في جلب المنتجات الأكثر مشاهدة: $e');
      return [];
    }
  }
}
