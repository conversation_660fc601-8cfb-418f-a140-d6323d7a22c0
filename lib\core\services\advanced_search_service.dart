import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:motorcycle_parts_shop/core/services/auth_supabase_service.dart';
import 'package:motorcycle_parts_shop/core/services/unified_storage_service.dart';
import 'package:motorcycle_parts_shop/models/product_model.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// خدمة البحث المتقدمة
class AdvancedSearchService {
  static final AdvancedSearchService _instance =
      AdvancedSearchService._internal();
  factory AdvancedSearchService() => _instance;
  AdvancedSearchService._internal();

  final SupabaseClient _supabase = Supabase.instance.client;
  final AuthSupabaseService _authService = AuthSupabaseService();
  final UnifiedStorageService _storage = UnifiedStorageService();

  List<String> _searchHistory = [];
  final Map<String, List<ProductModel>> _searchCache = {};
  List<String> _popularSearches = [];
  final List<String> _savedSearches = [];

  /// البحث النصي المتقدم
  Future<List<ProductModel>> searchProducts({
    required String searchQuery,
    List<String>? categories,
    List<String>? brands,
    double? minPrice,
    double? maxPrice,
    bool? inStock,
    String? sortBy,
    bool ascending = true,
    int limit = 20,
    int offset = 0,
  }) async {
    try {
      // تتبع البحث
      await _trackSearch(searchQuery);

      // التحقق من الكاش
      final cacheKey = _generateCacheKey(
        searchQuery,
        categories,
        brands,
        minPrice,
        maxPrice,
        inStock,
        sortBy,
        ascending,
      );
      if (_searchCache.containsKey(cacheKey)) {
        return _searchCache[cacheKey]!;
      }

      var queryBuilder = _supabase
          .from('products')
          .select('*, categories(*), brands(*)')
          .eq('is_active', true);

      // البحث النصي
      if (searchQuery.isNotEmpty) {
        queryBuilder = queryBuilder.or(
          'name.ilike.%$searchQuery%,description.ilike.%$searchQuery%,specifications->>brand.ilike.%$searchQuery%',
        );
      }

      // فلترة حسب الفئات
      if (categories != null && categories.isNotEmpty) {
        queryBuilder = queryBuilder.inFilter('category_id', categories);
      }

      // فلترة حسب العلامات التجارية
      if (brands != null && brands.isNotEmpty) {
        queryBuilder = queryBuilder.inFilter('brand_id', brands);
      }

      // فلترة حسب السعر
      if (minPrice != null) {
        queryBuilder = queryBuilder.gte('price', minPrice);
      }
      if (maxPrice != null) {
        queryBuilder = queryBuilder.lte('price', maxPrice);
      }

      // فلترة حسب المخزون
      if (inStock == true) {
        queryBuilder = queryBuilder.gt('stock_quantity', 0);
      }

      // تنفيذ الاستعلام مع الترتيب والتحديد
      dynamic finalQuery = queryBuilder;

      // الترتيب
      if (sortBy != null) {
        finalQuery = finalQuery.order(sortBy, ascending: ascending);
      } else {
        finalQuery = finalQuery.order('created_at', ascending: false);
      }

      // التحديد والإزاحة
      finalQuery = finalQuery.range(offset, offset + limit - 1);

      final response = await finalQuery;
      final products =
          (response as List)
              .map((json) => ProductModel.fromJson(json))
              .toList();

      // حفظ في الكاش
      _searchCache[cacheKey] = products;

      // تم إزالة تتبع النتائج لأن AnalyticsService غير متوفر

      return products;
    } catch (e) {
      debugPrint('Error in advanced search: $e');
      return [];
    }
  }

  /// البحث بالصورة المتقدم
  Future<List<ProductModel>> searchByImage(String imageUrl) async {
    try {
      // تحليل الصورة باستخدام Clarifai
      final concepts = await _analyzeImageWithClarifai(imageUrl);

      if (concepts.isEmpty) return [];

      // البحث بناءً على المفاهيم المكتشفة
      final searchQuery = concepts.join(' ');
      final products = await searchProducts(searchQuery: searchQuery);

      // حفظ البحث بالصورة في التاريخ
      await _trackImageSearch(searchQuery, concepts);

      return products;
    } catch (e) {
      debugPrint('Error in image search: $e');
      return [];
    }
  }

  /// البحث الصوتي المتقدم
  Future<List<ProductModel>> startVoiceSearch() async {
    try {
      // محاكاة البحث الصوتي (يمكن تطبيق speech_to_text هنا)
      final recognizedText = await _simulateVoiceRecognition();

      if (recognizedText.isNotEmpty) {
        // البحث بالنص المحول
        final products = await searchProducts(searchQuery: recognizedText);

        // حفظ البحث الصوتي في التاريخ
        _addToSearchHistory('🎤 $recognizedText');

        return products;
      } else {
        throw Exception('لم يتم التعرف على أي كلمات');
      }
    } catch (e) {
      debugPrint('❌ خطأ في البحث الصوتي: $e');
      rethrow;
    }
  }

  /// البحث الذكي مع الفلترة المتقدمة
  Future<Map<String, dynamic>> advancedSmartSearch({
    required String query,
    List<String>? categories,
    List<String>? brands,
    double? minPrice,
    double? maxPrice,
    bool? inStock,
    String? sortBy,
    bool ascending = true,
    int limit = 20,
    int offset = 0,
  }) async {
    try {
      // البحث الأساسي
      final products = await searchProducts(
        searchQuery: query,
        categories: categories,
        brands: brands,
        minPrice: minPrice,
        maxPrice: maxPrice,
        inStock: inStock,
        sortBy: sortBy,
        ascending: ascending,
        limit: limit,
        offset: offset,
      );

      // الحصول على اقتراحات ذكية
      final suggestions = await _getSmartSuggestions(query);

      // البحث في الفئات ذات الصلة
      final relatedCategories = await _getRelatedCategories(query);

      // المنتجات المشابهة
      final similarProducts = await _getSimilarProducts(query);

      // تتبع البحث في قاعدة البيانات
      if (_authService.currentUser != null) {
        await _trackSearchInDatabase(query, _authService.currentUser!.id);
      }

      // الاقتراحات التلقائية
      final autoComplete = await _getAutoCompleteSuggestions(query);

      return {
        'products': products,
        'suggestions': suggestions,
        'related_categories': relatedCategories,
        'similar_products': similarProducts,
        'auto_complete': autoComplete,
        'query': query,
        'total_results': products.length,
        'search_metadata': {
          'categories_used': categories,
          'brands_used': brands,
          'price_range': {'min': minPrice, 'max': maxPrice},
          'in_stock_only': inStock,
          'sort_by': sortBy,
          'ascending': ascending,
        },
      };
    } catch (e) {
      debugPrint('❌ خطأ في البحث الذكي المتقدم: $e');
      return _getEmptySearchResult(query);
    }
  }

  /// حفظ البحث في المفضلة
  Future<void> saveSearch(String query, Map<String, dynamic> filters) async {
    try {
      final searchData = {
        'query': query,
        'filters': filters,
        'saved_at': DateTime.now().toIso8601String(),
      };

      final savedSearches = _storage.getStringList('saved_searches') ?? [];
      savedSearches.add(json.encode(searchData));
      _storage.setStringList('saved_searches', savedSearches);

      debugPrint('✅ تم حفظ البحث: $query');
    } catch (e) {
      debugPrint('❌ خطأ في حفظ البحث: $e');
    }
  }

  /// البحث الصوتي
  Future<List<ProductModel>> searchByVoice(String voiceQuery) async {
    try {
      // تحويل الصوت إلى نص (يجب تطبيق هذا)
      final textQuery = await _convertVoiceToText(voiceQuery);

      if (textQuery.isEmpty) return [];

      // البحث بالنص المحول
      final products = await searchProducts(searchQuery: textQuery);

      // تم إزالة تتبع البحث الصوتي لأن AnalyticsService غير متوفر

      return products;
    } catch (e) {
      debugPrint('Error in voice search: $e');
      return [];
    }
  }

  /// البحث الذكي مع اقتراحات
  Future<Map<String, dynamic>> smartSearch(String query) async {
    try {
      // البحث الأساسي
      final products = await searchProducts(searchQuery: query);

      // الحصول على اقتراحات
      final suggestions = await _getSearchSuggestions(query);

      // البحث في الفئات ذات الصلة
      final relatedCategories = await _getRelatedCategories(query);

      // المنتجات المشابهة
      final similarProducts = await _getSimilarProducts(query);

      return {
        'products': products,
        'suggestions': suggestions,
        'related_categories': relatedCategories,
        'similar_products': similarProducts,
        'query': query,
        'total_results': products.length,
      };
    } catch (e) {
      debugPrint('Error in smart search: $e');
      return {
        'products': <ProductModel>[],
        'suggestions': <String>[],
        'related_categories': <String>[],
        'similar_products': <ProductModel>[],
        'query': query,
        'total_results': 0,
      };
    }
  }

  /// الحصول على اقتراحات البحث
  Future<List<String>> getSearchSuggestions(String query) async {
    if (query.length < 2) return [];

    try {
      final response = await _supabase
          .from('smart_search_log')
          .select('query')
          .ilike('query', '%$query%')
          .order('created_at', ascending: false)
          .limit(10);

      final suggestions =
          (response as List)
              .map((item) => item['query'] as String)
              .where((suggestion) => suggestion != query)
              .toSet()
              .toList();

      return suggestions;
    } catch (e) {
      debugPrint('Error getting search suggestions: $e');
      return [];
    }
  }

  /// الحصول على عمليات البحث الشائعة
  Future<List<String>> getPopularSearches({int limit = 10}) async {
    try {
      final response = await _supabase
          .from('smart_search_log')
          .select('query, COUNT(*) as search_count')
          .gte(
            'created_at',
            DateTime.now().subtract(const Duration(days: 7)).toIso8601String(),
          )
          .order('search_count', ascending: false)
          .limit(limit);

      _popularSearches =
          (response as List).map((item) => item['query'] as String).toList();

      return _popularSearches;
    } catch (e) {
      debugPrint('Error getting popular searches: $e');
      return [];
    }
  }

  /// حفظ البحث في التاريخ
  void _addToSearchHistory(String query) {
    if (query.trim().isEmpty) return;

    _searchHistory.remove(query);
    _searchHistory.insert(0, query);

    if (_searchHistory.length > 20) {
      _searchHistory = _searchHistory.take(20).toList();
    }
  }

  /// تتبع البحث
  Future<void> _trackSearch(String query) async {
    if (query.trim().isEmpty) return;

    _addToSearchHistory(query);

    final userId = _authService.currentUser?.id;
    if (userId != null) {
      try {
        await _supabase.from('smart_search_log').insert({
          'user_id': userId,
          'query': query,
          'created_at': DateTime.now().toIso8601String(),
        });
      } catch (e) {
        debugPrint('Error tracking search: $e');
      }
    }
  }

  /// توليد مفتاح الكاش
  String _generateCacheKey(
    String query,
    List<String>? categories,
    List<String>? brands,
    double? minPrice,
    double? maxPrice,
    bool? inStock,
    String? sortBy,
    bool ascending,
  ) {
    return [
      query,
      categories?.join(',') ?? '',
      brands?.join(',') ?? '',
      minPrice?.toString() ?? '',
      maxPrice?.toString() ?? '',
      inStock?.toString() ?? '',
      sortBy ?? '',
      ascending.toString(),
    ].join('|');
  }

  /// تحليل الصورة (مثال مبسط - يمكن تطوير لاحقاً مع Clarifai)
  Future<List<String>> _analyzeImageWithClarifai(String imageUrl) async {
    // مثال مبسط لتحليل الصورة
    // في التطبيق الحقيقي يمكن استخدام Clarifai API
    return ['motorcycle', 'parts', 'engine'];
  }

  /// تحويل الصوت إلى نص (مثال مبسط - يمكن تطوير لاحقاً مع Speech-to-Text)
  Future<String> _convertVoiceToText(String voiceQuery) async {
    // مثال مبسط لتحويل الصوت إلى نص
    // في التطبيق الحقيقي يمكن استخدام Speech-to-Text API
    return voiceQuery;
  }

  /// الحصول على اقتراحات البحث الداخلية
  Future<List<String>> _getSearchSuggestions(String query) async {
    try {
      final response = await _supabase
          .from('products')
          .select('name')
          .ilike('name', '%$query%')
          .limit(5);

      return (response as List).map((item) => item['name'] as String).toList();
    } catch (e) {
      debugPrint('Error getting internal suggestions: $e');
      return [];
    }
  }

  /// مسح الكاش
  void clearCache() {
    _searchCache.clear();
  }

  /// مسح تاريخ البحث
  void clearSearchHistory() {
    _searchHistory.clear();
  }

  /// تتبع البحث بالصورة
  Future<void> _trackImageSearch(String query, List<String> keywords) async {
    try {
      final searchData = {
        'type': 'image_search',
        'query': query,
        'keywords': keywords,
        'timestamp': DateTime.now().toIso8601String(),
      };

      _addToSearchHistory('🖼️ $query');

      if (_authService.currentUser != null) {
        await _supabase.from('smart_search_log').insert({
          'user_id': _authService.currentUser?.id,
          'query': query,
          'search_type': 'image',
          'metadata': searchData,
          'created_at': DateTime.now().toIso8601String(),
        });
      }
    } catch (e) {
      debugPrint('❌ خطأ في تتبع البحث بالصورة: $e');
    }
  }

  /// محاكاة التعرف الصوتي
  Future<String> _simulateVoiceRecognition() async {
    // محاكاة التعرف الصوتي - يمكن استبدالها بـ speech_to_text
    await Future.delayed(const Duration(seconds: 2));
    return 'قطع غيار محرك'; // نص تجريبي
  }

  /// الحصول على اقتراحات ذكية من قاعدة البيانات
  Future<List<String>> _getSmartSuggestions(String query) async {
    try {
      final suggestions = <String>[];

      // اقتراحات من تاريخ البحث الشائع
      final popularSearches = await _supabase
          .from('search_analytics')
          .select('query, search_count')
          .ilike('query', '%$query%')
          .order('search_count', ascending: false)
          .limit(5);

      for (final search in popularSearches) {
        suggestions.add(search['query'] as String);
      }

      // اقتراحات من أسماء المنتجات
      final productSuggestions = await _supabase
          .from('products')
          .select('name')
          .ilike('name', '%$query%')
          .eq('is_active', true)
          .limit(5);

      for (final product in productSuggestions) {
        suggestions.add(product['name'] as String);
      }

      // اقتراحات من أسماء الفئات
      final categorySuggestions = await _supabase
          .from('categories')
          .select('name')
          .ilike('name', '%$query%')
          .eq('is_active', true)
          .limit(3);

      for (final category in categorySuggestions) {
        suggestions.add(category['name'] as String);
      }

      // إزالة التكرار وترتيب حسب الصلة
      return suggestions.toSet().take(10).toList();
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على الاقتراحات الذكية: $e');
      return [];
    }
  }

  /// الحصول على اقتراحات الإكمال التلقائي
  Future<List<String>> _getAutoCompleteSuggestions(String query) async {
    if (query.length < 2) return [];

    try {
      final response = await _supabase
          .from('products')
          .select('name, description')
          .or('name.ilike.%$query%,description.ilike.%$query%')
          .limit(5);

      final suggestions = <String>[];
      for (final item in response) {
        final name = item['name'] as String;
        final description = item['description'] as String?;

        if (name.toLowerCase().contains(query.toLowerCase())) {
          suggestions.add(name);
        }

        if (description != null &&
            description.toLowerCase().contains(query.toLowerCase())) {
          // استخراج الجملة التي تحتوي على الكلمة المفتاحية
          final sentences = description.split('.');
          for (final sentence in sentences) {
            if (sentence.toLowerCase().contains(query.toLowerCase())) {
              suggestions.add(sentence.trim());
              break;
            }
          }
        }
      }

      return suggestions.toSet().take(5).toList();
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على اقتراحات الإكمال التلقائي: $e');
      return [];
    }
  }

  /// الحصول على الفئات ذات الصلة من قاعدة البيانات
  Future<List<String>> _getRelatedCategories(String query) async {
    try {
      // البحث في الفئات التي تحتوي على الكلمة المفتاحية
      final categories = await _supabase
          .from('categories')
          .select('name')
          .or('name.ilike.%$query%,description.ilike.%$query%')
          .eq('is_active', true)
          .limit(5);

      return categories.map((cat) => cat['name'] as String).toList();
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على الفئات ذات الصلة: $e');
      return [];
    }
  }

  /// الحصول على المنتجات المشابهة من قاعدة البيانات
  Future<List<ProductModel>> _getSimilarProducts(String query) async {
    try {
      // البحث في المنتجات المشابهة
      final products = await _supabase
          .from('products')
          .select('*, categories(name), brands(name)')
          .or(
            'name.ilike.%$query%,description.ilike.%$query%,tags.ilike.%$query%',
          )
          .eq('is_active', true)
          .gt('stock_quantity', 0)
          .order('average_rating', ascending: false)
          .limit(5);

      return products.map((product) => ProductModel.fromJson(product)).toList();
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على المنتجات المشابهة: $e');
      return [];
    }
  }

  /// تتبع البحث في قاعدة البيانات
  Future<void> _trackSearchInDatabase(String query, String userId) async {
    try {
      // تسجيل البحث في جدول التحليلات
      await _supabase.from('search_analytics').upsert({
        'query': query.toLowerCase().trim(),
        'user_id': userId,
        'search_count': 1,
        'last_searched_at': DateTime.now().toIso8601String(),
      }, onConflict: 'query');

      // تحديث عداد البحث إذا كان موجوداً
      await _supabase.rpc(
        'increment_search_count',
        params: {'search_query': query.toLowerCase().trim()},
      );
    } catch (e) {
      debugPrint('❌ خطأ في تتبع البحث: $e');
    }
  }

  /// إرجاع نتيجة بحث فارغة
  Map<String, dynamic> _getEmptySearchResult(String query) {
    return {
      'products': <ProductModel>[],
      'suggestions': <String>[],
      'related_categories': <String>[],
      'similar_products': <ProductModel>[],
      'auto_complete': <String>[],
      'query': query,
      'total_results': 0,
      'search_metadata': {},
    };
  }

  /// الحصول على البحثات المحفوظة
  List<Map<String, dynamic>> getSavedSearches() {
    try {
      final savedSearches = _storage.getStringList('saved_searches') ?? [];
      return savedSearches
          .map((searchJson) => json.decode(searchJson) as Map<String, dynamic>)
          .toList();
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على البحثات المحفوظة: $e');
      return [];
    }
  }

  /// حذف بحث محفوظ
  Future<void> deleteSavedSearch(int index) async {
    try {
      final savedSearches = _storage.getStringList('saved_searches') ?? [];
      if (index >= 0 && index < savedSearches.length) {
        savedSearches.removeAt(index);
        _storage.setStringList('saved_searches', savedSearches);
      }
    } catch (e) {
      debugPrint('❌ خطأ في حذف البحث المحفوظ: $e');
    }
  }

  /// مسح جميع البحثات المحفوظة
  Future<void> clearSavedSearches() async {
    try {
      _savedSearches.clear();
      _storage.remove('saved_searches');
    } catch (e) {
      debugPrint('❌ خطأ في مسح البحثات المحفوظة: $e');
    }
  }

  // Getters
  List<String> get searchHistory => List.unmodifiable(_searchHistory);
  List<String> get popularSearches => List.unmodifiable(_popularSearches);
  List<String> get savedSearches => List.unmodifiable(_savedSearches);
}
