import 'package:flutter/material.dart';
import 'package:motorcycle_parts_shop/core/services/advertisement_service.dart';
import 'package:motorcycle_parts_shop/models/advertisement_model.dart';
import 'package:provider/provider.dart';

class AdvertisementManagementScreen extends StatefulWidget {
  const AdvertisementManagementScreen({super.key});

  @override
  State<AdvertisementManagementScreen> createState() =>
      _AdvertisementManagementScreenState();
}

class _AdvertisementManagementScreenState
    extends State<AdvertisementManagementScreen> {
  final _formKey = GlobalKey<FormState>();
  late AdvertisementService _advertisementService;
  List<AdvertisementModel> _advertisements = [];
  bool _isLoading = true;

  // متغيرات نموذج الإعلان
  String _title = '';
  String? _description;
  String _imageUrl = '';
  String? _targetUrl;
  bool _isActive = true;
  DateTime _startDate = DateTime.now();
  DateTime? _endDate;
  int _displayOrder = 0;

  @override
  void initState() {
    super.initState();
    _loadAdvertisements();
  }

  Future<void> _loadAdvertisements() async {
    _advertisementService = Provider.of<AdvertisementService>(
      context,
      listen: false,
    );
    try {
      final ads = await _advertisementService.getActiveAdvertisements();
      setState(() {
        _advertisements = ads;
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading ads: $e');
      setState(() => _isLoading = false);
    }
  }

  Future<void> _submitForm() async {
    if (!_formKey.currentState!.validate()) return;

    final newAd = AdvertisementModel(
      id: '', // سيتم توليده من قاعدة البيانات
      title: _title,
      description: _description,
      imageUrl: _imageUrl,
      targetUrl: _targetUrl,
      isActive: _isActive,
      startDate: _startDate,
      endDate: _endDate,
      displayOrder: _displayOrder,
      clickCount: 0, // تهيئة عدد النقرات بصفر للإعلان الجديد
      impressionCount: 0, // تهيئة عدد مرات الظهور بصفر للإعلان الجديد
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    try {
      await _advertisementService.createAdvertisement(newAd);
      await _loadAdvertisements();
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('تم إضافة الإعلان بنجاح')));
    } catch (e) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('فشل في الإضافة: $e')));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة الإعلانات'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showEditDialog(context, null),
          ),
        ],
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : ListView.builder(
                itemCount: _advertisements.length,
                itemBuilder: (context, index) {
                  final ad = _advertisements[index];
                  return ListTile(
                    title: Text(ad.title),
                    subtitle: Text(ad.startDate.toString()),
                    trailing: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        IconButton(
                          icon: const Icon(Icons.edit),
                          onPressed: () => _showEditDialog(context, ad),
                        ),
                        IconButton(
                          icon: const Icon(Icons.delete),
                          onPressed: () => _deleteAdvertisement(ad.id),
                        ),
                      ],
                    ),
                  );
                },
              ),
    );
  }

  Future<void> _showEditDialog(
    BuildContext context,
    AdvertisementModel? ad,
  ) async {
    if (ad != null) {
      _title = ad.title;
      _description = ad.description;
      _imageUrl = ad.imageUrl;
      _targetUrl = ad.targetUrl;
      _isActive = ad.isActive;
      _startDate = ad.startDate;
      _endDate = ad.endDate;
      _displayOrder = ad.displayOrder;
    }

    await showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: Text(ad == null ? 'إضافة إعلان جديد' : 'تعديل الإعلان'),
              content: SingleChildScrollView(
                child: Form(
                  key: _formKey,
                  child: Column(
                    children: [
                      TextFormField(
                        initialValue: _title,
                        decoration: const InputDecoration(labelText: 'العنوان'),
                        validator: (value) => value!.isEmpty ? 'مطلوب' : null,
                        onChanged: (value) => _title = value,
                      ),
                      TextFormField(
                        initialValue: _description,
                        decoration: const InputDecoration(labelText: 'الوصف'),
                        onChanged: (value) => _description = value,
                      ),
                      TextFormField(
                        initialValue: _imageUrl,
                        decoration: const InputDecoration(
                          labelText: 'رابط الصورة',
                        ),
                        validator: (value) => value!.isEmpty ? 'مطلوب' : null,
                        onChanged: (value) => _imageUrl = value,
                      ),
                      TextFormField(
                        initialValue: _targetUrl,
                        decoration: const InputDecoration(
                          labelText: 'رابط الهدف',
                        ),
                        onChanged: (value) => _targetUrl = value,
                      ),
                      SwitchListTile(
                        title: const Text('الحالة النشطة'),
                        value: _isActive,
                        onChanged: (value) => setState(() => _isActive = value),
                      ),
                      ListTile(
                        title: const Text('تاريخ البدء'),
                        subtitle: Text('${_startDate.toLocal()}'.split(' ')[0]),
                        onTap: () async {
                          final date = await showDatePicker(
                            context: context,
                            initialDate: _startDate,
                            firstDate: DateTime.now(),
                            lastDate: DateTime.now().add(
                              const Duration(days: 365),
                            ),
                          );
                          if (date != null) {
                            setState(() => _startDate = date);
                          }
                        },
                      ),
                      ListTile(
                        title: const Text('تاريخ الانتهاء'),
                        subtitle: Text(
                          _endDate == null
                              ? 'غير محدد'
                              : '${_endDate!.toLocal()}'.split(' ')[0],
                        ),
                        onTap: () async {
                          final date = await showDatePicker(
                            context: context,
                            initialDate: _endDate ?? _startDate,
                            firstDate: _startDate,
                            lastDate: DateTime.now().add(
                              const Duration(days: 730),
                            ),
                          );
                          if (date != null) {
                            setState(() => _endDate = date);
                          }
                        },
                      ),
                      TextFormField(
                        initialValue: _displayOrder.toString(),
                        decoration: const InputDecoration(
                          labelText: 'ترتيب العرض',
                        ),
                        keyboardType: TextInputType.number,
                        validator: (value) => value!.isEmpty ? 'مطلوب' : null,
                        onChanged:
                            (value) => _displayOrder = int.tryParse(value) ?? 0,
                      ),
                    ],
                  ),
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('إلغاء'),
                ),
                ElevatedButton(
                  onPressed: () {
                    if (_formKey.currentState!.validate()) {
                      _submitForm();
                      Navigator.pop(context);
                    }
                  },
                  child: const Text('حفظ'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  Future<void> _deleteAdvertisement(String id) async {
    try {
      await _advertisementService.deleteAdvertisement(id);
      await _loadAdvertisements();
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('تم الحذف بنجاح')));
    } catch (e) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('فشل في الحذف: $e')));
    }
  }
}
