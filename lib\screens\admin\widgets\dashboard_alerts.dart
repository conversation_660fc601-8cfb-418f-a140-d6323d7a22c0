import 'package:flutter/material.dart';

class DashboardAlerts extends StatelessWidget {
  const DashboardAlerts({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'التنبيهات والإشعارات',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                IconButton(
                  icon: const Icon(Icons.refresh), 
                  onPressed: () {
                    // تحديث قائمة الإشعارات
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('جاري تحديث الإشعارات...'),
                        duration: Duration(seconds: 1),
                      ),
                    );
                  }
                ),
              ],
            ),
            const SizedBox(height: 16),
            ListView(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              children: [
                _buildAlertTile(
                  context,
                  icon: Icons.warning,
                  color: Colors.red,
                  title: 'مخزون منخفض',
                  description: 'زيت المحرك - متبقي 5 قطع فقط',
                  time: 'منذ ساعة',
                ),
                const Divider(),
                _buildAlertTile(
                  context,
                  icon: Icons.shopping_cart,
                  color: Colors.green,
                  title: 'طلب جديد',
                  description: 'طلب جديد #1234 بقيمة 1500 ج.م',
                  time: 'منذ ساعتين',
                ),
                const Divider(),
                _buildAlertTile(
                  context,
                  icon: Icons.star,
                  color: Colors.orange,
                  title: 'تقييم جديد',
                  description: 'تقييم 5 نجوم لمنتج فلتر الزيت',
                  time: 'منذ 3 ساعات',
                ),
                const Divider(),
                _buildAlertTile(
                  context,
                  icon: Icons.error,
                  color: Colors.blue,
                  title: 'تحديث النظام',
                  description: 'تم تحديث أسعار 15 منتج',
                  time: 'منذ 4 ساعات',
                ),
              ],
            ),
            const SizedBox(height: 16),
            Center(
              child: TextButton(
                onPressed: () {
                  Navigator.pushNamed(context, '/admin/notifications');
                },
                child: const Text('عرض كل الإشعارات'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAlertTile(
    BuildContext context, {
    required IconData icon,
    required Color color,
    required String title,
    required String description,
    required String time,
  }) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(icon, color: color),
      ),
      title: Text(title, style: const TextStyle(fontWeight: FontWeight.bold)),
      subtitle: Text(description),
      trailing: Text(
        time,
        style: TextStyle(color: Colors.grey[600], fontSize: 12),
      ),
      onTap: () {
        // عرض تفاصيل الإشعار
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(title),
            content: Text(description),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إغلاق'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  // التنقل إلى الصفحة المناسبة حسب نوع الإشعار
                  if (title.contains('مخزون')) {
                    Navigator.pushNamed(context, '/admin/inventory');
                  } else if (title.contains('طلب')) {
                    Navigator.pushNamed(context, '/admin/orders');
                  } else if (title.contains('تقييم')) {
                    Navigator.pushNamed(context, '/admin/reviews');
                  }
                },
                child: const Text('عرض التفاصيل'),
              ),
            ],
          ),
        );
      },
    );
  }
}
