import 'package:flutter/material.dart';
import 'package:motorcycle_parts_shop/models/product_model.dart';
import 'wishlist_service.dart';

/// خدمة المفضلة (قائمة الرغبات) للتطبيق - محدثة لتستخدم WishlistService
class FavoritesService extends ChangeNotifier {
  static final FavoritesService _instance = FavoritesService._internal();
  final WishlistService _wishlistService = WishlistService();

  // نمط Singleton للتأكد من وجود نسخة واحدة فقط من الخدمة
  factory FavoritesService() {
    return _instance;
  }

  FavoritesService._internal() {
    // الاستماع لتغييرات WishlistService
    _wishlistService.addListener(_onWishlistChanged);
  }

  /// معالج تغييرات WishlistService
  void _onWishlistChanged() {
    notifyListeners();
  }

  // الحصول على قائمة المفضلة (تفويض إلى WishlistService)
  List<String> get favoriteIds =>
      _wishlistService.wishlistProducts.map((p) => p.id).toList();
  List<ProductModel> get favoriteProducts => _wishlistService.wishlistProducts;
  bool get isLoading => _wishlistService.isLoading;
  String? get error => _wishlistService.error;
  int get wishlistCount => _wishlistService.wishlistCount;

  /// تهيئة خدمة المفضلة (تفويض إلى WishlistService)
  Future<void> initialize() async {
    await _wishlistService.initialize();
  }

  /// التحقق مما إذا كان المنتج في المفضلة (تفويض إلى WishlistService)
  bool isFavorite(String productId) {
    return _wishlistService.isInWishlist(productId);
  }

  /// إضافة منتج إلى المفضلة (تفويض إلى WishlistService)
  Future<void> addToFavorites(String productId) async {
    await _wishlistService.addToWishlist(productId);
  }

  /// إزالة منتج من المفضلة (تفويض إلى WishlistService)
  Future<void> removeFromFavorites(String productId) async {
    await _wishlistService.removeFromWishlist(productId);
  }

  /// تبديل حالة المفضلة للمنتج (تفويض إلى WishlistService)
  Future<void> toggleFavorite(String productId) async {
    await _wishlistService.toggleWishlist(productId);
  }

  /// مسح جميع المفضلة (تفويض إلى WishlistService)
  Future<void> clearFavorites() async {
    await _wishlistService.clearWishlist();
  }

  /// إعادة تحميل قائمة الرغبات
  Future<void> refresh() async {
    await _wishlistService.refresh();
  }

  /// مسح الأخطاء
  void clearError() {
    _wishlistService.clearError();
  }

  /// الحصول على منتج من قائمة الرغبات بواسطة المعرف
  ProductModel? getWishlistProduct(String productId) {
    return _wishlistService.getWishlistProduct(productId);
  }

  /// تحديث منتج في قائمة الرغبات
  void updateWishlistProduct(ProductModel updatedProduct) {
    _wishlistService.updateWishlistProduct(updatedProduct);
  }

  @override
  void dispose() {
    _wishlistService.removeListener(_onWishlistChanged);
    super.dispose();
  }
}
