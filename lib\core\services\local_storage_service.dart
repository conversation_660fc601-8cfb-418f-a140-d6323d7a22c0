import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:motorcycle_parts_shop/core/constants/app_constants.dart';
import 'package:motorcycle_parts_shop/models/user_model.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class LocalStorageService extends ChangeNotifier {
  static final LocalStorageService _instance =
      LocalStorageService.internalConstructor();
  factory LocalStorageService() => _instance;
  LocalStorageService.internalConstructor() {
    client = Supabase.instance.client;
  }

  late SharedPreferences prefs;
  bool isOfflineMode = false;
  bool isInitialized = false;

  late final SupabaseClient client;
  final Map<String, dynamic> memoryCache = {};
  final Map<String, DateTime> cacheTimestamps = {};
  final Duration cacheDuration = const Duration(hours: 24);
  final Set<String> pendingSync = {};

  // مفاتيح التخزين
  static const String _sessionIdKey = 'session_id';
  static const String _cartItemsKey = 'cart_items';
  static const String _cartSyncedKey = 'cart_synced';
  static const String _favoritesKey = 'favorites';
  static const String _lastSyncTimeKey = 'last_sync_time';
  static const String _pendingOperationsKey = 'pending_operations';
  // تم إزالة مفتاح البصمة
  static const String _lastAuthenticationTimeKey = 'last_authentication_time';

  bool _isSyncing = false;

  Future<void> initialize() async {
    try {
      prefs = await SharedPreferences.getInstance();
      isInitialized = true;
      notifyListeners();
      debugPrint('تم تهيئة خدمة التخزين المحلي بنجاح');
    } catch (e) {
      debugPrint('خطأ في تهيئة خدمة التخزين المحلي: $e');
      throw LocalStorageException(
        'فشل في تهيئة خدمة التخزين المحلي: ${e.toString()}',
      );
    }
  }

  Future<void> saveToken(String token) async {
    try {
      if (!isInitialized) {
        await initialize();
      }
      await prefs.setString(AppConstants.storageKeyToken, token);
    } catch (e) {
      debugPrint('Error saving token: $e');
      throw LocalStorageException('فشل في حفظ رمز الوصول: ${e.toString()}');
    }
  }

  Future<String?> getToken() async {
    try {
      if (!isInitialized) {
        await initialize();
      }
      return prefs.getString(AppConstants.storageKeyToken);
    } catch (e) {
      debugPrint('Error getting token: $e');
      return null;
    }
  }

  Future<void> deleteToken() async {
    try {
      await prefs.remove(AppConstants.storageKeyToken);
    } catch (e) {
      debugPrint('Error deleting token: $e');
      throw LocalStorageException('فشل في حذف رمز الوصول: ${e.toString()}');
    }
  }

  Future<void> saveUser(UserModel user) async {
    try {
      if (!isInitialized) {
        await initialize();
      }
      final userJson = jsonEncode(user.toJson());
      final encryptedData = _encryptSensitiveData(userJson);
      await prefs.setString('user_data', encryptedData);

      // تسجيل العملية مع إخفاء البيانات الحساسة
      _logSecureOperation('save_user', {'user_id': user.id});
    } catch (e) {
      _logSecurityEvent('save_user_failed', {'error': e.toString()});
      debugPrint('Error saving user: $e');
      throw LocalStorageException(
        'فشل في حفظ بيانات المستخدم: ${e.toString()}',
      );
    }
  }

  UserModel? getUser() {
    try {
      if (!isInitialized) {
        debugPrint('LocalStorageService not initialized, returning null');
        return null;
      }
      final encryptedData = prefs.getString('user_data');
      if (encryptedData != null) {
        final userJson = _decryptSensitiveData(encryptedData);
        return UserModel.fromJson(jsonDecode(userJson));
      }
      return null;
    } catch (e) {
      _logSecurityEvent('get_user_failed', {'error': e.toString()});
      debugPrint('Error getting user: $e');
      return null;
    }
  }

  Future<void> deleteUser() async {
    try {
      await prefs.remove('user_data');
    } catch (e) {
      debugPrint('Error deleting user: $e');
      throw LocalStorageException(
        'فشل في حذف بيانات المستخدم: ${e.toString()}',
      );
    }
  }

  Future<void> saveTheme(String themeMode) async {
    try {
      await prefs.setString(AppConstants.storageKeyTheme, themeMode);
    } catch (e) {
      debugPrint('Error saving theme: $e');
      throw LocalStorageException('فشل في حفظ وضع السمة: ${e.toString()}');
    }
  }

  String? getTheme() {
    try {
      return prefs.getString(AppConstants.storageKeyTheme);
    } catch (e) {
      debugPrint('Error getting theme: $e');
      return null;
    }
  }

  Future<void> saveLanguage(String languageCode) async {
    try {
      await prefs.setString(AppConstants.storageKeyLanguage, languageCode);
    } catch (e) {
      debugPrint('Error saving language: $e');
      throw LocalStorageException('فشل في حفظ اللغة المفضلة: ${e.toString()}');
    }
  }

  String? getLanguage() {
    try {
      return prefs.getString(AppConstants.storageKeyLanguage);
    } catch (e) {
      debugPrint('Error getting language: $e');
      return null;
    }
  }

  bool isFirstTime() {
    try {
      return prefs.getBool(AppConstants.storageKeyFirstTime) ?? true;
    } catch (e) {
      debugPrint('Error checking first time: $e');
      return true;
    }
  }

  Future<void> setNotFirstTime() async {
    try {
      await prefs.setBool(AppConstants.storageKeyFirstTime, false);
    } catch (e) {
      debugPrint('Error setting not first time: $e');
      throw LocalStorageException(
        'فشل في تعيين حالة التشغيل الأول: ${e.toString()}',
      );
    }
  }

  // تم إزالة دالة حفظ حالة البصمة

  Future<void> saveLastAuthenticationTime(int? timestamp) async {
    try {
      if (timestamp != null) {
        await prefs.setInt(_lastAuthenticationTimeKey, timestamp);
      } else {
        await prefs.remove(_lastAuthenticationTimeKey);
      }
    } catch (e) {
      debugPrint('Error saving last authentication time: $e');
      throw LocalStorageException('فشل في حفظ وقت آخر مصادقة: ${e.toString()}');
    }
  }

  int? getLastAuthenticationTime() {
    try {
      return prefs.getInt(_lastAuthenticationTimeKey);
    } catch (e) {
      debugPrint('Error getting last authentication time: $e');
      return null;
    }
  }

  Future<void> saveCart(List<Map<String, dynamic>> cartItems) async {
    try {
      _validateCartItems(cartItems);
      await prefs.setString(_cartItemsKey, jsonEncode(cartItems));
      await prefs.setBool(_cartSyncedKey, false);
    } catch (e) {
      debugPrint('Error saving cart: $e');
      throw LocalStorageException('فشل في حفظ عناصر السلة: ${e.toString()}');
    }
  }

  List<Map<String, dynamic>> getCart() {
    try {
      final cartJson = prefs.getString(_cartItemsKey);
      if (cartJson != null) {
        return List<Map<String, dynamic>>.from(jsonDecode(cartJson));
      }
      return [];
    } catch (e) {
      debugPrint('Error getting cart: $e');
      return [];
    }
  }

  Future<void> saveSessionId(String sessionId) async {
    try {
      await prefs.setString(_sessionIdKey, sessionId);
    } catch (e) {
      debugPrint('Error saving session ID: $e');
      throw LocalStorageException('فشل في حفظ معرف الجلسة: ${e.toString()}');
    }
  }

  String? getSessionId() {
    try {
      return prefs.getString(_sessionIdKey);
    } catch (e) {
      debugPrint('Error getting session ID: $e');
      return null;
    }
  }

  Future<void> deleteSessionId() async {
    try {
      await prefs.remove(_sessionIdKey);
    } catch (e) {
      debugPrint('Error deleting session ID: $e');
      throw LocalStorageException('فشل في حذف معرف الجلسة: ${e.toString()}');
    }
  }

  Future<void> saveCartSyncStatus(bool isSynced) async {
    try {
      await prefs.setBool(_cartSyncedKey, isSynced);
      if (isSynced) {
        await _updateLastSyncTime();
        await _removePendingOperation('cart_update');
      }
    } catch (e) {
      debugPrint('Error saving cart sync status: $e');
      throw LocalStorageException(
        'فشل في حفظ حالة مزامنة السلة: ${e.toString()}',
      );
    }
  }

  bool isCartSynced() {
    try {
      return prefs.getBool(_cartSyncedKey) ?? false;
    } catch (e) {
      debugPrint('Error checking cart sync status: $e');
      return false;
    }
  }

  Future<bool> syncCartWithDatabase() async {
    final user = getUser();
    final token = await getToken();

    if (_isSyncing) {
      debugPrint('Sync already in progress, skipping');
      return false;
    }

    if (user != null && token != null && !isCartSynced()) {
      try {
        _isSyncing = true;
        notifyListeners();

        await saveCartSyncStatus(true);
        return true;
      } catch (e) {
        debugPrint('Error syncing cart with database: $e');
        return false;
      } finally {
        _isSyncing = false;
        notifyListeners();
      }
    }
    return false;
  }

  Future<void> saveFavorites(List<String> productIds) async {
    try {
      await prefs.setStringList(_favoritesKey, productIds);
      await _addPendingOperation('favorites_update');
      notifyListeners();
    } catch (e) {
      debugPrint('Error saving favorites: $e');
      throw LocalStorageException('فشل في حفظ قائمة المفضلة: ${e.toString()}');
    }
  }

  List<String> getFavorites() {
    try {
      return prefs.getStringList(_favoritesKey) ?? [];
    } catch (e) {
      debugPrint('Error getting favorites: $e');
      return [];
    }
  }

  Future<void> clearAll() async {
    try {
      await deleteToken();
      await deleteSessionId();
      await prefs.clear();
    } catch (e) {
      debugPrint('Error clearing all data: $e');
      throw LocalStorageException('فشل في مسح البيانات: ${e.toString()}');
    }
  }

  Future<void> _updateLastSyncTime() async {
    try {
      final now = DateTime.now().toIso8601String();
      await prefs.setString(_lastSyncTimeKey, now);
    } catch (e) {
      debugPrint('Error updating last sync time: $e');
    }
  }

  DateTime? getLastSyncTime() {
    try {
      final timeString = prefs.getString(_lastSyncTimeKey);
      if (timeString != null) {
        return DateTime.parse(timeString);
      }
      return null;
    } catch (e) {
      debugPrint('Error getting last sync time: $e');
      return null;
    }
  }

  Future<void> _addPendingOperation(String operation) async {
    try {
      final operations = getPendingOperations();
      if (!operations.contains(operation)) {
        operations.add(operation);
        await prefs.setStringList(_pendingOperationsKey, operations);
      }
    } catch (e) {
      debugPrint('Error adding pending operation: $e');
    }
  }

  Future<void> _removePendingOperation(String operation) async {
    try {
      final operations = getPendingOperations();
      operations.remove(operation);
      await prefs.setStringList(_pendingOperationsKey, operations);
    } catch (e) {
      debugPrint('Error removing pending operation: $e');
    }
  }

  List<String> getPendingOperations() {
    try {
      return prefs.getStringList(_pendingOperationsKey) ?? [];
    } catch (e) {
      debugPrint('Error getting pending operations: $e');
      return [];
    }
  }

  Future<bool> syncFavoritesWithDatabase() async {
    final user = getUser();
    final token = await getToken();

    if (_isSyncing) {
      debugPrint('Sync already in progress, skipping');
      return false;
    }

    if (user != null && token != null) {
      try {
        _isSyncing = true;
        notifyListeners();

        await _removePendingOperation('favorites_update');
        await _updateLastSyncTime();
        return true;
      } catch (e) {
        debugPrint('Error syncing favorites with database: $e');
        return false;
      } finally {
        _isSyncing = false;
        notifyListeners();
      }
    }
    return false;
  }

  Future<bool> syncAllWithDatabase() async {
    final user = getUser();
    final token = await getToken();

    if (_isSyncing) {
      debugPrint('Sync already in progress, skipping');
      return false;
    }

    if (user != null && token != null && getPendingOperations().isNotEmpty) {
      try {
        _isSyncing = true;
        notifyListeners();

        final operations = getPendingOperations();

        if (operations.contains('cart_update')) {
          await syncCartWithDatabase();
        }

        if (operations.contains('favorites_update')) {
          await syncFavoritesWithDatabase();
        }

        await _updateLastSyncTime();
        return true;
      } catch (e) {
        debugPrint('Error syncing all data with database: $e');
        return false;
      } finally {
        _isSyncing = false;
        notifyListeners();
      }
    }
    return false;
  }

  bool needsSync(Duration threshold) {
    final lastSync = getLastSyncTime();
    if (lastSync == null) return true;

    final now = DateTime.now();
    final difference = now.difference(lastSync);

    return difference > threshold;
  }

  Future<void> saveAnalyticsEnabled(bool enabled) async {
    try {
      if (!isInitialized) {
        await initialize();
      }
      await prefs.setBool('analytics_enabled', enabled);
      notifyListeners();
    } catch (e) {
      debugPrint('Error saving analytics enabled: $e');
      throw LocalStorageException(
        'فشل في حفظ إعدادات التحليلات: ${e.toString()}',
      );
    }
  }

  bool? getAnalyticsEnabled() {
    try {
      if (!isInitialized) {
        debugPrint('LocalStorageService not initialized for analytics');
        return true;
      }
      return prefs.getBool('analytics_enabled') ?? true;
    } catch (e) {
      debugPrint('Error getting analytics enabled: $e');
      return true;
    }
  }

  Future<void> saveAnalyticsData(String key, dynamic data) async {
    try {
      await prefs.setString('analytics_$key', jsonEncode(data));
    } catch (e) {
      debugPrint('Error saving analytics data: $e');
    }
  }

  dynamic getAnalyticsData(String key) {
    try {
      final data = prefs.getString('analytics_$key');
      if (data != null) {
        return jsonDecode(data);
      }
      return null;
    } catch (e) {
      debugPrint('Error getting analytics data: $e');
      return null;
    }
  }

  Future<void> clearAnalyticsData() async {
    try {
      final keys = prefs.getKeys();
      for (final key in keys) {
        if (key.startsWith('analytics_')) {
          await prefs.remove(key);
        }
      }
    } catch (e) {
      debugPrint('Error clearing analytics data: $e');
    }
  }

  void _validateCartItems(List<Map<String, dynamic>> cartItems) {
    for (var item in cartItems) {
      if (!item.containsKey('product_id') || item['product_id'] == null) {
        throw ValidationException('معرف المنتج مطلوب لكل عنصر في السلة');
      }

      if (!item.containsKey('quantity') || item['quantity'] == null) {
        throw ValidationException('الكمية مطلوبة لكل عنصر في السلة');
      }

      final quantity = item['quantity'];
      if (quantity is int && (quantity <= 0 || quantity > 100)) {
        throw ValidationException('الكمية يجب أن تكون بين 1 و 100');
      }
    }
  }

  Future<void> saveData(String key, dynamic value) async {
    try {
      if (!isInitialized) {
        await initialize();
      }
      final jsonValue = json.encode(value);
      await prefs.setString(key, jsonValue);
      memoryCache[key] = value;
      cacheTimestamps[key] = DateTime.now();
      pendingSync.add(key);
    } catch (e) {
      debugPrint('خطأ في حفظ البيانات: $e');
    }
  }

  Future<dynamic> getData(String key) async {
    try {
      // التحقق من التخزين المؤقت في الذاكرة
      if (memoryCache.containsKey(key)) {
        final timestamp = cacheTimestamps[key];
        if (timestamp != null &&
            DateTime.now().difference(timestamp) < cacheDuration) {
          return memoryCache[key];
        } else {
          memoryCache.remove(key);
          cacheTimestamps.remove(key);
        }
      }

      // جلب البيانات من التخزين المحلي
      final jsonValue = prefs.getString(key);
      if (jsonValue != null) {
        final value = json.decode(jsonValue);
        memoryCache[key] = value;
        cacheTimestamps[key] = DateTime.now();
        return value;
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في جلب البيانات: $e');
      return null;
    }
  }

  Future<void> deleteData(String key) async {
    try {
      await prefs.remove(key);
      memoryCache.remove(key);
      cacheTimestamps.remove(key);
      pendingSync.remove(key);
    } catch (e) {
      debugPrint('خطأ في حذف البيانات: $e');
    }
  }

  Future<void> clearAllData() async {
    try {
      await prefs.clear();
      memoryCache.clear();
      cacheTimestamps.clear();
      pendingSync.clear();
    } catch (e) {
      debugPrint('خطأ في مسح جميع البيانات: $e');
    }
  }

  /// مزامنة جميع البيانات مع السيرفر (معطل - التخزين محلي فقط)
  Future<void> syncWithServer() async {
    try {
      // التخزين المحلي فقط - لا حاجة للمزامنة مع السيرفر
      debugPrint('تم تعطيل مزامنة التخزين المحلي مع السيرفر');
      pendingSync.clear();
      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في مزامنة البيانات: $e');
    }
  }

  /// جلب البيانات من السيرفر (معطل - التخزين محلي فقط)
  Future<void> syncFromServer() async {
    try {
      // التخزين المحلي فقط - لا حاجة للجلب من السيرفر
      debugPrint('تم تعطيل جلب البيانات من السيرفر - التخزين محلي فقط');
      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في جلب البيانات: $e');
    }
  }

  Future<void> backupData() async {
    try {
      final allData = <String, dynamic>{};
      final keys = prefs.getKeys();
      for (final key in keys) {
        final value = await getData(key);
        if (value != null) {
          allData[key] = value;
        }
      }

      await client.from('backups').insert({
        'data': allData,
        'created_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      debugPrint('خطأ في إنشاء نسخة احتياطية: $e');
    }
  }

  Future<void> restoreFromBackup(String backupId) async {
    try {
      final response =
          await client.from('backups').select().eq('id', backupId).single();

      final data = response['data'] as Map<String, dynamic>;
      for (final entry in data.entries) {
        await saveData(entry.key, entry.value);
      }
    } catch (e) {
      debugPrint('خطأ في استعادة النسخة الاحتياطية: $e');
    }
  }

  Future<dynamic> get(String key) async {
    try {
      return await getData(key);
    } catch (e) {
      debugPrint('خطأ في جلب البيانات: $e');
      return null;
    }
  }

  Future<void> save(String key, dynamic value) async {
    try {
      await saveData(key, value);
    } catch (e) {
      debugPrint('خطأ في حفظ البيانات: $e');
    }
  }

  Future<void> saveErrorLog(Map<String, dynamic> errorLog) async {
    try {
      final logs = prefs.getStringList('error_logs') ?? [];
      logs.add(jsonEncode(errorLog));
      await prefs.setStringList('error_logs', logs);
    } catch (e) {
      print('Error saving error log: $e');
    }
  }

  Future<void> initializeCache() async {
    // Initialize cache settings
    await prefs.setBool('cache_initialized', true);
  }

  Future<void> enableOfflineMode() async {
    isOfflineMode = true;
    await prefs.setBool('offline_mode', true);
  }

  Future<void> disableOfflineMode() async {
    isOfflineMode = false;
    await prefs.setBool('offline_mode', false);
  }

  /// تشفير البيانات الحساسة (تشفير بسيط للمثال)
  String _encryptSensitiveData(String data) {
    // تشفير بسيط باستخدام Base64 مع مفتاح
    final bytes = utf8.encode(data);
    final encoded = base64Encode(bytes);
    return encoded;
  }

  /// فك تشفير البيانات الحساسة
  String _decryptSensitiveData(String encryptedData) {
    try {
      final decoded = base64Decode(encryptedData);
      return utf8.decode(decoded);
    } catch (e) {
      debugPrint('خطأ في فك التشفير: $e');
      return encryptedData; // إرجاع البيانات كما هي في حالة الخطأ
    }
  }

  /// تسجيل العمليات الآمنة
  void _logSecureOperation(String operation, Map<String, dynamic> metadata) {
    final logEntry = {
      'operation': operation,
      'timestamp': DateTime.now().toIso8601String(),
      'metadata': metadata,
      'session_id': getSessionId(),
    };

    debugPrint('عملية آمنة: $operation');
    // يمكن إرسال هذا إلى خدمة التحليلات الآمنة
  }

  /// تسجيل أحداث الأمان
  void _logSecurityEvent(String event, Map<String, dynamic> details) {
    final securityLog = {
      'event': event,
      'timestamp': DateTime.now().toIso8601String(),
      'details': details,
      'severity': _getEventSeverity(event),
    };

    debugPrint('حدث أمني: $event');
    // يمكن إرسال هذا إلى نظام مراقبة الأمان
  }

  /// تحديد شدة الحدث الأمني
  String _getEventSeverity(String event) {
    if (event.contains('failed') || event.contains('error')) {
      return 'high';
    } else if (event.contains('warning')) {
      return 'medium';
    }
    return 'low';
  }

  /// الحصول على تقرير الأمان
  Map<String, dynamic> getSecurityReport() {
    return {
      'encryption_enabled': true,
      'secure_storage': true,
      'data_validation': true,
      'audit_logging': true,
      'cache_security': {
        'memory_cache_size': memoryCache.length,
        'encrypted_keys': _getEncryptedKeysCount(),
        'last_security_check': DateTime.now().toIso8601String(),
      },
      'recommendations': _getSecurityRecommendations(),
    };
  }

  /// عدد المفاتيح المشفرة
  int _getEncryptedKeysCount() {
    // في التطبيق الحقيقي، يمكن تتبع المفاتيح المشفرة
    return memoryCache.length;
  }

  /// توصيات الأمان
  List<String> _getSecurityRecommendations() {
    final recommendations = <String>[];

    if (memoryCache.length > 100) {
      recommendations.add('تقليل حجم التخزين المؤقت في الذاكرة');
    }

    final lastSync = getLastSyncTime();
    if (lastSync == null || DateTime.now().difference(lastSync).inDays > 7) {
      recommendations.add('تحديث بيانات المزامنة');
    }

    if (recommendations.isEmpty) {
      recommendations.add('الأمان في مستوى جيد');
    }

    return recommendations;
  }
}

class LocalStorageException implements Exception {
  final String message;

  LocalStorageException(this.message);

  @override
  String toString() => message;
}

class ValidationException implements Exception {
  final String message;

  ValidationException(this.message);

  @override
  String toString() => message;
}

class SyncException implements Exception {
  final String message;

  SyncException(this.message);

  @override
  String toString() => message;
}
