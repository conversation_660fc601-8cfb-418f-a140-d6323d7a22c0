import 'package:flutter/material.dart';

/// Widget يعمل كحد فاصل للأخطاء (Error Boundary) في التطبيق.
/// يقوم بمعالجة الأخطاء التي تحدث في واجهة المستخدم ويظهر واجهة بديلة عند حدوث خطأ.
class ErrorBoundary extends StatelessWidget {
  final Widget child; // الـ Widget الذي يتم تغليفه داخل حد الأخطاء
  final Widget Function(BuildContext, Object)? errorBuilder; // دالة لبناء واجهة الخطأ المخصصة

  const ErrorBoundary({super.key, required this.child, this.errorBuilder});

  @override
  Widget build(BuildContext context) {
    // تعريف كيفية عرض الأخطاء في التطبيق
    ErrorWidget.builder = (FlutterErrorDetails details) {
      return Container(
        padding: const EdgeInsets.all(24.0),
        margin: const EdgeInsets.all(16.0),
        decoration: BoxDecoration(
          color: Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child:
            // استخدام واجهة الخطأ المخصصة إذا تم توفيرها، أو عرض الواجهة الافتراضية
            errorBuilder?.call(context, details.exception) ??
            Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // أيقونة الخطأ
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.error.withOpacity(0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.error_outline_rounded,
                    color: Theme.of(context).colorScheme.error,
                    size: 48,
                  ),
                ),
                const SizedBox(height: 24),
                // نص عنوان الخطأ
                Text(
                  'عذراً، حدث خطأ ما',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: Theme.of(context).colorScheme.error,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 12),
                // نص وصف الخطأ
                Text(
                  'نعتذر عن هذا الخطأ، يرجى المحاولة مرة أخرى',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).textTheme.bodySmall?.color,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                // زر العودة
                ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  style: ElevatedButton.styleFrom(
                    minimumSize: const Size(200, 48),
                  ),
                  child: const Text('العودة'),
                ),
              ],
            ),
      );
    };
    return child;
  }
}

/// Widget آخر يعمل كحد فاصل للأخطاء مع إمكانية إعادة المحاولة.
/// يقوم بحفظ حالة الخطأ وإعادة بناء الواجهة عند الضغط على زر إعادة المحاولة.
class ErrorBoundaryWrapper extends StatefulWidget {
  final Widget child; // الـ Widget الذي يتم تغليفه داخل حد الأخطاء
  final Widget Function(BuildContext, Object)? errorBuilder; // دالة لبناء واجهة الخطأ المخصصة

  const ErrorBoundaryWrapper({
    super.key,
    required this.child,
    this.errorBuilder,
  });

  @override
  State<ErrorBoundaryWrapper> createState() => _ErrorBoundaryWrapperState();
}

class _ErrorBoundaryWrapperState extends State<ErrorBoundaryWrapper> {
  Object? _error; // حالة الخطأ

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    // إذا كان هناك خطأ، يتم عرض واجهة الخطأ
    if (_error != null) {
      return widget.errorBuilder?.call(context, _error!) ??
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // أيقونة الخطأ
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.error.withOpacity(0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.error_outline,
                    color: Theme.of(context).colorScheme.error,
                    size: 48,
                  ),
                ),
                const SizedBox(height: 16),
                // نص عنوان الخطأ
                Text(
                  'عذراً، حدث خطأ ما',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: Theme.of(context).colorScheme.error,
                  ),
                ),
                const SizedBox(height: 8),
                // نص وصف الخطأ
                Text(
                  'يرجى المحاولة مرة أخرى',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).textTheme.bodySmall?.color,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                // زر إعادة المحاولة
                ElevatedButton(
                  onPressed: () {
                    setState(() {
                      _error = null; // إعادة تعيين حالة الخطأ
                    });
                  },
                  style: ElevatedButton.styleFrom(
                    minimumSize: const Size(180, 48),
                  ),
                  child: const Text('إعادة المحاولة'),
                ),
              ],
            ),
          );
    }

    // إذا لم يكن هناك خطأ، يتم عرض الـ Widget الأصلي
    return widget.child;
  }
}