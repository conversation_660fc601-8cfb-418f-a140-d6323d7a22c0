
// استيراد حزمة فلاتر الأساسية لإنشاء واجهات المستخدم
import 'package:flutter/material.dart';
// استيراد مكتبة الرياضيات لاستخدام وظائف مثل max و min
import 'dart:math' as math;

/// كائن مساعد محسّن للرسوم المتحركة يوفر تأثيرات احترافية للتطبيق
/// يتضمن انتقالات صفحات مخصصة، رسوم متحركة متدرجة، وتفاعلات مرئية
class EnhancedAnimationHelper {
  /// ينشئ رسمًا متحركًا متدرجًا لقائمة من العناصر
  /// [position] هو موقع العنصر في القائمة
  /// [staggerDuration] هو المدة بين كل رسم متحرك
  static Animation<double> staggeredAnimation({
    required AnimationController controller, // وحدة التحكم في الرسوم
    required int position, // موقع العنصر في القائمة
    Duration staggerDuration = const Duration(milliseconds: 50), // مدة التأخير بين العناصر
  }) {
    final double start = math.max(0, position * staggerDuration.inMilliseconds / controller.duration!.inMilliseconds);
    final double end = math.min(1.0, start + 0.5); // الرسم يأخذ حتى 50% من المدة الكلية
    
    return Tween<double>(
      begin: 0.0, // قيمة البداية
      end: 1.0, // قيمة النهاية
    ).animate(CurvedAnimation(
      parent: controller,
      curve: Interval(start, end, curve: Curves.easeOutCubic), // منحنى التسارع
    ));
  }

  /// ينشئ رسمًا متحركًا للتلاشي
  static Widget fadeIn({
    required Widget child, // العنصر المراد تطبيق التأثير عليه
    required Animation<double> animation, // الرسم المتحرك
    Offset? offset, // الإزاحة الاختيارية للحركة
  }) {
    return AnimatedBuilder(
      animation: animation,
      builder: (context, child) {
        return Opacity(
          opacity: animation.value, // الشفافية بناءً على قيمة الرسم
          child: Transform.translate(
            offset: offset != null 
                ? Offset(offset.dx * (1 - animation.value), offset.dy * (1 - animation.value))
                : Offset(0, 20 * (1 - animation.value)), // إزاحة افتراضية لأسفل
            child: child,
          ),
        );
      },
      child: child,
    );
  }

  /// ينشئ رسمًا متحركًا لتغيير الحجم
  static Widget scale({
    required Widget child,
    required Animation<double> animation,
    Alignment alignment = Alignment.center, // نقطة مركز التغيير
  }) {
    return AnimatedBuilder(
      animation: animation,
      builder: (context, child) {
        return Transform.scale(
          scale: 0.8 + (0.2 * animation.value), // التغيير من 0.8 إلى 1.0
          alignment: alignment,
          child: Opacity(
            opacity: animation.value, // الشفافية مع التغيير
            child: child,
          ),
        );
      },
      child: child,
    );
  }

  /// ينشئ رسمًا متحركًا للانزلاق
  static Widget slide({
    required Widget child,
    required Animation<double> animation,
    Offset beginOffset = const Offset(0.0, 0.2), // نقطة البداية للإزاحة
  }) {
    return AnimatedBuilder(
      animation: animation,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(
            beginOffset.dx * (1 - animation.value),
            beginOffset.dy * (1 - animation.value),
          ),
          child: child,
        );
      },
      child: child,
    );
  }

  /// ينشئ انتقالًا مخصصًا لمسار الصفحة
  static PageRouteBuilder<T> pageRouteBuilder<T>({
    required Widget page, // الصفحة المراد عرضها
    RouteSettings? settings, // إعدادات المسار اختيارية
    Duration duration = const Duration(milliseconds: 300), // مدة الانتقال
    PageTransitionType transitionType = PageTransitionType.rightToLeft, // نوع الانتقال الافتراضي
  }) {
    return PageRouteBuilder<T>(
      settings: settings,
      transitionDuration: duration,
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        final curvedAnimation = CurvedAnimation(
          parent: animation,
          curve: Curves.easeOutCubic, // منحنى التسارع للدخول
          reverseCurve: Curves.easeInCubic, // منحنى التسارع للخروج
        );

        switch (transitionType) {
          case PageTransitionType.fade:
            return FadeTransition(opacity: curvedAnimation, child: child); // انتقال تلاشي
          case PageTransitionType.rightToLeft:
            return SlideTransition(
              position: Tween<Offset>(begin: const Offset(1, 0), end: Offset.zero)
                  .animate(curvedAnimation),
              child: child,
            ); // انتقال من اليمين إلى اليسار
          case PageTransitionType.leftToRight:
            return SlideTransition(
              position: Tween<Offset>(begin: const Offset(-1, 0), end: Offset.zero)
                  .animate(curvedAnimation),
              child: child,
            ); // انتقال من اليسار إلى اليمين
          case PageTransitionType.topToBottom:
            return SlideTransition(
              position: Tween<Offset>(begin: const Offset(0, -1), end: Offset.zero)
                  .animate(curvedAnimation),
              child: child,
            ); // انتقال من الأعلى إلى الأسفل
          case PageTransitionType.bottomToTop:
            return SlideTransition(
              position: Tween<Offset>(begin: const Offset(0, 1), end: Offset.zero)
                  .animate(curvedAnimation),
              child: child,
            ); // انتقال من الأسفل إلى الأعلى
          case PageTransitionType.scale:
            return ScaleTransition(
              scale: Tween<double>(begin: 0.8, end: 1.0).animate(curvedAnimation),
              child: FadeTransition(opacity: curvedAnimation, child: child),
            ); // انتقال تغيير الحجم مع التلاشي
          case PageTransitionType.rotate:
            return RotationTransition(
              turns: Tween<double>(begin: 0.1, end: 0.0).animate(curvedAnimation),
              child: FadeTransition(opacity: curvedAnimation, child: child),
            ); // انتقال دوران مع التلاشي
          case PageTransitionType.scaleWithFadeOut:
            return FadeTransition(
              opacity: curvedAnimation,
              child: ScaleTransition(
                scale: Tween<double>(begin: 0.9, end: 1.0).animate(curvedAnimation),
                child: child,
              ),
            ); // انتقال تغيير الحجم مع تلاشي خارجي
        }
      },
    );
  }

  /// ينشئ رسمًا متحركًا لعنصر Hero مع انتقال تلاشي
  static Widget heroWithFade({
    required String tag, // المعرف الخاص بالعنصر
    required Widget child, // العنصر المراد تطبيق التأثير عليه
  }) {
    return Hero(
      tag: tag,
      flightShuttleBuilder: (BuildContext flightContext,
          Animation<double> animation,
          HeroFlightDirection flightDirection,
          BuildContext fromHeroContext,
          BuildContext toHeroContext) {
        return AnimatedBuilder(
          animation: animation,
          builder: (context, _) {
            return Opacity(
              opacity: flightDirection == HeroFlightDirection.push
                  ? animation.value
                  : 1 - animation.value, // الشفافية بناءً على اتجاه الانتقال
              child: fromHeroContext.widget,
            );
          },
        );
      },
      child: child,
    );
  }

  /// ينشئ تأثير تموج عند النقر
  static Widget rippleEffect({
    required Widget child,
    required VoidCallback onTap, // الإجراء عند النقر
    Color? splashColor, // لون التموج
    Color? highlightColor, // لون الإبراز
    BorderRadius borderRadius = BorderRadius.zero, // شكل الحواف
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        splashColor: splashColor,
        highlightColor: highlightColor,
        borderRadius: borderRadius,
        child: child,
      ),
    );
  }

  /// ينشئ رسمًا متحركًا نابضًا
  static Widget pulse({
    required Widget child,
    Duration duration = const Duration(milliseconds: 1500), // مدة النبض
    bool repeat = true, // التكرار
  }) {
    return TweenAnimationBuilder<double>(
      tween: Tween(begin: 0.95, end: 1.05), // تغيير طفيف في الحجم
      duration: duration,
      curve: Curves.easeInOut, // منحنى التسارع
      builder: (context, value, child) {
        return Transform.scale(
          scale: value,
          child: child,
        );
      },
      child: child,
    );
  }
}

/// تعريف أنواع انتقالات الصفحات المختلفة
enum PageTransitionType {
  fade, // تلاشي
  rightToLeft, // من اليمين إلى اليسار
  leftToRight, // من اليسار إلى اليمين
  topToBottom, // من الأعلى إلى الأسفل
  bottomToTop, // من الأسفل إلى الأعلى
  scale, // تغيير الحجم
  rotate, // دوران
  scaleWithFadeOut, // تغيير الحجم مع تلاشي
}

/// امتداد لوحدة التحكم في الرسوم لإنشاء رسوم متدرجة
extension StaggeredAnimationExtension on AnimationController {
  Animation<double> createStaggered(int position, {Duration staggerDuration = const Duration(milliseconds: 50)}) {
    return EnhancedAnimationHelper.staggeredAnimation(
      controller: this,
      position: position,
      staggerDuration: staggerDuration,
    );
  }
}