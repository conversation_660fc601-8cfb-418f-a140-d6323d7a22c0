
/// 🧠 مجموعة خدمات الذكاء الاصطناعي الشاملة
///
/// ملف تصدير شامل لجميع خدمات الذكاء الاصطناعي في التطبيق
/// يشمل Clarifai و Cloudinary والخدمات المساعدة
library;

import 'package:motorcycle_parts_shop/core/services/clarifai_service.dart';
import 'package:motorcycle_parts_shop/core/services/smart_product_analyzer.dart';
import 'package:motorcycle_parts_shop/core/services/cloudinary_service.dart';
import 'package:motorcycle_parts_shop/core/services/advanced_media_manager.dart';
import 'package:motorcycle_parts_shop/core/services/auth_supabase_service.dart';
import 'package:motorcycle_parts_shop/core/config/clarifai_config.dart';
import '../interfaces/statistics_provider.dart';
import '../interfaces/maintenance_provider.dart';

// 🧠 خدمات Clarifai الأساسية
export 'clarifai_service.dart';
export 'smart_product_analyzer.dart';

// ☁️ خدمات Cloudinary المتقدمة
export 'cloudinary_service.dart';
export 'advanced_media_manager.dart';

// ⚙️ تكوينات الخدمات
export '../config/clarifai_config.dart';
export '../config/cloudinary_config.dart' hide Environment;

// 🔐 خدمات المصادقة والقاعدة
export 'auth_supabase_service.dart';

/// 🚀 مدير خدمات الذكاء الاصطناعي الموحد
///
/// فئة مركزية لإدارة جميع خدمات الذكاء الاصطناعي
/// مع تهيئة موحدة وإدارة شاملة
class AIServicesManager implements StatisticsProvider, MaintenanceProvider {
  static final AIServicesManager _instance = AIServicesManager._internal();
  factory AIServicesManager() => _instance;
  AIServicesManager._internal();

  // 🧠 خدمات Clarifai
  final ClarifaiService clarifaiService = ClarifaiService();
  final SmartProductAnalyzer productAnalyzer = SmartProductAnalyzer();

  // ☁️ خدمات Cloudinary
  final CloudinaryService cloudinaryService = CloudinaryService();
  final AdvancedMediaManager mediaManager = AdvancedMediaManager();

  // 🔐 خدمات أساسية
  final AuthSupabaseService authService = AuthSupabaseService();

  bool _isInitialized = false;

  /// 🚀 تهيئة جميع الخدمات
  Future<void> initializeAll() async {
    if (_isInitialized) return;

    try {
      print('🚀 بدء تهيئة خدمات الذكاء الاصطناعي...');

      // 1. تهيئة الخدمات الأساسية
      print('🔐 تهيئة خدمة المصادقة...');
      await authService.initialize();

      // 2. تهيئة خدمات Cloudinary
      print('☁️ تهيئة خدمات Cloudinary...');
      await cloudinaryService.initialize();
      await mediaManager.initialize();

      // 3. تهيئة خدمات Clarifai
      print('🧠 تهيئة خدمات Clarifai...');
      await clarifaiService.initialize();
      await productAnalyzer.initialize();

      _isInitialized = true;
      print('✅ تم تهيئة جميع خدمات الذكاء الاصطناعي بنجاح!');
    } catch (e) {
      print('❌ خطأ في تهيئة خدمات الذكاء الاصطناعي: $e');
      rethrow;
    }
  }

  /// 📊 الحصول على إحصائيات شاملة
  Map<String, dynamic> getAllStats() {
    return {
      'initialization_status': _isInitialized,
      'clarifai_stats': {
        'service': clarifaiService.getDetailedStats(),
        'analyzer': productAnalyzer.getDetailedStats(),
      },
      'cloudinary_stats': {
        'service': cloudinaryService.getDetailedStats(),
        'media_manager': mediaManager.getDetailedStats(),
      },
      'auth_stats': {
        'is_initialized': authService.isInitialized,
        'current_user': authService.currentUser?.id,
      },
    };
  }

  // ========================================
  // تطبيق StatisticsProvider
  // ========================================

  @override
  Map<String, dynamic> getDetailedStats() {
    return getAllStats();
  }

  @override
  void resetStatistics() {
    resetAllStatistics();
  }

  // ========================================
  // تطبيق MaintenanceProvider
  // ========================================

  @override
  void performMaintenance() {
    performMaintenanceAll();
  }

  @override
  bool get isHealthy => allServicesHealthy;

  @override
  MaintenanceStatus get maintenanceStatus {
    if (!_isInitialized) return MaintenanceStatus.critical;
    if (!allServicesHealthy) return MaintenanceStatus.needsMaintenance;
    return MaintenanceStatus.healthy;
  }

  @override
  void cleanup() {
    clearAllCaches();
    performMaintenance();
  }

  @override
  bool quickHealthCheck() {
    return allServicesHealthy;
  }

  @override
  void schedulePeriodicMaintenance(Duration interval) {
    // يمكن تطوير هذه الوظيفة لاحقاً لجدولة صيانة دورية
  }

  @override
  Map<String, dynamic> getBasicStats() {
    final detailed = getDetailedStats();
    return {
      'total_services': 5,
      'healthy_services': checkServicesHealth().values.where((v) => v).length,
      'initialization_status': _isInitialized,
      'overall_health': allServicesHealthy,
    };
  }

  @override
  bool validateStats() {
    try {
      final stats = getDetailedStats();
      return stats.isNotEmpty && stats.containsKey('initialization_status');
    } catch (e) {
      return false;
    }
  }

  /// 🧹 تنظيف جميع الخدمات
  void performMaintenanceAll() {
    print('🧹 بدء تنظيف جميع الخدمات...');

    // تنظيف خدمات Clarifai
    clarifaiService.performMaintenance();
    productAnalyzer.performMaintenance();

    // تنظيف خدمات Cloudinary
    cloudinaryService.performMaintenance();
    mediaManager.performMaintenance();

    print('✅ تم تنظيف جميع الخدمات بنجاح');
  }

  /// 🔄 إعادة تعيين جميع الإحصائيات
  void resetAllStatistics() {
    print('🔄 إعادة تعيين جميع الإحصائيات...');

    // إعادة تعيين إحصائيات Clarifai
    clarifaiService.resetStatistics();
    productAnalyzer.resetStatistics();

    // إعادة تعيين إحصائيات Cloudinary
    cloudinaryService.resetStatistics();

    print('✅ تم إعادة تعيين جميع الإحصائيات');
  }

  /// 🧹 مسح جميع الذاكرة المؤقتة
  void clearAllCaches() {
    print('🧹 مسح جميع الذاكرة المؤقتة...');

    // مسح ذاكرة Clarifai
    clarifaiService.clearCache();

    // مسح ذاكرة Cloudinary
    cloudinaryService.clearCache();

    print('✅ تم مسح جميع الذاكرة المؤقتة');
  }

  /// 🔍 فحص حالة جميع الخدمات
  Map<String, bool> checkServicesHealth() {
    return {
      'ai_services_manager': _isInitialized,
      'clarifai_service': clarifaiService.isInitialized,
      'product_analyzer': productAnalyzer.isInitialized,
      'cloudinary_service': cloudinaryService.isInitialized,
      'media_manager': mediaManager.isInitialized,
      'auth_service': authService.isInitialized,
    };
  }

  /// 📈 تقرير الأداء الشامل
  Map<String, dynamic> getPerformanceReport() {
    final stats = getAllStats();
    final health = checkServicesHealth();

    return {
      'timestamp': DateTime.now().toIso8601String(),
      'services_health': health,
      'overall_health': health.values.every((status) => status),
      'performance_metrics': {
        'clarifai': {
          'success_rate':
              stats['clarifai_stats']['service']['service_stats']['success_rate'],
          'avg_response_time':
              stats['clarifai_stats']['service']['service_stats']['average_response_time_ms'],
          'cache_hit_rate':
              stats['clarifai_stats']['service']['cache_stats']['cache_hit_rate'],
        },
        'cloudinary': {
          'success_rate': stats['cloudinary_stats']['service']['success_rate'],
          'avg_upload_time':
              stats['cloudinary_stats']['service']['average_upload_time_ms'],
          'cache_hit_rate':
              stats['cloudinary_stats']['image_service']['service_stats']['cache_hit_rate'],
        },
      },
      'error_summary': {
        'clarifai_errors': stats['clarifai_stats']['errors']['total_errors'],
        'cloudinary_errors':
            stats['cloudinary_stats']['errors']['total_errors'],
      },
    };
  }

  /// 🎯 اختبار مفصل لجميع الخدمات
  Future<Map<String, bool>> detailedHealthCheck() async {
    final results = <String, bool>{};

    try {
      // اختبار Clarifai
      results['clarifai'] = clarifaiService.isInitialized;

      // اختبار Cloudinary
      results['cloudinary'] = cloudinaryService.isInitialized;

      // اختبار قاعدة البيانات
      results['database'] = authService.isInitialized;

      return results;
    } catch (e) {
      print('خطأ في فحص الخدمات: $e');
      return {'error': false};
    }
  }

  /// 📱 معلومات النظام
  Map<String, dynamic> getSystemInfo() {
    return {
      'app_name': 'متجر قطع غيار الدراجات النارية',
      'version': '1.0.0',
      'ai_services': {
        'clarifai': {
          'enabled': true,
          'models_count': ClarifaiConfig.supportedModels.length,
          'features': [
            'تحليل الصور',
            'كشف الكائنات',
            'استخراج النص',
            'تحليل الألوان',
            'فلترة المحتوى',
          ],
        },
        'cloudinary': {
          'enabled': true,
          'features': ['رفع الصور', 'تحويل الصور', 'تحسين الأداء', 'CDN عالمي'],
        },
      },
      'initialization_time': DateTime.now().toIso8601String(),
    };
  }

  /// 🔧 إعدادات متقدمة
  void configureAdvancedSettings({
    bool enableDebugMode = false,
    bool enableDetailedLogging = false,
    bool enablePerformanceMonitoring = true,
  }) {
    print('🔧 تكوين الإعدادات المتقدمة...');

    // يمكن إضافة تكوينات متقدمة هنا

    print('✅ تم تكوين الإعدادات المتقدمة');
  }

  /// 📊 تصدير البيانات
  Map<String, dynamic> exportData() {
    return {
      'export_timestamp': DateTime.now().toIso8601String(),
      'system_info': getSystemInfo(),
      'performance_report': getPerformanceReport(),
      'all_stats': getAllStats(),
    };
  }

  /// 🔄 إعادة تشغيل الخدمات
  Future<void> restartServices() async {
    print('🔄 إعادة تشغيل الخدمات...');

    _isInitialized = false;

    // إعادة تهيئة جميع الخدمات
    await initializeAll();

    print('✅ تم إعادة تشغيل جميع الخدمات');
  }

  /// 🤖 توليد توصيات شخصية متقدمة
  Future<List<Map<String, dynamic>>> generatePersonalizedRecommendations({
    required String userId,
    int limit = 10,
    String? category,
  }) async {
    try {
      // تحليل سلوك المستخدم
      final userBehavior = await _analyzeUserBehavior(userId);

      // الحصول على تفضيلات المستخدم
      final preferences = await _getUserPreferences(userId);

      // توليد التوصيات بناءً على الذكاء الاصطناعي
      final recommendations = await _generateAIRecommendations(
        userId: userId,
        behavior: userBehavior,
        preferences: preferences,
        limit: limit,
        category: category,
      );

      return recommendations;
    } catch (e) {
      print('❌ خطأ في توليد التوصيات الشخصية: $e');
      return [];
    }
  }

  /// 🔍 كشف الاحتيال التلقائي
  Future<Map<String, dynamic>> detectFraud({
    required String userId,
    required Map<String, dynamic> transactionData,
  }) async {
    try {
      double riskScore = 0.0;
      final riskFactors = <String>[];

      // فحص أنماط الشراء غير العادية
      final purchasePattern = await _analyzePurchasePattern(userId);
      if (purchasePattern['unusual_frequency'] == true) {
        riskScore += 30.0;
        riskFactors.add('تكرار شراء غير عادي');
      }

      // فحص قيمة المعاملة
      final transactionAmount = transactionData['amount'] as double? ?? 0.0;
      final averageTransaction = await _getAverageTransactionAmount(userId);
      if (transactionAmount > averageTransaction * 5) {
        riskScore += 25.0;
        riskFactors.add('قيمة معاملة عالية غير عادية');
      }

      // فحص الموقع الجغرافي
      final location = transactionData['location'] as String?;
      if (location != null) {
        final isUnusualLocation = await _checkUnusualLocation(userId, location);
        if (isUnusualLocation) {
          riskScore += 20.0;
          riskFactors.add('موقع جغرافي غير عادي');
        }
      }

      // فحص وقت المعاملة
      final transactionTime = DateTime.now();
      if (transactionTime.hour < 6 || transactionTime.hour > 23) {
        riskScore += 15.0;
        riskFactors.add('وقت معاملة غير عادي');
      }

      // تحديد مستوى المخاطر
      String riskLevel;
      bool isBlocked = false;
      String recommendedAction;

      if (riskScore >= 70) {
        riskLevel = 'high';
        isBlocked = true;
        recommendedAction = 'حظر المعاملة والتحقق اليدوي';
      } else if (riskScore >= 40) {
        riskLevel = 'medium';
        recommendedAction = 'طلب تحقق إضافي';
      } else {
        riskLevel = 'low';
        recommendedAction = 'السماح بالمعاملة';
      }

      return {
        'risk_score': riskScore,
        'risk_level': riskLevel,
        'risk_factors': riskFactors,
        'is_blocked': isBlocked,
        'recommended_action': recommendedAction,
        'analysis_timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      print('❌ خطأ في كشف الاحتيال: $e');
      return {
        'risk_score': 0.0,
        'risk_level': 'low',
        'risk_factors': [],
        'is_blocked': false,
        'recommended_action': 'السماح بالمعاملة',
        'error': e.toString(),
      };
    }
  }

  /// 💰 تحسين الأسعار الديناميكي
  Future<double> calculateDynamicPrice({
    required String productId,
    required String userId,
    required double basePrice,
  }) async {
    try {
      double adjustmentFactor = 1.0;

      // تحليل الطلب على المنتج
      final demandScore = await _calculateDemandScore(productId);

      // تحليل المخزون
      final stockLevel = await _getProductStockLevel(productId);

      // تحليل سلوك المستخدم
      final userBehavior = await _analyzeUserBehavior(userId);
      final loyaltyScore = userBehavior['purchase_score'] ?? 0.0;

      // تطبيق تعديلات السعر
      if (demandScore > 8.0) {
        adjustmentFactor += 0.1; // زيادة 10% للطلب العالي
      } else if (demandScore < 3.0) {
        adjustmentFactor -= 0.05; // تخفيض 5% للطلب المنخفض
      }

      if (stockLevel < 5) {
        adjustmentFactor += 0.15; // زيادة 15% للمخزون المنخفض
      } else if (stockLevel > 50) {
        adjustmentFactor -= 0.1; // تخفيض 10% للمخزون العالي
      }

      // خصم للعملاء المخلصين
      if (loyaltyScore > 20.0) {
        adjustmentFactor -= 0.05; // خصم 5% للعملاء المخلصين
      }

      // التأكد من أن السعر لا يقل عن الحد الأدنى أو يزيد عن الحد الأقصى
      adjustmentFactor = adjustmentFactor.clamp(0.8, 1.3);

      return basePrice * adjustmentFactor;
    } catch (e) {
      print('❌ خطأ في حساب السعر الديناميكي: $e');
      return basePrice;
    }
  }

  /// 🤖 دعم العملاء الذكي 24/7
  Future<String> getSmartCustomerSupport({
    required String query,
    required String userId,
  }) async {
    try {
      // تحليل الاستفسار
      final queryType = _analyzeQueryType(query);

      // الحصول على سياق المستخدم
      final userContext = await _getUserContext(userId);

      // توليد الرد المناسب
      String response = await _generateSmartResponse(
        query: query,
        queryType: queryType,
        userContext: userContext,
      );

      // حفظ المحادثة للتعلم المستقبلي
      await _saveSupportInteraction(userId, query, response);

      return response;
    } catch (e) {
      print('❌ خطأ في دعم العملاء الذكي: $e');
      return 'عذراً، حدث خطأ في النظام. يرجى المحاولة مرة أخرى أو التواصل مع فريق الدعم.';
    }
  }

  // الوظائف المساعدة الخاصة - تعتمد على قاعدة البيانات
  Future<Map<String, dynamic>> _analyzeUserBehavior(String userId) async {
    try {
      // تحليل سلوك المستخدم من قاعدة البيانات
      final behaviorData = await authService.client
          .from('user_behavior_analytics')
          .select('*')
          .eq('user_id', userId)
          .order('created_at', ascending: false)
          .limit(100);

      if (behaviorData.isEmpty) {
        return _getDefaultBehaviorScore();
      }

      double viewScore = 0.0;
      double purchaseScore = 0.0;
      double searchScore = 0.0;
      double timeSpentScore = 0.0;

      for (final data in behaviorData) {
        final action = data['action'] as String;
        final timestamp = DateTime.parse(data['created_at']);
        final recency = DateTime.now().difference(timestamp).inDays;
        final recencyWeight = (1.0 - (recency / 30.0)).clamp(0.1, 1.0);

        switch (action) {
          case 'product_view':
            viewScore += 1.0 * recencyWeight;
            break;
          case 'product_purchase':
            purchaseScore += 5.0 * recencyWeight;
            break;
          case 'search_query':
            searchScore += 2.0 * recencyWeight;
            break;
          case 'time_spent':
            final duration = data['metadata']?['duration'] as int? ?? 0;
            timeSpentScore += (duration / 60.0) * recencyWeight;
            break;
        }
      }

      return {
        'view_score': viewScore,
        'purchase_score': purchaseScore,
        'search_score': searchScore,
        'time_spent_score': timeSpentScore,
        'engagement_level':
            (viewScore + purchaseScore + searchScore + timeSpentScore) / 4,
      };
    } catch (e) {
      print('❌ خطأ في تحليل سلوك المستخدم: $e');
      return _getDefaultBehaviorScore();
    }
  }

  Future<Map<String, dynamic>> _getUserPreferences(String userId) async {
    try {
      // تحليل المشتريات السابقة من قاعدة البيانات
      final purchases = await authService.client
          .from('order_items')
          .select(
            '*, products(category_id, brand_id, price), orders!inner(user_id)',
          )
          .eq('orders.user_id', userId)
          .order('created_at', ascending: false)
          .limit(50);

      final categoryPreferences = <String, double>{};
      final brandPreferences = <String, double>{};
      final priceRangePreferences = <String, double>{};

      for (final purchase in purchases) {
        final product = purchase['products'];
        if (product != null) {
          final categoryId = product['category_id'] as String?;
          final brandId = product['brand_id'] as String?;
          final price = (product['price'] as num?)?.toDouble() ?? 0.0;

          // تفضيلات الفئات
          if (categoryId != null) {
            categoryPreferences[categoryId] =
                (categoryPreferences[categoryId] ?? 0.0) + 1.0;
          }

          // تفضيلات العلامات التجارية
          if (brandId != null) {
            brandPreferences[brandId] =
                (brandPreferences[brandId] ?? 0.0) + 1.0;
          }

          // تفضيلات النطاق السعري
          final priceRange = _getPriceRange(price);
          priceRangePreferences[priceRange] =
              (priceRangePreferences[priceRange] ?? 0.0) + 1.0;
        }
      }

      return {
        'categories': categoryPreferences,
        'brands': brandPreferences,
        'price_ranges': priceRangePreferences,
      };
    } catch (e) {
      print('❌ خطأ في الحصول على تفضيلات المستخدم: $e');
      return {
        'categories': <String, double>{},
        'brands': <String, double>{},
        'price_ranges': <String, double>{},
      };
    }
  }

  Map<String, dynamic> _getDefaultBehaviorScore() {
    return {
      'view_score': 1.0,
      'purchase_score': 0.0,
      'search_score': 1.0,
      'time_spent_score': 1.0,
      'engagement_level': 1.0,
    };
  }

  String _getPriceRange(double price) {
    if (price < 100) return 'low';
    if (price < 500) return 'medium';
    if (price < 1000) return 'high';
    return 'premium';
  }

  Future<List<Map<String, dynamic>>> _generateAIRecommendations({
    required String userId,
    required Map<String, dynamic> behavior,
    required Map<String, dynamic> preferences,
    required int limit,
    String? category,
  }) async {
    try {
      // بناء استعلام التوصيات من قاعدة البيانات
      var queryBuilder = authService.client
          .from('products')
          .select('*, categories(name), brands(name), product_analysis(*)')
          .eq('is_active', true)
          .gt('stock_quantity', 0);

      // فلترة حسب الفئة إذا تم تحديدها
      if (category != null) {
        queryBuilder = queryBuilder.eq('category_id', category);
      }

      final products = await queryBuilder.limit(limit * 3);

      // حساب نقاط التوصية لكل منتج
      final scoredProducts = <Map<String, dynamic>>[];

      for (final product in products) {
        final score = _calculateRecommendationScore(
          product: product,
          behaviorScore: behavior,
          preferences: preferences,
        );

        scoredProducts.add({
          'product_id': product['id'],
          'name': product['name'],
          'price': product['price'],
          'image_url': product['image_url'],
          'category': product['categories']?['name'],
          'brand': product['brands']?['name'],
          'score': score,
          'reason': _getRecommendationReason(product, preferences),
          'confidence': _calculateConfidence(score),
        });
      }

      // ترتيب حسب النتيجة وإرجاع أفضل النتائج
      scoredProducts.sort(
        (a, b) => (b['score'] as double).compareTo(a['score'] as double),
      );

      return scoredProducts.take(limit).toList();
    } catch (e) {
      print('❌ خطأ في توليد التوصيات من قاعدة البيانات: $e');
      return [];
    }
  }

  /// حساب نتيجة التوصية للمنتج
  double _calculateRecommendationScore({
    required Map<String, dynamic> product,
    required Map<String, dynamic> behaviorScore,
    required Map<String, dynamic> preferences,
  }) {
    double score = 0.0;

    // نتيجة أساسية بناءً على شعبية المنتج
    final averageRating =
        (product['average_rating'] as num?)?.toDouble() ?? 0.0;
    final reviewCount = (product['review_count'] as num?)?.toInt() ?? 0;
    score += averageRating * 2.0;
    score += reviewCount * 0.1;

    // نتيجة بناءً على تفضيلات الفئة
    final categoryPrefs =
        preferences['categories'] as Map<String, double>? ?? {};
    final productCategoryId = product['category_id'] as String?;
    if (productCategoryId != null &&
        categoryPrefs.containsKey(productCategoryId)) {
      score += categoryPrefs[productCategoryId]! * 3.0;
    }

    // نتيجة بناءً على تفضيلات العلامة التجارية
    final brandPrefs = preferences['brands'] as Map<String, double>? ?? {};
    final productBrandId = product['brand_id'] as String?;
    if (productBrandId != null && brandPrefs.containsKey(productBrandId)) {
      score += brandPrefs[productBrandId]! * 2.0;
    }

    // نتيجة بناءً على النطاق السعري المفضل
    final priceRangePrefs =
        preferences['price_ranges'] as Map<String, double>? ?? {};
    final productPrice = (product['price'] as num?)?.toDouble() ?? 0.0;
    final productPriceRange = _getPriceRange(productPrice);
    if (priceRangePrefs.containsKey(productPriceRange)) {
      score += priceRangePrefs[productPriceRange]! * 1.5;
    }

    // نتيجة بناءً على مستوى التفاعل
    final engagementLevel = behaviorScore['engagement_level'] as double? ?? 0.0;
    score *= (1.0 + engagementLevel * 0.2);

    // تقليل النتيجة للمنتجات غالية الثمن إذا كان المستخدم يفضل الأسعار المنخفضة
    if (productPrice > 1000 && engagementLevel < 5.0) {
      score *= 0.8;
    }

    // إضافة نقاط للمنتجات الجديدة
    final createdAt = DateTime.parse(product['created_at']);
    final daysSinceCreation = DateTime.now().difference(createdAt).inDays;
    if (daysSinceCreation < 30) {
      score += 1.0; // نقطة إضافية للمنتجات الجديدة
    }

    return score;
  }

  /// الحصول على سبب التوصية
  String _getRecommendationReason(
    Map<String, dynamic> product,
    Map<String, dynamic> preferences,
  ) {
    final reasons = <String>[];

    final categoryPrefs =
        preferences['categories'] as Map<String, double>? ?? {};
    final brandPrefs = preferences['brands'] as Map<String, double>? ?? {};

    if (categoryPrefs.containsKey(product['category_id'])) {
      reasons.add('من فئة مفضلة لديك');
    }

    if (brandPrefs.containsKey(product['brand_id'])) {
      reasons.add('من علامة تجارية تفضلها');
    }

    final averageRating =
        (product['average_rating'] as num?)?.toDouble() ?? 0.0;
    if (averageRating >= 4.0) {
      reasons.add('تقييم عالي من العملاء');
    }

    final createdAt = DateTime.parse(product['created_at']);
    final daysSinceCreation = DateTime.now().difference(createdAt).inDays;
    if (daysSinceCreation < 30) {
      reasons.add('منتج جديد');
    }

    return reasons.isNotEmpty ? reasons.join(' • ') : 'مقترح لك';
  }

  /// حساب مستوى الثقة في التوصية
  double _calculateConfidence(double score) {
    if (score >= 15.0) return 0.95;
    if (score >= 10.0) return 0.85;
    if (score >= 5.0) return 0.75;
    if (score >= 2.0) return 0.65;
    return 0.5;
  }

  Future<Map<String, dynamic>> _analyzePurchasePattern(String userId) async {
    try {
      // تحليل نمط الشراء من قاعدة البيانات
      final recentOrders = await authService.client
          .from('orders')
          .select('created_at, total_amount')
          .eq('user_id', userId)
          .gte(
            'created_at',
            DateTime.now().subtract(const Duration(days: 30)).toIso8601String(),
          )
          .order('created_at', ascending: false);

      if (recentOrders.length > 10) {
        // أكثر من 10 طلبات في الشهر الماضي يعتبر غير عادي
        return {'unusual_frequency': true, 'order_count': recentOrders.length};
      }

      return {'unusual_frequency': false, 'order_count': recentOrders.length};
    } catch (e) {
      print('❌ خطأ في تحليل نمط الشراء: $e');
      return {'unusual_frequency': false, 'order_count': 0};
    }
  }

  Future<double> _getAverageTransactionAmount(String userId) async {
    try {
      // حساب متوسط قيمة المعاملات من قاعدة البيانات
      final orders = await authService.client
          .from('orders')
          .select('total_amount')
          .eq('user_id', userId)
          .order('created_at', ascending: false)
          .limit(20);

      if (orders.isEmpty) return 500.0; // قيمة افتراضية

      final totalAmount = orders.fold<double>(
        0.0,
        (sum, order) =>
            sum + ((order['total_amount'] as num?)?.toDouble() ?? 0.0),
      );

      return totalAmount / orders.length;
    } catch (e) {
      print('❌ خطأ في حساب متوسط قيمة المعاملات: $e');
      return 500.0;
    }
  }

  Future<bool> _checkUnusualLocation(String userId, String location) async {
    try {
      // فحص المواقع المعتادة للمستخدم من قاعدة البيانات
      final userLocations = await authService.client
          .from('user_locations')
          .select('location')
          .eq('user_id', userId)
          .limit(10);

      final knownLocations =
          userLocations.map((loc) => loc['location'] as String).toList();

      // إذا كان الموقع غير موجود في المواقع المعروفة
      return !knownLocations.contains(location);
    } catch (e) {
      print('❌ خطأ في فحص الموقع غير العادي: $e');
      return false;
    }
  }

  Future<double> _calculateDemandScore(String productId) async {
    try {
      // حساب نقاط الطلب من قاعدة البيانات
      final now = DateTime.now();
      final lastWeek = now.subtract(const Duration(days: 7));

      // عدد المشاهدات في الأسبوع الماضي
      final viewsResponse = await authService.client
          .from('product_views')
          .select('id')
          .eq('product_id', productId)
          .gte('created_at', lastWeek.toIso8601String());
      final viewsCount = viewsResponse.length;

      // عدد المشتريات في الأسبوع الماضي
      final purchasesResponse = await authService.client
          .from('order_items')
          .select('id')
          .eq('product_id', productId)
          .gte('created_at', lastWeek.toIso8601String());
      final purchasesCount = purchasesResponse.length;

      // حساب نقاط الطلب (المشاهدات * 0.1 + المشتريات * 2)
      final demandScore = (viewsCount * 0.1) + (purchasesCount * 2.0);
      return demandScore.clamp(0.0, 10.0);
    } catch (e) {
      print('❌ خطأ في حساب نقاط الطلب: $e');
      return 5.0;
    }
  }

  Future<int> _getProductStockLevel(String productId) async {
    try {
      // الحصول على مستوى المخزون من قاعدة البيانات
      final product =
          await authService.client
              .from('products')
              .select('stock_quantity')
              .eq('id', productId)
              .single();

      return (product['stock_quantity'] as num?)?.toInt() ?? 0;
    } catch (e) {
      print('❌ خطأ في الحصول على مستوى المخزون: $e');
      return 25;
    }
  }

  String _analyzeQueryType(String query) {
    if (query.contains('طلب') || query.contains('أوردر')) return 'order';
    if (query.contains('منتج') || query.contains('قطعة')) return 'product';
    if (query.contains('دفع') || query.contains('فاتورة')) return 'payment';
    return 'general';
  }

  Future<Map<String, dynamic>> _getUserContext(String userId) async {
    try {
      // الحصول على آخر طلب للمستخدم
      final lastOrder = await authService.client
          .from('orders')
          .select('id, status, total_amount, created_at')
          .eq('user_id', userId)
          .order('created_at', ascending: false)
          .limit(1);

      // الحصول على تفضيلات المستخدم
      final preferences = await _getUserPreferences(userId);

      // الحصول على معلومات المستخدم
      final userProfile =
          await authService.client
              .from('user_profiles')
              .select('name, phone, preferred_language')
              .eq('user_id', userId)
              .maybeSingle();

      return {
        'last_order': lastOrder.isNotEmpty ? lastOrder.first : null,
        'preferences': preferences,
        'profile': userProfile,
        'user_id': userId,
      };
    } catch (e) {
      print('❌ خطأ في الحصول على سياق المستخدم: $e');
      return {'last_order': null, 'preferences': {}, 'profile': null};
    }
  }

  Future<String> _generateSmartResponse({
    required String query,
    required String queryType,
    required Map<String, dynamic> userContext,
  }) async {
    try {
      final lastOrder = userContext['last_order'] as Map<String, dynamic>?;
      final profile = userContext['profile'] as Map<String, dynamic>?;
      final userName = profile?['name'] as String? ?? 'عزيزي العميل';

      switch (queryType) {
        case 'order':
          if (lastOrder != null) {
            final orderId = lastOrder['id'];
            final status = lastOrder['status'];
            final statusText = _getOrderStatusText(status);
            return 'أهلاً $userName! آخر طلب لك رقم $orderId حالته: $statusText. هل تريد تفاصيل أكثر؟';
          }
          return 'أهلاً $userName! يمكنني مساعدتك في استفسارات الطلبات. ما هو رقم طلبك؟';

        case 'product':
          final categoryPrefs =
              userContext['preferences']['categories']
                  as Map<String, double>? ??
              {};
          if (categoryPrefs.isNotEmpty) {
            final topCategory =
                categoryPrefs.entries
                    .reduce((a, b) => a.value > b.value ? a : b)
                    .key;
            final categoryName = await _getCategoryName(topCategory);
            return 'أهلاً $userName! أرى أنك مهتم بـ$categoryName. أي منتج تبحث عنه تحديداً؟';
          }
          return 'أهلاً $userName! أي منتج تبحث عنه؟ يمكنني مساعدتك في العثور على قطع الغيار المناسبة.';

        case 'payment':
          if (lastOrder != null && lastOrder['status'] == 'pending_payment') {
            return 'أهلاً $userName! أرى أن لديك طلب في انتظار الدفع. هل تحتاج مساعدة في إتمام عملية الدفع؟';
          }
          return 'أهلاً $userName! بخصوص الدفع، نقبل جميع طرق الدفع الرئيسية. هل تحتاج مساعدة في إتمام عملية الدفع؟';

        default:
          if (lastOrder != null) {
            return 'أهلاً وسهلاً $userName! كيف يمكنني مساعدتك اليوم؟ هل تريد متابعة طلبك الأخير أم تبحث عن منتجات جديدة؟';
          }
          return 'أهلاً وسهلاً $userName في متجر قطع غيار الدراجات النارية! كيف يمكنني مساعدتك اليوم؟';
      }
    } catch (e) {
      print('❌ خطأ في توليد الرد الذكي: $e');
      return 'أهلاً بك في متجر قطع غيار الدراجات النارية. كيف يمكنني مساعدتك اليوم؟';
    }
  }

  Future<void> _saveSupportInteraction(
    String userId,
    String query,
    String response,
  ) async {
    try {
      // حفظ التفاعل في قاعدة البيانات للتعلم المستقبلي
      await authService.client.from('support_interactions').insert({
        'user_id': userId,
        'query': query,
        'response': response,
        'query_type': _analyzeQueryType(query),
        'created_at': DateTime.now().toIso8601String(),
        'metadata': {
          'response_time': DateTime.now().millisecondsSinceEpoch,
          'ai_generated': true,
        },
      });

      print('💾 تم حفظ تفاعل الدعم في قاعدة البيانات');
    } catch (e) {
      print('❌ خطأ في حفظ تفاعل الدعم: $e');
    }
  }

  String _getOrderStatusText(String status) {
    switch (status) {
      case 'pending':
        return 'قيد المراجعة';
      case 'confirmed':
        return 'مؤكد';
      case 'processing':
        return 'قيد التحضير';
      case 'shipped':
        return 'تم الشحن';
      case 'delivered':
        return 'تم التسليم';
      case 'cancelled':
        return 'ملغي';
      case 'pending_payment':
        return 'في انتظار الدفع';
      default:
        return status;
    }
  }

  Future<String> _getCategoryName(String categoryId) async {
    try {
      final category =
          await authService.client
              .from('categories')
              .select('name')
              .eq('id', categoryId)
              .single();
      return category['name'] as String;
    } catch (e) {
      return 'المنتجات';
    }
  }

  /// 🎯 Getters للوصول السريع
  bool get isInitialized => _isInitialized;
  bool get allServicesHealthy =>
      checkServicesHealth().values.every((status) => status);
}
