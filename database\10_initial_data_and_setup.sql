-- ===================================================================
-- البيانات الأولية والإعداد النهائي
-- متجر قطع غيار الدراجات النارية - Supabase Database
-- ===================================================================

-- ===================================================================
-- 1. إدراج طرق الدفع المتاحة
-- ===================================================================

INSERT INTO payment_methods (name, description, type, min_amount, max_amount, is_active, sort_order) VALUES
    ('الدفع عند الاستلام', 'الدفع نقداً عند استلام الطلب', 'cash', 0, 10000, true, 1),
    ('إنستاباي', 'الدفع الفوري عبر البنوك المصرية', 'instapay', 1, 100000, true, 2);

-- ===================================================================
-- 2. إدراج طريقة الشحن الوحيدة
-- ===================================================================

INSERT INTO shipping_methods (name, description, cost, estimated_days, is_active, sort_order) VALUES
    ('التوصيل للمنزل', 'التوصيل خلال 3-5 أيام عمل لجميع أنحاء الجمهورية', 80.00, 4, true, 1);

-- ===================================================================
-- 3. إدراج الشركات المصنعة الأساسية
-- ===================================================================

INSERT INTO companies (name, description, is_active) VALUES
    ('هوندا', 'شركة هوندا اليابانية لصناعة الدراجات النارية', true),
    ('ياماها', 'شركة ياماها اليابانية للدراجات النارية', true),
    ('سوزوكي', 'شركة سوزوكي اليابانية للمركبات', true),
    ('كاواساكي', 'شركة كاواساكي اليابانية للدراجات الرياضية', true),
    ('بي إم دبليو', 'شركة بي إم دبليو الألمانية للدراجات الفاخرة', true),
    ('دوكاتي', 'شركة دوكاتي الإيطالية للدراجات الرياضية', true),
    ('هارلي ديفيدسون', 'شركة هارلي ديفيدسون الأمريكية', true),
    ('كي تي إم', 'شركة كي تي إم النمساوية للدراجات', true),
    ('أبريليا', 'شركة أبريليا الإيطالية للدراجات', true),
    ('تريومف', 'شركة تريومف البريطانية للدراجات', true);

-- ===================================================================
-- 4. إدراج الفئات الأساسية
-- ===================================================================

INSERT INTO categories (name, description, is_active, sort_order) VALUES
    ('قطع المحرك', 'قطع غيار المحرك والأجزاء الداخلية', true, 1),
    ('نظام الفرامل', 'أقراص وأحذية وسوائل الفرامل', true, 2),
    ('نظام التعليق', 'مساعدات الصدمات والزنبركات', true, 3),
    ('الإطارات والعجلات', 'إطارات وعجلات بجميع المقاسات', true, 4),
    ('نظام الإضاءة', 'مصابيح أمامية وخلفية وإشارات', true, 5),
    ('قطع الكهرباء', 'بطاريات وأسلاك ومولدات', true, 6),
    ('نظام العادم', 'شكمانات وأنابيب العادم', true, 7),
    ('أدوات الصيانة', 'أدوات وزيوت ومواد التنظيف', true, 8),
    ('الإكسسوارات', 'خوذات وقفازات وملابس الحماية', true, 9),
    ('قطع الهيكل', 'أجزاء الهيكل الخارجي والداخلي', true, 10);

-- ===================================================================
-- 5. إدراج الفئات الفرعية
-- ===================================================================

-- فئات فرعية لقطع المحرك
INSERT INTO categories (name, description, parent_id, is_active, sort_order)
SELECT 'مكابس وحلقات', 'مكابس المحرك والحلقات', id, true, 1 FROM categories WHERE name = 'قطع المحرك';

INSERT INTO categories (name, description, parent_id, is_active, sort_order)
SELECT 'صمامات', 'صمامات السحب والعادم', id, true, 2 FROM categories WHERE name = 'قطع المحرك';

INSERT INTO categories (name, description, parent_id, is_active, sort_order)
SELECT 'فلاتر الهواء', 'فلاتر هواء المحرك', id, true, 3 FROM categories WHERE name = 'قطع المحرك';

-- فئات فرعية لنظام الفرامل
INSERT INTO categories (name, description, parent_id, is_active, sort_order)
SELECT 'أقراص الفرامل', 'أقراص الفرامل الأمامية والخلفية', id, true, 1 FROM categories WHERE name = 'نظام الفرامل';

INSERT INTO categories (name, description, parent_id, is_active, sort_order)
SELECT 'تيل الفرامل', 'تيل الفرامل للأقراص والطبول', id, true, 2 FROM categories WHERE name = 'نظام الفرامل';

-- ===================================================================
-- 6. إدراج عروض وخصومات تجريبية
-- ===================================================================

INSERT INTO offers (title, description, discount_percentage, start_date, end_date, is_active, applies_to) VALUES
    ('خصم 15% على قطع المحرك', 'خصم خاص على جميع قطع غيار المحرك', 15.00, NOW(), NOW() + INTERVAL '30 days', true, 'categories'),
    ('خصم 20% للعملاء الجدد', 'خصم ترحيبي للعملاء الجدد', 20.00, NOW(), NOW() + INTERVAL '60 days', true, 'all'),
    ('عرض الجمعة البيضاء', 'خصومات تصل إلى 50% على منتجات مختارة', 50.00, NOW(), NOW() + INTERVAL '7 days', true, 'products');

-- ===================================================================
-- 7. إدراج كوبونات تجريبية
-- ===================================================================

INSERT INTO coupons (code, description, discount_type, discount_value, min_purchase_amount, start_date, end_date, usage_limit, is_active) VALUES
    ('WELCOME50', 'كوبون ترحيبي بخصم 50 جنيه', 'fixed', 50.00, 200.00, NOW(), NOW() + INTERVAL '90 days', 100, true),
    ('SAVE10', 'خصم 10% على أي طلب', 'percentage', 10.00, 100.00, NOW(), NOW() + INTERVAL '30 days', 50, true),
    ('FREESHIP', 'شحن مجاني للطلبات فوق 500 جنيه', 'fixed', 30.00, 500.00, NOW(), NOW() + INTERVAL '60 days', 200, true);

-- ===================================================================
-- 8. إدراج إعلانات تجريبية
-- ===================================================================

INSERT INTO advertisements (title, description, position, start_date, end_date, is_active) VALUES
    ('عروض الشتاء الحارة', 'خصومات تصل إلى 40% على قطع الغيار', 'banner', NOW(), NOW() + INTERVAL '45 days', true),
    ('منتجات جديدة من هوندا', 'اكتشف أحدث قطع غيار هوندا الأصلية', 'sidebar', NOW(), NOW() + INTERVAL '30 days', true),
    ('خدمة التوصيل السريع', 'توصيل في نفس اليوم للقاهرة والجيزة', 'inline', NOW(), NOW() + INTERVAL '90 days', true);

-- ===================================================================
-- 9. تحديث عدادات المنتجات في الفئات والشركات
-- ===================================================================

-- تحديث عدد المنتجات في كل فئة
UPDATE categories SET products_count = (
    SELECT COUNT(*) FROM products WHERE category_id = categories.id
);

-- تحديث عدد المنتجات في كل شركة
UPDATE companies SET products_count = (
    SELECT COUNT(*) FROM products WHERE company_id = companies.id
);

-- ===================================================================
-- 11. إعداد المهام المجدولة (Cron Jobs)
-- ===================================================================

-- تنظيف البيانات القديمة يومياً في الساعة 2 صباحاً
-- SELECT cron.schedule('cleanup-old-data', '0 2 * * *', 'SELECT cleanup_old_data();');

-- تحديث إحصائيات البحث كل ساعة
-- SELECT cron.schedule('update-search-stats', '0 * * * *', 'SELECT update_search_analytics();');

-- نسخ احتياطي يومي في الساعة 3 صباحاً
-- SELECT cron.schedule('daily-backup', '0 3 * * *', 'SELECT perform_daily_backup();');

-- ===================================================================
-- 12. إعداد الفهارس المتقدمة للأداء
-- ===================================================================

-- فهرس مركب للطلبات حسب المستخدم والحالة
CREATE INDEX IF NOT EXISTS idx_orders_user_status_date ON orders(user_id, status, created_at);

-- فهرس مركب للمنتجات حسب الفئة والسعر والتقييم
CREATE INDEX IF NOT EXISTS idx_products_category_price_rating ON products(category_id, price, average_rating) WHERE is_available = true;

-- فهرس للبحث السريع في المراجعات المعتمدة
CREATE INDEX IF NOT EXISTS idx_reviews_product_approved ON reviews(product_id, is_approved, created_at) WHERE is_approved = true;

-- فهرس للإشعارات غير المقروءة
CREATE INDEX IF NOT EXISTS idx_notifications_user_unread ON notifications(user_id, is_read, created_at) WHERE is_read = false;

-- ===================================================================
-- 13. إعداد المشاهدات (Views) للتقارير
-- ===================================================================

-- مشاهدة إحصائيات المبيعات اليومية
CREATE OR REPLACE VIEW daily_sales_stats
WITH (security_invoker = true)
AS
SELECT
    DATE(created_at) AS sale_date,
    COUNT(*) AS orders_count,
    SUM(total_amount) AS total_revenue,
    AVG(total_amount) AS average_order_value,
    COUNT(DISTINCT user_id) AS unique_customers
FROM orders
WHERE status = 'delivered'
GROUP BY DATE(created_at)
ORDER BY sale_date DESC;

-- مشاهدة أفضل المنتجات مبيعاً
CREATE OR REPLACE VIEW top_selling_products
WITH (security_invoker = true)
AS
SELECT
    p.id,
    p.name,
    p.sku,
    p.price,
    SUM(oi.quantity) AS total_sold,
    SUM(oi.total_price) AS total_revenue,
    p.average_rating,
    p.review_count
FROM products p
INNER JOIN order_items oi ON p.id = oi.product_id
INNER JOIN orders o ON oi.order_id = o.id
WHERE o.status = 'delivered'
GROUP BY p.id, p.name, p.sku, p.price, p.average_rating, p.review_count
ORDER BY total_sold DESC;

-- مشاهدة إحصائيات العملاء
CREATE OR REPLACE VIEW customer_stats
WITH (security_invoker = true)
AS
SELECT
    p.id,
    p.name,
    p.email,
    p.order_count,
    p.total_spent,
    p.average_rating,
    p.created_at AS registration_date,
    p.last_login_at,
    CASE
        WHEN p.last_login_at > NOW() - INTERVAL '7 days' THEN 'نشط'
        WHEN p.last_login_at > NOW() - INTERVAL '30 days' THEN 'متوسط النشاط'
        ELSE 'غير نشط'
    END AS activity_status
FROM profiles p
WHERE p.profile_type = 'customer'
ORDER BY p.total_spent DESC;
-- ===================================================================
-- 14. رسائل التأكيد والإنجاز
-- ===================================================================

DO $$
BEGIN
    RAISE NOTICE '🎉 تم إنجاز إعداد قاعدة البيانات بالكامل!';
    RAISE NOTICE '';
    RAISE NOTICE '✅ تم إدراج البيانات الأولية:';
    RAISE NOTICE '   💳 طرق الدفع الأساسية';
    RAISE NOTICE '   🚚 طرق الشحن المختلفة';
    RAISE NOTICE '   🏢 الشركات المصنعة الرئيسية';
    RAISE NOTICE '   📂 الفئات والفئات الفرعية';
    RAISE NOTICE '   🎯 العروض والكوبونات التجريبية';
    RAISE NOTICE '   📢 الإعلانات التجريبية';
    RAISE NOTICE '';
    RAISE NOTICE '🚀 تم إنشاء الفهارس المتقدمة للأداء';
    RAISE NOTICE '📊 تم إنشاء المشاهدات للتقارير';
    RAISE NOTICE '🔧 تم إعداد النظام للاستخدام';
    RAISE NOTICE '';
    RAISE NOTICE '🎯 قاعدة البيانات جاهزة للاستخدام!';
    RAISE NOTICE '📱 يمكن الآن ربطها بالتطبيق';
    RAISE NOTICE '';
    RAISE NOTICE '⚠️  تذكير: قم بتغيير كلمات المرور الافتراضية';
    RAISE NOTICE '🔒 تأكد من تطبيق إعدادات الأمان في الإنتاج';
END $$;
