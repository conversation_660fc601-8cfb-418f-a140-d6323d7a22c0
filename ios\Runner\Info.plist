<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>الماجيك لقطع الغيار</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>الماجيك لقطع الغيار</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<!-- إعدادات تسجيل الدخول بجوجل -->
	<key>CFBundleURLTypes</key>
	<array>
		<!-- Supabase URL Scheme -->
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>supabase-auth</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>io.supabase.motorcycleparts</string>
			</array>
		</dict>
		<!-- Google Sign-In URL Scheme -->
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>google-signin</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<!-- معرف عميل جوجل المعكوس -->
				<string>com.googleusercontent.apps.239331604810-5p58i2v2vg9usjh6c8qtr0rnldtug7nu</string>
			</array>
		</dict>
	</array>
	<!-- تأكد من تكوين URL إعادة التوجيه في لوحة تحكم Supabase: io.supabase.motorcycleparts://login-callback/ -->
	<!-- إذن الميكروفون للتعرف الصوتي -->
	<key>NSMicrophoneUsageDescription</key>
	<string>هذا التطبيق يحتاج إلى الوصول إلى الميكروفون لتمكين التعرف الصوتي والبحث الصوتي عن المنتجات.</string>
	<!-- إذن الكاميرا -->
	<key>NSCameraUsageDescription</key>
	<string>هذا التطبيق يحتاج إلى الوصول إلى الكاميرا لالتقاط صور المنتجات والبحث بالصور.</string>
	<!-- إذن معرض الصور -->
	<key>NSPhotoLibraryUsageDescription</key>
	<string>هذا التطبيق يحتاج إلى الوصول إلى معرض الصور لاختيار صور المنتجات.</string>
	<!-- إذن الموقع الجغرافي -->
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>هذا التطبيق يحتاج إلى الوصول إلى موقعك لعرض المتاجر القريبة وتقدير وقت التوصيل.</string>
</dict>
</plist>