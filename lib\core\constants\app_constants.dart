import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

class AppConstants {
  // API URLs
  static String get baseUrl => dotenv.env['SUPABASE_URL'] ?? '';
  static String get apiKey => dotenv.env['SUPABASE_ANON_KEY'] ?? '';

  // تكوين التطبيق
  static String get appName =>
      dotenv.env['APP_NAME'] ?? 'متجر قطع غيار الدراجات النارية';
  static String get appVersion => dotenv.env['APP_VERSION'] ?? '1.0.0';

  // جداول قاعدة البيانات الأساسية
  static const String profilesTable = 'profiles';
  static const String productsTable = 'products';
  static const String categoriesTable = 'categories';
  static const String ordersTable = 'orders';
  static const String orderItemsTable = 'order_items';
  static const String reviewsTable = 'reviews';
  static const String wishlistTable = 'wishlists';
  // تم حذف user_favorites - نستخدم wishlists فقط
  static const String cartTable = 'cart_items';
  static const String addressesTable = 'addresses';
  // تم حذف adminsTable لأنه مدمج في profiles
  static const String offersTable = 'offers';
  static const String notificationsTable = 'notifications';
  static const String notificationTemplatesTable = 'notification_templates';
  static const String notificationSettingsTable = 'notification_settings';

  // جداول التحليلات والسلوك المتقدمة
  static const String userBehaviorAnalyticsTable = 'user_behavior_analytics';
  static const String searchAnalyticsTable = 'search_analytics';
  static const String userNotificationStatsTable = 'user_notification_stats';
  static const String notificationAnalyticsTable = 'notification_analytics';
  static const String supportInteractionsTable = 'support_interactions';
  static const String userLocationsTable = 'user_locations';
  static const String productViewsTable = 'product_views';

  // جداول الذكاء الاصطناعي
  static const String productAnalysisTable = 'product_analysis';
  static const String aiAnalyticsTable = 'ai_analytics';
  static const String smartSearchLogTable = 'smart_search_log';
  static const String advertisementsTable = 'advertisements';

  // مسارات التخزين في Supabase
  static const String productImagesPath = 'product_images';
  static const String categoryImagesPath = 'category_images';

  // إعدادات Cloudinary
  static String get cloudinaryCloudName =>
      dotenv.env['CLOUDINARY_CLOUD_NAME'] ?? '';
  static String get cloudinaryApiKey => dotenv.env['CLOUDINARY_API_KEY'] ?? '';
  static String get cloudinaryApiSecret =>
      dotenv.env['CLOUDINARY_API_SECRET'] ?? '';
  static const String cloudinaryProductImagesFolder = 'product_images';
  static const String cloudinaryCategoryImagesFolder = 'category_images';

  // حالات الطلب - تم تحديثها لتتطابق مع قاعدة البيانات
  static const String orderStatusPending = 'pending';
  static const String orderStatusProcessing = 'processing'; // إضافة جديدة
  static const String orderStatusShipped =
      'shipped'; // تم تغييرها من 'delivering'
  static const String orderStatusDelivered = 'delivered';
  static const String orderStatusCancelled = 'cancelled';

  // طرق الدفع
  static const String paymentMethodCash = 'cash';
  static const String paymentMethodWallet = 'wallet';
  static const String paymentMethodInstapay = 'instapay';
  static const String paymentMethodBankTransfer = 'bank_transfer';

  // العملة
  static const String currencySymbol = 'ج.م';

  // أنواع الملفات الشخصية (Profile Types)
  static const String profileTypeCustomer = 'customer';
  static const String profileTypeAdmin = 'admin';

  // صلاحيات المشرفين
  static const String permissionProducts = 'products';
  static const String permissionOrders = 'orders';
  static const String permissionCustomers = 'customers';
  static const String permissionAdmins = 'admins';
  static const String permissionStatistics = 'statistics';

  // رسائل الخطأ
  static const String errorNetworkMessage = 'تحقق من اتصالك بالإنترنت';
  static const String errorServerMessage = 'حدث خطأ في الخادم، حاول مرة أخرى';
  static const String errorAuthMessage = 'خطأ في تسجيل الدخول، تحقق من بياناتك';

  // مفاتيح التخزين المحلي
  static const String storageKeyToken = 'token';
  static const String storageKeyProfile = 'profile';
  static const String storageKeyTheme = 'theme';
  static const String storageKeyLanguage = 'language';
  static const String storageKeyFirstTime = 'first_time';

  // دالة مساعدة للحصول على نص حالة الطلب
  static String getOrderStatusText(String status) {
    switch (status) {
      case orderStatusPending:
        return 'قيد الانتظار';
      case orderStatusProcessing:
        return 'قيد المعالجة';
      case orderStatusShipped:
        return 'تم الشحن';
      case orderStatusDelivered:
        return 'تم التوصيل';
      case orderStatusCancelled:
        return 'ملغي';
      default:
        return 'غير معروف';
    }
  }

  // دالة مساعدة للحصول على لون حالة الطلب
  static Color getOrderStatusColor(String status) {
    switch (status) {
      case orderStatusPending:
        return Colors.orange;
      case orderStatusProcessing:
        return Colors.blue;
      case orderStatusShipped:
        return Colors.indigo;
      case orderStatusDelivered:
        return Colors.green;
      case orderStatusCancelled:
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  // قائمة جميع حالات الطلب المتاحة
  static const List<String> allOrderStatuses = [
    orderStatusPending,
    orderStatusProcessing,
    orderStatusShipped,
    orderStatusDelivered,
    orderStatusCancelled,
  ];

  // التحقق من صحة حالة الطلب
  static bool isValidOrderStatus(String status) {
    return allOrderStatuses.contains(status);
  }
}
