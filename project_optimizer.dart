// ===================================================================
// أداة تحسين وتنظيم المشروع المتقدمة
// متجر قطع غيار الدراجات النارية
// ===================================================================

import 'dart:io';

/// فئة تحسين المشروع المتقدمة
class ProjectOptimizer {
  static const String _version = '1.0.0';

  /// تشغيل تحسين شامل للمشروع
  static Future<void> runFullOptimization() async {
    print('⚡ بدء التحسين الشامل للمشروع...');
    print('📅 التاريخ: ${DateTime.now()}');
    print('🔢 الإصدار: $_version');
    print('');

    try {
      // 1. تحسين ملف pubspec.yaml
      await _optimizePubspec();

      // 2. تحسين imports في الملفات
      await _optimizeImports();

      // 3. إزالة الملفات غير المستخدمة
      await _removeUnusedFiles();

      // 4. تحسين بنية المجلدات
      await _optimizeFolderStructure();

      // 5. تحسين إعدادات البناء
      await _optimizeBuildSettings();

      // 6. إنشاء ملف .gitignore محسن
      await _createOptimizedGitignore();

      // 7. تحسين إعدادات IDE
      await _optimizeIDESettings();

      print('');
      print('✅ تم التحسين الشامل بنجاح!');
      print('🚀 المشروع محسن للأداء الأمثل');
      print('📦 حجم المشروع مُحسن');
    } catch (e) {
      print('❌ خطأ في التحسين: $e');
    }
  }

  /// تحسين ملف pubspec.yaml
  static Future<void> _optimizePubspec() async {
    print('📦 تحسين ملف pubspec.yaml...');

    final pubspecFile = File('pubspec.yaml');
    if (await pubspecFile.exists()) {
      final content = await pubspecFile.readAsString();
      
      // إزالة التعليقات غير الضرورية
      final lines = content.split('\n');
      final optimizedLines = <String>[];
      
      for (final line in lines) {
        // الاحتفاظ بالتعليقات المهمة فقط
        if (line.trim().startsWith('#') && 
            !line.contains('خط') && 
            !line.contains('Configuration') &&
            !line.contains('محدث') &&
            !line.contains('إصدار مستقر')) {
          continue;
        }
        optimizedLines.add(line);
      }
      
      await pubspecFile.writeAsString(optimizedLines.join('\n'));
      print('   ✅ تم تحسين pubspec.yaml');
    }
  }

  /// تحسين imports في الملفات
  static Future<void> _optimizeImports() async {
    print('📝 تحسين imports في الملفات...');

    final libDir = Directory('lib');
    if (await libDir.exists()) {
      await for (final entity in libDir.list(recursive: true)) {
        if (entity is File && entity.path.endsWith('.dart')) {
          await _optimizeFileImports(entity);
        }
      }
    }
  }

  /// تحسين imports في ملف واحد
  static Future<void> _optimizeFileImports(File file) async {
    try {
      final content = await file.readAsString();
      final lines = content.split('\n');
      final imports = <String>[];
      final otherLines = <String>[];
      
      bool inImportSection = true;
      
      for (final line in lines) {
        if (line.trim().startsWith('import ')) {
          if (inImportSection) {
            imports.add(line);
          } else {
            otherLines.add(line);
          }
        } else if (line.trim().isEmpty && inImportSection) {
          // استمرار في قسم الـ imports
        } else {
          inImportSection = false;
          otherLines.add(line);
        }
      }
      
      // ترتيب الـ imports
      imports.sort();
      
      // إعادة كتابة الملف
      final optimizedContent = [
        ...imports,
        '',
        ...otherLines,
      ].join('\n');
      
      if (optimizedContent != content) {
        await file.writeAsString(optimizedContent);
        print('   ✅ تم تحسين: ${file.path}');
      }
    } catch (e) {
      print('   ⚠️ خطأ في تحسين: ${file.path} ($e)');
    }
  }

  /// إزالة الملفات غير المستخدمة
  static Future<void> _removeUnusedFiles() async {
    print('🗑️ إزالة الملفات غير المستخدمة...');

    final unusedFiles = [
      'test/widget_test.dart',
      'integration_test/app_test.dart',
      'lib/main_backup.dart',
      'lib/temp.dart',
    ];

    for (final filePath in unusedFiles) {
      final file = File(filePath);
      if (await file.exists()) {
        try {
          await file.delete();
          print('   ✅ تم حذف: $filePath');
        } catch (e) {
          print('   ⚠️ لا يمكن حذف: $filePath ($e)');
        }
      }
    }
  }

  /// تحسين بنية المجلدات
  static Future<void> _optimizeFolderStructure() async {
    print('📁 تحسين بنية المجلدات...');

    // إنشاء مجلدات مفقودة إذا لزم الأمر
    final requiredDirs = [
      'lib/core/constants',
      'lib/core/utils',
      'lib/core/widgets',
      'lib/features/auth/data',
      'lib/features/auth/domain',
      'lib/features/auth/presentation',
    ];

    for (final dirPath in requiredDirs) {
      final dir = Directory(dirPath);
      if (!await dir.exists()) {
        try {
          await dir.create(recursive: true);
          print('   ✅ تم إنشاء: $dirPath');
        } catch (e) {
          print('   ⚠️ لا يمكن إنشاء: $dirPath ($e)');
        }
      }
    }
  }

  /// تحسين إعدادات البناء
  static Future<void> _optimizeBuildSettings() async {
    print('⚙️ تحسين إعدادات البناء...');

    // تحسين إعدادات Android
    await _optimizeAndroidBuildSettings();
    
    // تحسين إعدادات iOS
    await _optimizeIOSBuildSettings();
  }

  /// تحسين إعدادات Android
  static Future<void> _optimizeAndroidBuildSettings() async {
    final gradleFile = File('android/app/build.gradle.kts');
    if (await gradleFile.exists()) {
      final content = await gradleFile.readAsString();
      
      // التحقق من وجود إعدادات التحسين
      if (!content.contains('minifyEnabled')) {
        print('   ⚠️ يُنصح بتفعيل minifyEnabled في إعدادات Android');
      }
      
      if (!content.contains('shrinkResources')) {
        print('   ⚠️ يُنصح بتفعيل shrinkResources في إعدادات Android');
      }
      
      print('   ✅ تم فحص إعدادات Android');
    }
  }

  /// تحسين إعدادات iOS
  static Future<void> _optimizeIOSBuildSettings() async {
    final infoPlistFile = File('ios/Runner/Info.plist');
    if (await infoPlistFile.exists()) {
      print('   ✅ تم فحص إعدادات iOS');
    }
  }

  /// إنشاء ملف .gitignore محسن
  static Future<void> _createOptimizedGitignore() async {
    print('📄 إنشاء ملف .gitignore محسن...');

    const gitignoreContent = '''# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.buildlog/
.history
.svn/
migrate_working_dir/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.packages
.pub-cache/
.pub/
/build/

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release

# iOS related
**/ios/**/*.mode1v3
**/ios/**/*.mode2v3
**/ios/**/*.moved-aside
**/ios/**/*.pbxuser
**/ios/**/*.perspectivev3
**/ios/**/*sync/
**/ios/**/.sconsign.dblite
**/ios/**/.tags*
**/ios/**/.vagrant/
**/ios/**/DerivedData/
**/ios/**/Icon?
**/ios/**/Pods/
**/ios/**/.symlinks/
**/ios/**/profile
**/ios/**/xcuserdata
**/ios/.generated/
**/ios/Flutter/App.framework
**/ios/Flutter/Flutter.framework
**/ios/Flutter/Flutter.podspec
**/ios/Flutter/Generated.xcconfig
**/ios/Flutter/ephemeral/
**/ios/Flutter/app.flx
**/ios/Flutter/app.zip
**/ios/Flutter/flutter_assets/
**/ios/Flutter/flutter_export_environment.sh
**/ios/ServiceDefinitions.json
**/ios/Runner/GeneratedPluginRegistrant.*

# Windows related
**/windows/**/flutter/ephemeral/

# Coverage
coverage/

# Environment files
.env.local
.env.development
.env.production

# Temporary files
*.tmp
*.temp
*~

# Log files
*.log
logs/

# Database files
*.db
*.sqlite
*.sqlite3

# Backup files
*.bak
*.backup

# Archive files
*.zip
*.tar.gz
*.rar

# IDE files
.vscode/settings.json
.vscode/launch.json
.vscode/tasks.json
''';

    final gitignoreFile = File('.gitignore');
    await gitignoreFile.writeAsString(gitignoreContent);
    print('   ✅ تم إنشاء .gitignore محسن');
  }

  /// تحسين إعدادات IDE
  static Future<void> _optimizeIDESettings() async {
    print('💻 تحسين إعدادات IDE...');

    // إنشاء مجلد .vscode إذا لم يكن موجوداً
    final vscodeDir = Directory('.vscode');
    if (!await vscodeDir.exists()) {
      await vscodeDir.create();
    }

    // إنشاء ملف إعدادات VS Code محسن
    const vscodeSettings = '''{
  "dart.flutterSdkPath": null,
  "dart.lineLength": 80,
  "dart.insertArgumentPlaceholders": false,
  "dart.showTodos": true,
  "dart.closingLabels": true,
  "dart.previewFlutterUiGuides": true,
  "dart.previewFlutterUiGuidesCustomTracking": true,
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll": true,
    "source.organizeImports": true
  },
  "files.associations": {
    "*.dart": "dart"
  },
  "emmet.includeLanguages": {
    "dart": "html"
  }
}''';

    final settingsFile = File('.vscode/settings.json');
    await settingsFile.writeAsString(vscodeSettings);
    print('   ✅ تم تحسين إعدادات VS Code');
  }

  /// تشغيل فحص الأداء
  static Future<void> performanceCheck() async {
    print('🔍 فحص أداء المشروع...');

    // فحص عدد الملفات
    final libDir = Directory('lib');
    if (await libDir.exists()) {
      final dartFiles = await libDir
          .list(recursive: true)
          .where((entity) => entity is File && entity.path.endsWith('.dart'))
          .length;
      
      print('   📊 عدد ملفات Dart: $dartFiles');
      
      if (dartFiles > 100) {
        print('   ⚠️ عدد كبير من الملفات - فكر في تقسيم المشروع');
      } else {
        print('   ✅ عدد الملفات مناسب');
      }
    }

    // فحص حجم الأصول
    final assetsDir = Directory('assets');
    if (await assetsDir.exists()) {
      int totalSize = 0;
      await for (final entity in assetsDir.list(recursive: true)) {
        if (entity is File) {
          totalSize += await entity.length();
        }
      }
      
      final sizeMB = totalSize / 1024 / 1024;
      print('   📊 حجم الأصول: ${sizeMB.toStringAsFixed(2)} MB');
      
      if (sizeMB > 50) {
        print('   ⚠️ حجم كبير للأصول - فكر في ضغط الصور');
      } else {
        print('   ✅ حجم الأصول مناسب');
      }
    }
  }
}

/// تشغيل التحسين من سطر الأوامر
void main() async {
  await ProjectOptimizer.runFullOptimization();
  await ProjectOptimizer.performanceCheck();
}
