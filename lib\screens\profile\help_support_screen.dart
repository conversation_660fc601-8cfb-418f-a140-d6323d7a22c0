import 'package:flutter/material.dart';
import 'package:motorcycle_parts_shop/core/theme/app_theme.dart';
import 'package:motorcycle_parts_shop/core/utils/responsive_helper.dart';
import 'package:motorcycle_parts_shop/screens/support/chatbot_screen.dart';

class HelpSupportScreen extends StatelessWidget {
  const HelpSupportScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('المساعدة والدعم'), centerTitle: true),
      body: ResponsiveBuilder(
        builder: (context, constraints) {
          final padding = ResponsiveHelper.getPadding(context);

          return ListView(
            padding: EdgeInsets.all(padding),
            children: [
              _buildFAQSection(),
              const SizedBox(height: 24),
              _buildContactSection(context),
            ],
          );
        },
      ),
    );
  }

  Widget _buildFAQSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'الأسئلة الشائعة',
          style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        _buildFAQItem(
          'كيف يمكنني تتبع طلبي؟',
          'يمكنك تتبع طلبك من خلال قسم "سجل الطلبات" في الملف الشخصي.',
        ),
        _buildFAQItem(
          'ما هي طرق الدفع المتاحة؟',
          'نقبل الدفع عند الاستلام  والمحافظ الإلكترونية.',
        ),
        _buildFAQItem(
          'كيف يمكنني إرجاع منتج؟',
          'يمكنك إرجاع المنتج خلال 14 يوماً من تاريخ الاستلام. تواصل مع خدمة العملاء للمزيد من التفاصيل.',
        ),
        _buildFAQItem(
          'ما هي سياسة الضمان؟',
          'نقدم ضمان لمدة سنة على جميع قطع الغيار الأصلية.',
        ),
      ],
    );
  }

  Widget _buildFAQItem(String question, String answer) {
    return ExpansionTile(
      title: Text(
        question,
        style: const TextStyle(fontWeight: FontWeight.w600),
      ),
      children: [
        Padding(padding: const EdgeInsets.all(16.0), child: Text(answer)),
      ],
    );
  }

  Widget _buildContactSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'تواصل معنا',
          style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        ListTile(
          leading: const CircleAvatar(
            backgroundColor: AppTheme.primaryColor,
            child: Icon(Icons.phone, color: Colors.white),
          ),
          title: const Text('الهاتف'),
          subtitle: const Text('0123456789'),
          onTap: () {
            // تنفيذ مكالمة هاتفية
            showDialog(
              context: context,
              builder:
                  (context) => AlertDialog(
                    title: const Text('اتصال هاتفي'),
                    content: const Text('هل تريد الاتصال بالرقم 0123456789؟'),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.pop(context),
                        child: const Text('إلغاء'),
                      ),
                      TextButton(
                        onPressed: () {
                          Navigator.pop(context);
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(content: Text('جاري الاتصال...')),
                          );
                          // هنا يمكن استخدام مكتبة url_launcher لتنفيذ المكالمة الهاتفية
                        },
                        child: const Text('اتصال'),
                      ),
                    ],
                  ),
            );
          },
        ),
        ListTile(
          leading: const CircleAvatar(
            backgroundColor: AppTheme.primaryColor,
            child: Icon(Icons.email, color: Colors.white),
          ),
          title: const Text('البريد الإلكتروني'),
          subtitle: const Text('<EMAIL>'),
          onTap: () {
            // فتح تطبيق البريد الإلكتروني
            showDialog(
              context: context,
              builder:
                  (context) => AlertDialog(
                    title: const Text('البريد الإلكتروني'),
                    content: const Text(
                      'هل تريد إرسال بريد إلكتروني إلى <EMAIL>؟',
                    ),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.pop(context),
                        child: const Text('إلغاء'),
                      ),
                      TextButton(
                        onPressed: () {
                          Navigator.pop(context);
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text(
                                'جاري فتح تطبيق البريد الإلكتروني...',
                              ),
                            ),
                          );
                          // هنا يمكن استخدام مكتبة url_launcher لفتح تطبيق البريد الإلكتروني
                        },
                        child: const Text('إرسال'),
                      ),
                    ],
                  ),
            );
          },
        ),
        ListTile(
          leading: const CircleAvatar(
            backgroundColor: AppTheme.primaryColor,
            child: Icon(Icons.chat, color: Colors.white),
          ),
          title: const Text('المحادثة المباشرة'),
          subtitle: const Text('متاح من 9 صباحاً حتى 9 مساءً'),
          onTap: () {
            // بدء محادثة مباشرة
            showDialog(
              context: context,
              builder:
                  (context) => AlertDialog(
                    title: const Text('المحادثة المباشرة'),
                    content: const Text(
                      'هل تريد بدء محادثة مباشرة مع فريق الدعم؟',
                    ),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.pop(context),
                        child: const Text('إلغاء'),
                      ),
                      TextButton(
                        onPressed: () {
                          Navigator.pop(context);
                          // فتح شاشة المحادثة المباشرة (ChatbotScreen)
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const ChatbotScreen(),
                            ),
                          );
                        },
                        child: const Text('بدء المحادثة'),
                      ),
                    ],
                  ),
            );
          },
        ),
      ],
    );
  }
}
