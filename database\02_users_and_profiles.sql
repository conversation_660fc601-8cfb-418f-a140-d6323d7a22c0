-- ===================================================================
-- جداول المستخدمين والملفات الشخصية
-- متجر قطع غيار الدراجات النارية - Supabase Database
-- ===================================================================

-- ===================================================================
-- 1. جدول الملفات الشخصية (المستخدمين)
-- ===================================================================
CREATE TABLE IF NOT EXISTS profiles (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(20),
    address TEXT,
    profile_type VARCHAR(20) NOT NULL DEFAULT 'customer' CHECK (profile_type IN ('customer', 'admin')),
    birth_date DATE,
    governorate VARCHAR(50),
    center VARCHAR(50),
    last_login_at TIMESTAMPTZ,
    order_count INTEGER DEFAULT 0,
    wishlist_count INTEGER DEFAULT 0,
    average_rating DECIMAL(3,2) DEFAULT 0.00,
    total_spent DECIMAL(12,2) DEFAULT 0.00,
    raw_user_meta_data JSONB,
    unread_notifications_count INTEGER DEFAULT 0,
    total_notifications_count INTEGER DEFAULT 0,
    last_notification_check TIMESTAMPTZ,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_profiles_email ON profiles(email);
CREATE INDEX IF NOT EXISTS idx_profiles_profile_type ON profiles(profile_type);
CREATE INDEX IF NOT EXISTS idx_profiles_governorate ON profiles(governorate);
CREATE INDEX IF NOT EXISTS idx_profiles_created_at ON profiles(created_at);

-- ===================================================================
-- 2. جدول العناوين
-- ===================================================================
CREATE TABLE IF NOT EXISTS addresses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    title VARCHAR(50) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    street_address TEXT NOT NULL,
    city VARCHAR(50) NOT NULL,
    state VARCHAR(50) NOT NULL,
    postal_code VARCHAR(10),
    phone_number VARCHAR(20) NOT NULL,
    is_default BOOLEAN DEFAULT false,
    latitude DECIMAL(10,8),
    longitude DECIMAL(11,8),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_addresses_user_id ON addresses(user_id);
CREATE INDEX IF NOT EXISTS idx_addresses_is_default ON addresses(is_default);

-- ===================================================================
-- 3. جدول إعدادات الإشعارات
-- ===================================================================
CREATE TABLE IF NOT EXISTS notification_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    push_notifications BOOLEAN DEFAULT true,
    email_notifications BOOLEAN DEFAULT true,
    sms_notifications BOOLEAN DEFAULT false,
    order_updates BOOLEAN DEFAULT true,
    promotional_offers BOOLEAN DEFAULT true,
    new_products BOOLEAN DEFAULT false,
    price_drops BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(user_id)
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_notification_settings_user_id ON notification_settings(user_id);

-- ===================================================================
-- 4. جدول مواقع المستخدمين (للتوصيل)
-- ===================================================================
CREATE TABLE IF NOT EXISTS user_locations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    latitude DECIMAL(10,8) NOT NULL,
    longitude DECIMAL(11,8) NOT NULL,
    address_text TEXT,
    location_type VARCHAR(20) DEFAULT 'delivery' CHECK (location_type IN ('delivery', 'pickup', 'current')),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_user_locations_user_id ON user_locations(user_id);
CREATE INDEX IF NOT EXISTS idx_user_locations_location_type ON user_locations(location_type);

-- ===================================================================
-- الدوال المساعدة
-- ===================================================================

-- دالة إنشاء مستخدم وملف تعريف تلقائياً
CREATE OR REPLACE FUNCTION create_user_and_profile()
RETURNS TRIGGER AS $$
DECLARE
    user_email TEXT;
    user_name TEXT;
BEGIN
    -- التأكد من وجود البريد الإلكتروني
    user_email := COALESCE(NEW.email, 'user-' || NEW.id || '@temp.com');

    -- استخراج الاسم من البيانات الوصفية أو البريد الإلكتروني
    user_name := COALESCE(
        NEW.raw_user_meta_data->>'name',
        NEW.raw_user_meta_data->>'full_name',
        NEW.raw_user_meta_data->>'display_name',
        split_part(user_email, '@', 1),
        'مستخدم جديد'
    );

    -- إنشاء ملف تعريف المستخدم
    INSERT INTO profiles (
        id, email, name, phone, profile_type,
        raw_user_meta_data, created_at, updated_at
    ) VALUES (
        NEW.id, user_email, user_name,
        COALESCE(NEW.raw_user_meta_data->>'phone', NEW.phone, ''),
        'customer', NEW.raw_user_meta_data, NOW(), NOW()
    )
    ON CONFLICT (id) DO UPDATE SET
        email = EXCLUDED.email,
        name = EXCLUDED.name,
        phone = EXCLUDED.phone,
        updated_at = NOW(),
        raw_user_meta_data = EXCLUDED.raw_user_meta_data;

    -- إنشاء إعدادات إشعارات افتراضية
    BEGIN
        INSERT INTO notification_settings (user_id)
        VALUES (NEW.id)
        ON CONFLICT (user_id) DO NOTHING;
    EXCEPTION
        WHEN OTHERS THEN
            -- تجاهل الخطأ إذا لم يكن جدول notification_settings موجود
            NULL;
    END;

    RETURN NEW;
EXCEPTION
    WHEN OTHERS THEN
        RAISE WARNING 'خطأ في إنشاء ملف تعريف المستخدم: %', SQLERRM;
        RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path = public;

-- ===================================================================
-- المحفزات
-- ===================================================================

-- محفز إنشاء ملف تعريف تلقائياً
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION create_user_and_profile();

-- محفزات تحديث الطوابع الزمنية
CREATE TRIGGER update_profiles_updated_at
    BEFORE UPDATE ON profiles
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_addresses_updated_at
    BEFORE UPDATE ON addresses
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_notification_settings_updated_at
    BEFORE UPDATE ON notification_settings
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_locations_updated_at
    BEFORE UPDATE ON user_locations
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- ===================================================================
-- سياسات الأمان (RLS)
-- ===================================================================

-- تفعيل RLS
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE addresses ENABLE ROW LEVEL SECURITY;
ALTER TABLE notification_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_locations ENABLE ROW LEVEL SECURITY;


-- سياسات للملفات الشخصية (آمنة بدون تكرار لا نهائي)
CREATE POLICY "Users can view own profile" ON profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON profiles
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can delete own profile" ON profiles
    FOR DELETE USING (auth.uid() = id);

-- سياسة المشرفين (آمنة باستخدام auth.users مباشرة)
CREATE POLICY "Admins can manage all profiles" ON profiles
    FOR ALL USING (
        -- التحقق من جدول auth.users مباشرة لتجنب التكرار اللا نهائي
        EXISTS (
            SELECT 1 FROM auth.users
            WHERE id = auth.uid()
            AND raw_user_meta_data->>'profile_type' = 'admin'
        )
    );
-- سياسات أمان لجدول العناوين
CREATE POLICY "Users can manage own addresses" ON addresses
FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Admins can manage all addresses" ON addresses
FOR ALL USING (
    EXISTS (
        SELECT 1 FROM auth.users
        WHERE id = auth.uid()
        AND raw_user_meta_data->>'profile_type' = 'admin'
    )
);

-- سياسات لإعدادات الإشعارات
CREATE POLICY "Users can manage own notification settings" ON notification_settings FOR ALL USING (auth.uid() = user_id);

-- سياسات لمواقع المستخدمين
CREATE POLICY "Users can manage own locations" ON user_locations FOR ALL USING (auth.uid() = user_id);

-- ===================================================================
-- دوال مساعدة آمنة
-- ===================================================================

-- دالة آمنة لتعيين سياق المشرف
CREATE OR REPLACE FUNCTION set_admin_context(user_id UUID)
RETURNS VOID AS $$
DECLARE
    user_type TEXT;
BEGIN
    -- جلب نوع المستخدم مباشرة من جدول auth.users
    SELECT
        CASE
            WHEN raw_user_meta_data->>'profile_type' = 'admin' THEN 'admin'
            ELSE 'customer'
        END INTO user_type
    FROM auth.users
    WHERE id = user_id;

    -- تعيين متغير الجلسة
    PERFORM set_config('app.current_user_type', COALESCE(user_type, 'customer'), true);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- تعيين متغير افتراضي
SELECT set_config('app.current_user_type', 'customer', true);

-- ===================================================================
-- رسالة نجاح
-- ===================================================================

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '✅ تم إنشاء جداول المستخدمين والملفات الشخصية بنجاح!';
    RAISE NOTICE '';
    RAISE NOTICE '👤 الجداول المنشأة:';
    RAISE NOTICE '   • profiles - الملفات الشخصية للمستخدمين';
    RAISE NOTICE '   • addresses - عناوين المستخدمين';
    RAISE NOTICE '   • notification_settings - إعدادات الإشعارات';
    RAISE NOTICE '   • user_locations - مواقع المستخدمين';
    RAISE NOTICE '';
    RAISE NOTICE '🔒 الأمان:';
    RAISE NOTICE '   • Row Level Security مفعل';
    RAISE NOTICE '   • سياسات الأمان آمنة (بدون تكرار لا نهائي)';
    RAISE NOTICE '   • دوال مساعدة آمنة للمشرفين';
    RAISE NOTICE '';
    RAISE NOTICE '🔄 المحفزات:';
    RAISE NOTICE '   • تحديث updated_at تلقائياً';
    RAISE NOTICE '   • إنشاء ملف شخصي عند التسجيل';
    RAISE NOTICE '';
END $$;
