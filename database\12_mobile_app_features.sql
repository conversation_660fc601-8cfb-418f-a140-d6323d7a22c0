-- ===================================================================
-- ميزات التطبيق المحمول
-- متجر قطع غيار الدراجات النارية - Supabase Database
-- ===================================================================

-- ===================================================================
-- 1. إعدادات التطبيق العامة
-- ===================================================================

-- جدول إعدادات التطبيق العامة
CREATE TABLE IF NOT EXISTS app_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value JSONB NOT NULL DEFAULT '{}'::jsonb,
    description TEXT,
    is_public BOOLEAN DEFAULT false, -- هل يمكن للمستخدمين رؤيتها
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_app_settings_key ON app_settings(setting_key);
CREATE INDEX IF NOT EXISTS idx_app_settings_public ON app_settings(is_public);

-- ===================================================================
-- 2. إعدادات المستخدم للتطبيق
-- ===================================================================

-- جدول إعدادات شخصية للمستخدمين في التطبيق
CREATE TABLE IF NOT EXISTS user_app_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    theme VARCHAR(20) DEFAULT 'light' CHECK (theme IN ('light', 'dark', 'auto')),
    language VARCHAR(10) DEFAULT 'ar' CHECK (language IN ('ar', 'en')),
    currency VARCHAR(3) DEFAULT 'EGP',
    timezone VARCHAR(50) DEFAULT 'Africa/Cairo',
    push_enabled BOOLEAN DEFAULT true,
    location_enabled BOOLEAN DEFAULT false,
    biometric_enabled BOOLEAN DEFAULT false,
    auto_backup BOOLEAN DEFAULT true,
    sound_enabled BOOLEAN DEFAULT true,
    vibration_enabled BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(user_id)
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_user_app_settings_user_id ON user_app_settings(user_id);
CREATE INDEX IF NOT EXISTS idx_user_app_settings_theme ON user_app_settings(theme);
CREATE INDEX IF NOT EXISTS idx_user_app_settings_language ON user_app_settings(language);

-- ===================================================================
-- 3. أجهزة المستخدمين (FCM Tokens)
-- ===================================================================

-- جدول أجهزة المستخدمين للإشعارات
CREATE TABLE IF NOT EXISTS user_devices (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    device_token VARCHAR(255) NOT NULL,
    device_type VARCHAR(20) NOT NULL CHECK (device_type IN ('android', 'ios', 'web')),
    device_name VARCHAR(100),
    device_model VARCHAR(100),
    app_version VARCHAR(20),
    os_version VARCHAR(20),
    is_active BOOLEAN DEFAULT true,
    last_used_at TIMESTAMPTZ DEFAULT NOW(),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(device_token)
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_user_devices_user_id ON user_devices(user_id);
CREATE INDEX IF NOT EXISTS idx_user_devices_token ON user_devices(device_token);
CREATE INDEX IF NOT EXISTS idx_user_devices_type ON user_devices(device_type);
CREATE INDEX IF NOT EXISTS idx_user_devices_active ON user_devices(is_active);

-- ===================================================================
-- 4. حملات الإشعارات
-- ===================================================================

-- جدول حملات الإشعارات الجماعية
CREATE TABLE IF NOT EXISTS notification_campaigns (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    image_url TEXT,
    action_url TEXT,
    target_audience JSONB DEFAULT '{}'::jsonb, -- معايير الاستهداف
    scheduled_at TIMESTAMPTZ,
    sent_at TIMESTAMPTZ,
    status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'scheduled', 'sending', 'sent', 'cancelled')),
    total_recipients INTEGER DEFAULT 0,
    successful_sends INTEGER DEFAULT 0,
    failed_sends INTEGER DEFAULT 0,
    opened_count INTEGER DEFAULT 0,
    clicked_count INTEGER DEFAULT 0,
    created_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_notification_campaigns_status ON notification_campaigns(status);
CREATE INDEX IF NOT EXISTS idx_notification_campaigns_scheduled_at ON notification_campaigns(scheduled_at);
CREATE INDEX IF NOT EXISTS idx_notification_campaigns_created_by ON notification_campaigns(created_by);

-- ===================================================================
-- 5. جلسات المستخدمين
-- ===================================================================

-- جدول جلسات المستخدمين
CREATE TABLE IF NOT EXISTS user_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES profiles(id) ON DELETE SET NULL,
    session_id VARCHAR(100) UNIQUE NOT NULL,
    device_info JSONB DEFAULT '{}'::jsonb,
    ip_address INET,
    location_data JSONB DEFAULT '{}'::jsonb,
    started_at TIMESTAMPTZ DEFAULT NOW(),
    ended_at TIMESTAMPTZ,
    duration_seconds INTEGER,
    pages_visited INTEGER DEFAULT 0,
    actions_performed INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_session_id ON user_sessions(session_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_started_at ON user_sessions(started_at);
CREATE INDEX IF NOT EXISTS idx_user_sessions_active ON user_sessions(is_active);

-- ===================================================================
-- 6. أحداث التطبيق
-- ===================================================================

-- جدول أحداث التطبيق للتحليلات
CREATE TABLE IF NOT EXISTS app_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES profiles(id) ON DELETE SET NULL,
    session_id VARCHAR(100),
    event_name VARCHAR(100) NOT NULL,
    event_category VARCHAR(50),
    event_data JSONB DEFAULT '{}'::jsonb,
    screen_name VARCHAR(100),
    timestamp TIMESTAMPTZ DEFAULT NOW()
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_app_events_user_id ON app_events(user_id);
CREATE INDEX IF NOT EXISTS idx_app_events_session_id ON app_events(session_id);
CREATE INDEX IF NOT EXISTS idx_app_events_name ON app_events(event_name);
CREATE INDEX IF NOT EXISTS idx_app_events_category ON app_events(event_category);
CREATE INDEX IF NOT EXISTS idx_app_events_timestamp ON app_events(timestamp);

-- ===================================================================
-- 7. نظام الأمان المتقدم
-- ===================================================================

-- جدول محاولات تسجيل الدخول
CREATE TABLE IF NOT EXISTS login_attempts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255),
    ip_address INET NOT NULL,
    user_agent TEXT,
    device_info JSONB DEFAULT '{}'::jsonb,
    success BOOLEAN NOT NULL,
    failure_reason VARCHAR(100),
    location_data JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_login_attempts_email ON login_attempts(email);
CREATE INDEX IF NOT EXISTS idx_login_attempts_ip ON login_attempts(ip_address);
CREATE INDEX IF NOT EXISTS idx_login_attempts_success ON login_attempts(success);
CREATE INDEX IF NOT EXISTS idx_login_attempts_created_at ON login_attempts(created_at);

-- جدول الجلسات النشطة
CREATE TABLE IF NOT EXISTS active_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    device_info JSONB DEFAULT '{}'::jsonb,
    ip_address INET,
    last_activity TIMESTAMPTZ DEFAULT NOW(),
    expires_at TIMESTAMPTZ NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_active_sessions_user_id ON active_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_active_sessions_token ON active_sessions(session_token);
CREATE INDEX IF NOT EXISTS idx_active_sessions_expires_at ON active_sessions(expires_at);

-- ===================================================================
-- 8. إعدادات الخصوصية
-- ===================================================================

-- جدول إعدادات الخصوصية للمستخدمين
CREATE TABLE IF NOT EXISTS user_privacy_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    profile_visibility VARCHAR(20) DEFAULT 'private' CHECK (profile_visibility IN ('public', 'friends', 'private')),
    show_online_status BOOLEAN DEFAULT false,
    allow_friend_requests BOOLEAN DEFAULT true,
    show_purchase_history BOOLEAN DEFAULT false,
    allow_product_recommendations BOOLEAN DEFAULT true,
    allow_marketing_emails BOOLEAN DEFAULT true,
    allow_analytics_tracking BOOLEAN DEFAULT true,
    data_retention_days INTEGER DEFAULT 365,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(user_id)
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_user_privacy_settings_user_id ON user_privacy_settings(user_id);

-- ===================================================================
-- الدوال المساعدة لميزات التطبيق المحمول (مع التعديلات)
-- ===================================================================

-- دالة تسجيل جهاز جديد (مصححة)
CREATE OR REPLACE FUNCTION register_user_device(
    p_user_id UUID,
    p_device_token VARCHAR,
    p_device_type VARCHAR,
    p_device_name VARCHAR DEFAULT NULL,
    p_device_model VARCHAR DEFAULT NULL,
    p_app_version VARCHAR DEFAULT NULL,
    p_os_version VARCHAR DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    v_device_id UUID;
BEGIN
    -- إلغاء تفعيل الأجهزة القديمة بنفس التوكن
    UPDATE user_devices SET is_active = false WHERE device_token = p_device_token;

    -- تسجيل الجهاز الجديد
    INSERT INTO user_devices (
        user_id, device_token, device_type, device_name, device_model,
        app_version, os_version, is_active, last_used_at
    ) VALUES (
        p_user_id, p_device_token, p_device_type, p_device_name, p_device_model,
        p_app_version, p_os_version, true, NOW()
    ) RETURNING id INTO v_device_id;

    -- إنشاء إعدادات التطبيق الافتراضية إذا لم تكن موجودة
    INSERT INTO user_app_settings (user_id)
    VALUES (p_user_id)
    ON CONFLICT (user_id) DO NOTHING;

    -- إنشاء إعدادات الخصوصية الافتراضية إذا لم تكن موجودة
    INSERT INTO user_privacy_settings (user_id)
    VALUES (p_user_id)
    ON CONFLICT (user_id) DO NOTHING;

    RETURN v_device_id;
END;
$$;

-- دالة تسجيل حدث في التطبيق (مصححة)
CREATE OR REPLACE FUNCTION log_app_event(
    p_event_name VARCHAR,
    p_user_id UUID DEFAULT NULL,
    p_session_id VARCHAR DEFAULT NULL,
    p_event_category VARCHAR DEFAULT NULL,
    p_event_data JSONB DEFAULT '{}'::jsonb,
    p_screen_name VARCHAR DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    v_event_id UUID;
BEGIN
    INSERT INTO app_events (
        user_id, session_id, event_name, event_category, event_data, screen_name
    ) VALUES (
        p_user_id, p_session_id, p_event_name, p_event_category, p_event_data, p_screen_name
    ) RETURNING id INTO v_event_id;

    -- تحديث إحصائيات الجلسة إذا كانت موجودة
    IF p_session_id IS NOT NULL THEN
        UPDATE user_sessions SET
            actions_performed = actions_performed + 1,
            ended_at = NOW(),
            duration_seconds = EXTRACT(EPOCH FROM (NOW() - started_at))::INTEGER
        WHERE session_id = p_session_id AND is_active = true;
    END IF;

    RETURN v_event_id;
END;
$$;

-- دالة بدء جلسة جديدة
CREATE OR REPLACE FUNCTION start_user_session(
    p_session_id VARCHAR,
    p_user_id UUID DEFAULT NULL,
    p_device_info JSONB DEFAULT '{}'::jsonb,
    p_ip_address INET DEFAULT NULL,
    p_location_data JSONB DEFAULT '{}'::jsonb
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    v_session_uuid UUID;
BEGIN
    -- إنهاء الجلسات النشطة للمستخدم
    IF p_user_id IS NOT NULL THEN
        UPDATE user_sessions SET
            is_active = false,
            ended_at = NOW(),
            duration_seconds = EXTRACT(EPOCH FROM (NOW() - started_at))::INTEGER
        WHERE user_id = p_user_id AND is_active = true;
    END IF;

    -- بدء جلسة جديدة
    INSERT INTO user_sessions (
        user_id, session_id, device_info, ip_address, location_data, is_active
    ) VALUES (
        p_user_id, p_session_id, p_device_info, p_ip_address, p_location_data, true
    ) RETURNING id INTO v_session_uuid;

    -- تحديث آخر نشاط للمستخدم
    IF p_user_id IS NOT NULL THEN
        UPDATE profiles SET last_login_at = NOW() WHERE id = p_user_id;
    END IF;

    RETURN v_session_uuid;
END;
$$;

-- دالة تسجيل محاولة تسجيل دخول
CREATE OR REPLACE FUNCTION log_login_attempt(
    p_email VARCHAR,
    p_ip_address INET,
    p_success BOOLEAN,
    p_user_agent TEXT DEFAULT NULL,
    p_device_info JSONB DEFAULT '{}'::jsonb,
    p_failure_reason VARCHAR DEFAULT NULL,
    p_location_data JSONB DEFAULT '{}'::jsonb
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    v_attempt_id UUID;
BEGIN
    INSERT INTO login_attempts (
        email, ip_address, user_agent, device_info, success, failure_reason, location_data
    ) VALUES (
        p_email, p_ip_address, p_user_agent, p_device_info, p_success, p_failure_reason, p_location_data
    ) RETURNING id INTO v_attempt_id;

    RETURN v_attempt_id;
END;
$$;

-- دالة إرسال حملة إشعارات
CREATE OR REPLACE FUNCTION send_notification_campaign(
    p_campaign_id UUID
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    v_campaign RECORD;
    v_device RECORD;
    v_sent_count INTEGER := 0;
    v_failed_count INTEGER := 0;
BEGIN
    -- الحصول على بيانات الحملة
    SELECT * INTO v_campaign FROM notification_campaigns WHERE id = p_campaign_id;

    IF NOT FOUND OR v_campaign.status != 'scheduled' THEN
        RETURN false;
    END IF;

    -- تحديث حالة الحملة
    UPDATE notification_campaigns SET
        status = 'sending',
        sent_at = NOW(),
        updated_at = NOW()
    WHERE id = p_campaign_id;

    -- إرسال الإشعارات للأجهزة المستهدفة
    FOR v_device IN (
        SELECT DISTINCT ud.* FROM user_devices ud
        INNER JOIN profiles p ON ud.user_id = p.id
        WHERE ud.is_active = true
        -- يمكن إضافة معايير الاستهداف هنا بناءً على v_campaign.target_audience
    ) LOOP
        BEGIN
            -- هنا يتم الإرسال الفعلي للإشعار عبر FCM أو خدمة أخرى
            -- في الوقت الحالي نسجل فقط في جدول الإشعارات
            INSERT INTO notifications (
                user_id, title, body, type, data, delivery_status
            ) VALUES (
                v_device.user_id, v_campaign.title, v_campaign.message, 'campaign',
                jsonb_build_object('campaign_id', p_campaign_id, 'device_token', v_device.device_token),
                'sent'
            );

            v_sent_count := v_sent_count + 1;
        EXCEPTION
            WHEN OTHERS THEN
                v_failed_count := v_failed_count + 1;
        END;
    END LOOP;

    -- تحديث إحصائيات الحملة
    UPDATE notification_campaigns SET
        status = 'sent',
        total_recipients = v_sent_count + v_failed_count,
        successful_sends = v_sent_count,
        failed_sends = v_failed_count,
        updated_at = NOW()
    WHERE id = p_campaign_id;

    RETURN true;
END;
$$;

-- دالة تنظيف البيانات القديمة للتطبيق المحمول
CREATE OR REPLACE FUNCTION cleanup_mobile_app_data()
RETURNS TABLE (
    table_name TEXT,
    deleted_count INTEGER
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    v_deleted_count INTEGER;
BEGIN
    -- تنظيف أحداث التطبيق القديمة (أكثر من 6 أشهر)
    DELETE FROM app_events WHERE timestamp < NOW() - INTERVAL '6 months';
    GET DIAGNOSTICS v_deleted_count = ROW_COUNT;

    table_name := 'app_events';
    deleted_count := v_deleted_count;
    RETURN NEXT;

    -- تنظيف الجلسات القديمة (أكثر من 3 أشهر)
    DELETE FROM user_sessions WHERE started_at < NOW() - INTERVAL '3 months';
    GET DIAGNOSTICS v_deleted_count = ROW_COUNT;

    table_name := 'user_sessions';
    deleted_count := v_deleted_count;
    RETURN NEXT;

    -- تنظيف محاولات تسجيل الدخول القديمة (أكثر من شهر)
    DELETE FROM login_attempts WHERE created_at < NOW() - INTERVAL '1 month';
    GET DIAGNOSTICS v_deleted_count = ROW_COUNT;

    table_name := 'login_attempts';
    deleted_count := v_deleted_count;
    RETURN NEXT;

    -- تنظيف الجلسات المنتهية الصلاحية
    DELETE FROM active_sessions WHERE expires_at < NOW();
    GET DIAGNOSTICS v_deleted_count = ROW_COUNT;

    table_name := 'active_sessions';
    deleted_count := v_deleted_count;
    RETURN NEXT;

    -- إلغاء تفعيل الأجهزة غير المستخدمة (أكثر من 3 أشهر)
    UPDATE user_devices SET is_active = false
    WHERE last_used_at < NOW() - INTERVAL '3 months' AND is_active = true;
    GET DIAGNOSTICS v_deleted_count = ROW_COUNT;

    table_name := 'user_devices (deactivated)';
    deleted_count := v_deleted_count;
    RETURN NEXT;
END;
$$;

-- ===================================================================
-- المحفزات للتحديث التلقائي
-- ===================================================================

-- محفز تحديث الطوابع الزمنية
CREATE TRIGGER update_app_settings_updated_at
    BEFORE UPDATE ON app_settings
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_app_settings_updated_at
    BEFORE UPDATE ON user_app_settings
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_notification_campaigns_updated_at
    BEFORE UPDATE ON notification_campaigns
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_privacy_settings_updated_at
    BEFORE UPDATE ON user_privacy_settings
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- ===================================================================
-- سياسات الأمان (RLS)
-- ===================================================================

-- تفعيل RLS
ALTER TABLE app_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_app_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_devices ENABLE ROW LEVEL SECURITY;
ALTER TABLE notification_campaigns ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE app_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE login_attempts ENABLE ROW LEVEL SECURITY;
ALTER TABLE active_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_privacy_settings ENABLE ROW LEVEL SECURITY;

-- سياسات إعدادات التطبيق
CREATE POLICY "Anyone can view public app settings" ON app_settings FOR SELECT USING (is_public = true);
CREATE POLICY "Admins can manage app settings" ON app_settings FOR ALL USING (
    EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND profile_type = 'admin')
);

-- سياسات إعدادات المستخدم
CREATE POLICY "Users can manage own app settings" ON user_app_settings FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Users can manage own devices" ON user_devices FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Users can manage own privacy settings" ON user_privacy_settings FOR ALL USING (auth.uid() = user_id);

-- سياسات حملات الإشعارات (للمشرفين فقط)
CREATE POLICY "Admins can manage notification campaigns" ON notification_campaigns FOR ALL USING (
    EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND profile_type = 'admin')
);

-- سياسات الجلسات والأحداث
CREATE POLICY "Users can view own sessions" ON user_sessions FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can view own app events" ON app_events FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "System can insert app events" ON app_events FOR INSERT WITH CHECK (true);
CREATE POLICY "Users can manage own active sessions" ON active_sessions FOR ALL USING (auth.uid() = user_id);

-- سياسات محاولات تسجيل الدخول (للمشرفين فقط)
CREATE POLICY "Admins can view login attempts" ON login_attempts FOR SELECT USING (
    EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND profile_type = 'admin')
);
CREATE POLICY "System can insert login attempts" ON login_attempts FOR INSERT WITH CHECK (true);

-- ===================================================================
-- البيانات الأولية - إعدادات التطبيق
-- ===================================================================

INSERT INTO app_settings (setting_key, setting_value, description, is_public) VALUES
    ('app_version', '{"current": "1.0.0", "minimum_supported": "1.0.0"}', 'إصدار التطبيق الحالي والحد الأدنى المدعوم', true),
    ('maintenance_mode', '{"enabled": false, "message": "التطبيق تحت الصيانة"}', 'وضع الصيانة', true),
    ('force_update', '{"enabled": false, "version": "1.0.0", "message": "يرجى تحديث التطبيق"}', 'إجبار التحديث', true),
    ('payment_methods', '{"cash_on_delivery": true, "instapay": true, "wallet": true}', 'طرق الدفع المتاحة', true),
    ('shipping_cost', '{"default": 80.00, "free_shipping_threshold": 500.00}', 'تكلفة الشحن', true),
    ('currency_settings', '{"primary": "EGP", "symbol": "ج.م", "decimal_places": 2}', 'إعدادات العملة', true),
    ('contact_info', '{"phone": "+201234567890", "email": "<EMAIL>", "whatsapp": "+201234567890"}', 'معلومات التواصل', true),
    ('social_media', '{"facebook": "", "instagram": "", "twitter": "", "youtube": ""}', 'روابط وسائل التواصل الاجتماعي', true),
    ('app_features', '{"voice_search": true, "image_search": true, "gps_tracking": true, "biometric_login": true}', 'ميزات التطبيق المتاحة', true),
    ('notification_settings', '{"max_daily_notifications": 10, "quiet_hours_start": "22:00", "quiet_hours_end": "08:00"}', 'إعدادات الإشعارات', false),
    ('analytics_settings', '{"google_analytics_id": "", "firebase_project_id": "", "tracking_enabled": true}', 'إعدادات التحليلات', false),
    ('security_settings', '{"max_login_attempts": 5, "lockout_duration_minutes": 30, "session_timeout_hours": 24}', 'إعدادات الأمان', false)
ON CONFLICT (setting_key) DO NOTHING;

-- ===================================================================
-- جدول التخزين المحلي للتطبيق
-- ===================================================================

CREATE TABLE IF NOT EXISTS local_storage (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
    key TEXT NOT NULL,
    value JSONB,
    expires_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(user_id, key)
);

-- فهارس التخزين المحلي
CREATE INDEX IF NOT EXISTS idx_local_storage_user_key ON local_storage(user_id, key);
CREATE INDEX IF NOT EXISTS idx_local_storage_expires_at ON local_storage(expires_at);

-- تفعيل RLS للتخزين المحلي
ALTER TABLE local_storage ENABLE ROW LEVEL SECURITY;

-- سياسات أمان التخزين المحلي
CREATE POLICY "Users can manage own local storage" ON local_storage
    FOR ALL USING (auth.uid() = user_id);

-- ===================================================================
-- جدول إعدادات Cloudinary
-- ===================================================================

CREATE TABLE IF NOT EXISTS cloudinary_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    cloud_name TEXT NOT NULL,
    api_key TEXT NOT NULL,
    api_secret TEXT NOT NULL,
    upload_preset TEXT,
    folder_structure JSONB DEFAULT '{}',
    transformation_settings JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- إدراج إعدادات افتراضية
INSERT INTO cloudinary_settings (cloud_name, api_key, api_secret, upload_preset) VALUES
    ('default', 'default_key', 'default_secret', 'motorcycle_parts')
ON CONFLICT DO NOTHING;

-- تفعيل RLS لإعدادات Cloudinary
ALTER TABLE cloudinary_settings ENABLE ROW LEVEL SECURITY;

-- سياسات أمان إعدادات Cloudinary
CREATE POLICY "Anyone can read cloudinary settings" ON cloudinary_settings
    FOR SELECT USING (true);

CREATE POLICY "Only admins can modify cloudinary settings" ON cloudinary_settings
    FOR ALL USING (
        EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND profile_type = 'admin')
    );

-- ===================================================================
-- دالة تنظيف التخزين المحلي المنتهي الصلاحية
-- ===================================================================

CREATE OR REPLACE FUNCTION cleanup_expired_local_storage()
RETURNS INTEGER AS $$
DECLARE
    v_deleted_count INTEGER;
BEGIN
    -- حذف البيانات المنتهية الصلاحية
    DELETE FROM local_storage
    WHERE expires_at IS NOT NULL AND expires_at < NOW();

    GET DIAGNOSTICS v_deleted_count = ROW_COUNT;

    -- تسجيل العملية
    INSERT INTO system_logs (log_type, message, details)
    VALUES (
        'storage_cleanup',
        'تم تنظيف التخزين المحلي المنتهي الصلاحية',
        jsonb_build_object('deleted_items', v_deleted_count)
    );

    RETURN v_deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ===================================================================
-- رسالة نجاح
-- ===================================================================

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '✅ تم إنشاء جداول ميزات التطبيق المحمول بنجاح!';
    RAISE NOTICE '';
    RAISE NOTICE '⚙️ إعدادات التطبيق:';
    RAISE NOTICE '   • app_settings - إعدادات التطبيق العامة';
    RAISE NOTICE '   • user_app_settings - إعدادات المستخدم الشخصية';
    RAISE NOTICE '';
    RAISE NOTICE '📱 إدارة الأجهزة والإشعارات:';
    RAISE NOTICE '   • user_devices - أجهزة المستخدمين';
    RAISE NOTICE '   • notification_campaigns - حملات الإشعارات';
    RAISE NOTICE '';
    RAISE NOTICE '📊 تتبع الجلسات والأحداث:';
    RAISE NOTICE '   • user_sessions - جلسات المستخدمين';
    RAISE NOTICE '   • app_events - أحداث التطبيق';
    RAISE NOTICE '';
    RAISE NOTICE '🔒 الأمان والخصوصية:';
    RAISE NOTICE '   • login_attempts - محاولات تسجيل الدخول';
    RAISE NOTICE '   • active_sessions - الجلسات النشطة';
    RAISE NOTICE '   • user_privacy_settings - إعدادات الخصوصية';
    RAISE NOTICE '';
    RAISE NOTICE '🗄️ التخزين والإعدادات:';
    RAISE NOTICE '   • local_storage - التخزين المحلي للتطبيق';
    RAISE NOTICE '   • cloudinary_settings - إعدادات Cloudinary';
    RAISE NOTICE '';
    RAISE NOTICE '🔄 تم إنشاء المحفزات التلقائية';
END $$;