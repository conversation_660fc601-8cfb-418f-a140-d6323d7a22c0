{"buildFiles": ["C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\motorcycle_parts_shop\\android\\app\\.cxx\\Debug\\3u4q1e59\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\motorcycle_parts_shop\\android\\app\\.cxx\\Debug\\3u4q1e59\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}