import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AccessibilityService extends ChangeNotifier {
  static const String _textScaleFactorKey = 'textScaleFactor';
  static const String _highContrastKey = 'highContrast';
  static const String _reduceAnimationsKey = 'reduceAnimations';
  static const String _screenReaderKey = 'screenReader';
  static const String _fontFamilyKey = 'fontFamily';
  static const String _enableRtlKey = 'enableRtl';

  late SharedPreferences _prefs;
  bool _isInitialized = false;

  double _textScaleFactor = 1.0;
  bool _highContrastEnabled = false;
  bool _reduceAnimations = false;
  bool _screenReaderEnabled = false;
  String _fontFamily = 'Cairo';
  bool _enableRtl = true; // تفعيل RTL افتراضيًا للغة العربية

  // Getters
  double get textScaleFactor => _textScaleFactor;
  bool get isHighContrastEnabled => _highContrastEnabled;
  bool get reduceAnimations => _reduceAnimations;
  bool get isScreenReaderEnabled => _screenReaderEnabled;
  String get fontFamily => _fontFamily;
  bool get enableRtl => _enableRtl;
  bool get isInitialized => _isInitialized;

  // Initialize the service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _prefs = await SharedPreferences.getInstance();
      _loadSettings();
      _isInitialized = true;
    } catch (e) {
      debugPrint('فشل في تهيئة خدمة الوصول: $e');
      // استخدام القيم الافتراضية في حالة الفشل
    }
  }

  // Load saved settings
  void _loadSettings() {
    _textScaleFactor = _prefs.getDouble(_textScaleFactorKey) ?? 1.0;
    _highContrastEnabled = _prefs.getBool(_highContrastKey) ?? false;
    _reduceAnimations = _prefs.getBool(_reduceAnimationsKey) ?? false;
    _screenReaderEnabled = _prefs.getBool(_screenReaderKey) ?? false;
    _fontFamily = _prefs.getString(_fontFamilyKey) ?? 'Cairo';
    _enableRtl = _prefs.getBool(_enableRtlKey) ?? true;
    notifyListeners();
  }

  // Update text scale factor
  Future<void> setTextScaleFactor(double value) async {
    if (value < 0.5 || value > 2.0) {
      return; // التحقق من القيمة ضمن النطاق المقبول
    }

    _textScaleFactor = value;
    await _prefs.setDouble(_textScaleFactorKey, value);
    notifyListeners();
  }

  // Update high contrast mode
  Future<void> setHighContrastEnabled(bool value) async {
    _highContrastEnabled = value;
    await _prefs.setBool(_highContrastKey, value);
    notifyListeners();
  }

  // Update reduce animations setting
  Future<void> setReduceAnimations(bool value) async {
    _reduceAnimations = value;
    await _prefs.setBool(_reduceAnimationsKey, value);
    notifyListeners();
  }

  // Update screen reader setting
  Future<void> setScreenReaderEnabled(bool value) async {
    _screenReaderEnabled = value;
    await _prefs.setBool(_screenReaderKey, value);
    notifyListeners();
  }

  // Update font family
  Future<void> setFontFamily(String value) async {
    _fontFamily = value;
    await _prefs.setString(_fontFamilyKey, value);
    notifyListeners();
  }

  // Update RTL setting
  Future<void> setEnableRtl(bool value) async {
    _enableRtl = value;
    await _prefs.setBool(_enableRtlKey, value);
    notifyListeners();
  }

  // تطبيق إعدادات الوصول على مستوى التطبيق
  ThemeData applyAccessibilityOverrides(ThemeData baseTheme) {
    // تطبيق التباين العالي إذا كان مفعلاً
    if (_highContrastEnabled) {
      baseTheme = baseTheme.copyWith(
        textTheme: baseTheme.textTheme.apply(
          bodyColor: Colors.white,
          displayColor: Colors.white,
        ),
        scaffoldBackgroundColor: Colors.black,
        cardColor: Colors.grey[900],
        primaryColor: Colors.yellow,
        colorScheme: baseTheme.colorScheme.copyWith(
          primary: Colors.yellow,
          secondary: Colors.yellow,
          onPrimary: Colors.black,
          onSecondary: Colors.black,
        ),
      );
    }

    // تطبيق خط النص المخصص
    baseTheme = baseTheme.copyWith(
      textTheme: baseTheme.textTheme.apply(fontFamily: _fontFamily),
      primaryTextTheme: baseTheme.primaryTextTheme.apply(
        fontFamily: _fontFamily,
      ),
    );

    return baseTheme;
  }

  // إعادة تعيين جميع الإعدادات إلى القيم الافتراضية
  Future<void> resetToDefaults() async {
    await setTextScaleFactor(1.0);
    await setHighContrastEnabled(false);
    await setReduceAnimations(false);
    await setScreenReaderEnabled(false);
    await setFontFamily('Cairo');
    await setEnableRtl(true);
  }

  // التحقق من وجود تحديثات في الإعدادات
  Future<void> refreshSettings() async {
    if (!_isInitialized) {
      await initialize();
      return;
    }
    _loadSettings();
  }
}
