import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// خدمة التخزين المحلي
class StorageService extends ChangeNotifier {
  static final StorageService _instance = StorageService._internal();
  late SharedPreferences _prefs;
  bool _isInitialized = false;
  bool _autoCleanEnabled = true;

  factory StorageService() => _instance;
  StorageService._internal();

  bool get isInitialized => _isInitialized;
  bool get autoCleanEnabled => _autoCleanEnabled;

  /// تهيئة الخدمة
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      _prefs = await SharedPreferences.getInstance();
      _autoCleanEnabled = _prefs.getBool('auto_clean_enabled') ?? true;
      _isInitialized = true;
      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في تهيئة خدمة التخزين: $e');
      rethrow;
    }
  }

  /// حفظ بيانات نصية
  Future<bool> setString(String key, String value) async {
    if (!_isInitialized) await initialize();
    try {
      return await _prefs.setString(key, value);
    } catch (e) {
      debugPrint('خطأ في حفظ البيانات النصية: $e');
      return false;
    }
  }

  /// جلب بيانات نصية
  Future<String?> getString(String key) async {
    if (!_isInitialized) await initialize();
    try {
      return _prefs.getString(key);
    } catch (e) {
      debugPrint('خطأ في جلب البيانات النصية: $e');
      return null;
    }
  }

  /// حفظ بيانات رقمية صحيحة
  Future<bool> setInt(String key, int value) async {
    if (!_isInitialized) await initialize();
    try {
      return await _prefs.setInt(key, value);
    } catch (e) {
      debugPrint('خطأ في حفظ البيانات الرقمية: $e');
      return false;
    }
  }

  /// جلب بيانات رقمية صحيحة
  Future<int?> getInt(String key) async {
    if (!_isInitialized) await initialize();
    try {
      return _prefs.getInt(key);
    } catch (e) {
      debugPrint('خطأ في جلب البيانات الرقمية: $e');
      return null;
    }
  }

  /// حفظ بيانات منطقية
  Future<bool> setBool(String key, bool value) async {
    if (!_isInitialized) await initialize();
    try {
      return await _prefs.setBool(key, value);
    } catch (e) {
      debugPrint('خطأ في حفظ البيانات المنطقية: $e');
      return false;
    }
  }

  /// جلب بيانات منطقية
  bool? getBool(String key) {
    if (!_isInitialized) return null;
    try {
      return _prefs.getBool(key);
    } catch (e) {
      debugPrint('خطأ في جلب البيانات المنطقية: $e');
      return null;
    }
  }

  /// حفظ كائن JSON
  Future<bool> setJson(String key, Map<String, dynamic> value) async {
    if (!_isInitialized) await initialize();
    try {
      final jsonString = json.encode(value);
      return await _prefs.setString(key, jsonString);
    } catch (e) {
      debugPrint('خطأ في حفظ كائن JSON: $e');
      return false;
    }
  }

  /// جلب كائن JSON
  Map<String, dynamic>? getJson(String key) {
    if (!_isInitialized) return null;
    try {
      final jsonString = _prefs.getString(key);
      if (jsonString == null) return null;
      return json.decode(jsonString) as Map<String, dynamic>;
    } catch (e) {
      debugPrint('خطأ في جلب كائن JSON: $e');
      return null;
    }
  }

  /// حذف مفتاح معين
  Future<bool> remove(String key) async {
    if (!_isInitialized) await initialize();
    try {
      return await _prefs.remove(key);
    } catch (e) {
      debugPrint('خطأ في حذف المفتاح: $e');
      return false;
    }
  }

  /// التحقق من وجود مفتاح
  bool containsKey(String key) {
    if (!_isInitialized) return false;
    try {
      return _prefs.containsKey(key);
    } catch (e) {
      debugPrint('خطأ في التحقق من وجود المفتاح: $e');
      return false;
    }
  }

  /// مسح جميع البيانات
  Future<bool> clearAll() async {
    if (!_isInitialized) await initialize();
    try {
      return await _prefs.clear();
    } catch (e) {
      debugPrint('خطأ في مسح جميع البيانات: $e');
      return false;
    }
  }

  /// مسح الذاكرة المؤقتة
  Future<void> clearCache() async {
    if (!_isInitialized) await initialize();
    try {
      final keys = _prefs.getKeys();
      final cacheKeys = keys.where((key) => key.startsWith('cache_')).toList();
      
      for (final key in cacheKeys) {
        await _prefs.remove(key);
      }
      
      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في مسح الذاكرة المؤقتة: $e');
    }
  }

  /// تفعيل/إلغاء التنظيف التلقائي
  Future<void> setAutoCleanEnabled(bool enabled) async {
    if (!_isInitialized) await initialize();
    try {
      _autoCleanEnabled = enabled;
      await _prefs.setBool('auto_clean_enabled', enabled);
      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في تحديث إعداد التنظيف التلقائي: $e');
    }
  }

  /// الحصول على حجم البيانات المخزنة (تقديري)
  Map<String, dynamic> getStorageInfo() {
    if (!_isInitialized) {
      return {
        'total_keys': 0,
        'cache_keys': 0,
        'estimated_size_kb': 0,
      };
    }

    try {
      final keys = _prefs.getKeys();
      final cacheKeys = keys.where((key) => key.startsWith('cache_')).toList();
      
      // تقدير تقريبي لحجم البيانات
      int estimatedSize = 0;
      for (final key in keys) {
        final value = _prefs.get(key);
        if (value is String) {
          estimatedSize += value.length;
        } else {
          estimatedSize += 50; // تقدير تقريبي للأنواع الأخرى
        }
      }

      return {
        'total_keys': keys.length,
        'cache_keys': cacheKeys.length,
        'estimated_size_kb': (estimatedSize / 1024).round(),
      };
    } catch (e) {
      debugPrint('خطأ في حساب معلومات التخزين: $e');
      return {
        'total_keys': 0,
        'cache_keys': 0,
        'estimated_size_kb': 0,
      };
    }
  }
}
