import 'dart:convert';
import 'package:motorcycle_parts_shop/models/category_model.dart';
import 'package:motorcycle_parts_shop/models/company_model.dart';

/// نتيجة التحقق من صحة البيانات
class ValidationResult {
  final bool isValid;
  final List<String> errors;
  final List<String> warnings;

  const ValidationResult({
    required this.isValid,
    required this.errors,
    required this.warnings,
  });

  bool get hasErrors => errors.isNotEmpty;
  bool get hasWarnings => warnings.isNotEmpty;

  String get errorMessage => errors.join(', ');
  String get warningMessage => warnings.join(', ');
}

class ProductModel {
  /// إنشاء نسخة فارغة من المنتج (لا تحتوي على بيانات افتراضية)
  static ProductModel empty() {
    return ProductModel(
      id: '',
      sku: '',
      brand: '',
      isAvailable: false,
      name: '',
      description: '',
      price: 0.0,
      categoryId: '',
      companyId: '',
      images: [],
      specifications: {},
      quantity: 0,
      rating: 0.0,
      reviewsCount: 0,
      viewCount: 0,
      isFeatured: false,
      isBestSelling: false,
      isNew: true,
      newUntil: DateTime.now().add(const Duration(days: 10)),
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  final String id; // معرف المنتج الفريد
  final String name; // اسم المنتج
  final String description; // وصف المنتج
  final double price; // سعر المنتج الأصلي
  final double? discountPrice; // سعر المنتج بعد الخصم (اختياري)
  final double? originalPrice; // السعر الأصلي للمنتج قبل أي خصومات (اختياري)
  final String categoryId; // معرف الفئة التي ينتمي إليها المنتج
  final String? categoryName; // اسم الفئة (للعرض فقط، اختياري)
  final String companyId; // معرف الشركة المصنعة للمنتج
  final List<String> images; // قائمة صور المنتج (image_urls في قاعدة البيانات)
  final String? imageUrl; // رابط الصورة الرئيسية للمنتج
  final Map<String, dynamic>
  specifications; // مواصفات المنتج (مثل اللون، الحجم...)
  final int
  quantity; // الكمية المتاحة في المخزون (stock_quantity في قاعدة البيانات)
  final double
  rating; // تقييم المنتج من قبل العملاء (average_rating في قاعدة البيانات)
  final int reviewsCount; // عدد التقييمات (review_count في قاعدة البيانات)
  final int viewCount; // عدد مرات مشاهدة المنتج
  final int salesCount; // عدد المبيعات
  final bool isFeatured; // هل المنتج مميز؟
  final bool isBestSelling; // هل المنتج من الأكثر مبيعًا؟
  final bool isNew; // هل المنتج جديد؟
  final DateTime? newUntil; // تاريخ انتهاء كون المنتج "جديد"
  final DateTime createdAt; // تاريخ إضافة المنتج
  final DateTime updatedAt; // تاريخ آخر تحديث للمنتج
  final String sku; // كود المخزون الفريد
  final String brand; // العلامة التجارية
  final bool isAvailable; // حالة التوفر
  final int minStockLevel; // الحد الأدنى للمخزون
  final double? weight; // الوزن
  final Map<String, dynamic>? dimensions; // الأبعاد
  final String? metaTitle; // عنوان SEO
  final String? metaDescription; // وصف SEO
  final List<String> tags; // علامات البحث

  final bool isOnSale; // هل المنتج معروض للبيع بخصم؟
  bool? isFavorite; // هل المنتج في المفضلة؟

  // الكائنات المرتبطة (اختيارية)
  final CategoryModel? category; // كائن الفئة المرتبط (اختياري)
  final CompanyModel? company; // كائن الشركة المصنعة المرتبط (اختياري)

  /// المُنشئ الرئيسي لنموذج المنتج
  ProductModel({
    required this.id,
    required this.sku,
    required this.brand,
    required this.isAvailable,
    required this.name,
    required this.description,
    required this.price,
    this.discountPrice,
    this.originalPrice,
    required this.categoryId,
    this.categoryName,
    required this.companyId,
    required this.images,
    this.imageUrl,
    required this.specifications,
    required this.quantity,
    required this.rating,
    required this.reviewsCount,
    required this.viewCount,
    this.salesCount = 0,
    required this.isFeatured,
    required this.isBestSelling,
    required this.isNew,
    this.newUntil,
    required this.createdAt,
    required this.updatedAt,
    this.minStockLevel = 5,
    this.weight,
    this.dimensions,
    this.metaTitle,
    this.metaDescription,
    this.tags = const [],
    this.category,
    this.company,
    this.isOnSale = false,
    this.isFavorite,
  });

  // تحويل من JSON إلى كائن ProductModel
  factory ProductModel.fromJson(Map<String, dynamic> json) {
    CategoryModel? categoryObj;
    if (json['category'] != null && json['category'] is Map<String, dynamic>) {
      categoryObj = CategoryModel.fromJson(json['category']);
    }

    CompanyModel? companyObj;
    if (json['company'] != null && json['company'] is Map<String, dynamic>) {
      companyObj = CompanyModel.fromJson(json['company']);
    }

    List<String> imagesList = [];
    // محاولة قراءة من image_urls أولاً ثم images
    final imagesData = json['image_urls'] ?? json['images'];
    if (imagesData != null) {
      if (imagesData is List) {
        imagesList = List<String>.from(
          imagesData.map((img) => img?.toString() ?? ''),
        );
      } else if (imagesData is String) {
        try {
          final List<dynamic> parsedImages = jsonDecode(imagesData);
          imagesList =
              parsedImages.map((img) => img?.toString() ?? '').toList();
        } catch (e) {
          imagesList = [imagesData.toString()];
        }
      }
    }

    Map<String, dynamic> specs = {};
    if (json['specifications'] != null) {
      if (json['specifications'] is Map) {
        specs = Map<String, dynamic>.from(json['specifications']);
      } else if (json['specifications'] is String) {
        try {
          specs = jsonDecode(json['specifications']);
        } catch (e) {
          specs = {'error': 'Failed to parse specifications: $e'};
        }
      }
    }

    return ProductModel(
      id: json['id']?.toString() ?? '',
      sku: json['sku']?.toString() ?? '',
      brand: json['brand']?.toString() ?? '',
      isAvailable: json['is_available'] ?? false,
      name: json['name']?.toString() ?? '',
      description: json['description']?.toString() ?? '',
      price: _parseDouble(json['price'], 0) ?? 0,
      discountPrice: _parseDouble(json['discount_price'], null),
      originalPrice: _parseDouble(json['original_price'], null),
      categoryId: json['category_id']?.toString() ?? '',
      categoryName: json['category_name']?.toString(),
      companyId: json['company_id']?.toString() ?? '',
      images: imagesList,
      specifications: specs,
      quantity:
          json['stock_quantity'] is int
              ? json['stock_quantity']
              : json['quantity'] is int
              ? json['quantity']
              : int.tryParse(
                    json['stock_quantity']?.toString() ??
                        json['quantity']?.toString() ??
                        '0',
                  ) ??
                  0,
      rating: _parseDouble(json['average_rating'] ?? json['rating'], 0) ?? 0,
      reviewsCount:
          json['review_count'] is int
              ? json['review_count']
              : json['reviews_count'] is int
              ? json['reviews_count']
              : int.tryParse(
                    json['review_count']?.toString() ??
                        json['reviews_count']?.toString() ??
                        '0',
                  ) ??
                  0,
      viewCount:
          json['view_count'] is int
              ? json['view_count']
              : int.tryParse(json['view_count']?.toString() ?? '0') ?? 0,
      salesCount:
          json['sales_count'] is int
              ? json['sales_count']
              : int.tryParse(json['sales_count']?.toString() ?? '0') ?? 0,
      isFeatured: json['is_featured'] ?? false,
      isBestSelling: json['is_best_selling'] ?? false,
      isNew: json['is_new'] ?? false,
      newUntil:
          json['new_until'] != null ? DateTime.parse(json['new_until']) : null,
      createdAt:
          json['created_at'] != null
              ? DateTime.parse(json['created_at'])
              : DateTime.now(),
      updatedAt:
          json['updated_at'] != null
              ? DateTime.parse(json['updated_at'])
              : DateTime.now(),
      minStockLevel:
          json['min_stock_level'] is int
              ? json['min_stock_level']
              : int.tryParse(json['min_stock_level']?.toString() ?? '5') ?? 5,
      weight: _parseDouble(json['weight'], null),
      dimensions:
          json['dimensions'] is Map<String, dynamic>
              ? Map<String, dynamic>.from(json['dimensions'])
              : null,
      metaTitle: json['meta_title']?.toString(),
      metaDescription: json['meta_description']?.toString(),
      tags:
          json['tags'] is List
              ? List<String>.from(
                json['tags'].map((tag) => tag?.toString() ?? ''),
              )
              : [],
      category: categoryObj,
      company: companyObj,
      isOnSale: json['is_on_sale'] ?? false,
      isFavorite: json['is_favorite'] as bool?,
    );
  }

  /// دالة مساعدة لتحويل القيم إلى double بشكل آمن
  static double? _parseDouble(dynamic value, [double? defaultValue]) {
    if (value == null) return defaultValue;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      try {
        return double.parse(value);
      } catch (e) {
        return defaultValue;
      }
    }
    return defaultValue;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = {
      'id': id,
      'sku': sku,
      'brand': brand,
      'is_available': isAvailable,
      'name': name,
      'description': description,
      'price': price,
      'discount_price': discountPrice,
      'original_price': originalPrice,
      'category_id': categoryId,
      'category_name': categoryName,
      'company_id': companyId,
      'image_urls': images,
      'image_url': imageUrl,
      'specifications': specifications,
      'stock_quantity': quantity,
      'min_stock_level': minStockLevel,
      'weight': weight,
      'dimensions': dimensions,
      'average_rating': rating,
      'review_count': reviewsCount,
      'view_count': viewCount,
      'sales_count': salesCount,
      'meta_title': metaTitle,
      'meta_description': metaDescription,
      'tags': tags,
      'is_featured': isFeatured,
      'is_best_selling': isBestSelling,
      'is_new': isNew,
      'new_until': newUntil?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'is_on_sale': isOnSale,
      'is_favorite': isFavorite,
    };

    if (category != null) json['category'] = category!.toJson();
    if (company != null) json['company'] = company!.toJson();

    return json;
  }

  ProductModel copyWith({
    String? id,
    String? sku,
    String? brand,
    bool? isAvailable,
    String? name,
    String? description,
    double? price,
    double? discountPrice,
    String? categoryId,
    String? categoryName,
    String? companyId,
    List<String>? images,
    Map<String, dynamic>? specifications,
    int? quantity,
    double? rating,
    int? reviewsCount,
    int? viewCount,
    int? salesCount,
    bool? isFeatured,
    bool? isBestSelling,
    bool? isNew,
    DateTime? newUntil,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? minStockLevel,
    double? weight,
    Map<String, dynamic>? dimensions,
    String? metaTitle,
    String? metaDescription,
    List<String>? tags,
    bool? isOnSale,
  }) {
    return ProductModel(
      id: id ?? this.id,
      sku: sku ?? this.sku,
      brand: brand ?? this.brand,
      isAvailable: isAvailable ?? this.isAvailable,
      name: name ?? this.name,
      description: description ?? this.description,
      price: price ?? this.price,
      discountPrice: discountPrice ?? this.discountPrice,
      categoryId: categoryId ?? this.categoryId,
      categoryName: categoryName ?? this.categoryName,
      companyId: companyId ?? this.companyId,
      images: images ?? this.images,
      specifications: specifications ?? this.specifications,
      quantity: quantity ?? this.quantity,
      rating: rating ?? this.rating,
      reviewsCount: reviewsCount ?? this.reviewsCount,
      viewCount: viewCount ?? this.viewCount,
      salesCount: salesCount ?? this.salesCount,
      isFeatured: isFeatured ?? this.isFeatured,
      isBestSelling: isBestSelling ?? this.isBestSelling,
      isNew: isNew ?? this.isNew,
      newUntil: newUntil ?? this.newUntil,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      minStockLevel: minStockLevel ?? this.minStockLevel,
      weight: weight ?? this.weight,
      dimensions: dimensions ?? this.dimensions,
      metaTitle: metaTitle ?? this.metaTitle,
      metaDescription: metaDescription ?? this.metaDescription,
      tags: tags ?? this.tags,
      isOnSale: isOnSale ?? this.isOnSale,
    );
  }

  double? get discountPercentage {
    if (discountPrice == null) return null;
    return ((price - discountPrice!) / price) * 100;
  }

  bool get hasDiscount => discountPrice != null && discountPrice! < price;

  double get finalPrice => discountPrice ?? price;

  bool get isInStock => quantity > 0 && isAvailable;

  String get mainImage => images.isNotEmpty ? images.first : '';

  List<String> get additionalImages =>
      images.length > 1 ? images.sublist(1) : [];

  bool get hasMultipleImages => images.length > 1;

  int get imagesCount => images.length;

  bool get isNewProduct {
    // التحقق من الحقل is_new أولاً
    if (!isNew) return false;

    // التحقق من تاريخ انتهاء كون المنتج جديد
    if (newUntil != null) {
      return DateTime.now().isBefore(newUntil!);
    }

    // التحقق الاحتياطي بناءً على تاريخ الإنشاء (10 أيام)
    final DateTime now = DateTime.now();
    final Duration difference = now.difference(createdAt);
    return difference.inDays <= 10;
  }

  /// عدد الأيام المتبقية لكون المنتج "جديد"
  int get daysLeftAsNew {
    if (!isNew) return 0;

    if (newUntil != null) {
      final difference = newUntil!.difference(DateTime.now());
      return difference.inDays.clamp(0, double.infinity).toInt();
    }

    // الحساب الاحتياطي
    final difference = DateTime.now().difference(createdAt);
    return (10 - difference.inDays).clamp(0, 10);
  }

  /// نص وصفي لحالة المنتج الجديد
  String get newStatusText {
    if (!isNewProduct) return '';

    final daysLeft = daysLeftAsNew;
    if (daysLeft == 0) return 'جديد';
    if (daysLeft == 1) return 'جديد (يوم واحد متبقي)';
    return 'جديد ($daysLeft أيام متبقية)';
  }

  static int compareByPrice(ProductModel a, ProductModel b) =>
      a.finalPrice.compareTo(b.finalPrice);
  static int compareByRating(ProductModel a, ProductModel b) =>
      b.rating.compareTo(a.rating);
  static int compareByPopularity(ProductModel a, ProductModel b) =>
      b.reviewsCount.compareTo(a.reviewsCount);
  static int compareByDate(ProductModel a, ProductModel b) =>
      b.createdAt.compareTo(a.createdAt);

  static ProductModel fromCartItem(Map<String, dynamic> cartItem) {
    final double? parsedPrice = _parseDouble(cartItem['price'], 0);
    final double priceValue = parsedPrice ?? 0;
    int quantity = 1;
    if (cartItem['quantity'] != null) {
      if (cartItem['quantity'] is int) {
        quantity = cartItem['quantity'];
      } else if (cartItem['quantity'] is String) {
        quantity = int.tryParse(cartItem['quantity']) ?? 1;
      }
    }

    return ProductModel(
      id: cartItem['product_id']?.toString() ?? '',
      sku: cartItem['sku']?.toString() ?? '',
      brand: cartItem['brand']?.toString() ?? '',
      isAvailable: true,
      name: cartItem['name']?.toString() ?? '',
      description: cartItem['description']?.toString() ?? '',
      price: priceValue,
      discountPrice: _parseDouble(cartItem['discount_price'], null),
      categoryId: cartItem['category_id']?.toString() ?? '',
      categoryName: cartItem['category_name']?.toString(),
      companyId: cartItem['company_id']?.toString() ?? '',
      images:
          cartItem['image_url'] != null
              ? [cartItem['image_url'].toString()]
              : [],
      specifications: {},
      quantity: quantity,
      rating: 0,
      reviewsCount: 0,
      viewCount: 0,
      salesCount: 0,
      isFeatured: false,
      isBestSelling: false,
      isNew: false,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      minStockLevel: 5,
      tags: [],
    );
  }

  bool isCompatibleWith(ProductModel otherProduct) {
    if (categoryId == otherProduct.categoryId) return true;
    return false;
  }

  dynamic getSpecification(String key, [dynamic defaultValue]) =>
      specifications[key] ?? defaultValue;

  bool hasSpecification(String key) =>
      specifications.containsKey(key) && specifications[key] != null;
}

extension LoadingProductModel on ProductModel {
  static ProductModel loading() {
    return ProductModel(
      id: 'loading',
      sku: 'loading',
      brand: 'loading',
      isAvailable: false,
      name: 'جاري التحميل...',
      description: 'جاري تحميل تفاصيل المنتج',
      price: 0.0,
      discountPrice: null,
      categoryId: 'loading',
      categoryName: null,
      companyId: 'loading',
      images: [],
      imageUrl: null,
      specifications: {},
      quantity: 0,
      rating: 0.0,
      reviewsCount: 0,
      viewCount: 0,
      salesCount: 0,
      isFeatured: false,
      isBestSelling: false,
      isNew: false,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      minStockLevel: 5,
      tags: [],
      category: null,
      company: null,
    );
  }

  /// التحقق مما إذا كان المنتج فارغًا أو غير صالح
  bool get isEmpty => id.isEmpty || name.isEmpty;

  /// التحقق من صحة بيانات المنتج
  bool get isValid => !isEmpty && price > 0 && quantity >= 0;

  /// التحقق الشامل من صحة بيانات المنتج
  ValidationResult validateProduct() {
    final errors = <String>[];
    final warnings = <String>[];

    // التحقق من الحقول الأساسية
    if (id.isEmpty) errors.add('معرف المنتج مطلوب');
    if (sku.isEmpty) errors.add('كود المنتج (SKU) مطلوب');
    if (name.isEmpty) errors.add('اسم المنتج مطلوب');
    if (name.length < 3) warnings.add('اسم المنتج قصير جداً');
    if (name.length > 200) errors.add('اسم المنتج طويل جداً');

    // التحقق من الوصف
    if (description.isEmpty) warnings.add('وصف المنتج مفقود');
    if (description.length > 5000) warnings.add('وصف المنتج طويل جداً');

    // التحقق من السعر
    if (price < 0) errors.add('السعر يجب أن يكون موجب');
    if (price == 0) warnings.add('السعر صفر - تأكد من صحة السعر');
    if (price > 1000000) warnings.add('السعر مرتفع جداً');

    // التحقق من سعر الخصم
    if (discountPrice != null) {
      if (discountPrice! < 0) errors.add('سعر الخصم يجب أن يكون موجب');
      if (discountPrice! >= price) {
        errors.add('سعر الخصم يجب أن يكون أقل من السعر الأصلي');
      }
    }

    // التحقق من الكمية
    if (quantity < 0) errors.add('الكمية يجب أن تكون موجبة أو صفر');
    if (quantity == 0 && isAvailable) {
      warnings.add('المنتج متاح لكن الكمية صفر');
    }
    if (quantity > 0 && !isAvailable) {
      warnings.add('المنتج غير متاح لكن الكمية موجودة');
    }

    // التحقق من التقييم
    if (rating < 0 || rating > 5) errors.add('التقييم يجب أن يكون بين 0 و 5');
    if (reviewsCount < 0) errors.add('عدد المراجعات يجب أن يكون موجب');
    if (rating > 0 && reviewsCount == 0) {
      warnings.add('يوجد تقييم لكن لا توجد مراجعات');
    }

    // التحقق من العلامة التجارية والفئة
    if (brand.isEmpty) warnings.add('العلامة التجارية مفقودة');
    if (categoryId.isEmpty) errors.add('معرف الفئة مطلوب');
    if (companyId.isEmpty) errors.add('معرف الشركة مطلوب');

    // التحقق من الصور
    if (images.isEmpty) warnings.add('لا توجد صور للمنتج');
    if (images.length > 10) warnings.add('عدد الصور كثير جداً');

    // التحقق من الوزن والأبعاد
    if (weight != null && weight! <= 0) warnings.add('الوزن يجب أن يكون موجب');
    if (dimensions != null) {
      final length = dimensions!['length'];
      final width = dimensions!['width'];
      final height = dimensions!['height'];
      if (length != null && length <= 0) warnings.add('الطول يجب أن يكون موجب');
      if (width != null && width <= 0) warnings.add('العرض يجب أن يكون موجب');
      if (height != null && height <= 0) {
        warnings.add('الارتفاع يجب أن يكون موجب');
      }
    }

    // التحقق من الحد الأدنى للمخزون
    if (minStockLevel < 0) errors.add('الحد الأدنى للمخزون يجب أن يكون موجب');
    if (quantity > 0 && quantity <= minStockLevel) {
      warnings.add('الكمية أقل من أو تساوي الحد الأدنى للمخزون');
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  /// الحصول على رابط صورة محسن بحجم معين
  String getOptimizedImageUrl([int width = 300, int height = 300]) {
    if (images.isEmpty) return '';
    final mainImage = images.first;
    if (!mainImage.contains('?')) {
      return '$mainImage?width=$width&height=$height&quality=80';
    }
    return mainImage;
  }
}
