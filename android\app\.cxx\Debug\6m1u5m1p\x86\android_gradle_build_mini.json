{"buildFiles": ["C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\motorcycle_parts_shop\\android\\app\\.cxx\\Debug\\6m1u5m1p\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\motorcycle_parts_shop\\android\\app\\.cxx\\Debug\\6m1u5m1p\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}