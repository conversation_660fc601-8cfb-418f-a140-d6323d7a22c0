import 'package:flutter/cupertino.dart';
import 'package:motorcycle_parts_shop/core/constants/app_constants.dart';
import 'package:motorcycle_parts_shop/core/services/auth_supabase_service.dart';
import 'package:motorcycle_parts_shop/models/advertisement_model.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class AdvertisementService {
  final SupabaseClient _supabase = AuthSupabaseService().client;

  /// جلب الإعلانات النشطة
  Future<List<AdvertisementModel>> getActiveAdvertisements() async {
    try {
      final now = DateTime.now().toIso8601String();
      final response = await _supabase
          .from(AppConstants.advertisementsTable)
          .select()
          .eq('is_active', true)
          .lte('start_date', now)
          .or('end_date.is.null,end_date.gte.$now')
          .order('display_order');

      if (response.isEmpty) {
        debugPrint('لا توجد إعلانات نشطة متاحة حاليًا');
        return [];
      }

      return (response as List)
          .map((ad) => AdvertisementModel.fromJson(ad))
          .toList();
    } catch (e) {
      debugPrint('خطأ في جلب الإعلانات النشطة: $e');
      return [];
    }
  }

  /// جلب الإعلانات حسب حملة إعلانية معينة
  Future<List<AdvertisementModel>> getAdvertisementsByCampaign(String campaignId) async {
    try {
      final response = await _supabase
          .from(AppConstants.advertisementsTable)
          .select()
          .eq('campaign_id', campaignId)
          .order('display_order');

      if (response.isEmpty) {
        debugPrint('لا توجد إعلانات متاحة لحملة: $campaignId');
        return [];
      }

      return (response as List)
          .map((ad) => AdvertisementModel.fromJson(ad))
          .toList();
    } catch (e) {
      debugPrint('خطأ في جلب إعلانات الحملة: $e');
      return [];
    }
  }

  /// إنشاء إعلان جديد
  Future<void> createAdvertisement(AdvertisementModel advertisement) async {
    try {
      _validateAdvertisement(advertisement);
      await _supabase
          .from(AppConstants.advertisementsTable)
          .insert(advertisement.toJson());
    } catch (e) {
      debugPrint('خطأ في إنشاء الإعلان: $e');
      throw Exception('فشل في إنشاء الإعلان: $e');
    }
  }

  /// تحديث إعلان موجود
  Future<void> updateAdvertisement(AdvertisementModel advertisement) async {
    try {
      _validateAdvertisement(advertisement);
      await _supabase
          .from(AppConstants.advertisementsTable)
          .update(advertisement.toJson())
          .eq('id', advertisement.id);
    } catch (e) {
      debugPrint('خطأ في تحديث الإعلان: $e');
      throw Exception('فشل في تحديث الإعلان: $e');
    }
  }

  /// حذف إعلان
  Future<void> deleteAdvertisement(String advertisementId) async {
    try {
      await _supabase
          .from(AppConstants.advertisementsTable)
          .delete()
          .eq('id', advertisementId);
    } catch (e) {
      debugPrint('خطأ في حذف الإعلان: $e');
      throw Exception('فشل في حذف الإعلان: $e');
    }
  }

  /// التحقق من صحة بيانات الإعلان
  void _validateAdvertisement(AdvertisementModel ad) {
    if (ad.title.isEmpty) {
      throw ArgumentError('عنوان الإعلان مطلوب');
    }
    if (ad.imageUrl.isEmpty) {
      throw ArgumentError('رابط الصورة الإعلانية مطلوب');
    }
    if (ad.endDate != null && ad.endDate!.isBefore(ad.startDate)) {
      throw ArgumentError('تاريخ النهاية لا يمكن أن يكون قبل تاريخ البداية');
    }
  }

  /// تسجيل مشاهدة للإعلان (زيادة عدد مرات الظهور)
  Future<void> incrementImpressionCount(String advertisementId) async {
    try {
      await _supabase.rpc(
        'increment_ad_impression_count',
        params: {'ad_id': advertisementId},
      );
    } catch (e) {
      debugPrint('خطأ في تحديث عدد مرات الظهور: $e');
    }
  }

  /// تسجيل نقرة على الإعلان (زيادة عدد النقرات)
  Future<void> incrementClickCount(String advertisementId) async {
    try {
      await _supabase.rpc(
        'increment_ad_click_count',
        params: {'ad_id': advertisementId},
      );
    } catch (e) {
      debugPrint('خطأ في تحديث عدد النقرات: $e');
    }
  }
}