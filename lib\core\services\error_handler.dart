import 'package:flutter/material.dart';
import 'package:motorcycle_parts_shop/core/services/analytics_service.dart';
import 'package:motorcycle_parts_shop/core/theme/app_theme.dart';

/// نظام معالجة الأخطاء الموحد والمحسن
class ErrorHandler extends ChangeNotifier {
  static final ErrorHandler _instance = ErrorHandler._internal();
  factory ErrorHandler() => _instance;
  ErrorHandler._internal();

  static AnalyticsService? _analyticsService;
  final Map<String, int> _retryAttempts = {};
  final Map<String, DateTime> _lastErrorTime = {};
  final List<Map<String, dynamic>> _errorHistory = [];

  static const int maxRetryAttempts = 3;
  static const Duration retryDelay = Duration(seconds: 2);
  static const Duration errorCooldown = Duration(minutes: 5);

  /// تهيئة معالج الأخطاء
  static void initialize(AnalyticsService analyticsService) {
    _analyticsService = analyticsService;
  }

  /// معالجة العمليات مع إعادة المحاولة الذكية
  Future<T?> handleWithRetry<T>(
    Future<T> Function() operation, {
    String? operationName,
    int maxRetries = maxRetryAttempts,
    Duration delay = retryDelay,
    bool showUserMessage = true,
    Map<String, dynamic>? context,
  }) async {
    final opName =
        operationName ?? 'operation_${DateTime.now().millisecondsSinceEpoch}';
    int attempts = 0;

    // التحقق من cooldown للأخطاء المتكررة
    if (_isInCooldown(opName)) {
      debugPrint('العملية $opName في فترة انتظار بسبب الأخطاء المتكررة');
      return null;
    }

    while (attempts < maxRetries) {
      try {
        final result = await operation();
        // إعادة تعيين عدد المحاولات عند النجاح
        _retryAttempts.remove(opName);
        _lastErrorTime.remove(opName);
        return result;
      } catch (e, stackTrace) {
        attempts++;
        _retryAttempts[opName] = attempts;

        _logError(opName, e, attempts, context, stackTrace);

        if (attempts >= maxRetries) {
          _lastErrorTime[opName] = DateTime.now();
          rethrow;
        }

        // انتظار متدرج قبل إعادة المحاولة
        await Future.delayed(_calculateRetryDelay(attempts, delay));
      }
    }
    return null;
  }

  /// حساب تأخير إعادة المحاولة بشكل متدرج
  Duration _calculateRetryDelay(int attempt, Duration baseDelay) {
    return baseDelay * (1 << (attempt - 1)); // 2s, 4s, 8s
  }

  /// التحقق من فترة الانتظار
  bool _isInCooldown(String operationName) {
    final lastError = _lastErrorTime[operationName];
    if (lastError == null) return false;
    return DateTime.now().difference(lastError) < errorCooldown;
  }

  /// تسجيل الخطأ مع التفاصيل
  void _logError(
    String operation,
    dynamic error,
    int attempt,
    Map<String, dynamic>? context,
    StackTrace? stackTrace,
  ) {
    final errorData = {
      'operation': operation,
      'error': error.toString(),
      'attempt': attempt,
      'timestamp': DateTime.now().toIso8601String(),
      'context': context,
    };

    _errorHistory.add(errorData);

    // الاحتفاظ بآخر 100 خطأ فقط
    if (_errorHistory.length > 100) {
      _errorHistory.removeAt(0);
    }

    debugPrint('خطأ في $operation (المحاولة $attempt): $error');
    if (stackTrace != null) {
      debugPrint('Stack trace: $stackTrace');
    }

    _analyticsService?.logError(operation, error.toString());
    notifyListeners();
  }

  /// الحصول على إحصائيات الأخطاء المتقدمة مع مراقبة شاملة
  Map<String, dynamic> getErrorStatistics() {
    final now = DateTime.now();
    final recentErrors =
        _errorHistory.where((e) {
          final timestamp = DateTime.parse(e['timestamp']);
          return now.difference(timestamp).inHours < 24;
        }).toList();

    final last1hErrors =
        _errorHistory.where((e) {
          final timestamp = DateTime.parse(e['timestamp']);
          return now.difference(timestamp).inHours < 1;
        }).toList();

    return {
      'basic_metrics': {
        'total_errors': _errorHistory.length,
        'recent_errors_24h': recentErrors.length,
        'recent_errors_1h': last1hErrors.length,
        'retry_attempts': Map.from(_retryAttempts),
        'operations_in_cooldown':
            _lastErrorTime.keys.where(_isInCooldown).toList(),
      },
      'performance_analysis': {
        'error_frequency': _calculateErrorFrequency(),
        'success_rate': _calculateSuccessRate(),
        'recovery_rate': _calculateRecoveryRate(),
        'system_health_score': _calculateSystemHealth(),
      },
      'error_patterns': {
        'most_common_errors': _getMostCommonErrors(),
        'error_distribution_by_hour': _getErrorDistributionByHour(),
        'problematic_operations': _getProblematicOperations(),
        'error_trends': _analyzeErrorTrends(),
      },
      'monitoring_insights': {
        'peak_error_times': _getPeakErrorTimes(),
        'error_clustering_detected': _detectErrorClustering(),
        'system_stability': _assessSystemStability(),
        'recommendations': _getSystemRecommendations(),
      },
    };
  }

  /// حساب تكرار الأخطاء
  double _calculateErrorFrequency() {
    if (_errorHistory.isEmpty) return 0.0;
    final now = DateTime.now();
    final last24h =
        _errorHistory.where((e) {
          final timestamp = DateTime.parse(e['timestamp']);
          return now.difference(timestamp).inHours < 24;
        }).length;
    return (last24h / 24.0); // أخطاء في الساعة
  }

  /// حساب معدل النجاح
  double _calculateSuccessRate() {
    final totalOps = _retryAttempts.values.fold(
      0,
      (sum, attempts) => sum + attempts,
    );
    final successfulOps = _retryAttempts.length;
    return totalOps > 0 ? (successfulOps / totalOps) * 100 : 100.0;
  }

  /// حساب معدل الاسترداد
  double _calculateRecoveryRate() {
    if (_retryAttempts.isEmpty) return 100.0;
    final recoveredOps =
        _retryAttempts.values.where((attempts) => attempts > 0).length;
    return (recoveredOps / _retryAttempts.length) * 100;
  }

  /// حساب صحة النظام
  double _calculateSystemHealth() {
    final errorFreq = _calculateErrorFrequency();
    final successRate = _calculateSuccessRate();
    final recoveryRate = _calculateRecoveryRate();

    // نقاط الصحة (0-100)
    final healthScore =
        (successRate * 0.4) +
        (recoveryRate * 0.3) +
        ((24 - errorFreq.clamp(0, 24)) / 24 * 100 * 0.3);

    return healthScore.clamp(0.0, 100.0);
  }

  /// الحصول على الأخطاء الأكثر شيوعاً
  List<Map<String, dynamic>> _getMostCommonErrors() {
    final errorCounts = <String, int>{};

    for (final error in _errorHistory) {
      final errorType = error['error'].toString();
      errorCounts[errorType] = (errorCounts[errorType] ?? 0) + 1;
    }

    final sortedErrors =
        errorCounts.entries.toList()
          ..sort((a, b) => b.value.compareTo(a.value));

    return sortedErrors
        .take(5)
        .map(
          (e) => {
            'error': e.key,
            'count': e.value,
            'percentage': (_errorHistory.isNotEmpty
                    ? (e.value / _errorHistory.length) * 100
                    : 0)
                .toStringAsFixed(1),
          },
        )
        .toList();
  }

  /// توزيع الأخطاء حسب الساعة
  Map<int, int> _getErrorDistributionByHour() {
    final distribution = <int, int>{};

    for (final error in _errorHistory) {
      final timestamp = DateTime.parse(error['timestamp']);
      final hour = timestamp.hour;
      distribution[hour] = (distribution[hour] ?? 0) + 1;
    }

    return distribution;
  }

  /// العمليات الأكثر إشكالية
  List<Map<String, dynamic>> _getProblematicOperations() {
    final operations =
        _retryAttempts.entries.toList()
          ..sort((a, b) => b.value.compareTo(a.value));

    return operations
        .take(5)
        .map(
          (e) => {
            'operation': e.key,
            'retry_count': e.value,
            'status': e.value >= maxRetryAttempts ? 'critical' : 'warning',
          },
        )
        .toList();
  }

  /// تحليل اتجاهات الأخطاء
  Map<String, dynamic> _analyzeErrorTrends() {
    final now = DateTime.now();
    final last1h =
        _errorHistory.where((e) {
          final timestamp = DateTime.parse(e['timestamp']);
          return now.difference(timestamp).inHours < 1;
        }).length;

    final last24h =
        _errorHistory.where((e) {
          final timestamp = DateTime.parse(e['timestamp']);
          return now.difference(timestamp).inHours < 24;
        }).length;

    return {
      'trend': last1h > 5 ? 'increasing' : 'stable',
      'severity':
          last1h > 10
              ? 'critical'
              : last1h > 5
              ? 'warning'
              : 'normal',
      'prediction': last1h > 10 ? 'system_stress' : 'normal_operation',
    };
  }

  /// أوقات ذروة الأخطاء
  List<String> _getPeakErrorTimes() {
    final distribution = _getErrorDistributionByHour();
    final sortedHours =
        distribution.entries.toList()
          ..sort((a, b) => b.value.compareTo(a.value));

    return sortedHours
        .take(3)
        .map((e) => '${e.key}:00-${e.key + 1}:00')
        .toList();
  }

  /// كشف تجمع الأخطاء
  bool _detectErrorClustering() {
    final now = DateTime.now();
    final last10min =
        _errorHistory.where((e) {
          final timestamp = DateTime.parse(e['timestamp']);
          return now.difference(timestamp).inMinutes < 10;
        }).length;

    return last10min > 5; // أكثر من 5 أخطاء في 10 دقائق
  }

  /// تقييم استقرار النظام
  String _assessSystemStability() {
    final healthScore = _calculateSystemHealth();

    if (healthScore >= 90) return 'excellent';
    if (healthScore >= 80) return 'good';
    if (healthScore >= 70) return 'fair';
    if (healthScore >= 60) return 'poor';
    return 'critical';
  }

  /// توصيات النظام
  List<String> _getSystemRecommendations() {
    final recommendations = <String>[];
    final healthScore = _calculateSystemHealth();
    final errorFreq = _calculateErrorFrequency();

    if (healthScore < 70) {
      recommendations.add('تحسين معالجة الأخطاء العامة');
    }

    if (errorFreq > 5) {
      recommendations.add('تقليل تكرار الأخطاء');
    }

    if (_detectErrorClustering()) {
      recommendations.add('فحص تجمع الأخطاء الحديث');
    }

    final problematicOps = _getProblematicOperations();
    if (problematicOps.isNotEmpty) {
      recommendations.add('معالجة العمليات الإشكالية');
    }

    if (recommendations.isEmpty) {
      recommendations.add('النظام يعمل بكفاءة عالية');
    }

    return recommendations;
  }

  /// مسح تاريخ الأخطاء
  void clearErrorHistory() {
    _errorHistory.clear();
    _retryAttempts.clear();
    _lastErrorTime.clear();
    notifyListeners();
  }

  /// معالجة الأخطاء مع عرض رسالة للمستخدم
  static void handleError(
    BuildContext context,
    dynamic error, {
    String? customMessage,
    bool showSnackBar = true,
    bool logError = true,
    VoidCallback? onRetry,
  }) {
    final message = customMessage ?? _getErrorMessage(error);

    if (showSnackBar && context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: AppTheme.errorColor,
          duration: const Duration(seconds: 4),
          action:
              onRetry != null
                  ? SnackBarAction(
                    label: 'إعادة المحاولة',
                    textColor: Colors.white,
                    onPressed: onRetry,
                  )
                  : null,
        ),
      );
    }

    // تسجيل الخطأ
    if (logError) {
      logErrorDetails('UI_ERROR', error);
    }
  }

  /// معالجة أخطاء الشبكة
  static void handleNetworkError(
    BuildContext context, {
    VoidCallback? onRetry,
  }) {
    handleError(
      context,
      'network_error',
      customMessage: 'تحقق من اتصالك بالإنترنت وحاول مرة أخرى',
      onRetry: onRetry,
    );
  }

  /// معالجة أخطاء المصادقة
  static void handleAuthError(BuildContext context, {VoidCallback? onLogin}) {
    handleError(
      context,
      'auth_error',
      customMessage: 'انتهت صلاحية جلستك، يرجى تسجيل الدخول مرة أخرى',
      onRetry: onLogin,
    );
  }

  /// معالجة أخطاء الخادم
  static void handleServerError(BuildContext context, {VoidCallback? onRetry}) {
    handleError(
      context,
      'server_error',
      customMessage: 'حدث خطأ في الخادم، حاول مرة أخرى لاحقاً',
      onRetry: onRetry,
    );
  }

  /// تسجيل تفاصيل الخطأ
  static void logErrorDetails(
    String context,
    dynamic error, [
    StackTrace? stackTrace,
  ]) {
    // تم إزالة errorDetails لأنه غير مستخدم

    // تسجيل في وحدة التحكم
    debugPrint('ERROR [$context]: ${error.toString()}');
    if (stackTrace != null) {
      debugPrint('STACK TRACE: ${stackTrace.toString()}');
    }

    // تسجيل في خدمة التحليلات
    _analyticsService?.logError(context, error.toString());
  }

  /// الحصول على رسالة خطأ مناسبة للمستخدم
  static String _getErrorMessage(dynamic error) {
    final errorString = error.toString().toLowerCase();

    if (errorString.contains('network') ||
        errorString.contains('connection') ||
        errorString.contains('timeout')) {
      return 'تحقق من اتصالك بالإنترنت وحاول مرة أخرى';
    }

    if (errorString.contains('unauthorized') ||
        errorString.contains('authentication') ||
        errorString.contains('token')) {
      return 'انتهت صلاحية جلستك، يرجى تسجيل الدخول مرة أخرى';
    }

    if (errorString.contains('server') ||
        errorString.contains('500') ||
        errorString.contains('internal')) {
      return 'حدث خطأ في الخادم، حاول مرة أخرى لاحقاً';
    }

    if (errorString.contains('not found') || errorString.contains('404')) {
      return 'المورد المطلوب غير موجود';
    }

    if (errorString.contains('permission') ||
        errorString.contains('forbidden') ||
        errorString.contains('403')) {
      return 'ليس لديك صلاحية للوصول لهذا المورد';
    }

    if (errorString.contains('validation') || errorString.contains('invalid')) {
      return 'البيانات المدخلة غير صحيحة';
    }

    return 'حدث خطأ غير متوقع، حاول مرة أخرى';
  }

  /// عرض حوار خطأ مفصل
  static void showErrorDialog(
    BuildContext context,
    String title,
    String message, {
    VoidCallback? onRetry,
    VoidCallback? onCancel,
  }) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Row(
              children: [
                Icon(Icons.error_outline, color: AppTheme.errorColor),
                const SizedBox(width: 8),
                Text(title),
              ],
            ),
            content: Text(message),
            actions: [
              if (onCancel != null)
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                    onCancel();
                  },
                  child: const Text('إلغاء'),
                ),
              if (onRetry != null)
                ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                    onRetry();
                  },
                  child: const Text('إعادة المحاولة'),
                ),
              if (onRetry == null && onCancel == null)
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('موافق'),
                ),
            ],
          ),
    );
  }

  /// معالجة الاستثناءات العامة
  static T? handleException<T>(
    String context,
    T Function() operation, {
    T? fallbackValue,
    bool logError = true,
  }) {
    try {
      return operation();
    } catch (error, stackTrace) {
      if (logError) {
        logErrorDetails(context, error, stackTrace);
      }
      return fallbackValue;
    }
  }

  /// معالجة العمليات غير المتزامنة
  static Future<T?> handleAsyncException<T>(
    String context,
    Future<T> Function() operation, {
    T? fallbackValue,
    bool logError = true,
  }) async {
    try {
      return await operation();
    } catch (error, stackTrace) {
      if (logError) {
        logErrorDetails(context, error, stackTrace);
      }
      return fallbackValue;
    }
  }
}

/// استثناءات مخصصة للتطبيق
class AppException implements Exception {
  final String message;
  final String code;
  final dynamic originalError;

  const AppException(this.message, {this.code = 'UNKNOWN', this.originalError});

  @override
  String toString() => 'AppException: $message (Code: $code)';
}

class NetworkException extends AppException {
  const NetworkException(super.message) : super(code: 'NETWORK');
}

class AuthException extends AppException {
  const AuthException(super.message) : super(code: 'AUTH');
}

class ValidationException extends AppException {
  const ValidationException(super.message) : super(code: 'VALIDATION');
}

class ServerException extends AppException {
  const ServerException(super.message) : super(code: 'SERVER');
}
