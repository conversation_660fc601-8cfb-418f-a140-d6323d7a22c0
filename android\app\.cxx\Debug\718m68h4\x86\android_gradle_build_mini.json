{"buildFiles": ["C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\new_app\\motorcycle_parts_shop\\android\\app\\.cxx\\Debug\\718m68h4\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\new_app\\motorcycle_parts_shop\\android\\app\\.cxx\\Debug\\718m68h4\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}