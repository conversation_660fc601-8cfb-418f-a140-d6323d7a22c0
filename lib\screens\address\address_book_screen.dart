import 'package:flutter/material.dart';
import 'package:motorcycle_parts_shop/core/services/address_service.dart';
import 'package:motorcycle_parts_shop/core/theme/app_theme.dart';
import 'package:motorcycle_parts_shop/core/utils/responsive_helper.dart';
import 'package:motorcycle_parts_shop/models/address_model.dart';
import 'package:motorcycle_parts_shop/screens/address/add_address_screen.dart';
import 'package:motorcycle_parts_shop/screens/address/edit_address_screen.dart';

class AddressBookScreen extends StatefulWidget {
  const AddressBookScreen({super.key});

  @override
  AddressBookScreenState createState() => AddressBookScreenState();
}

class AddressBookScreenState extends State<AddressBookScreen>
    with SingleTickerProviderStateMixin {
  final AddressService _addressService = AddressService();
  List<AddressModel> _addresses = [];
  bool _isLoading = true;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.2),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );
    _loadAddresses();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadAddresses() async {
    if (!mounted) return;
    setState(() {
      _isLoading = true;
    });

    try {
      final addresses = await _addressService.getUserAddresses();
      if (mounted) {
        setState(() {
          _addresses = addresses;
          _isLoading = false;
        });
        _animationController.forward();
      }
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('فشل تحميل العناوين: ${e.toString()}'),
          backgroundColor: AppTheme.errorColor,
          duration: const Duration(seconds: 4),
        ),
      );
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _deleteAddress(String addressId) async {
    final confirmed = await _showConfirmationDialog(
      title: 'حذف العنوان',
      content: 'هل أنت متأكد من حذف هذا العنوان؟',
      confirmText: 'حذف',
      confirmButtonColor: AppTheme.errorColor,
      onConfirm: () => _addressService.deleteAddress(addressId),
    );

    if (confirmed != true || !mounted) return;

    try {
      await _addressService.deleteAddress(addressId);
      await _loadAddresses();
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم حذف العنوان بنجاح'),
          backgroundColor: AppTheme.successColor,
          duration: Duration(seconds: 3),
        ),
      );
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('فشل حذف العنوان: ${e.toString()}'),
          backgroundColor: AppTheme.errorColor,
          duration: const Duration(seconds: 4),
        ),
      );
    }
  }

  Future<void> _setDefaultAddress(String addressId) async {
    final confirmed = await _showConfirmationDialog(
      title: 'تعيين العنوان الافتراضي',
      content: 'هل تريد تعيين هذا العنوان كعنوان افتراضي؟',
      confirmText: 'تعيين',
      confirmButtonColor: AppTheme.primaryColor,
      onConfirm: () => _addressService.setDefaultAddress(addressId),
    );

    if (confirmed != true || !mounted) return;

    try {
      await _addressService.setDefaultAddress(addressId);
      await _loadAddresses();
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم تعيين العنوان الافتراضي بنجاح'),
          backgroundColor: AppTheme.successColor,
          duration: Duration(seconds: 3),
        ),
      );
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('فشل تعيين العنوان الافتراضي: ${e.toString()}'),
          backgroundColor: AppTheme.errorColor,
          duration: const Duration(seconds: 4),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('دفتر العناوين'),
        centerTitle: true,
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        elevation: 0,
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SlideTransition(
          position: _slideAnimation,
          child:
              _isLoading
                  ? const Center(
                    child: CircularProgressIndicator(
                      color: AppTheme.primaryColor,
                    ),
                  )
                  : RefreshIndicator(
                    onRefresh: _loadAddresses,
                    color: AppTheme.primaryColor,
                    child:
                        _addresses.isEmpty
                            ? _buildEmptyState()
                            : _buildAddressList(),
                  ),
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder:
                  (context) => AddAddressScreen(onAddressAdded: _loadAddresses),
            ),
          );
        },
        backgroundColor: AppTheme.primaryColor,
        icon: const Icon(Icons.add_location_alt_outlined, color: Colors.white),
        label: const Text('إضافة عنوان', style: TextStyle(color: Colors.white)),
        elevation: 4.0,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      ),
    );
  }

  Widget _buildEmptyState() {
    return ResponsiveBuilder(
      builder: (context, constraints) {
        final padding = ResponsiveHelper.getPadding(context);
        final imageHeight = ResponsiveHelper.isMobile(context) ? 120.0 : 150.0;

        return Center(
          child: Padding(
            padding: EdgeInsets.all(padding),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // تأكد من تعريف الصورة في pubspec.yaml تحت assets
                Image.asset(
                  'assets/images/empty_address.png',
                  height: imageHeight,
                  errorBuilder:
                      (context, error, stackTrace) => Icon(
                        Icons.map_outlined,
                        size: imageHeight * 0.67,
                        color: Colors.grey,
                      ),
                ),
                const SizedBox(height: 24),
                Text(
                  'دفتر عناوينك فارغ!',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 12),
                Text(
                  'ابدأ بإضافة عنوان جديد لتسهيل عمليات الشراء المستقبلية.',
                  style: Theme.of(
                    context,
                  ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 32),
                ElevatedButton.icon(
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder:
                            (context) => AddAddressScreen(
                              onAddressAdded: _loadAddresses,
                            ),
                      ),
                    );
                  },
                  icon: const Icon(
                    Icons.add_circle_outline,
                    color: Colors.white,
                  ),
                  label: const Text(
                    'إضافة عنوان جديد',
                    style: TextStyle(color: Colors.white),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.accentColor,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 32,
                      vertical: 12,
                    ),
                    textStyle: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildAddressList() {
    return ResponsiveBuilder(
      builder: (context, constraints) {
        final padding = ResponsiveHelper.getPadding(context);

        return ListView.separated(
          padding: EdgeInsets.all(padding),
          itemCount: _addresses.length,
          separatorBuilder: (context, index) => const SizedBox(height: 12),
          cacheExtent: 1000, // تحسين أداء التمرير
          itemBuilder: (context, index) {
            final address = _addresses[index];
            return _buildAddressCard(address);
          },
        );
      },
    );
  }

  Widget _buildAddressCard(AddressModel address) {
    return Card(
      elevation: 3.0,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      margin: EdgeInsets.zero,
      child: InkWell(
        onTap: () {
          // يمكن إضافة إجراء عند النقر على البطاقة، مثل تحديد العنوان
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Icon(
                        address.title.toLowerCase().contains('المنزل')
                            ? Icons.home_work_outlined
                            : address.title.toLowerCase().contains('العمل')
                            ? Icons.business_center_outlined
                            : Icons.location_on_outlined,
                        color: AppTheme.primaryColor,
                        size: 24,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        address.title,
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: AppTheme.primaryColor,
                        ),
                      ),
                    ],
                  ),
                  if (address.isDefault)
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 10,
                        vertical: 5,
                      ),
                      decoration: BoxDecoration(
                        color: AppTheme.accentColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(
                          color: AppTheme.accentColor,
                          width: 1,
                        ),
                      ),
                      child: Row(
                        children: [
                          const Icon(
                            Icons.star_rounded,
                            color: AppTheme.accentColor,
                            size: 16,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            'افتراضي',
                            style: TextStyle(
                              color: AppTheme.accentColor,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
              const Divider(height: 24, thickness: 0.5),
              _buildAddressDetailRow(Icons.person_outline, address.fullName),
              const SizedBox(height: 8),
              _buildAddressDetailRow(
                Icons.signpost_outlined,
                address.fullAddress,
              ),
              const SizedBox(height: 8),
              _buildAddressDetailRow(Icons.phone_outlined, address.phoneNumber),
              const SizedBox(height: 16),
              _buildActionButtons(address),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAddressDetailRow(IconData icon, String text) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(icon, size: 20, color: Colors.grey[700]),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            text,
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(height: 1.4),
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons(AddressModel address) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        if (!address.isDefault)
          TextButton.icon(
            onPressed:
                () => _showConfirmationDialog(
                  title: 'تعيين كافتراضي',
                  content: 'هل تريد تعيين هذا العنوان كعنوان افتراضي؟',
                  confirmText: 'تعيين',
                  onConfirm: () => _setDefaultAddress(address.id),
                  confirmButtonColor: AppTheme.primaryColor,
                ),
            icon: const Icon(Icons.star_border, color: AppTheme.primaryColor),
            label: const Text(
              'تعيين كافتراضي',
              style: TextStyle(color: AppTheme.primaryColor),
            ),
            style: TextButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            ),
          ),
        TextButton.icon(
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder:
                    (context) => EditAddressScreen(
                      address: address,
                      onAddressUpdated: _loadAddresses,
                    ),
              ),
            );
          },
          icon: Icon(Icons.edit_outlined, color: Colors.blueGrey[700]),
          label: Text('تعديل', style: TextStyle(color: Colors.blueGrey[700])),
          style: TextButton.styleFrom(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
        ),
        TextButton.icon(
          onPressed:
              () => _showConfirmationDialog(
                title: 'حذف العنوان',
                content: 'هل أنت متأكد من حذف هذا العنوان؟',
                confirmText: 'حذف',
                onConfirm: () => _deleteAddress(address.id),
                confirmButtonColor: AppTheme.errorColor,
              ),
          icon: const Icon(Icons.delete_outline, color: AppTheme.errorColor),
          label: const Text(
            'حذف',
            style: TextStyle(color: AppTheme.errorColor),
          ),
          style: TextButton.styleFrom(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
        ),
      ],
    );
  }

  Future<bool?> _showConfirmationDialog({
    required String title,
    required String content,
    required Future<void> Function() onConfirm, // Added onConfirm parameter
    String confirmText = 'تأكيد',
    // String cancelText = 'إلغاء', // Removed unused parameter
    Color confirmButtonColor = AppTheme.primaryColor,
  }) async {
    return showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Text(
            title,
            style: Theme.of(dialogContext).textTheme.titleLarge,
          ),
          content: Text(
            content,
            style: Theme.of(dialogContext).textTheme.bodyMedium,
          ),
          actionsPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 12,
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(false),
              child: const Text('إلغاء', style: TextStyle(color: Colors.grey)),
            ),
            ElevatedButton(
              onPressed: () async {
                Navigator.of(context).pop(true);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor:
                    confirmButtonColor, // Removed redundant null check
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                padding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 10,
                ),
              ),
              child: Text(confirmText),
            ),
          ],
        );
      },
    );
  }
}
