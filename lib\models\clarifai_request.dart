import 'dart:io';

/// أنواع طلبات Clarifai
enum ClarifaiRequestType {
  imageAnalysis,
  objectDetection,
  textExtraction,
  colorAnalysis,
  moderation,
}

/// نموذج طلب Clarifai
class ClarifaiRequest {
  final String id;
  final ClarifaiRequestType type;
  final String modelKey;
  final File imageFile;
  final Map<String, dynamic> options;
  final DateTime startTime;

  ClarifaiRequest({
    required this.id,
    required this.type,
    required this.modelKey,
    required this.imageFile,
    required this.options,
    DateTime? startTime,
  }) : startTime = startTime ?? DateTime.now();

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.toString(),
      'model_key': modelKey,
      'file_path': imageFile.path,
      'options': options,
      'start_time': startTime.toIso8601String(),
    };
  }

  factory ClarifaiRequest.fromJson(Map<String, dynamic> json) {
    return ClarifaiRequest(
      id: json['id'] ?? '',
      type: ClarifaiRequestType.values.firstWhere(
        (e) => e.toString() == json['type'],
        orElse: () => ClarifaiRequestType.imageAnalysis,
      ),
      modelKey: json['model_key'] ?? '',
      imageFile: File(json['file_path'] ?? ''),
      options: json['options'] ?? {},
      startTime: DateTime.tryParse(json['start_time'] ?? '') ?? DateTime.now(),
    );
  }
}
