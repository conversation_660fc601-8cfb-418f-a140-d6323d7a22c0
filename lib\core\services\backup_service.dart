import 'package:motorcycle_parts_shop/models/backup_log_model.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class BackupService {
  final SupabaseClient _client;
  final String _tableName = 'backup_logs';

  BackupService(this._client);

  Future<String> createBackup({String? backupName}) async {
    try {
      final response = await _client.rpc(
        'create_backup',
        params: {'p_backup_name': backupName},
      );

      return response as String;
    } catch (e) {
      throw Exception('فشل في إنشاء النسخة الاحتياطية: $e');
    }
  }

  Future<List<BackupLogModel>> getBackupLogs() async {
    try {
      final response = await _client
          .from(_tableName)
          .select()
          .order('created_at', ascending: false);

      return response.map((json) => BackupLogModel.fromJson(json)).toList();
    } catch (e) {
      throw Exception('فشل في جلب سجلات النسخ الاحتياطي: $e');
    }
  }

  Future<BackupLogModel?> getBackupLog(String id) async {
    try {
      final response =
          await _client.from(_tableName).select().eq('id', id).single();

      return response != null ? BackupLogModel.fromJson(response) : null;
    } catch (e) {
      throw Exception('فشل في جلب سجل النسخ الاحتياطي: $e');
    }
  }

  Future<void> deleteBackup(String id) async {
    try {
      final backup = await getBackupLog(id);
      if (backup == null) {
        throw Exception('سجل النسخ الاحتياطي غير موجود');
      }

      // حذف الملف الفعلي
      await _client.storage.from('backups').remove([backup.backupPath]);

      // حذف السجل من قاعدة البيانات
      await _client.from(_tableName).delete().eq('id', id);
    } catch (e) {
      throw Exception('فشل في حذف النسخة الاحتياطية: $e');
    }
  }

  Future<void> restoreBackup(String id) async {
    try {
      final backup = await getBackupLog(id);
      if (backup == null) {
        throw Exception('سجل النسخ الاحتياطي غير موجود');
      }

      if (!backup.isSuccessful) {
        throw Exception('النسخة الاحتياطية غير مكتملة');
      }

      // استعادة النسخة الاحتياطية
      await _client.rpc(
        'restore_backup',
        params: {'p_backup_path': backup.backupPath},
      );
    } catch (e) {
      throw Exception('فشل في استعادة النسخة الاحتياطية: $e');
    }
  }

  Future<void> scheduleBackup({
    required String cronExpression,
    String? backupName,
  }) async {
    try {
      await _client.rpc(
        'schedule_backup',
        params: {
          'p_cron_expression': cronExpression,
          'p_backup_name': backupName,
        },
      );
    } catch (e) {
      throw Exception('فشل في جدولة النسخ الاحتياطي: $e');
    }
  }
}
