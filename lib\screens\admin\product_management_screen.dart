import 'dart:convert';
import 'dart:io';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:motorcycle_parts_shop/core/utils/responsive_helper.dart';
import 'package:path_provider/path_provider.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class ProductManagementScreen extends ConsumerStatefulWidget {
  static const routeName = '/admin/products';

  const ProductManagementScreen({super.key});

  @override
  ConsumerState<ProductManagementScreen> createState() =>
      ProductManagementScreenState();
}

class ProductManagementScreenState
    extends ConsumerState<ProductManagementScreen> {
  final _searchController = TextEditingController();
  String _selectedCategory = 'الكل';
  String _selectedSort = 'الأحدث';
  int _currentPage = 1;
  final int _itemsPerPage = 10;
  int _totalItems = 0;
  String? _selectedProductId;
  bool _isLoading = false;
  bool _isExporting = false; // متغير جديد لتتبع حالة التصدير
  bool _isImporting = false; // متغير جديد لتتبع حالة الاستيراد
  List<Map<String, dynamic>> _products = [];

  @override
  void initState() {
    super.initState();
    _loadProducts();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  // تحميل المنتجات
  Future<void> _loadProducts() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // حساب الإزاحة للصفحة الحالية
      final int offset = (_currentPage - 1) * _itemsPerPage;

      // بناء استعلام Supabase
      final query = Supabase.instance.client.from('products').select();

      // إضافة فلتر الفئة إذا لم تكن "الكل"
      if (_selectedCategory != 'الكل') {
        query.eq('category', _selectedCategory);
      }

      // إضافة فلتر البحث إذا كان هناك نص بحث
      final searchQuery = _searchController.text.trim();
      if (searchQuery.isNotEmpty) {
        query.ilike('name', '%$searchQuery%');
      }

      // إضافة الترتيب
      switch (_selectedSort) {
        case 'الأحدث':
          query.order('created_at', ascending: false);
          break;
        case 'الأقدم':
          query.order('created_at', ascending: true);
          break;
        case 'السعر - الأعلى':
          query.order('price', ascending: false);
          break;
        case 'السعر - الأقل':
          query.order('price', ascending: true);
          break;
      }

      // تنفيذ الاستعلام مع الصفحات
      final response = await query.range(offset, offset + _itemsPerPage - 1);

      // تحديث البيانات
      setState(() {
        _products = List<Map<String, dynamic>>.from(response);
        _totalItems = _products.length;
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('خطأ في تحميل المنتجات: $e');
      setState(() {
        _isLoading = false;
      });
      _showSnackBar('حدث خطأ أثناء تحميل المنتجات');
    }
  }

  // الانتقال إلى شاشة إضافة منتج جديد
  void _navigateToAddProduct() {
    Navigator.pushNamed(context, '/admin/add-product').then((_) {
      // إعادة تحميل المنتجات بعد العودة من شاشة الإضافة
      _loadProducts();
      _showSnackBar('تم العودة من إضافة المنتج');
    });
  }

  // تحرير منتج
  void _editProduct(String productId) {
    Navigator.pushNamed(
      context,
      '/admin/edit-product',
      arguments: {'productId': productId},
    ).then((_) {
      // إعادة تحميل المنتجات بعد العودة من شاشة التحرير
      _loadProducts();
      _showSnackBar('تم تحديث المنتج بنجاح');
    });
  }

  // حذف منتج
  void _deleteProduct(String productId) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('تأكيد الحذف'),
            content: const Text('هل أنت متأكد من حذف هذا المنتج؟'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              TextButton(
                onPressed: () async {
                  Navigator.pop(context);

                  setState(() {
                    _isLoading = true;
                  });

                  try {
                    // حذف المنتج من قاعدة البيانات
                    await Supabase.instance.client
                        .from('products')
                        .delete()
                        .eq('id', productId);

                    debugPrint('تم حذف المنتج: $productId');
                    // إعادة تحميل المنتجات بعد الحذف
                    await _loadProducts();
                    _showSnackBar('تم حذف المنتج بنجاح');
                  } catch (e) {
                    debugPrint('خطأ في حذف المنتج: $e');
                    setState(() {
                      _isLoading = false;
                    });
                    _showSnackBar('حدث خطأ أثناء حذف المنتج');
                  }
                },
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: const Text('حذف'),
              ),
            ],
          ),
    );
  }

  // نسخ منتج
  Future<void> _duplicateProduct(String productId) async {
    setState(() {
      _isLoading = true;
    });

    try {
      // الحصول على بيانات المنتج المراد نسخه
      final response =
          await Supabase.instance.client
              .from('products')
              .select()
              .eq('id', productId)
              .single();

      if (response != null) {
        // إنشاء نسخة من بيانات المنتج مع تعديلات بسيطة
        final productData = Map<String, dynamic>.from(response);

        // حذف الحقول التي لا يجب نسخها
        productData.remove('id');
        productData.remove('created_at');
        productData.remove('updated_at');

        // تعديل اسم المنتج ليشير إلى أنه نسخة
        productData['name'] = '${productData['name']} (نسخة)';

        // إضافة المنتج الجديد
        await Supabase.instance.client.from('products').insert(productData);

        debugPrint('تم نسخ المنتج: $productId');
        await _loadProducts();
        _showSnackBar('تم نسخ المنتج بنجاح');
      }
    } catch (e) {
      debugPrint('خطأ في نسخ المنتج: $e');
      _showSnackBar('حدث خطأ أثناء نسخ المنتج');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  // عرض تفاصيل المنتج
  void _viewProductDetails(String productId) {
    Navigator.pushNamed(
      context,
      '/admin/product-details',
      arguments: {'productId': productId},
    );
  }

  // تحديث البيانات
  void _refreshProducts() {
    _loadProducts();
    _showSnackBar('تم تحديث البيانات');
  }

  // تصدير البيانات
  Future<void> _exportProducts() async {
    // عرض مربع حوار تأكيد قبل التصدير
    final bool? confirmExport = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('تأكيد التصدير'),
            content: const Text('هل تريد تصدير جميع بيانات المنتجات؟'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: const Text('إلغاء'),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context, true),
                child: const Text('تصدير'),
              ),
            ],
          ),
    );

    if (confirmExport != true) return;

    setState(() {
      _isLoading = true;
      _isExporting = true;
    });

    try {
      // الحصول على جميع المنتجات
      final response = await Supabase.instance.client.from('products').select();

      if (response != null) {
        // تحويل البيانات إلى JSON
        final jsonData = jsonEncode(response);

        // حفظ الملف
        final directory = await getApplicationDocumentsDirectory();
        final fileName =
            'products_export_${DateTime.now().millisecondsSinceEpoch}.json';
        final filePath = '${directory.path}/$fileName';
        final file = File(filePath);
        await file.writeAsString(jsonData);

        debugPrint('تم تصدير البيانات إلى: ${file.path}');
        _showSnackBar(
          'تم تصدير ${response.length} منتج بنجاح إلى: $fileName',
          isSuccess: true,
        );
      }
    } catch (e) {
      debugPrint('خطأ في تصدير البيانات: $e');
      _showSnackBar('حدث خطأ أثناء تصدير البيانات: $e', isError: true);
    } finally {
      setState(() {
        _isLoading = false;
        _isExporting = false;
      });
    }
  }

  // استيراد البيانات
  Future<void> _importProducts() async {
    try {
      // اختيار ملف
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['json'],
        dialogTitle: 'اختر ملف JSON لاستيراد المنتجات',
      );

      if (result != null && result.files.single.path != null) {
        // عرض مربع حوار تأكيد قبل الاستيراد
        final bool? confirmImport = await showDialog<bool>(
          context: context,
          builder:
              (context) => AlertDialog(
                title: const Text('تأكيد الاستيراد'),
                content: const Text(
                  'سيتم استيراد المنتجات من الملف المحدد. هل تريد المتابعة؟\n\nملاحظة: قد يستغرق هذا بعض الوقت حسب حجم الملف.',
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.pop(context, false),
                    child: const Text('إلغاء'),
                  ),
                  TextButton(
                    onPressed: () => Navigator.pop(context, true),
                    child: const Text('استيراد'),
                  ),
                ],
              ),
        );

        if (confirmImport != true) return;

        setState(() {
          _isLoading = true;
          _isImporting = true;
        });

        // قراءة الملف
        final file = File(result.files.single.path!);
        final jsonData = await file.readAsString();
        final List<dynamic> productsData = jsonDecode(jsonData);

        // عرض شريط تقدم مع معلومات
        int importedCount = 0;

        // إضافة المنتجات إلى قاعدة البيانات
        for (var productData in productsData) {
          // حذف الحقول التي لا يجب استيرادها
          productData.remove('id');
          productData.remove('created_at');
          productData.remove('updated_at');

          // إضافة المنتج
          await Supabase.instance.client.from('products').insert(productData);

          importedCount++;
          // تحديث حالة التقدم كل 5 منتجات
          if (importedCount % 5 == 0) {
            setState(() {});
          }
        }

        debugPrint('تم استيراد $importedCount منتج');
        await _loadProducts();
        _showSnackBar('تم استيراد $importedCount منتج بنجاح', isSuccess: true);
      }
    } catch (e) {
      debugPrint('خطأ في استيراد البيانات: $e');
      _showSnackBar('حدث خطأ أثناء استيراد البيانات: $e', isError: true);
    } finally {
      setState(() {
        _isLoading = false;
        _isImporting = false;
      });
    }
  }

  // الانتقال إلى صفحة معينة
  void _goToPage(int page) {
    setState(() {
      _currentPage = page;
    });
    _loadProducts();
  }

  // عرض رسالة Snackbar محسنة
  void _showSnackBar(
    String message, {
    bool isError = false,
    bool isSuccess = false,
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            if (isError)
              const Icon(Icons.error, color: Colors.white)
            else if (isSuccess)
              const Icon(Icons.check_circle, color: Colors.white)
            else
              const Icon(Icons.info, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor:
            isError
                ? Colors.red
                : isSuccess
                ? Colors.green
                : null,
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        margin: const EdgeInsets.all(8),
        action: SnackBarAction(
          label: 'إغلاق',
          textColor: Colors.white,
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }

  // البحث عن المنتجات
  void _searchProducts() {
    // إعادة تعيين الصفحة إلى الصفحة الأولى عند البحث
    setState(() {
      _currentPage = 1;
    });

    final searchQuery = _searchController.text.trim();
    debugPrint(
      'البحث عن: $searchQuery، الفئة: $_selectedCategory، الترتيب: $_selectedSort',
    );
    _loadProducts(); // إعادة تحميل المنتجات مع تطبيق معايير البحث
  }

  @override
  Widget build(BuildContext context) {
    // حساب عدد الصفحات
    final totalPages = (_totalItems / _itemsPerPage).ceil();

    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة المنتجات'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'تحديث البيانات',
            onPressed: _isLoading ? null : _refreshProducts,
          ),
          IconButton(
            icon:
                _isExporting
                    ? const SizedBox(
                      width: 24,
                      height: 24,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                    : const Icon(Icons.file_upload),
            tooltip: 'تصدير البيانات',
            onPressed: (_isLoading || _isExporting) ? null : _exportProducts,
          ),
          IconButton(
            icon:
                _isImporting
                    ? const SizedBox(
                      width: 24,
                      height: 24,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                    : const Icon(Icons.file_download),
            tooltip: 'استيراد البيانات',
            onPressed: (_isLoading || _isImporting) ? null : _importProducts,
          ),
          IconButton(
            icon: const Icon(Icons.add),
            tooltip: 'إضافة منتج جديد',
            onPressed: _isLoading ? null : _navigateToAddProduct,
          ),
        ],
      ),
      body: ResponsiveBuilder(
        builder: (context, constraints) {
          final padding = ResponsiveHelper.getPadding(context);

          return Padding(
            padding: EdgeInsets.all(padding),
            child: Column(
              children: [
                // Search and Filter Bar
                ResponsiveHelper.isMobile(context)
                    ? Column(
                      children: [
                        TextField(
                          controller: _searchController,
                          decoration: InputDecoration(
                            hintText: 'بحث عن منتج...',
                            prefixIcon: const Icon(Icons.search),
                            suffixIcon: IconButton(
                              icon: const Icon(Icons.clear),
                              tooltip: 'مسح البحث',
                              onPressed: () {
                                _searchController.clear();
                                _searchProducts();
                              },
                            ),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          onSubmitted: (_) => _searchProducts(),
                        ),
                        const SizedBox(height: 16),
                        Row(
                          children: [
                            Expanded(
                              child: DropdownButtonFormField<String>(
                                value: _selectedCategory,
                                decoration: InputDecoration(
                                  labelText: 'الفئة',
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                ),
                                items:
                                    [
                                      'الكل',
                                      'المحرك',
                                      'الفرامل',
                                      'الإطارات',
                                      'الكهرباء',
                                    ].map((category) {
                                      return DropdownMenuItem(
                                        value: category,
                                        child: Text(category),
                                      );
                                    }).toList(),
                                onChanged:
                                    _isLoading
                                        ? null
                                        : (value) {
                                          setState(() {
                                            _selectedCategory = value!;
                                            _searchProducts();
                                          });
                                        },
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: DropdownButtonFormField<String>(
                                value: _selectedSort,
                                decoration: InputDecoration(
                                  labelText: 'الترتيب',
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                ),
                                items:
                                    [
                                      'الأحدث',
                                      'الأقدم',
                                      'السعر - الأعلى',
                                      'السعر - الأقل',
                                    ].map((sort) {
                                      return DropdownMenuItem(
                                        value: sort,
                                        child: Text(sort),
                                      );
                                    }).toList(),
                                onChanged:
                                    _isLoading
                                        ? null
                                        : (value) {
                                          setState(() {
                                            _selectedSort = value!;
                                            _searchProducts();
                                          });
                                        },
                              ),
                            ),
                          ],
                        ),
                      ],
                    )
                    : Row(
                      children: [
                        Expanded(
                          flex: 3,
                          child: TextField(
                            controller: _searchController,
                            decoration: InputDecoration(
                              hintText: 'بحث عن منتج...',
                              prefixIcon: const Icon(Icons.search),
                              suffixIcon: IconButton(
                                icon: const Icon(Icons.clear),
                                tooltip: 'مسح البحث',
                                onPressed: () {
                                  _searchController.clear();
                                  _searchProducts();
                                },
                              ),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            onSubmitted: (_) => _searchProducts(),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: DropdownButtonFormField<String>(
                            value: _selectedCategory,
                            decoration: InputDecoration(
                              labelText: 'الفئة',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            items:
                                [
                                  'الكل',
                                  'المحرك',
                                  'الفرامل',
                                  'الإطارات',
                                  'الكهرباء',
                                ].map((category) {
                                  return DropdownMenuItem(
                                    value: category,
                                    child: Text(category),
                                  );
                                }).toList(),
                            onChanged:
                                _isLoading
                                    ? null
                                    : (value) {
                                      setState(() {
                                        _selectedCategory = value!;
                                        _searchProducts(); // البحث عند تغيير الفئة
                                      });
                                    },
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: DropdownButtonFormField<String>(
                            value: _selectedSort,
                            decoration: InputDecoration(
                              labelText: 'الترتيب',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            items:
                                [
                                  'الأحدث',
                                  'الأقدم',
                                  'السعر - الأعلى',
                                  'السعر - الأقل',
                                ].map((sort) {
                                  return DropdownMenuItem(
                                    value: sort,
                                    child: Text(sort),
                                  );
                                }).toList(),
                            onChanged:
                                _isLoading
                                    ? null
                                    : (value) {
                                      setState(() {
                                        _selectedSort = value!;
                                        _searchProducts(); // البحث عند تغيير الترتيب
                                      });
                                    },
                          ),
                        ),
                      ],
                    ),
                const SizedBox(height: 16),
                // إضافة شريط معلومات
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.blue.shade200),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.info, color: Colors.blue),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'إجمالي المنتجات: $_totalItems | الصفحة: $_currentPage من ${totalPages > 0 ? totalPages : 1}',
                          style: const TextStyle(color: Colors.blue),
                        ),
                      ),
                      if (_selectedProductId != null) ...[
                        const Text(
                          ' | المنتج المحدد: ',
                          style: TextStyle(color: Colors.blue),
                        ),
                        Text(
                          _products.firstWhere(
                            (p) => p['id'].toString() == _selectedProductId,
                            orElse: () => {'name': 'غير معروف'},
                          )['name'],
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.blue,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                const SizedBox(height: 8),
                // Loading Indicator
                if (_isLoading)
                  Column(
                    children: [
                      LinearProgressIndicator(
                        backgroundColor: Colors.grey[200],
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _isExporting
                            ? 'جاري تصدير البيانات...'
                            : _isImporting
                            ? 'جاري استيراد البيانات...'
                            : 'جاري تحميل البيانات...',
                        style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                      ),
                    ],
                  ),
                const SizedBox(height: 8),
                // Products Table
                Expanded(
                  child: Card(
                    child:
                        _isLoading && _products.isEmpty
                            ? const Center(
                              child: Text('جاري تحميل البيانات...'),
                            )
                            : _products.isEmpty
                            ? const Center(child: Text('لا توجد منتجات متاحة'))
                            : SingleChildScrollView(
                              scrollDirection: Axis.horizontal,
                              child: DataTable(
                                columns: const [
                                  DataColumn(label: Text('صورة')),
                                  DataColumn(label: Text('الاسم')),
                                  DataColumn(label: Text('SKU')),
                                  DataColumn(label: Text('السعر')),
                                  DataColumn(label: Text('المخزون')),
                                  DataColumn(label: Text('الفئة')),
                                  DataColumn(label: Text('الحالة')),
                                  DataColumn(label: Text('إجراءات')),
                                ],
                                rows: List.generate(_products.length, (index) {
                                  final product = _products[index];
                                  final productId =
                                      product['id']?.toString() ?? '';
                                  return DataRow(
                                    selected: productId == _selectedProductId,
                                    onSelectChanged: (selected) {
                                      setState(() {
                                        _selectedProductId =
                                            selected! ? productId : null;
                                      });
                                    },
                                    cells: [
                                      DataCell(
                                        Container(
                                          width: 50,
                                          height: 50,
                                          decoration: BoxDecoration(
                                            color: Colors.grey[200],
                                            borderRadius: BorderRadius.circular(
                                              4,
                                            ),
                                            image:
                                                product['image_url'] != null
                                                    ? DecorationImage(
                                                      image: NetworkImage(
                                                        product['image_url'],
                                                      ),
                                                      fit: BoxFit.cover,
                                                    )
                                                    : null,
                                          ),
                                          child:
                                              product['image_url'] == null
                                                  ? const Icon(Icons.image)
                                                  : null,
                                        ),
                                      ),
                                      DataCell(
                                        Text(product['name'] ?? 'بدون اسم'),
                                      ),
                                      DataCell(Text(product['sku'] ?? '-')),
                                      DataCell(
                                        Text('${product['price'] ?? 0} ج.م'),
                                      ),
                                      DataCell(
                                        Text('${product['stock'] ?? 0}'),
                                      ),
                                      DataCell(
                                        Text(product['category'] ?? '-'),
                                      ),
                                      DataCell(
                                        Chip(
                                          label: Text(
                                            (product['stock'] ?? 0) > 0
                                                ? 'متوفر'
                                                : 'غير متوفر',
                                          ),
                                          backgroundColor:
                                              (product['stock'] ?? 0) > 0
                                                  ? Colors.green[100]
                                                  : Colors.red[100],
                                          labelStyle: TextStyle(
                                            color:
                                                (product['stock'] ?? 0) > 0
                                                    ? Colors.green[900]
                                                    : Colors.red[900],
                                          ),
                                        ),
                                      ),
                                      DataCell(
                                        Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            IconButton(
                                              icon: const Icon(Icons.edit),
                                              tooltip: 'تحرير',
                                              onPressed:
                                                  _isLoading
                                                      ? null
                                                      : () => _editProduct(
                                                        productId,
                                                      ),
                                            ),
                                            IconButton(
                                              icon: const Icon(Icons.delete),
                                              tooltip: 'حذف',
                                              color: Colors.red,
                                              onPressed:
                                                  _isLoading
                                                      ? null
                                                      : () => _deleteProduct(
                                                        productId,
                                                      ),
                                            ),
                                            IconButton(
                                              icon: const Icon(Icons.copy),
                                              tooltip: 'نسخ',
                                              onPressed:
                                                  _isLoading
                                                      ? null
                                                      : () => _duplicateProduct(
                                                        productId,
                                                      ),
                                            ),
                                            IconButton(
                                              icon: const Icon(
                                                Icons.visibility,
                                              ),
                                              tooltip: 'عرض التفاصيل',
                                              onPressed:
                                                  _isLoading
                                                      ? null
                                                      : () =>
                                                          _viewProductDetails(
                                                            productId,
                                                          ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  );
                                }),
                              ),
                            ),
                  ),
                ),
                const SizedBox(height: 16),
                // Pagination Controls
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    IconButton(
                      icon: const Icon(Icons.arrow_back),
                      tooltip: 'الصفحة السابقة',
                      onPressed:
                          (_isLoading || _currentPage <= 1)
                              ? null
                              : () => _goToPage(_currentPage - 1),
                    ),
                    Text(
                      'الصفحة $_currentPage من ${totalPages > 0 ? totalPages : 1}',
                    ),
                    IconButton(
                      icon: const Icon(Icons.arrow_forward),
                      tooltip: 'الصفحة التالية',
                      onPressed:
                          (_isLoading ||
                                  _currentPage >= totalPages ||
                                  totalPages <= 1)
                              ? null
                              : () => _goToPage(_currentPage + 1),
                    ),
                  ],
                ),
              ],
            ),
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _isLoading ? null : _navigateToAddProduct,
        tooltip: 'إضافة منتج جديد',
        backgroundColor: _isLoading ? Colors.grey : null,
        child: const Icon(Icons.add),
      ),
    );
  }
}
