import 'package:flutter/material.dart';
import 'package:motorcycle_parts_shop/core/theme/app_theme.dart';

class AboutScreen extends StatelessWidget {
  const AboutScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('عن التطبيق'), centerTitle: true),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Center(
              child: Column(
                children: [
                  const Icon(
                    Icons.store,
                    size: 80,
                    color: AppTheme.primaryColor,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'متجر قطع غيار الدراجات النارية',
                    style: Theme.of(context).textTheme.headlineSmall,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'الإصدار 1.0.0',
                    style: TextStyle(color: AppTheme.textSecondaryColor),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 32),
            const Text(
              'عن المتجر',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const Text(
              'متجر متخصص في بيع قطع غيار الدراجات النارية الأصلية والمعتمدة. نوفر مجموعة واسعة من القطع لمختلف الماركات والموديلات مع ضمان الجودة وخدمة العملاء المتميزة.',
            ),
            const SizedBox(height: 24),
            const Text(
              'المميزات',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            _buildFeatureItem('قطع غيار أصلية ومعتمدة'),
            _buildFeatureItem('ضمان على جميع المنتجات'),
            _buildFeatureItem('خدمة توصيل سريعة'),
            _buildFeatureItem('دعم فني متخصص'),
            _buildFeatureItem('أسعار تنافسية'),
            const SizedBox(height: 24),
            const Text(
              'التواصل',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            _buildContactItem(Icons.home, 'العنوان', 'القاهرة، مصر'),
            _buildContactItem(Icons.phone, 'الهاتف', '0123456789'),
            _buildContactItem(
              Icons.email,
              'البريد الإلكتروني',
              '<EMAIL>',
            ),
            const SizedBox(height: 24),
            const Text(
              'الحقوق',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const Text(
              'جميع الحقوق محفوظة © 2024 متجر قطع غيار الدراجات النارية',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureItem(String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        children: [
          const Icon(
            Icons.check_circle,
            color: AppTheme.primaryColor,
            size: 20,
          ),
          const SizedBox(width: 8),
          Text(text),
        ],
      ),
    );
  }

  Widget _buildContactItem(IconData icon, String title, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        children: [
          Icon(icon, color: AppTheme.primaryColor, size: 20),
          const SizedBox(width: 8),
          Text('$title: ', style: const TextStyle(fontWeight: FontWeight.bold)),
          Text(value),
        ],
      ),
    );
  }
}
