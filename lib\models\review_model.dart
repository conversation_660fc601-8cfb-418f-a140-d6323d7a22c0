
class ReviewModel {
  final String id;
  final String userId;
  final String productId;
  final int rating;
  final String? comment;
  final List<String>? images;
  final String? title;
  final bool verifiedPurchase;
  final DateTime createdAt;
  final DateTime updatedAt;

  ReviewModel({
    required this.id,
    required this.userId,
    required this.productId,
    required this.rating,
    this.comment,
    this.images,
    this.title,
    required this.verifiedPurchase,
    required this.createdAt,
    required this.updatedAt,
  }) : assert(rating >= 1 && rating <= 5, 'التقييم يجب أن يكون بين 1 و 5');

  factory ReviewModel.fromJson(Map<String, dynamic> json) {
    return ReviewModel(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      productId: json['product_id'] as String,
      rating: json['rating'] as int,
      comment: json['comment'] as String?,
      images:
          json['images'] != null
              ? List<String>.from(json['images'] as List)
              : null,
      title: json['title'] as String?,
      verifiedPurchase: json['verified_purchase'] as bool? ?? false,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'product_id': productId,
      'rating': rating,
      'comment': comment,
      'images': images,
      'title': title,
      'verified_purchase': verifiedPurchase,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  ReviewModel copyWith({
    String? id,
    String? userId,
    String? productId,
    int? rating,
    String? comment,
    List<String>? images,
    String? title,
    bool? verifiedPurchase,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ReviewModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      productId: productId ?? this.productId,
      rating: rating ?? this.rating,
      comment: comment ?? this.comment,
      images: images ?? this.images,
      title: title ?? this.title,
      verifiedPurchase: verifiedPurchase ?? this.verifiedPurchase,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  bool get hasImages => images != null && images!.isNotEmpty;
  bool get hasComment => comment != null && comment!.trim().isNotEmpty;
  bool get hasTitle => title != null && title!.trim().isNotEmpty;
  String get mainImage => images?.first ?? '';
  List<String> get additionalImages =>
      images != null && images!.length > 1 ? images!.sublist(1) : [];
}
