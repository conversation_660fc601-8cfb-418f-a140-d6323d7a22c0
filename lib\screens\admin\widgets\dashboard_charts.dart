import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:motorcycle_parts_shop/core/services/analytics_service.dart';
import 'package:provider/provider.dart';

class DashboardCharts extends StatefulWidget {
  const DashboardCharts({super.key});

  @override
  State<DashboardCharts> createState() => _DashboardChartsState();
}

class _DashboardChartsState extends State<DashboardCharts> {
  bool _isLoading = true;
  late AnalyticsService _analyticsService;

  // بيانات المبيعات الشهرية
  List<FlSpot> _monthlySalesSpots = [];

  // بيانات توزيع المبيعات حسب الفئة
  List<PieChartSectionData> _categorySalesSections = [];

  @override
  void initState() {
    super.initState();
    _initializeServices();
  }

  Future<void> _initializeServices() async {
    try {
      _analyticsService = Provider.of<AnalyticsService>(context, listen: false);

      await _loadChartData();
    } catch (e) {
      debugPrint('خطأ في تهيئة خدمات الرسوم البيانية: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _loadChartData() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // جلب بيانات المبيعات الشهرية
      final List<double> monthlySales =
          await _analyticsService.getMonthlySalesData();

      // تحويل البيانات إلى تنسيق مناسب للرسم البياني
      _monthlySalesSpots = [];
      for (int i = 0; i < monthlySales.length; i++) {
        _monthlySalesSpots.add(FlSpot(i.toDouble(), monthlySales[i]));
      }

      // إذا لم تكن هناك بيانات، استخدم بيانات افتراضية للعرض
      if (_monthlySalesSpots.isEmpty) {
        _monthlySalesSpots = [
          const FlSpot(0, 3000),
          const FlSpot(1, 4500),
          const FlSpot(2, 3800),
          const FlSpot(3, 5200),
          const FlSpot(4, 4800),
          const FlSpot(5, 6000),
        ];
      }

      // جلب بيانات توزيع المبيعات حسب الفئة
      final Map<String, double> categorySales =
          await _analyticsService.getCategorySalesData();

      // تحويل البيانات إلى تنسيق مناسب للرسم البياني
      _categorySalesSections = [];
      final colors = [
        Colors.blue,
        Colors.orange,
        Colors.green,
        Colors.purple,
        Colors.red,
      ];

      int i = 0;
      categorySales.forEach((category, value) {
        _categorySalesSections.add(
          PieChartSectionData(
            color: colors[i % colors.length],
            value: value,
            title: category,
            radius: 50,
            titleStyle: const TextStyle(color: Colors.white, fontSize: 12),
          ),
        );
        i++;
      });

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('خطأ في تحميل بيانات الرسوم البيانية: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        const SizedBox(height: 24),
        _isLoading
            ? const Center(child: CircularProgressIndicator())
            : Row(
              children: [
                Expanded(
                  flex: 2,
                  child: Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'المبيعات الشهرية',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          SizedBox(
                            height: 300,
                            child: LineChart(
                              LineChartData(
                                gridData: FlGridData(show: false),
                                titlesData: FlTitlesData(
                                  leftTitles: AxisTitles(
                                    sideTitles: SideTitles(showTitles: true),
                                  ),
                                  bottomTitles: AxisTitles(
                                    sideTitles: SideTitles(
                                      showTitles: true,
                                      getTitlesWidget: (value, meta) {
                                        const months = [
                                          'يناير',
                                          'فبراير',
                                          'مارس',
                                          'أبريل',
                                          'مايو',
                                          'يونيو',
                                        ];
                                        if (value >= 0 &&
                                            value < months.length) {
                                          return Text(months[value.toInt()]);
                                        }
                                        return const Text('');
                                      },
                                    ),
                                  ),
                                  rightTitles: AxisTitles(
                                    sideTitles: SideTitles(showTitles: false),
                                  ),
                                  topTitles: AxisTitles(
                                    sideTitles: SideTitles(showTitles: false),
                                  ),
                                ),
                                borderData: FlBorderData(
                                  show: true,
                                  border: Border.all(
                                    color: Colors.grey.shade300,
                                  ),
                                ),
                                lineBarsData: [
                                  LineChartBarData(
                                    spots: _monthlySalesSpots,
                                    isCurved: true,
                                    color: Colors.blue,
                                    barWidth: 3,
                                    dotData: FlDotData(show: true),
                                    belowBarData: BarAreaData(
                                      show: true,
                                      color: Colors.blue.withOpacity(0.1),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'توزيع المبيعات حسب الفئة',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          SizedBox(
                            height: 300,
                            child: PieChart(
                              PieChartData(
                                sectionsSpace: 2,
                                centerSpaceRadius: 40,
                                sections: _categorySalesSections,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
      ],
    );
  }
}
