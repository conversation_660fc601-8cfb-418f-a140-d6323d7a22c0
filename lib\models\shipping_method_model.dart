
class ShippingMethodModel {
  final String id;
  final String name;
  final String? description;
  final double basePrice;
  final int estimatedDays;
  final Map<String, dynamic>? rules;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  ShippingMethodModel({
    required this.id,
    required this.name,
    this.description,
    required this.basePrice,
    required this.estimatedDays,
    this.rules,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
  }) : assert(estimatedDays > 0, 'عدد الأيام المتوقعة يجب أن يكون أكبر من صفر'),
       assert(basePrice >= 0, 'السعر الأساسي يجب أن يكون صفر أو أكثر');

  factory ShippingMethodModel.fromJson(Map<String, dynamic> json) {
    return ShippingMethodModel(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      basePrice: (json['cost'] as num).toDouble(),
      estimatedDays: json['estimated_days'] as int,
      rules:
          json['rules'] != null
              ? Map<String, dynamic>.from(json['rules'] as Map)
              : null,
      isActive: json['is_active'] as bool? ?? false,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'cost': basePrice,
      'estimated_days': estimatedDays,
      'rules': rules,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  ShippingMethodModel copyWith({
    String? id,
    String? name,
    String? description,
    double? basePrice,
    int? estimatedDays,
    Map<String, dynamic>? rules,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ShippingMethodModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      basePrice: basePrice ?? this.basePrice,
      estimatedDays: estimatedDays ?? this.estimatedDays,
      rules: rules ?? this.rules,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  DateTime calculateEstimatedDelivery([DateTime? orderDate]) {
    final startDate = orderDate ?? DateTime.now();
    return startDate.add(Duration(days: estimatedDays));
  }

  double calculateShippingCost(String governorate, {int itemCount = 1}) {
    // قاعدة حساب أساسية للتكلفة تعتمد على المحافظة وعدد القطع
    // يمكن تخصيص هذه الدالة حسب قواعد العمل
    double cost = basePrice;

    if (rules != null) {
      // تطبيق القواعد المخصصة إذا وجدت
      if (rules!['item_multiplier'] != null) {
        cost += itemCount * (rules!['item_multiplier'] as num).toDouble();
      }

      if (rules!['governorate_fees'] != null &&
          rules!['governorate_fees'][governorate] != null) {
        cost += (rules!['governorate_fees'][governorate] as num).toDouble();
      }
    }

    return cost;
  }
}
