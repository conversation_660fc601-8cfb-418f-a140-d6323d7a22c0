import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:motorcycle_parts_shop/core/services/unified_storage_service.dart';

/// خدمة الترجمة المحسنة والسريعة
class LocalizationService extends ChangeNotifier {
  static final LocalizationService _instance = LocalizationService._internal();
  factory LocalizationService() => _instance;
  LocalizationService._internal();

  final UnifiedStorageService _storage = UnifiedStorageService();
  
  String _currentLanguage = 'ar';
  bool _isInitialized = false;
  Map<String, Map<String, String>> _translations = {};
  
  // اللغات المدعومة
  static const List<String> supportedLanguages = ['ar', 'en'];
  static const Map<String, String> languageNames = {
    'ar': 'العربية',
    'en': 'English',
  };

  // Getters
  String get currentLanguage => _currentLanguage;
  bool get isInitialized => _isInitialized;
  bool get isRTL => _currentLanguage == 'ar';
  Locale get currentLocale => Locale(_currentLanguage);
  List<String> get availableLanguages => supportedLanguages;

  /// تهيئة الخدمة
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // تحميل اللغة المحفوظة
      _currentLanguage = _storage.getString('selected_language') ?? 'ar';
      
      // تحميل الترجمات
      await _loadTranslations();
      
      _isInitialized = true;
      debugPrint('✅ تم تهيئة خدمة الترجمة بنجاح - اللغة: $_currentLanguage');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة خدمة الترجمة: $e');
      _isInitialized = true;
    }
  }

  /// تحميل الترجمات من الملفات
  Future<void> _loadTranslations() async {
    try {
      for (final language in supportedLanguages) {
        final String jsonString = await rootBundle.loadString(
          'assets/translations/$language.json'
        );
        final Map<String, dynamic> jsonMap = json.decode(jsonString);
        
        _translations[language] = jsonMap.map(
          (key, value) => MapEntry(key, value.toString())
        );
      }
      
      debugPrint('🌐 تم تحميل الترجمات لـ ${_translations.length} لغة');
    } catch (e) {
      debugPrint('❌ خطأ في تحميل الترجمات: $e');
      // إنشاء ترجمات افتراضية
      _createDefaultTranslations();
    }
  }

  /// إنشاء ترجمات افتراضية
  void _createDefaultTranslations() {
    _translations = {
      'ar': {
        'app_name': 'متجر قطع غيار الدراجات النارية',
        'welcome': 'مرحباً بك',
        'login': 'تسجيل الدخول',
        'register': 'إنشاء حساب',
        'home': 'الرئيسية',
        'products': 'المنتجات',
        'cart': 'السلة',
        'profile': 'الملف الشخصي',
        'settings': 'الإعدادات',
        'search': 'البحث',
        'add_to_cart': 'إضافة للسلة',
        'buy_now': 'اشتري الآن',
        'price': 'السعر',
        'quantity': 'الكمية',
        'total': 'المجموع',
        'checkout': 'الدفع',
        'order_history': 'تاريخ الطلبات',
        'favorites': 'المفضلة',
        'notifications': 'الإشعارات',
        'language': 'اللغة',
        'theme': 'المظهر',
        'logout': 'تسجيل الخروج',
        'cancel': 'إلغاء',
        'confirm': 'تأكيد',
        'save': 'حفظ',
        'delete': 'حذف',
        'edit': 'تعديل',
        'loading': 'جاري التحميل...',
        'error': 'خطأ',
        'success': 'نجح',
        'retry': 'إعادة المحاولة',
        'no_data': 'لا توجد بيانات',
        'no_internet': 'لا يوجد اتصال بالإنترنت',
      },
      'en': {
        'app_name': 'Motorcycle Parts Shop',
        'welcome': 'Welcome',
        'login': 'Login',
        'register': 'Register',
        'home': 'Home',
        'products': 'Products',
        'cart': 'Cart',
        'profile': 'Profile',
        'settings': 'Settings',
        'search': 'Search',
        'add_to_cart': 'Add to Cart',
        'buy_now': 'Buy Now',
        'price': 'Price',
        'quantity': 'Quantity',
        'total': 'Total',
        'checkout': 'Checkout',
        'order_history': 'Order History',
        'favorites': 'Favorites',
        'notifications': 'Notifications',
        'language': 'Language',
        'theme': 'Theme',
        'logout': 'Logout',
        'cancel': 'Cancel',
        'confirm': 'Confirm',
        'save': 'Save',
        'delete': 'Delete',
        'edit': 'Edit',
        'loading': 'Loading...',
        'error': 'Error',
        'success': 'Success',
        'retry': 'Retry',
        'no_data': 'No Data',
        'no_internet': 'No Internet Connection',
      },
    };
  }

  /// تغيير اللغة
  Future<void> changeLanguage(String languageCode) async {
    if (!supportedLanguages.contains(languageCode)) {
      debugPrint('❌ اللغة غير مدعومة: $languageCode');
      return;
    }

    if (_currentLanguage == languageCode) return;

    _currentLanguage = languageCode;
    
    // حفظ اللغة الجديدة
    await _storage.saveString('selected_language', languageCode);
    
    notifyListeners();
    debugPrint('🌐 تم تغيير اللغة إلى: $languageCode');
  }

  /// الحصول على ترجمة نص
  String translate(String key, {Map<String, String>? params}) {
    final translations = _translations[_currentLanguage];
    if (translations == null) {
      debugPrint('❌ لا توجد ترجمات للغة: $_currentLanguage');
      return key;
    }

    String translation = translations[key] ?? key;
    
    // استبدال المعاملات إذا وجدت
    if (params != null) {
      params.forEach((paramKey, paramValue) {
        translation = translation.replaceAll('{$paramKey}', paramValue);
      });
    }

    return translation;
  }

  /// اختصار للترجمة
  String tr(String key, {Map<String, String>? params}) {
    return translate(key, params: params);
  }

  /// الحصول على اسم اللغة
  String getLanguageName(String languageCode) {
    return languageNames[languageCode] ?? languageCode;
  }

  /// الحصول على اسم اللغة الحالية
  String get currentLanguageName => getLanguageName(_currentLanguage);

  /// التحقق من دعم اللغة
  bool isLanguageSupported(String languageCode) {
    return supportedLanguages.contains(languageCode);
  }

  /// الحصول على اتجاه النص
  TextDirection get textDirection {
    return _currentLanguage == 'ar' ? TextDirection.rtl : TextDirection.ltr;
  }

  /// الحصول على محاذاة النص
  TextAlign get textAlign {
    return _currentLanguage == 'ar' ? TextAlign.right : TextAlign.left;
  }

  /// الحصول على محاذاة النص المعاكسة
  TextAlign get oppositeTextAlign {
    return _currentLanguage == 'ar' ? TextAlign.left : TextAlign.right;
  }

  /// الحصول على MainAxisAlignment للـ RTL
  MainAxisAlignment get mainAxisAlignment {
    return _currentLanguage == 'ar' 
        ? MainAxisAlignment.end 
        : MainAxisAlignment.start;
  }

  /// الحصول على CrossAxisAlignment للـ RTL
  CrossAxisAlignment get crossAxisAlignment {
    return _currentLanguage == 'ar' 
        ? CrossAxisAlignment.end 
        : CrossAxisAlignment.start;
  }

  /// تنسيق الأرقام حسب اللغة
  String formatNumber(num number) {
    if (_currentLanguage == 'ar') {
      // تحويل الأرقام للعربية
      return number.toString().replaceAllMapped(
        RegExp(r'[0-9]'),
        (match) => '٠١٢٣٤٥٦٧٨٩'[int.parse(match.group(0)!)],
      );
    }
    return number.toString();
  }

  /// تنسيق العملة
  String formatCurrency(double amount, {String currency = 'ريال'}) {
    final formattedAmount = formatNumber(amount);
    
    if (_currentLanguage == 'ar') {
      return '$formattedAmount $currency';
    } else {
      return '$currency $formattedAmount';
    }
  }

  /// تنسيق التاريخ
  String formatDate(DateTime date) {
    if (_currentLanguage == 'ar') {
      final months = [
        'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
        'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
      ];
      return '${formatNumber(date.day)} ${months[date.month - 1]} ${formatNumber(date.year)}';
    } else {
      final months = [
        'January', 'February', 'March', 'April', 'May', 'June',
        'July', 'August', 'September', 'October', 'November', 'December'
      ];
      return '${months[date.month - 1]} ${date.day}, ${date.year}';
    }
  }

  /// إضافة ترجمات جديدة
  void addTranslations(String languageCode, Map<String, String> translations) {
    if (_translations[languageCode] == null) {
      _translations[languageCode] = {};
    }
    
    _translations[languageCode]!.addAll(translations);
    debugPrint('✅ تم إضافة ${translations.length} ترجمة للغة $languageCode');
  }

  /// الحصول على جميع الترجمات للغة معينة
  Map<String, String>? getTranslationsForLanguage(String languageCode) {
    return _translations[languageCode];
  }

  /// البحث في الترجمات
  List<String> searchTranslations(String query) {
    final results = <String>[];
    final currentTranslations = _translations[_currentLanguage];
    
    if (currentTranslations != null) {
      currentTranslations.forEach((key, value) {
        if (value.toLowerCase().contains(query.toLowerCase()) ||
            key.toLowerCase().contains(query.toLowerCase())) {
          results.add(key);
        }
      });
    }
    
    return results;
  }

  /// تصدير الترجمات
  Map<String, dynamic> exportTranslations() {
    return {
      'current_language': _currentLanguage,
      'translations': _translations,
      'exported_at': DateTime.now().toIso8601String(),
    };
  }

  /// استيراد الترجمات
  Future<void> importTranslations(Map<String, dynamic> data) async {
    try {
      if (data['translations'] is Map) {
        _translations = Map<String, Map<String, String>>.from(
          data['translations'].map(
            (key, value) => MapEntry(
              key,
              Map<String, String>.from(value),
            ),
          ),
        );
      }
      
      if (data['current_language'] is String &&
          supportedLanguages.contains(data['current_language'])) {
        await changeLanguage(data['current_language']);
      }
      
      debugPrint('✅ تم استيراد الترجمات بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في استيراد الترجمات: $e');
      throw Exception('فشل في استيراد الترجمات');
    }
  }

  /// إعادة تحميل الترجمات
  Future<void> reloadTranslations() async {
    try {
      await _loadTranslations();
      notifyListeners();
      debugPrint('🔄 تم إعادة تحميل الترجمات');
    } catch (e) {
      debugPrint('❌ خطأ في إعادة تحميل الترجمات: $e');
    }
  }

  /// الحصول على إحصائيات الترجمة
  Map<String, dynamic> getTranslationStats() {
    final stats = <String, dynamic>{};
    
    _translations.forEach((language, translations) {
      stats[language] = {
        'count': translations.length,
        'keys': translations.keys.toList(),
      };
    });
    
    return {
      'current_language': _currentLanguage,
      'supported_languages': supportedLanguages,
      'languages_stats': stats,
      'total_languages': _translations.length,
    };
  }

  @override
  void dispose() {
    _translations.clear();
    super.dispose();
  }
}
