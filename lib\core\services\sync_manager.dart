import 'package:flutter/material.dart';
import 'package:motorcycle_parts_shop/core/services/ai_services.dart';
import 'package:motorcycle_parts_shop/core/services/cart_service.dart';
import 'package:motorcycle_parts_shop/core/services/favorites_service.dart';
import 'package:motorcycle_parts_shop/core/services/local_storage_service.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// مدير المزامنة
/// يدير مزامنة البيانات بين التخزين المحلي وقاعدة البيانات
class SyncManager extends ChangeNotifier {
  final SupabaseClient _supabase;
  final CartService _cartService;
  final FavoritesService _favoritesService;
  final LocalStorageService _localStorageService;

  bool _isInitialized = false;
  bool _isSyncing = false;
  String? _error;
  String _status = 'idle';
  double _progress = 0.0;
  DateTime? _lastSuccessfulSync;

  SyncManager({
    required SupabaseClient supabase,
    required CartService cartService,
    required FavoritesService favoritesService,
    required LocalStorageService localStorageService,
  }) : _supabase = supabase,
       _cartService = cartService,
       _favoritesService = favoritesService,
       _localStorageService = localStorageService;

  bool get isInitialized => _isInitialized;
  bool get isSyncing => _isSyncing;
  String? get error => _error;
  String get status => _status;
  double get progress => _progress;
  DateTime? get lastSuccessfulSync => _lastSuccessfulSync;

 
  Future<void> initialize() async {
    if (_isInitialized) return;
    _setStatus('initializing');
    try {
      _isSyncing = true;
      notifyListeners();
      await _cartService.initialize();
      await _favoritesService.initialize();
      await syncAll();
      _setupRealtimeSubscriptions();
      _isInitialized = true;
      _setStatus('ready');
    } catch (e) {
      _error = e.toString();
      _setStatus('error');
      debugPrint('Error initializing SyncManager: $_error');
    } finally {
      _isSyncing = false;
      notifyListeners();
    }
  }

  /// مزامنة ثنائية الاتجاه (إلى ومن السيرفر)
  Future<void> syncAll() async {
    _setStatus('syncing');
    _progress = 0.0;
    notifyListeners();
    try {
      await syncToServer();
      _progress = 0.5;
      notifyListeners();
      await syncFromServer();
      _progress = 1.0;
      _lastSuccessfulSync = DateTime.now();
      _setStatus('success');
    } catch (e) {
      _error = e.toString();
      _setStatus('error');
      debugPrint('خطأ في مزامنة البيانات: $_error');
    } finally {
      _isSyncing = false;
      notifyListeners();
      Future.delayed(const Duration(seconds: 1), () {
        _progress = 0.0;
        notifyListeners();
      });
    }
  }

  /// مزامنة البيانات من الجهاز إلى السيرفر فقط
  Future<void> syncToServer() async {
    try {
      await _localStorageService.syncWithServer();
      debugPrint('تم مزامنة البيانات إلى السيرفر بنجاح');
    } catch (e) {
      _error = e.toString();
      debugPrint('خطأ في مزامنة البيانات إلى السيرفر: $_error');
      rethrow;
    }
  }

  /// مزامنة البيانات من السيرفر إلى الجهاز فقط
  Future<void> syncFromServer() async {
    try {
      await _localStorageService.syncFromServer();
      debugPrint('تم جلب البيانات من السيرفر بنجاح');
    } catch (e) {
      _error = e.toString();
      debugPrint('خطأ في جلب البيانات من السيرفر: $_error');
      rethrow;
    }
  }

  /// مزامنة البيانات يدويًا (ثنائية الاتجاه)
  Future<void> manualSync() async {
    if (_isSyncing || !_isInitialized) return;
    await syncAll();
  }

  /// مزامنة مخصصة (اختيار اتجاه المزامنة)
  Future<void> customSync({
    bool toServer = true,
    bool fromServer = true,
  }) async {
    if (_isSyncing || !_isInitialized) return;
    _setStatus('syncing');
    _progress = 0.0;
    notifyListeners();
    try {
      if (toServer) {
        await syncToServer();
        _progress = fromServer ? 0.5 : 1.0;
        notifyListeners();
      }
      if (fromServer) {
        await syncFromServer();
        _progress = 1.0;
        notifyListeners();
      }
      _lastSuccessfulSync = DateTime.now();
      _setStatus('success');
    } catch (e) {
      _error = e.toString();
      _setStatus('error');
    } finally {
      _isSyncing = false;
      notifyListeners();
      Future.delayed(const Duration(seconds: 1), () {
        _progress = 0.0;
        notifyListeners();
      });
    }
  }

  /// إعداد اشتراكات Realtime
  void _setupRealtimeSubscriptions() {
    _supabase
        .channel('data_changes')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: 'cart_items',
          callback: (_) => manualSync(),
        )
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: 'favorites',
          callback: (_) => manualSync(),
        )
        .subscribe();
  }

  /// تحديث حالة المزامنة
  void _setStatus(String newStatus) {
    _status = newStatus;
    notifyListeners();
  }
}
