
// نموذج بيانات الإعلانات للصفحة الرئيسية
class AdvertisementModel {
  final String id; // معرف الإعلان الفريد
  final String title; // عنوان الإعلان
  final String? description; // وصف الإعلان (اختياري)
  final String imageUrl; // رابط صورة الإعلان
  final String? targetUrl; // رابط الهدف عند النقر على الإعلان (اختياري)
  final bool isActive; // حالة نشاط الإعلان
  final DateTime startDate; // تاريخ بدء عرض الإعلان
  final DateTime? endDate; // تاريخ انتهاء عرض الإعلان (اختياري)
  final int displayOrder; // ترتيب عرض الإعلان
  final int clickCount; // عدد النقرات على الإعلان
  final int impressionCount; // عدد مرات ظهور الإعلان
  final String? campaignId; // معرف الحملة الإعلانية (اختياري)
  final Map<String, dynamic>?
  targetAudience; // معلومات الجمهور المستهدف (اختياري)
  final DateTime createdAt; // تاريخ إنشاء الإعلان
  final DateTime updatedAt; // تاريخ آخر تحديث للإعلان

  /// المُنشئ الرئيسي لنموذج الإعلان
  AdvertisementModel({
    required this.id, // المعرف مطلوب
    required this.title, // العنوان مطلوب
    this.description, // الوصف اختياري
    required this.imageUrl, // رابط الصورة مطلوب
    this.targetUrl, // رابط الهدف اختياري
    required this.isActive, // حالة النشاط مطلوبة
    required this.startDate, // تاريخ البدء مطلوب
    this.endDate, // تاريخ الانتهاء اختياري
    required this.displayOrder, // ترتيب العرض مطلوب
    required this.clickCount, // عدد النقرات مطلوب
    required this.impressionCount, // عدد مرات الظهور مطلوب
    this.campaignId, // معرف الحملة الإعلانية (اختياري)
    this.targetAudience, // معلومات الجمهور المستهدف (اختياري)
    required this.createdAt, // تاريخ الإنشاء مطلوب
    required this.updatedAt, // تاريخ التحديث مطلوب
  });

  // تحويل من JSON إلى كائن AdvertisementModel
  factory AdvertisementModel.fromJson(Map<String, dynamic> json) {
    return AdvertisementModel(
      id: json['id']?.toString() ?? '', // استخراج المعرف مع قيمة افتراضية
      title: json['title']?.toString() ?? '', // استخراج العنوان
      description: json['description']?.toString(), // استخراج الوصف إذا وجد
      imageUrl: json['image_url']?.toString() ?? '', // استخراج رابط الصورة
      targetUrl: json['target_url']?.toString(), // استخراج رابط الهدف إذا وجد
      isActive: json['is_active'] ?? false, // استخراج حالة النشاط
      startDate:
          json['start_date'] != null
              ? DateTime.parse(json['start_date'])
              : DateTime.now(), // تاريخ البدء
      endDate:
          json['end_date'] != null
              ? DateTime.parse(json['end_date'])
              : null, // تاريخ الانتهاء
      displayOrder: json['display_order'] ?? 0, // استخراج ترتيب العرض
      clickCount:
          json['click_count'] is int
              ? json['click_count']
              : int.tryParse(json['click_count']?.toString() ?? '0') ??
                  0, // عدد النقرات
      impressionCount:
          json['impression_count'] is int
              ? json['impression_count']
              : int.tryParse(json['impression_count']?.toString() ?? '0') ??
                  0, // عدد مرات الظهور
      campaignId: json['campaign_id']?.toString(), // معرف الحملة الإعلانية
      targetAudience:
          json['target_audience'] is Map
              ? Map<String, dynamic>.from(json['target_audience'])
              : null, // معلومات الجمهور المستهدف
      createdAt:
          json['created_at'] != null
              ? DateTime.parse(json['created_at'])
              : DateTime.now(), // تاريخ الإنشاء
      updatedAt:
          json['updated_at'] != null
              ? DateTime.parse(json['updated_at'])
              : DateTime.now(), // تاريخ التحديث
    );
  }

  // تحويل كائن AdvertisementModel إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'image_url': imageUrl,
      'target_url': targetUrl,
      'is_active': isActive,
      'start_date': startDate.toIso8601String(),
      'end_date': endDate?.toIso8601String(),
      'display_order': displayOrder,
      'click_count': clickCount,
      'impression_count': impressionCount,
      'campaign_id': campaignId,
      'target_audience': targetAudience,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  AdvertisementModel copyWith({
    String? id,
    String? title,
    String? description,
    String? imageUrl,
    String? targetUrl,
    bool? isActive,
    DateTime? startDate,
    DateTime? endDate,
    int? displayOrder,
    int? clickCount,
    int? impressionCount,
    String? campaignId,
    Map<String, dynamic>? targetAudience,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return AdvertisementModel(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      imageUrl: imageUrl ?? this.imageUrl,
      targetUrl: targetUrl ?? this.targetUrl,
      isActive: isActive ?? this.isActive,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      displayOrder: displayOrder ?? this.displayOrder,
      clickCount: clickCount ?? this.clickCount,
      impressionCount: impressionCount ?? this.impressionCount,
      campaignId: campaignId ?? this.campaignId,
      targetAudience: targetAudience ?? this.targetAudience,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  bool get isExpired {
    if (endDate == null) return false;
    return DateTime.now().isAfter(endDate!);
  }

  bool get isActiveNow {
    final now = DateTime.now();
    return isActive &&
        now.isAfter(startDate) &&
        (endDate == null || now.isBefore(endDate!));
  }

  // لا يمكن تعديل المتغيرات النهائية (final) مباشرة، لذا نستخدم copyWith
  AdvertisementModel incrementImpressions() {
    return copyWith(impressionCount: impressionCount + 1);
  }

  AdvertisementModel incrementClicks() {
    return copyWith(clickCount: clickCount + 1);
  }
}
