
/// واجهة موحدة لمقدمي الإحصائيات
abstract class StatisticsProvider {
  /// الحصول على إحصائيات مفصلة
  Map<String, dynamic> getDetailedStats();
  
  /// إعادة تعيين الإحصائيات
  void resetStatistics();
  
  /// الحصول على إحصائيات أساسية
  Map<String, dynamic> getBasicStats() {
    final detailed = getDetailedStats();
    return {
      'total_operations': detailed['total_operations'] ?? 0,
      'successful_operations': detailed['successful_operations'] ?? 0,
      'failed_operations': detailed['failed_operations'] ?? 0,
      'success_rate': detailed['success_rate'] ?? 0.0,
    };
  }
  
  /// التحقق من صحة الإحصائيات
  bool validateStats() {
    try {
      final stats = getDetailedStats();
      return stats.isNotEmpty;
    } catch (e) {
      return false;
    }
  }
}
