import 'package:flutter/material.dart';
import 'package:motorcycle_parts_shop/core/services/localization_service.dart';
import 'package:motorcycle_parts_shop/core/theme/app_theme.dart';
import 'package:provider/provider.dart';

class LanguageSettingsScreen extends StatefulWidget {
  const LanguageSettingsScreen({super.key});

  @override
  State<LanguageSettingsScreen> createState() => _LanguageSettingsScreenState();
}

class _LanguageSettingsScreenState extends State<LanguageSettingsScreen> {
  late String _selectedLanguage;

  @override
  void initState() {
    super.initState();
    _selectedLanguage =
        Provider.of<LocalizationService>(
          context,
          listen: false,
        ).currentLanguage;
  }

  @override
  Widget build(BuildContext context) {
    final localizationService = Provider.of<LocalizationService>(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizationService.translate('settings.language')),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              localizationService.translate('settings.select_language'),
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            _buildLanguageOption('العربية', 'ar', localizationService),
          
          ],
        ),
      ),
    );
  }

  Widget _buildLanguageOption(
    String languageName,
    String languageCode,
    LocalizationService localizationService,
  ) {
    final isSelected = _selectedLanguage == languageCode;

    return ListTile(
      title: Text(languageName),
      trailing:
          isSelected
              ? const Icon(Icons.check_circle, color: AppTheme.primaryColor)
              : null,
      onTap: () async {
        if (_selectedLanguage != languageCode) {
          setState(() {
            _selectedLanguage = languageCode;
          });
          await localizationService.changeLanguage(languageCode);
          if (mounted) {
            // Refresh the UI to apply the new language
            setState(() {});

            // Show a confirmation message
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  localizationService.translate('settings.language_changed'),
                ),
                backgroundColor: AppTheme.primaryColor,
              ),
            );
          }
        }
      },
    );
  }
}
