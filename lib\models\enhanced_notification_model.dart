import 'package:motorcycle_parts_shop/models/notification_model.dart';

class EnhancedNotificationModel extends NotificationModel {
  final String channel; // email, sms, push, in_app
  final Map<String, dynamic>? channelData; // بيانات إضافية خاصة بالقناة
  final bool isDelivered; // هل تم توصيل الإشعار
  final DateTime? deliveredAt; // وقت توصيل الإشعار
  final bool isActionable; // هل يمكن اتخاذ إجراء على الإشعار
  final String? actionType; // نوع الإجراء (مثل: فتح منتج، فتح طلب، إلخ)
  final String?
  actionData; // بيانات الإجراء (مثل: معرف المنتج، معرف الطلب، إلخ)
  final int priority; // أولوية الإشعار (1: منخفضة، 2: متوسطة، 3: عالية)
  final bool isArchived; // هل تم أرشفة الإشعار

  EnhancedNotificationModel({
    required super.id,
    required super.userId,
    super.templateId,
    required super.title,
    required super.body,
    required super.type,
    super.data,
    super.isRead = false,
    required super.createdAt,
    required this.channel,
    this.channelData,
    this.isDelivered = false,
    this.deliveredAt,
    this.isActionable = false,
    this.actionType,
    this.actionData,
    this.priority = 2,
    this.isArchived = false,
  });

  factory EnhancedNotificationModel.fromJson(Map<String, dynamic> json) {
    return EnhancedNotificationModel(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      title: json['title'] as String,
      body: json['body'] as String,
      type: json['type'] as String,
      isRead: json['is_read'] as bool? ?? false,
      createdAt: DateTime.parse(json['created_at'] as String),
      channel: json['channel'] as String,
      channelData: json['channel_data'] as Map<String, dynamic>?,
      templateId: json['template_id'] as String?,
      isDelivered: json['is_delivered'] as bool? ?? false,
      deliveredAt:
          json['delivered_at'] != null
              ? DateTime.parse(json['delivered_at'] as String)
              : null,
      isActionable: json['is_actionable'] as bool? ?? false,
      actionType: json['action_type'] as String?,
      actionData: json['action_data'] as String?,
      priority: json['priority'] as int? ?? 2,
      isArchived: json['is_archived'] as bool? ?? false,
    );
  }

  @override
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> baseJson = super.toJson();
    return {
      ...baseJson,
      'channel': channel,
      'channel_data': channelData,
      'is_delivered': isDelivered,
      'delivered_at': deliveredAt?.toIso8601String(),
      'is_actionable': isActionable,
      'action_type': actionType,
      'action_data': actionData,
      'priority': priority,
      'is_archived': isArchived,
    };
  }

  @override
  EnhancedNotificationModel copyWith({
    String? id,
    String? userId,
    String? templateId,
    String? title,
    String? body,
    String? type,
    Map<String, dynamic>? data,
    bool? isRead,
    DateTime? createdAt,
    String? channel,
    Map<String, dynamic>? channelData,
    bool? isDelivered,
    DateTime? deliveredAt,
    bool? isActionable,
    String? actionType,
    String? actionData,
    int? priority,
    bool? isArchived,
  }) {
    return EnhancedNotificationModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      templateId: templateId ?? this.templateId,
      title: title ?? this.title,
      body: body ?? this.body,
      type: type ?? this.type,
      data: data ?? this.data,
      isRead: isRead ?? this.isRead,
      createdAt: createdAt ?? this.createdAt,
      channel: channel ?? this.channel,
      channelData: channelData ?? this.channelData,
      isDelivered: isDelivered ?? this.isDelivered,
      deliveredAt: deliveredAt ?? this.deliveredAt,
      isActionable: isActionable ?? this.isActionable,
      actionType: actionType ?? this.actionType,
      actionData: actionData ?? this.actionData,
      priority: priority ?? this.priority,
      isArchived: isArchived ?? this.isArchived,
    );
  }

  // الحصول على اسم القناة بالعربية
  String get channelNameArabic {
    switch (channel) {
      case 'email':
        return 'بريد إلكتروني';
      case 'sms':
        return 'رسالة نصية';
      case 'push':
        return 'إشعار فوري';
      case 'in_app':
        return 'داخل التطبيق';
      default:
        return channel;
    }
  }

  // الحصول على اسم الأولوية بالعربية
  String get priorityNameArabic {
    switch (priority) {
      case 1:
        return 'منخفضة';
      case 2:
        return 'متوسطة';
      case 3:
        return 'عالية';
      default:
        return 'متوسطة';
    }
  }
}
