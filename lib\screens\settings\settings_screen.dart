import 'package:flutter/material.dart';
import 'package:motorcycle_parts_shop/core/services/connectivity_service.dart';
import 'package:motorcycle_parts_shop/core/services/theme_service.dart';
import 'package:motorcycle_parts_shop/core/theme/app_theme.dart';
import 'package:motorcycle_parts_shop/core/utils/responsive_helper.dart';
import 'package:motorcycle_parts_shop/screens/profile/notification_settings_screen.dart';
import 'package:motorcycle_parts_shop/screens/settings/accessibility_settings_screen.dart';
import 'package:motorcycle_parts_shop/screens/settings/account_info_screen.dart';
import 'package:motorcycle_parts_shop/screens/settings/app_info_screen.dart';
import 'package:motorcycle_parts_shop/screens/settings/language_settings_screen.dart';
import 'package:motorcycle_parts_shop/screens/settings/security_settings_screen.dart';
import 'package:provider/provider.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  @override
  void initState() {
    super.initState();
    // تهيئة الخدمات عند تحميل الشاشة إذا لزم الأمر
    final connectivityService = Provider.of<ConnectivityService>(
      context,
      listen: false,
    );
    connectivityService.initialize(); // تأكد من تهيئة خدمة الاتصال
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(80),
        child: Container(
          decoration: BoxDecoration(
            gradient: AppTheme.primaryGradient,
            boxShadow: AppTheme.cardShadow,
          ),
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Row(
                children: [
                  // زر الرجوع المحسن
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: IconButton(
                      icon: const Icon(
                        Icons.arrow_back_ios_rounded,
                        color: AppTheme.textLightColor,
                      ),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ),

                  const SizedBox(width: 16),

                  // أيقونة الإعدادات والعنوان
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      Icons.settings_rounded,
                      color: AppTheme.textLightColor,
                      size: 24,
                    ),
                  ),

                  const SizedBox(width: 12),

                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          'الإعدادات',
                          style: AppTheme.cardTitle.copyWith(
                            color: AppTheme.textLightColor,
                            fontSize: 18,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                        Text(
                          'تخصيص تجربتك',
                          style: AppTheme.cardSubtitle.copyWith(
                            color: AppTheme.textLightColor.withOpacity(0.8),
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
      body: ResponsiveBuilder(
        builder: (context, constraints) {
          final padding = ResponsiveHelper.getPadding(context);

          return ListView(
            padding: EdgeInsets.all(padding),
            children: [
              // Language settings
              _buildSectionHeader('اللغة', Icons.language_rounded),
              _buildSettingsCard([
                _buildSettingsTile(
                  icon: Icons.language_rounded,
                  title: 'لغة التطبيق',
                  subtitle: 'العربية',
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const LanguageSettingsScreen(),
                      ),
                    );
                  },
                ),
              ]),

              const SizedBox(height: 24),

              // Theme settings
              _buildSectionHeader('المظهر', Icons.palette_rounded),
              _buildSettingsCard([
                Consumer<ThemeService>(
                  builder:
                      (context, themeService, _) => _buildSettingsTile(
                        icon: Icons.brightness_6_rounded,
                        title: 'الوضع الداكن',
                        subtitle: 'تغيير مظهر التطبيق',
                        trailing: Switch(
                          value: themeService.isDarkMode(context),
                          activeColor: AppTheme.primaryColor,
                          onChanged: (bool value) {
                            themeService.setThemeMode(
                              value ? ThemeMode.dark : ThemeMode.light,
                            );
                          },
                        ),
                        onTap: () {
                          final currentMode = themeService.isDarkMode(context);
                          themeService.setThemeMode(
                            currentMode ? ThemeMode.light : ThemeMode.dark,
                          );
                        },
                      ),
                ),
                _buildDivider(),
                _buildSettingsTile(
                  icon: Icons.color_lens_rounded,
                  title: 'ألوان التطبيق',
                  subtitle: 'تخصيص الألوان والثيم',
                  onTap: () {
                    _showColorThemeDialog();
                  },
                ),
              ]),

              const SizedBox(height: 24),

              // Security settings
              _buildSectionHeader('الأمان'),
              ListTile(
                leading: const CircleAvatar(
                  backgroundColor: AppTheme.primaryColor,
                  child: Icon(Icons.lock, color: Colors.white),
                ),
                title: const Text('تغيير كلمة المرور'),
                trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                onTap: () {
                  Navigator.pushNamed(context, '/change-password');
                },
              ),
              const Divider(),

              // Offline mode settings
              _buildSectionHeader('وضع عدم الاتصال'),
              Consumer<ConnectivityService>(
                builder:
                    (context, connectivityService, _) => FutureBuilder<bool>(
                      future: connectivityService.checkConnectivity(),
                      builder: (context, snapshot) {
                        final isConnected = snapshot.data ?? false;
                        return ListTile(
                          leading: const CircleAvatar(
                            backgroundColor: AppTheme.primaryColor,
                            child: Icon(
                              Icons.offline_bolt,
                              color: Colors.white,
                            ),
                          ),
                          title: const Text('وضع عدم الاتصال'),
                          subtitle: Text(
                            isConnected
                                ? 'متصل بالإنترنت (${connectivityService.networkType.name})'
                                : 'غير متصل بالإنترنت - وضع عدم الاتصال مفعل',
                          ),
                          trailing: Icon(
                            isConnected ? Icons.wifi : Icons.wifi_off,
                            color: isConnected ? Colors.green : Colors.red,
                          ),
                          onTap: () {
                            // يمكن إضافة تفاصيل إضافية عن حالة الاتصال
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                  'جودة الاتصال: ${connectivityService.connectionQuality.name}',
                                ),
                              ),
                            );
                          },
                        );
                      },
                    ),
              ),
              const Divider(),

              // Account settings
              _buildSectionHeader('الحساب'),
              ListTile(
                leading: const CircleAvatar(
                  backgroundColor: AppTheme.primaryColor,
                  child: Icon(Icons.person, color: Colors.white),
                ),
                title: const Text('معلومات الحساب'),
                trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const AccountInfoScreen(),
                    ),
                  );
                },
              ),
              ListTile(
                leading: const CircleAvatar(
                  backgroundColor: AppTheme.primaryColor,
                  child: Icon(Icons.notifications, color: Colors.white),
                ),
                title: const Text('إعدادات الإشعارات'),
                trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const NotificationSettingsScreen(),
                    ),
                  );
                },
              ),
              ListTile(
                leading: const CircleAvatar(
                  backgroundColor: AppTheme.primaryColor,
                  child: Icon(Icons.security, color: Colors.white),
                ),
                title: const Text('الأمان والخصوصية'),
                trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const SecuritySettingsScreen(),
                    ),
                  );
                },
              ),
              const Divider(),

              // تم نقل إعدادات الخصوصية والتحليلات إلى صفحة الأمان والخصوصية

              // Advanced settings
              _buildSectionHeader('إعدادات متقدمة'),
              ListTile(
                leading: const CircleAvatar(
                  backgroundColor: AppTheme.primaryColor,
                  child: Icon(Icons.data_usage, color: Colors.white),
                ),
                title: const Text('تخزين البيانات'),
                subtitle: const Text('إدارة البيانات المخزنة محلياً'),
                trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                onTap: () {
                  // يمكن تطوير هذه الصفحة لاحقاً
                  ScaffoldMessenger.of(
                    context,
                  ).showSnackBar(const SnackBar(content: Text('قريباً...')));
                },
              ),
              ListTile(
                leading: const CircleAvatar(
                  backgroundColor: AppTheme.primaryColor,
                  child: Icon(Icons.accessibility, color: Colors.white),
                ),
                title: const Text('إمكانية الوصول'),
                subtitle: const Text('تخصيص إعدادات إمكانية الوصول'),
                trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const AccessibilitySettingsScreen(),
                    ),
                  );
                },
              ),
              const Divider(),

              // About section
              _buildSectionHeader('حول التطبيق'),
              ListTile(
                leading: const CircleAvatar(
                  backgroundColor: AppTheme.primaryColor,
                  child: Icon(Icons.info, color: Colors.white),
                ),
                title: const Text('معلومات التطبيق'),
                subtitle: const Text('الإصدار، الترخيص، والمعلومات القانونية'),
                trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const AppInfoScreen(),
                    ),
                  );
                },
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildSectionHeader(String title, [IconData? icon]) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: Row(
        children: [
          if (icon != null) ...[
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                gradient: AppTheme.primaryGradient,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: AppTheme.textLightColor, size: 20),
            ),
            const SizedBox(width: 12),
          ],
          Text(
            title,
            style: AppTheme.cardTitle.copyWith(
              fontSize: ResponsiveHelper.getFontSize(context, 18),
              fontWeight: FontWeight.w700,
              color: AppTheme.textPrimaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsCard(List<Widget> children) {
    return Container(
      decoration: BoxDecoration(
        gradient: AppTheme.cardGradient,
        borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
        boxShadow: AppTheme.cardShadow,
        border: Border.all(
          color: AppTheme.primaryColor.withOpacity(0.08),
          width: 1,
        ),
      ),
      child: Column(children: children),
    );
  }

  Widget _buildSettingsTile({
    required IconData icon,
    required String title,
    String? subtitle,
    Widget? trailing,
    VoidCallback? onTap,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // أيقونة محسنة
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(icon, color: AppTheme.primaryColor, size: 22),
              ),

              const SizedBox(width: 16),

              // النص
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: AppTheme.cardTitle.copyWith(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    if (subtitle != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        subtitle,
                        style: AppTheme.cardSubtitle.copyWith(fontSize: 14),
                      ),
                    ],
                  ],
                ),
              ),

              // العنصر الجانبي
              trailing ??
                  Icon(
                    Icons.arrow_forward_ios_rounded,
                    color: AppTheme.textTertiaryColor,
                    size: 16,
                  ),
            ],
          ),
        ),
      ),
    );
  }

  // دالة بناء الفاصل
  Widget _buildDivider() {
    return Container(
      height: 1,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.transparent,
            AppTheme.primaryColor.withOpacity(0.1),
            Colors.transparent,
          ],
        ),
      ),
    );
  }

  // دالة عرض حوار اختيار الألوان
  void _showColorThemeDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text('اختيار ألوان التطبيق', style: AppTheme.cardTitle),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'قريباً سيتم إضافة المزيد من خيارات الألوان',
                  style: AppTheme.cardSubtitle,
                ),
                const SizedBox(height: 20),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildColorOption(AppTheme.primaryColor, 'الأزرق'),
                    _buildColorOption(Colors.green, 'الأخضر'),
                    _buildColorOption(Colors.purple, 'البنفسجي'),
                    _buildColorOption(Colors.orange, 'البرتقالي'),
                  ],
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(
                  'إغلاق',
                  style: AppTheme.cardTitle.copyWith(
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
              ),
            ],
          ),
    );
  }

  Widget _buildColorOption(Color color, String name) {
    return Column(
      children: [
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
            border: Border.all(
              color: AppTheme.primaryColor.withOpacity(0.3),
              width: 2,
            ),
          ),
        ),
        const SizedBox(height: 8),
        Text(name, style: AppTheme.cardSubtitle.copyWith(fontSize: 12)),
      ],
    );
  }
}
