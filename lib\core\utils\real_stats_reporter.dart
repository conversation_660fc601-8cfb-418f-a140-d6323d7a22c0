import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// مُولد التقارير الإحصائية الحقيقية
class RealStatsReporter {
  static final SupabaseClient _supabase = Supabase.instance.client;
  static bool _isInitialized = false;
  static Timer? _periodicReportTimer;
  static final Map<String, dynamic> _cachedStats = {};

  /// تهيئة مُولد التقارير
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      debugPrint('📊 تهيئة مُولد التقارير الإحصائية...');
      
      // بدء التقارير الدورية
      _startPeriodicReporting();
      
      _isInitialized = true;
      debugPrint('✅ تم تهيئة مُولد التقارير بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة مُولد التقارير: $e');
    }
  }

  /// بدء التقارير الدورية
  static void _startPeriodicReporting() {
    // تحديث الإحصائيات كل 5 دقائق
    _periodicReportTimer = Timer.periodic(
      const Duration(minutes: 5),
      (timer) => _updateCachedStats(),
    );
  }

  /// تحديث الإحصائيات المخزنة مؤقتاً
  static Future<void> _updateCachedStats() async {
    try {
      final stats = await generateRealTimeStats();
      _cachedStats.addAll(stats);
      debugPrint('📊 تم تحديث الإحصائيات المخزنة مؤقتاً');
    } catch (e) {
      debugPrint('⚠️ خطأ في تحديث الإحصائيات: $e');
    }
  }

  /// توليد إحصائيات فورية
  static Future<Map<String, dynamic>> generateRealTimeStats() async {
    try {
      debugPrint('📈 توليد الإحصائيات الفورية...');
      
      final stats = <String, dynamic>{
        'timestamp': DateTime.now().toIso8601String(),
        'users': await _getUserStats(),
        'products': await _getProductStats(),
        'orders': await _getOrderStats(),
        'reviews': await _getReviewStats(),
        'categories': await _getCategoryStats(),
        'companies': await _getCompanyStats(),
        'performance': await _getPerformanceStats(),
      };

      debugPrint('✅ تم توليد الإحصائيات الفورية');
      return stats;
    } catch (e) {
      debugPrint('❌ خطأ في توليد الإحصائيات: $e');
      return {'error': e.toString()};
    }
  }

  /// إحصائيات المستخدمين
  static Future<Map<String, dynamic>> _getUserStats() async {
    try {
      // إجمالي المستخدمين
      final totalUsers = await _supabase
          .from('profiles')
          .select('id')
          .count();

      // المستخدمين النشطين (آخر 30 يوم)
      final activeUsers = await _supabase
          .from('profiles')
          .select('id')
          .gte('last_seen', DateTime.now().subtract(const Duration(days: 30)).toIso8601String())
          .count();

      // المستخدمين الجدد (آخر 7 أيام)
      final newUsers = await _supabase
          .from('profiles')
          .select('id')
          .gte('created_at', DateTime.now().subtract(const Duration(days: 7)).toIso8601String())
          .count();

      return {
        'total': totalUsers.count,
        'active_30_days': activeUsers.count,
        'new_7_days': newUsers.count,
      };
    } catch (e) {
      debugPrint('❌ خطأ في إحصائيات المستخدمين: $e');
      return {'error': e.toString()};
    }
  }

  /// إحصائيات المنتجات
  static Future<Map<String, dynamic>> _getProductStats() async {
    try {
      // إجمالي المنتجات
      final totalProducts = await _supabase
          .from('products')
          .select('id')
          .count();

      // المنتجات المتاحة
      final availableProducts = await _supabase
          .from('products')
          .select('id')
          .gt('stock_quantity', 0)
          .count();

      // المنتجات المميزة
      final featuredProducts = await _supabase
          .from('products')
          .select('id')
          .eq('is_featured', true)
          .count();

      // المنتجات الأكثر مشاهدة
      final topViewed = await _supabase
          .from('products')
          .select('id, name, view_count')
          .order('view_count', ascending: false)
          .limit(5);

      return {
        'total': totalProducts.count,
        'available': availableProducts.count,
        'featured': featuredProducts.count,
        'top_viewed': topViewed,
      };
    } catch (e) {
      debugPrint('❌ خطأ في إحصائيات المنتجات: $e');
      return {'error': e.toString()};
    }
  }

  /// إحصائيات الطلبات
  static Future<Map<String, dynamic>> _getOrderStats() async {
    try {
      // إجمالي الطلبات
      final totalOrders = await _supabase
          .from('orders')
          .select('id')
          .count();

      // الطلبات المكتملة
      final completedOrders = await _supabase
          .from('orders')
          .select('id')
          .eq('status', 'completed')
          .count();

      // الطلبات المعلقة
      final pendingOrders = await _supabase
          .from('orders')
          .select('id')
          .eq('status', 'pending')
          .count();

      // إجمالي المبيعات
      final totalSales = await _supabase
          .from('orders')
          .select('total_amount')
          .eq('status', 'completed');

      double totalRevenue = 0;
      for (final order in totalSales) {
        totalRevenue += (order['total_amount'] as num?)?.toDouble() ?? 0;
      }

      return {
        'total': totalOrders.count,
        'completed': completedOrders.count,
        'pending': pendingOrders.count,
        'total_revenue': totalRevenue,
      };
    } catch (e) {
      debugPrint('❌ خطأ في إحصائيات الطلبات: $e');
      return {'error': e.toString()};
    }
  }

  /// إحصائيات التقييمات
  static Future<Map<String, dynamic>> _getReviewStats() async {
    try {
      // إجمالي التقييمات
      final totalReviews = await _supabase
          .from('reviews')
          .select('id')
          .count();

      // متوسط التقييمات
      final avgRating = await _supabase
          .from('reviews')
          .select('rating');

      double averageRating = 0;
      if (avgRating.isNotEmpty) {
        double sum = 0;
        for (final review in avgRating) {
          sum += (review['rating'] as num?)?.toDouble() ?? 0;
        }
        averageRating = sum / avgRating.length;
      }

      return {
        'total': totalReviews.count,
        'average_rating': averageRating,
      };
    } catch (e) {
      debugPrint('❌ خطأ في إحصائيات التقييمات: $e');
      return {'error': e.toString()};
    }
  }

  /// إحصائيات الفئات
  static Future<Map<String, dynamic>> _getCategoryStats() async {
    try {
      // إجمالي الفئات
      final totalCategories = await _supabase
          .from('categories')
          .select('id')
          .count();

      // الفئات الأكثر شعبية
      final popularCategories = await _supabase
          .from('categories')
          .select('id, name, (products:products!category_id(count))')
          .order('products.count', ascending: false)
          .limit(5);

      return {
        'total': totalCategories.count,
        'popular': popularCategories,
      };
    } catch (e) {
      debugPrint('❌ خطأ في إحصائيات الفئات: $e');
      return {'error': e.toString()};
    }
  }

  /// إحصائيات الشركات
  static Future<Map<String, dynamic>> _getCompanyStats() async {
    try {
      // إجمالي الشركات
      final totalCompanies = await _supabase
          .from('companies')
          .select('id')
          .count();

      return {
        'total': totalCompanies.count,
      };
    } catch (e) {
      debugPrint('❌ خطأ في إحصائيات الشركات: $e');
      return {'error': e.toString()};
    }
  }

  /// إحصائيات الأداء
  static Future<Map<String, dynamic>> _getPerformanceStats() async {
    try {
      return {
        'cache_size': _cachedStats.length,
        'last_update': _cachedStats['timestamp'],
        'reporting_active': _periodicReportTimer?.isActive ?? false,
      };
    } catch (e) {
      debugPrint('❌ خطأ في إحصائيات الأداء: $e');
      return {'error': e.toString()};
    }
  }

  /// الحصول على الإحصائيات المخزنة مؤقتاً
  static Map<String, dynamic> getCachedStats() {
    return Map<String, dynamic>.from(_cachedStats);
  }

  /// توليد تقرير شامل
  static Future<Map<String, dynamic>> generateComprehensiveReport() async {
    try {
      debugPrint('📋 توليد التقرير الشامل...');
      
      final report = await generateRealTimeStats();
      
      // إضافة معلومات إضافية
      report['report_type'] = 'comprehensive';
      report['generated_by'] = 'RealStatsReporter';
      report['version'] = '1.0.0';
      
      debugPrint('✅ تم توليد التقرير الشامل');
      return report;
    } catch (e) {
      debugPrint('❌ خطأ في توليد التقرير الشامل: $e');
      return {'error': e.toString()};
    }
  }

  /// إيقاف التقارير الدورية
  static void dispose() {
    _periodicReportTimer?.cancel();
    _periodicReportTimer = null;
    _cachedStats.clear();
    _isInitialized = false;
    debugPrint('🗑️ تم إيقاف مُولد التقارير');
  }
}
