import 'package:flutter/material.dart';
import 'package:motorcycle_parts_shop/core/services/product_service.dart';
import 'package:motorcycle_parts_shop/core/theme/app_theme.dart';
import 'package:motorcycle_parts_shop/core/utils/responsive_helper.dart';
import 'package:motorcycle_parts_shop/core/widgets/loading_indicator.dart';
import 'package:motorcycle_parts_shop/models/category_model.dart';
import 'package:motorcycle_parts_shop/models/company_model.dart';
import 'package:motorcycle_parts_shop/screens/product/product_list_screen.dart';
import 'package:provider/provider.dart';

class AdvancedSearchScreen extends StatefulWidget {
  const AdvancedSearchScreen({super.key});

  @override
  State<AdvancedSearchScreen> createState() => _AdvancedSearchScreenState();
}

class _AdvancedSearchScreenState extends State<AdvancedSearchScreen> {
  final TextEditingController _searchController = TextEditingController();
  RangeValues _priceRange = const RangeValues(0, 10000);
  double _maxPriceValue = 10000;
  String? _selectedCategory;
  String? _selectedCompany;
  String? _selectedSortOption;
  bool _isLoading = true;
  List<CategoryModel> _categories = [];
  List<CompanyModel> _companies = [];

  final List<String> _sortOptions = [
    'السعر: من الأقل إلى الأعلى',
    'السعر: من الأعلى إلى الأقل',
    'التقييم: من الأعلى إلى الأقل',
    'الأحدث',
    'الأكثر مبيعاً',
  ];

  @override
  void initState() {
    super.initState();
    _loadInitialData();
  }

  Future<void> _loadInitialData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final productService = Provider.of<ProductService>(
        context,
        listen: false,
      );

      // جلب الفئات
      final categories = await productService.getCategories();

      // جلب الشركات المصنعة
      final companies = await productService.getCompanies();

      // جلب أعلى سعر للمنتجات لتحديد نطاق السعر
      final maxPrice = await productService.getMaxProductPrice();

      if (!mounted) return;
      setState(() {
        _categories = categories;
        _companies = companies;
        if (maxPrice > 0) {
          _maxPriceValue = maxPrice;
          _priceRange = RangeValues(0, maxPrice);
        }
        _isLoading = false;
      });
    } catch (e, stackTrace) {
      if (!mounted) return;
      setState(() {
        _isLoading = false;
      });
      debugPrint('Error loading initial data for advanced search: $e');
      debugPrint(stackTrace.toString());
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ أثناء تحميل البيانات: $e'),
          backgroundColor: AppTheme.errorColor,
        ),
      );
    }
  }

  void _applyFilters() {
    // إنشاء كائن معايير البحث
    final searchCriteria = {
      'query': _searchController.text,
      'minPrice': _priceRange.start,
      'maxPrice': _priceRange.end,
      'categoryId': _selectedCategory,
      'companyId': _selectedCompany,
      'sortOption': _selectedSortOption,
    };

    // الانتقال إلى شاشة قائمة المنتجات مع تمرير معايير البحث
    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => ProductListScreen(
              title: 'نتائج البحث المتقدم',
              searchCriteria: searchCriteria,
            ),
      ),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(
          ResponsiveHelper.isMobile(context) ? 70 : 80,
        ),
        child: Container(
          decoration: BoxDecoration(
            gradient: AppTheme.primaryGradient,
            boxShadow: AppTheme.cardShadow,
          ),
          child: SafeArea(
            child: Padding(
              padding: EdgeInsets.symmetric(
                horizontal: ResponsiveHelper.getPadding(
                  context,
                  mobile: 12,
                  tablet: 16,
                  desktop: 20,
                ),
                vertical: 8,
              ),
              child: Row(
                children: [
                  // زر الرجوع المحسن
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: IconButton(
                      icon: const Icon(
                        Icons.arrow_back_ios_rounded,
                        color: AppTheme.textLightColor,
                      ),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ),

                  const SizedBox(width: 16),

                  // أيقونة البحث والعنوان
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      Icons.search_rounded,
                      color: AppTheme.textLightColor,
                      size: 24,
                    ),
                  ),

                  const SizedBox(width: 12),

                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          'البحث المتقدم',
                          style: AppTheme.cardTitle.copyWith(
                            color: AppTheme.textLightColor,
                            fontSize: 18,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                        Text(
                          'ابحث بدقة أكبر',
                          style: AppTheme.cardSubtitle.copyWith(
                            color: AppTheme.textLightColor.withOpacity(0.8),
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
      body:
          _isLoading
              ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const LoadingIndicator(),
                    const SizedBox(height: 16),
                    Text(
                      'جاري تحميل بيانات البحث...',
                      style: AppTheme.cardSubtitle,
                    ),
                  ],
                ),
              )
              : ResponsiveBuilder(
                builder: (context, constraints) {
                  final padding = ResponsiveHelper.getPadding(context);

                  return SingleChildScrollView(
                    padding: EdgeInsets.all(padding),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // حقل البحث المحسن
                        _buildSearchField(),
                        const SizedBox(height: 32),

                        // نطاق السعر المحسن
                        _buildPriceRangeSection(),
                        const SizedBox(height: 32),

                        // اختيار الفئة
                        const Text(
                          'الفئة',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        DropdownButtonFormField<String>(
                          decoration: InputDecoration(
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          hint: const Text('اختر الفئة'),
                          value: _selectedCategory,
                          items: [
                            const DropdownMenuItem<String>(
                              value: null,
                              child: Text('جميع الفئات'),
                            ),
                            ..._categories.map((category) {
                              return DropdownMenuItem<String>(
                                value: category.id,
                                child: Text(category.name),
                              );
                            }),
                          ],
                          onChanged: (value) {
                            setState(() {
                              _selectedCategory = value;
                            });
                          },
                        ),
                        const SizedBox(height: 24),

                        // اختيار الشركة المصنعة
                        const Text(
                          'الشركة المصنعة',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        DropdownButtonFormField<String>(
                          decoration: InputDecoration(
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          hint: const Text('اختر الشركة المصنعة'),
                          value: _selectedCompany,
                          items: [
                            const DropdownMenuItem<String>(
                              value: null,
                              child: Text('جميع الشركات'),
                            ),
                            ..._companies.map((company) {
                              return DropdownMenuItem<String>(
                                value: company.id,
                                child: Text(company.name),
                              );
                            }),
                          ],
                          onChanged: (value) {
                            setState(() {
                              _selectedCompany = value;
                            });
                          },
                        ),
                        const SizedBox(height: 24),

                        // خيارات الترتيب
                        const Text(
                          'ترتيب حسب',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        DropdownButtonFormField<String>(
                          decoration: InputDecoration(
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          hint: const Text('اختر طريقة الترتيب'),
                          value: _selectedSortOption,
                          items:
                              _sortOptions.map((option) {
                                return DropdownMenuItem<String>(
                                  value: option,
                                  child: Text(option),
                                );
                              }).toList(),
                          onChanged: (value) {
                            setState(() {
                              _selectedSortOption = value;
                            });
                          },
                        ),
                        const SizedBox(height: 32),

                        // زر تطبيق الفلاتر
                        SizedBox(
                          width: double.infinity,
                          height: 50,
                          child: ElevatedButton(
                            onPressed: _applyFilters,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppTheme.primaryColor,
                              foregroundColor: Colors.white,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            child: const Text(
                              'تطبيق الفلاتر',
                              style: TextStyle(fontSize: 16),
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
    );
  }

  // 🎨 دوال بناء العناصر المتطورة
  Widget _buildSearchField() {
    return ResponsiveBuilder(
      builder: (context, constraints) {
        final padding = ResponsiveHelper.getPadding(
          context,
          mobile: 12,
          tablet: 16,
          desktop: 20,
        );
        final fontSize = ResponsiveHelper.getFontSize(context, 16);

        return Container(
          decoration: BoxDecoration(
            gradient: AppTheme.cardGradient,
            borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
            boxShadow: AppTheme.cardShadow,
            border: Border.all(
              color: AppTheme.primaryColor.withOpacity(0.1),
              width: 1,
            ),
          ),
          child: TextField(
            controller: _searchController,
            style: AppTheme.cardTitle.copyWith(fontSize: fontSize),
            decoration: InputDecoration(
              hintText: 'ابحث عن منتج، قطعة غيار، أو علامة تجارية...',
              hintStyle: AppTheme.cardSubtitle.copyWith(fontSize: fontSize - 2),
              prefixIcon: Container(
                margin: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  gradient: AppTheme.primaryGradient,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.search_rounded,
                  color: AppTheme.textLightColor,
                  size: 20,
                ),
              ),
              border: InputBorder.none,
              contentPadding: EdgeInsets.symmetric(
                horizontal: padding,
                vertical: padding,
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildPriceRangeSection() {
    return ResponsiveBuilder(
      builder: (context, constraints) {
        final padding = ResponsiveHelper.getPadding(context);

        return Container(
          padding: EdgeInsets.all(padding),
          decoration: BoxDecoration(
            gradient: AppTheme.cardGradient,
            borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
            boxShadow: AppTheme.cardShadow,
            border: Border.all(
              color: AppTheme.primaryColor.withOpacity(0.1),
              width: 1,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      gradient: AppTheme.primaryGradient,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.attach_money_rounded,
                      color: AppTheme.textLightColor,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'نطاق السعر',
                    style: AppTheme.cardTitle.copyWith(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 20),

              SliderTheme(
                data: SliderTheme.of(context).copyWith(
                  activeTrackColor: AppTheme.primaryColor,
                  inactiveTrackColor: AppTheme.primaryColor.withOpacity(0.2),
                  thumbColor: AppTheme.primaryColor,
                  overlayColor: AppTheme.primaryColor.withOpacity(0.2),
                  valueIndicatorColor: AppTheme.primaryColor,
                  rangeThumbShape: const RoundRangeSliderThumbShape(
                    enabledThumbRadius: 12,
                  ),
                ),
                child: RangeSlider(
                  values: _priceRange,
                  min: 0,
                  max: _maxPriceValue,
                  divisions: 100,
                  labels: RangeLabels(
                    '${_priceRange.start.round()} ج.م',
                    '${_priceRange.end.round()} ج.م',
                  ),
                  onChanged: (RangeValues values) {
                    setState(() {
                      _priceRange = values;
                    });
                  },
                ),
              ),

              const SizedBox(height: 16),

              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      '${_priceRange.start.round()} ج.م',
                      style: AppTheme.cardTitle.copyWith(
                        color: AppTheme.primaryColor,
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      '${_priceRange.end.round()} ج.م',
                      style: AppTheme.cardTitle.copyWith(
                        color: AppTheme.primaryColor,
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }
}
