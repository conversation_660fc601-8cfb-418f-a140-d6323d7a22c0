import 'package:flutter/material.dart';
import 'package:motorcycle_parts_shop/core/widgets/animation_helper.dart';

/// A wrapper class for EnhancedAnimationHelper to maintain backward compatibility
/// This class delegates all animation methods to EnhancedAnimationHelper
enum SlideDirection { up, down, left, right }

class AnimationHelper {
  /// Creates a fade transition animation
  static Widget fadeTransition({
    required Animation<double> animation,
    required Widget child,
    Offset? offset,
  }) {
    return EnhancedAnimationHelper.fadeIn(
      animation: animation,
      child: child,
      offset: offset,
    );
  }

  /// Creates a scale transition animation
  static Widget scaleTransition({
    required Animation<double> animation,
    required Widget child,
    Alignment alignment = Alignment.center,
  }) {
    return EnhancedAnimationHelper.scale(
      animation: animation,
      child: child,
      alignment: alignment,
    );
  }

  /// Creates a slide transition animation
  static Widget slideTransition({
    required Animation<double> animation,
    required Widget child,
    SlideDirection direction = SlideDirection.up,
    double distance = 0.2,
    Curve curve = Curves.easeOut,
  }) {
    // Convert SlideDirection to Offset
    Offset beginOffset;
    switch (direction) {
      case SlideDirection.up:
        beginOffset = Offset(0, distance);
        break;
      case SlideDirection.down:
        beginOffset = Offset(0, -distance);
        break;
      case SlideDirection.left:
        beginOffset = Offset(distance, 0);
        break;
      case SlideDirection.right:
        beginOffset = Offset(-distance, 0);
        break;
    }

    return EnhancedAnimationHelper.slide(
      animation: animation,
      child: child,
      beginOffset: beginOffset,
    );
  }

  /// Creates a staggered animation
  static Animation<double> staggeredAnimation({
    required AnimationController controller,
    required int position,
    Duration staggerDuration = const Duration(milliseconds: 50),
  }) {
    return EnhancedAnimationHelper.staggeredAnimation(
      controller: controller,
      position: position,
      staggerDuration: staggerDuration,
    );
  }

  /// Creates a ripple effect
  static Widget rippleEffect({
    required Widget child,
    required VoidCallback onTap,
    Color? splashColor,
    Color? highlightColor,
    BorderRadius borderRadius = BorderRadius.zero,
  }) {
    return EnhancedAnimationHelper.rippleEffect(
      child: child,
      onTap: onTap,
      splashColor: splashColor,
      highlightColor: highlightColor,
      borderRadius: borderRadius,
    );
  }

  /// Creates a hero with fade animation
  static Widget heroWithFade({
    required String tag,
    required Widget child,
  }) {
    return EnhancedAnimationHelper.heroWithFade(
      tag: tag,
      child: child,
    );
  }
}