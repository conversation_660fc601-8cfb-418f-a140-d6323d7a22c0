-- ===================================================================
-- نظام الإشعارات المتقدم
-- متجر قطع غيار الدراجات النارية - Supabase Database
-- ===================================================================

-- ===================================================================
-- 1. جدول قوالب الإشعارات
-- ===================================================================
CREATE TABLE IF NOT EXISTS notification_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL UNIQUE,
    title_template TEXT NOT NULL,
    body_template TEXT NOT NULL,
    type VARCHAR(50) NOT NULL,
    variables JSONB DEFAULT '[]'::jsonb, -- متغيرات القالب
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_notification_templates_name ON notification_templates(name);
CREATE INDEX IF NOT EXISTS idx_notification_templates_type ON notification_templates(type);
CREATE INDEX IF NOT EXISTS idx_notification_templates_is_active ON notification_templates(is_active);

-- ===================================================================
-- 2. جدول الإشعارات
-- ===================================================================
CREATE TABLE IF NOT EXISTS notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    template_id UUID REFERENCES notification_templates(id) ON DELETE SET NULL,
    title VARCHAR(255) NOT NULL,
    body TEXT NOT NULL,
    type VARCHAR(50) NOT NULL,
    data JSONB DEFAULT '{}'::jsonb,
    is_read BOOLEAN DEFAULT false,
    read_at TIMESTAMPTZ,
    sent_at TIMESTAMPTZ,
    delivery_status VARCHAR(20) DEFAULT 'pending' CHECK (delivery_status IN ('pending', 'sent', 'delivered', 'failed')),
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_type ON notifications(type);
CREATE INDEX IF NOT EXISTS idx_notifications_is_read ON notifications(is_read);
CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON notifications(created_at);

-- ===================================================================
-- 3. جدول إحصائيات الإشعارات للمستخدمين
-- ===================================================================
CREATE TABLE IF NOT EXISTS user_notification_stats (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    total_sent INTEGER DEFAULT 0,
    total_delivered INTEGER DEFAULT 0,
    total_read INTEGER DEFAULT 0,
    total_clicked INTEGER DEFAULT 0,
    last_notification_sent TIMESTAMPTZ,
    last_notification_read TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(user_id)
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_user_notification_stats_user_id ON user_notification_stats(user_id);

-- ===================================================================
-- 4. جدول تحليلات الإشعارات
-- ===================================================================
CREATE TABLE IF NOT EXISTS notification_analytics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    notification_id UUID NOT NULL REFERENCES notifications(id) ON DELETE CASCADE,
    event_type VARCHAR(20) NOT NULL CHECK (event_type IN ('sent', 'delivered', 'opened', 'clicked', 'dismissed')),
    event_timestamp TIMESTAMPTZ DEFAULT NOW(),
    device_info JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_notification_analytics_notification_id ON notification_analytics(notification_id);
CREATE INDEX IF NOT EXISTS idx_notification_analytics_event_type ON notification_analytics(event_type);
CREATE INDEX IF NOT EXISTS idx_notification_analytics_event_timestamp ON notification_analytics(event_timestamp);

-- ===================================================================
-- الدوال المساعدة
-- ===================================================================

-- دالة إرسال إشعار
CREATE OR REPLACE FUNCTION send_notification(
    p_user_id UUID,
    p_template_name VARCHAR,
    p_variables JSONB DEFAULT '{}'::jsonb,
    p_custom_title VARCHAR DEFAULT NULL,
    p_custom_body TEXT DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    v_template RECORD;
    v_title TEXT;
    v_body TEXT;
    v_notification_id UUID;
    v_variable_key TEXT;
    v_variable_value TEXT;
BEGIN
    -- الحصول على قالب الإشعار
    SELECT * INTO v_template
    FROM notification_templates
    WHERE name = p_template_name AND is_active = true;

    IF NOT FOUND THEN
        RAISE EXCEPTION 'قالب الإشعار غير موجود: %', p_template_name;
    END IF;

    -- استخدام العنوان والنص المخصص أو من القالب
    v_title := COALESCE(p_custom_title, v_template.title_template);
    v_body := COALESCE(p_custom_body, v_template.body_template);

    -- استبدال المتغيرات في العنوان والنص
    FOR v_variable_key IN SELECT jsonb_object_keys(p_variables)
    LOOP
        v_variable_value := p_variables->>v_variable_key;
        v_title := REPLACE(v_title, '{{' || v_variable_key || '}}', v_variable_value);
        v_body := REPLACE(v_body, '{{' || v_variable_key || '}}', v_variable_value);
    END LOOP;

    -- إنشاء الإشعار
    INSERT INTO notifications (
        user_id, template_id, title, body, type, data
    ) VALUES (
        p_user_id, v_template.id, v_title, v_body, v_template.type, p_variables
    ) RETURNING id INTO v_notification_id;

    -- تحديث إحصائيات المستخدم
    INSERT INTO user_notification_stats (user_id, total_sent, last_notification_sent)
    VALUES (p_user_id, 1, NOW())
    ON CONFLICT (user_id) DO UPDATE SET
        total_sent = user_notification_stats.total_sent + 1,
        last_notification_sent = NOW(),
        updated_at = NOW();

    -- تحديث عداد الإشعارات في الملف الشخصي (إذا كانت الحقول موجودة)
    BEGIN
        UPDATE profiles SET
            total_notifications_count = COALESCE(total_notifications_count, 0) + 1,
            unread_notifications_count = COALESCE(unread_notifications_count, 0) + 1
        WHERE id = p_user_id;
    EXCEPTION
        WHEN undefined_column THEN
            -- تجاهل الخطأ إذا لم تكن الحقول موجودة
            NULL;
    END;

    RETURN v_notification_id;
END;
$$;

-- دالة تحديث حالة قراءة الإشعار
CREATE OR REPLACE FUNCTION mark_notification_read(
    p_notification_id UUID,
    p_user_id UUID
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    v_row_count INTEGER;
BEGIN
    -- تحديث حالة الإشعار
    UPDATE notifications
    SET is_read = true, read_at = NOW()
    WHERE id = p_notification_id AND user_id = p_user_id AND is_read = false;

    GET DIAGNOSTICS v_row_count = ROW_COUNT;

    IF v_row_count > 0 THEN
        -- تحديث عداد الإشعارات غير المقروءة
        BEGIN
            UPDATE profiles SET
                unread_notifications_count = GREATEST(COALESCE(unread_notifications_count, 0) - 1, 0),
                last_notification_check = NOW()
            WHERE id = p_user_id;
        EXCEPTION
            WHEN undefined_column THEN
                -- تجاهل الخطأ إذا لم تكن الحقول موجودة
                NULL;
        END;

        -- تحديث إحصائيات المستخدم
        UPDATE user_notification_stats SET
            total_read = total_read + 1,
            last_notification_read = NOW(),
            updated_at = NOW()
        WHERE user_id = p_user_id;

        -- تسجيل حدث القراءة
        INSERT INTO notification_analytics (notification_id, event_type)
        VALUES (p_notification_id, 'opened');

        RETURN true;
    END IF;

    RETURN false;
END;
$$;

-- دالة تنظيف الإشعارات القديمة
CREATE OR REPLACE FUNCTION cleanup_old_notifications()
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    v_deleted_count INTEGER;
BEGIN
    -- حذف الإشعارات المقروءة الأقدم من 6 أشهر
    DELETE FROM notifications
    WHERE is_read = true AND created_at < NOW() - INTERVAL '6 months';

    GET DIAGNOSTICS v_deleted_count = ROW_COUNT;

    -- حذف تحليلات الإشعارات الأقدم من سنة
    DELETE FROM notification_analytics
    WHERE created_at < NOW() - INTERVAL '1 year';

    RETURN v_deleted_count;
END;
$$;

-- ===================================================================
-- المحفزات
-- ===================================================================

-- محفزات تحديث الطوابع الزمنية
CREATE TRIGGER update_notification_templates_updated_at
    BEFORE UPDATE ON notification_templates
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_notification_stats_updated_at
    BEFORE UPDATE ON user_notification_stats
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- ===================================================================
-- سياسات الأمان (RLS)
-- ===================================================================

-- تفعيل RLS
ALTER TABLE notification_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_notification_stats ENABLE ROW LEVEL SECURITY;
ALTER TABLE notification_analytics ENABLE ROW LEVEL SECURITY;

-- سياسات لقوالب الإشعارات
CREATE POLICY "Admins can manage notification templates" ON notification_templates FOR ALL USING (
    EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND profile_type = 'admin')
);

-- سياسات للإشعارات
CREATE POLICY "Users can view own notifications" ON notifications FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can update own notifications" ON notifications FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Admins can manage all notifications" ON notifications FOR ALL USING (
    EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND profile_type = 'admin')
);

-- سياسات لإحصائيات الإشعارات
CREATE POLICY "Users can view own notification stats" ON user_notification_stats FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Admins can view all notification stats" ON user_notification_stats FOR SELECT USING (
    EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND profile_type = 'admin')
);

-- سياسات لتحليلات الإشعارات
CREATE POLICY "Admins can view notification analytics" ON notification_analytics FOR SELECT USING (
    EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND profile_type = 'admin')
);

-- ===================================================================
-- البيانات الأولية - قوالب الإشعارات
-- ===================================================================

INSERT INTO notification_templates (name, title_template, body_template, type, variables, is_active) VALUES
    ('order_confirmed', 'تم تأكيد طلبك', 'تم تأكيد طلبك رقم {{order_number}} بنجاح. سيتم التواصل معك قريباً.', 'order', '["order_number", "total_amount"]', true),
    ('order_shipped', 'تم شحن طلبك', 'طلبك رقم {{order_number}} في الطريق إليك. رقم التتبع: {{tracking_number}}', 'order', '["order_number", "tracking_number"]', true),
    ('order_delivered', 'تم توصيل طلبك', 'تم توصيل طلبك رقم {{order_number}} بنجاح. نشكرك لثقتك بنا.', 'order', '["order_number"]', true),
    ('new_product', 'منتج جديد متاح', 'منتج جديد {{product_name}} متاح الآن في متجرنا بسعر {{price}} جنيه.', 'product', '["product_name", "price"]', true),
    ('price_drop', 'انخفاض في السعر', 'انخفض سعر {{product_name}} من {{old_price}} إلى {{new_price}} جنيه!', 'promotion', '["product_name", "old_price", "new_price"]', true),
    ('welcome', 'مرحباً بك', 'مرحباً {{user_name}}! نرحب بك في متجر قطع غيار الدراجات النارية.', 'welcome', '["user_name"]', true)
ON CONFLICT (name) DO NOTHING;

-- ===================================================================
-- رسالة نجاح
-- ===================================================================

DO $$
BEGIN
    RAISE NOTICE '✅ تم إنشاء نظام الإشعارات المتقدم بنجاح!';
    RAISE NOTICE '📋 جدول قوالب الإشعارات (notification_templates)';
    RAISE NOTICE '🔔 جدول الإشعارات (notifications)';
    RAISE NOTICE '📊 جدول إحصائيات الإشعارات (user_notification_stats)';
    RAISE NOTICE '📈 جدول تحليلات الإشعارات (notification_analytics)';
    RAISE NOTICE '⚡ تم إنشاء الدوال المساعدة';
    RAISE NOTICE '🔒 تم تطبيق سياسات الأمان';
    RAISE NOTICE '📝 تم إدراج قوالب الإشعارات الأساسية';
END $$;
