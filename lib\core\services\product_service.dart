import '../../models/category_model.dart';
import '../../models/company_model.dart';
import '../../models/product_model.dart';
import '../utils/common_operations.dart';
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// خدمة المنتجات لجلب وإدارة بيانات المنتجات مع دعم التحميل الكسول والتخزين المؤقت
class ProductService {
  final SupabaseClient _supabase;
  bool _isInitialized = false;
  static const Duration _cacheDuration = Duration(minutes: 5);
  final Map<String, dynamic> _cache = {};
  static const int _maxCacheSize = 100; // الحد الأقصى لعناصر التخزين
  static const int defaultPageSize = 10;

  ProductService(this._supabase) {
    _initialize();
  }

  bool get isInitialized => _isInitialized;

  /// تهيئة الخدمة مع التحقق من الاتصال بـ Supabase
  Future<void> _initialize() async {
    if (_isInitialized) return;

    try {
      await _checkConnection();
      _isInitialized = true;
      debugPrint('تم تهيئة خدمة المنتجات بنجاح');
    } catch (e) {
      debugPrint('خطأ في تهيئة خدمة المنتجات: $e');
      // السماح بالعمل دون اتصال مع تسجيل الخطأ
    }
  }

  /// التحقق من الاتصال بقاعدة البيانات
  Future<void> _checkConnection() async {
    try {
      await _supabase
          .from('products')
          .select('id')
          .limit(1)
          .timeout(const Duration(seconds: 10));
    } catch (e) {
      debugPrint('خطأ في الاتصال بخادم Supabase: $e');
      _isInitialized = true; // السماح بالعمل دون اتصال
    }
  }

  /// جلب البيانات من التخزين المؤقت مع تحديث إحصائيات الوصول
  T? _getFromCache<T>(String key) {
    final cachedData = _cache[key];
    if (cachedData != null &&
        cachedData['timestamp'] != null &&
        DateTime.now().difference(cachedData['timestamp']) < _cacheDuration) {
      // تحديث إحصائيات الوصول
      cachedData['accessCount'] = (cachedData['accessCount'] as int? ?? 0) + 1;
      cachedData['lastAccess'] = DateTime.now();

      return cachedData['data'] as T;
    } else if (cachedData != null) {
      // إزالة البيانات المنتهية الصلاحية
      _cache.remove(key);
    }
    return null;
  }

  /// إضافة البيانات إلى التخزين المؤقت مع تحسينات متقدمة
  void _addToCache(String key, dynamic data) {
    // تنظيف الذاكرة المؤقتة المنتهية الصلاحية
    _cleanExpiredCache();

    if (_cache.length >= _maxCacheSize) {
      _evictLeastRecentlyUsed(); // إزالة العنصر الأقل استخداماً
    }

    _cache[key] = {
      'data': data,
      'timestamp': DateTime.now(),
      'accessCount': 1,
      'lastAccess': DateTime.now(),
      'size': _estimateDataSize(data),
    };
  }

  /// تقدير حجم البيانات
  int _estimateDataSize(dynamic data) {
    if (data is List) {
      return data.length * 100; // تقدير تقريبي
    } else if (data is Map) {
      return data.length * 50;
    } else if (data is String) {
      return data.length;
    }
    return 100; // حجم افتراضي
  }

  /// تنظيف الذاكرة المؤقتة المنتهية الصلاحية
  void _cleanExpiredCache() {
    final now = DateTime.now();
    final expiredKeys = <String>[];

    for (final entry in _cache.entries) {
      final timestamp = entry.value['timestamp'] as DateTime;
      if (now.difference(timestamp) >= _cacheDuration) {
        expiredKeys.add(entry.key);
      }
    }

    for (final key in expiredKeys) {
      _cache.remove(key);
    }

    if (expiredKeys.isNotEmpty) {
      debugPrint(
        'تم تنظيف ${expiredKeys.length} عنصر منتهي الصلاحية من الذاكرة المؤقتة',
      );
    }
  }

  /// إزالة العناصر الأقل استخداماً من الذاكرة المؤقتة
  void _evictLeastRecentlyUsed() {
    if (_cache.isEmpty) return;

    String? lruKey;
    DateTime? oldestAccess;
    int lowestAccessCount = 999999;

    for (final entry in _cache.entries) {
      final lastAccess = entry.value['lastAccess'] as DateTime;
      final accessCount = entry.value['accessCount'] as int;

      // أولوية للعناصر الأقل استخداماً ثم الأقدم وصولاً
      if (accessCount < lowestAccessCount ||
          (accessCount == lowestAccessCount &&
              (oldestAccess == null || lastAccess.isBefore(oldestAccess)))) {
        oldestAccess = lastAccess;
        lowestAccessCount = accessCount;
        lruKey = entry.key;
      }
    }

    if (lruKey != null) {
      _cache.remove(lruKey);
      debugPrint('تم إزالة $lruKey من الذاكرة المؤقتة (LRU)');
    }
  }

  /// جلب عدد المنتجات الإجمالي
  Future<int> getProductsCount({String? category}) async {
    if (!_isInitialized) await _initialize();

    try {
      final cacheKey = 'products_count_$category';
      final cachedCount = _getFromCache<int>(cacheKey);
      if (cachedCount != null) {
        return cachedCount;
      }

      var queryBuilder = _supabase.from('products').select('*');

      if (category != null && category.isNotEmpty) {
        queryBuilder = queryBuilder.eq('category_id', category);
      }

      final List<dynamic> response = await queryBuilder;
      final int count = response.length;
      _addToCache(cacheKey, count);
      return count;
    } catch (e) {
      debugPrint('خطأ في جلب عدد المنتجات: $e');
      throw Exception('فشل جلب عدد المنتجات: $e');
    }
  }

  /// جلب المنتجات مع دعم ترقيم الصفحات
  Future<List<ProductModel>> getProducts({
    required int page,
    int pageSize = defaultPageSize,
    String? category,
    String sortBy = 'created_at',
    bool ascending = false,
  }) async {
    if (!_isInitialized) await _initialize();

    return await CommonOperations.executeWithErrorHandling(
      () async {
        final cacheKey =
            'products_${page}_${pageSize}_${category}_${sortBy}_$ascending';
        final cachedProducts = _getFromCache<List<ProductModel>>(cacheKey);
        if (cachedProducts != null) {
          return cachedProducts;
        }

        final int startRange = page * pageSize;
        final int endRange = startRange + pageSize - 1;

        var query = _supabase
            .from('products')
            .select(
              'id, name, description, price, category_id, company_id, created_at, view_count, sales_count, is_featured, stock_quantity, image_urls, specifications, average_rating, review_count',
            );

        if (category != null && category.isNotEmpty) {
          query = query.eq('category_id', category);
        }

        final response = await query
            .order(sortBy, ascending: ascending)
            .range(startRange, endRange)
            .timeout(const Duration(seconds: 10));

        final products =
            response.map((data) => ProductModel.fromJson(data)).toList();

        // التحقق من صحة البيانات
        final validProducts = <ProductModel>[];
        for (final product in products) {
          final validation = product.validateProduct();
          if (validation.isValid) {
            validProducts.add(product);
          } else {
            debugPrint(
              'منتج غير صالح: ${product.id} - ${validation.errorMessage}',
            );
          }
        }

        _addToCache(cacheKey, validProducts);
        return validProducts;
      },
      operationName: 'getProducts',
      maxRetries: 3,
      retryDelay: const Duration(seconds: 2),
      fallbackValue: <ProductModel>[],
    );
  }

  /// جلب المنتجات الشائعة
  Future<List<ProductModel>> getPopularProducts({int limit = 10}) async {
    if (!_isInitialized) await _initialize();

    try {
      final cacheKey = 'popular_products_$limit';
      final cachedProducts = _getFromCache<List<ProductModel>>(cacheKey);
      if (cachedProducts != null) {
        return cachedProducts;
      }

      final response = await _supabase
          .from('products')
          .select(
            'id, sku, name, description, brand, price, discount_price, original_price, category_id, company_id, created_at, view_count, sales_count, is_featured, is_best_selling, is_available, is_on_sale, stock_quantity, min_stock_level, weight, dimensions, image_urls, specifications, average_rating, review_count, meta_title, meta_description, tags',
          )
          .order('view_count', ascending: false)
          .limit(limit)
          .timeout(const Duration(seconds: 10));

      final products =
          response.map((data) => ProductModel.fromJson(data)).toList();
      _addToCache(cacheKey, products);
      return products;
    } catch (e) {
      debugPrint('خطأ في جلب المنتجات الشائعة: $e');
      throw Exception('فشل جلب المنتجات الشائعة: $e');
    }
  }

  /// جلب المنتجات المميزة
  Future<List<ProductModel>> getFeaturedProducts({
    required int page,
    int pageSize = defaultPageSize,
  }) async {
    if (!_isInitialized) await _initialize();

    try {
      final cacheKey = 'featured_${page}_$pageSize';
      final cachedProducts = _getFromCache<List<ProductModel>>(cacheKey);
      if (cachedProducts != null) {
        return cachedProducts;
      }

      final int startRange = page * pageSize;
      final int endRange = startRange + pageSize - 1;

      final response = await _supabase
          .from('products')
          .select(
            'id, sku, name, description, brand, price, discount_price, original_price, category_id, company_id, created_at, view_count, sales_count, is_featured, is_best_selling, is_available, is_on_sale, stock_quantity, min_stock_level, weight, dimensions, image_urls, specifications, average_rating, review_count, meta_title, meta_description, tags',
          )
          .eq('is_featured', true)
          .order('created_at', ascending: false)
          .range(startRange, endRange)
          .timeout(const Duration(seconds: 10));

      final products =
          response.map((data) => ProductModel.fromJson(data)).toList();
      _addToCache(cacheKey, products);
      return products;
    } catch (e) {
      debugPrint('خطأ في جلب المنتجات المميزة: $e');
      throw Exception('فشل جلب المنتجات المميزة: $e');
    }
  }

  /// جلب المنتجات الأكثر مبيعًا
  Future<List<ProductModel>> getBestSellingProducts({
    required int page,
    int pageSize = defaultPageSize,
  }) async {
    if (!_isInitialized) await _initialize();

    try {
      final cacheKey = 'bestselling_${page}_$pageSize';
      final cachedProducts = _getFromCache<List<ProductModel>>(cacheKey);
      if (cachedProducts != null) {
        return cachedProducts;
      }

      final int startRange = page * pageSize;
      final int endRange = startRange + pageSize - 1;

      final response = await _supabase
          .from('products')
          .select(
            'id, sku, name, description, brand, price, discount_price, original_price, category_id, company_id, created_at, view_count, sales_count, is_featured, is_best_selling, is_available, is_on_sale, stock_quantity, min_stock_level, weight, dimensions, image_urls, specifications, average_rating, review_count, meta_title, meta_description, tags',
          )
          .order('sales_count', ascending: false)
          .range(startRange, endRange)
          .timeout(const Duration(seconds: 10));

      final products =
          response.map((data) => ProductModel.fromJson(data)).toList();
      _addToCache(cacheKey, products);
      return products;
    } catch (e) {
      debugPrint('خطأ في جلب المنتجات الأكثر مبيعًا: $e');
      throw Exception('فشل جلب المنتجات الأكثر مبيعًا: $e');
    }
  }

  /// البحث عن المنتجات
  Future<List<ProductModel>> searchProducts({
    required String searchQuery,
    required int page,
    int pageSize = defaultPageSize,
  }) async {
    if (!_isInitialized) await _initialize();

    try {
      final cacheKey = 'search_${searchQuery}_${page}_$pageSize';
      final cachedProducts = _getFromCache<List<ProductModel>>(cacheKey);
      if (cachedProducts != null) {
        return cachedProducts;
      }

      final int startRange = page * pageSize;
      final int endRange = startRange + pageSize - 1;

      final response = await _supabase
          .from('products')
          .select(
            'id, name, description, price, category_id, company_id, created_at, view_count, sales_count, is_featured, stock_quantity, image_urls, specifications, average_rating, review_count',
          )
          .or('name.ilike.%$searchQuery%,description.ilike.%$searchQuery%')
          .order('created_at', ascending: false)
          .range(startRange, endRange)
          .timeout(const Duration(seconds: 10));

      final products =
          response.map((data) => ProductModel.fromJson(data)).toList();
      _addToCache(cacheKey, products);
      return products;
    } catch (e) {
      debugPrint('خطأ في البحث عن المنتجات: $e');
      throw Exception('فشل البحث عن المنتجات: $e');
    }
  }

  /// جلب الفئات
  Future<List<CategoryModel>> getCategories() async {
    if (!_isInitialized) await _initialize();

    try {
      final cachedCategories = _getFromCache<List<CategoryModel>>('categories');
      if (cachedCategories != null) {
        return cachedCategories;
      }

      final response = await _supabase
          .from('categories')
          .select('id, name, description')
          .order('name')
          .timeout(const Duration(seconds: 10));

      final categories =
          response.map((data) => CategoryModel.fromJson(data)).toList();
      _addToCache('categories', categories);
      return categories;
    } catch (e) {
      debugPrint('خطأ في جلب الفئات: $e');
      throw Exception('فشل جلب الفئات: $e');
    }
  }

  /// جلب الشركات
  Future<List<CompanyModel>> getCompanies() async {
    if (!_isInitialized) await _initialize();

    try {
      final cachedCompanies = _getFromCache<List<CompanyModel>>('companies');
      if (cachedCompanies != null) {
        return cachedCompanies;
      }

      final response = await _supabase
          .from('companies')
          .select('id, name, description')
          .order('name')
          .timeout(const Duration(seconds: 10));

      final companies =
          response.map((data) => CompanyModel.fromJson(data)).toList();
      _addToCache('companies', companies);
      return companies;
    } catch (e) {
      debugPrint('خطأ في جلب الشركات: $e');
      throw Exception('فشل جلب الشركات: $e');
    }
  }

  /// التحقق من حالة التفضيل
  Future<bool> isFavorite(String productId) async {
    if (!_isInitialized) await _initialize();

    try {
      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) {
        return false;
      }

      final response = await _supabase
          .from('wishlists')
          .select('id')
          .eq('user_id', userId)
          .eq('product_id', productId)
          .maybeSingle()
          .timeout(const Duration(seconds: 5));

      return response != null;
    } catch (e) {
      debugPrint('خطأ في التحقق من حالة تفضيل المنتج $productId: $e');
      return false;
    }
  }

  /// تبديل حالة التفضيل
  Future<bool> toggleFavoriteStatus(
    String productId,
    bool isCurrentlyFavorite,
  ) async {
    if (!_isInitialized) await _initialize();

    try {
      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('المستخدم غير مسجل دخوله.');
      }

      if (isCurrentlyFavorite) {
        await _supabase
            .from('wishlists')
            .delete()
            .match({'user_id': userId, 'product_id': productId})
            .timeout(const Duration(seconds: 5));
        debugPrint(
          'تمت إزالة المنتج $productId من قائمة الرغبات للمستخدم $userId',
        );
        return false;
      } else {
        await _supabase
            .from('wishlists')
            .insert({'user_id': userId, 'product_id': productId})
            .timeout(const Duration(seconds: 5));
        debugPrint(
          'تمت إضافة المنتج $productId إلى قائمة الرغبات للمستخدم $userId',
        );
        return true;
      }
    } catch (e) {
      debugPrint('خطأ في تبديل حالة تفضيل المنتج $productId: $e');
      throw Exception('فشل تبديل حالة التفضيل: $e');
    }
  }

  /// جلب تفاصيل المنتج بناءً على معرفه.
  Future<ProductModel?> getProductDetails(String productId) async {
    if (!_isInitialized) await _initialize();

    try {
      final query = _supabase
          .from('products')
          .select('*, categories(*), companies(*)')
          .eq('id', productId);

      final response = await query.maybeSingle().timeout(
        const Duration(seconds: 10),
      );
      return response != null ? ProductModel.fromJson(response) : null;
    } catch (e) {
      debugPrint('خطأ في جلب تفاصيل المنتج: $e');
      throw Exception('فشل جلب تفاصيل المنتج: $e');
    }
  }

  /// جلب أعلى سعر للمنتجات
  Future<double> getMaxProductPrice() async {
    if (!_isInitialized) await _initialize();

    try {
      final cacheKey = 'max_product_price';
      final cachedPrice = _getFromCache<double>(cacheKey);
      if (cachedPrice != null) {
        return cachedPrice;
      }

      final response = await _supabase
          .from('products')
          .select('price')
          .order('price', ascending: false)
          .limit(1)
          .maybeSingle()
          .timeout(const Duration(seconds: 5));

      if (response == null || response['price'] == null) {
        throw Exception('لا توجد منتجات أو أسعار متاحة');
      }

      final maxPrice = (response['price'] as num).toDouble();
      _addToCache(cacheKey, maxPrice);
      return maxPrice;
    } catch (e) {
      debugPrint('خطأ في جلب أعلى سعر للمنتجات: $e');
      throw Exception('فشل جلب أعلى سعر للمنتجات: $e');
    }
  }

  /// جلب منتج بواسطة المعرف
  Future<ProductModel?> getProductById(String productId) async {
    if (!_isInitialized) await _initialize();

    try {
      final cacheKey = 'product_$productId';
      final cachedProduct = _getFromCache<ProductModel>(cacheKey);
      if (cachedProduct != null) {
        return cachedProduct;
      }

      final response = await _supabase
          .from('products')
          .select(
            'id, name, description, price, category_id, company_id, created_at, view_count, sales_count, is_featured, stock_quantity, image_urls, specifications, average_rating, review_count',
          )
          .eq('id', productId)
          .maybeSingle()
          .timeout(const Duration(seconds: 5));

      if (response == null) {
        throw Exception('المنتج غير موجود: $productId');
      }

      final product = ProductModel.fromJson(response);
      _addToCache(cacheKey, product);
      return product;
    } catch (e) {
      debugPrint('خطأ في جلب المنتج بواسطة المعرف $productId: $e');
      throw Exception('فشل جلب المنتج: $e');
    }
  }

  /// جلب المنتجات بواسطة معرف الفئة
  Future<List<ProductModel>> getProductsByCategory({
    required String categoryId,
    int limit = defaultPageSize,
    int page = 0,
    String sortBy = 'created_at',
    bool ascending = false,
  }) async {
    if (!_isInitialized) await _initialize();

    try {
      final cacheKey =
          'products_category_${categoryId}_${page}_${limit}_${sortBy}_$ascending';
      final cachedProducts = _getFromCache<List<ProductModel>>(cacheKey);
      if (cachedProducts != null) {
        return cachedProducts;
      }

      final int startRange = page * limit;
      final int endRange = startRange + limit - 1;

      final response = await _supabase
          .from('products')
          .select(
            'id, name, description, price, category_id, company_id, created_at, view_count, sales_count, is_featured, stock_quantity, image_urls, specifications, average_rating, review_count',
          )
          .eq('category_id', categoryId)
          .order(sortBy, ascending: ascending)
          .range(startRange, endRange)
          .timeout(const Duration(seconds: 10));

      final products =
          response.map((data) => ProductModel.fromJson(data)).toList();
      _addToCache(cacheKey, products);
      return products;
    } catch (e) {
      debugPrint('خطأ في جلب المنتجات بواسطة الفئة $categoryId: $e');
      throw Exception('فشل جلب المنتجات بواسطة الفئة: $e');
    }
  }

  /// جلب المنتجات المشابهة
  Future<List<ProductModel>> getSimilarProducts({
    required String productId,
    String? categoryId,
    String? brand,
    int limit = defaultPageSize,
  }) async {
    if (!_isInitialized) await _initialize();

    try {
      final cacheKey =
          'similar_products_${productId}_${categoryId}_${brand}_$limit';
      final cachedProducts = _getFromCache<List<ProductModel>>(cacheKey);
      if (cachedProducts != null) {
        return cachedProducts;
      }

      var query = _supabase
          .from('products')
          .select(
            'id, name, description, price, category_id, company_id, created_at, view_count, sales_count, is_featured, stock_quantity, image_urls, specifications, average_rating, review_count',
          )
          .neq('id', productId);

      if (categoryId != null && categoryId.isNotEmpty) {
        query = query.eq('category_id', categoryId);
      }

      if (brand != null && brand.isNotEmpty) {
        query = query.eq(
          'company_id',
          brand,
        ); // Assuming brand is mapped to company_id
      }

      final response = await query
          .order('view_count', ascending: false)
          .limit(limit)
          .timeout(const Duration(seconds: 10));

      final products =
          response.map((data) => ProductModel.fromJson(data)).toList();
      _addToCache(cacheKey, products);
      return products;
    } catch (e) {
      debugPrint('خطأ في جلب المنتجات المشابهة للمنتج $productId: $e');
      throw Exception('فشل جلب المنتجات المشابهة: $e');
    }
  }

  /// جلب المنتجات بواسطة قائمة من المعرفات
  Future<List<ProductModel>> getProductsByIds(List<String> productIds) async {
    if (!_isInitialized) await _initialize();
    if (productIds.isEmpty) return [];

    try {
      final cacheKey = 'products_ids_${productIds.join('_')}';
      final cachedProducts = _getFromCache<List<ProductModel>>(cacheKey);
      if (cachedProducts != null) {
        return cachedProducts;
      }

      final response = await _supabase
          .from('products')
          .select(
            'id, name, description, price, category_id, company_id, created_at, view_count, sales_count, is_featured, stock_quantity, image_urls, specifications, average_rating, review_count',
          )
          .inFilter('id', productIds)
          .timeout(const Duration(seconds: 10));

      final products =
          response.map((data) => ProductModel.fromJson(data)).toList();
      _addToCache(cacheKey, products);
      return products;
    } catch (e) {
      debugPrint('خطأ في جلب المنتجات بواسطة المعرفات: $e');
      throw Exception('فشل جلب المنتجات بواسطة المعرفات: $e');
    }
  }

  /// جلب قائمة الشركات المتاحة
  Future<List<String>> getAvailableCompanies() async {
    if (!_isInitialized) await _initialize();

    try {
      final cacheKey = 'available_companies';
      final cachedCompanies = _getFromCache<List<String>>(cacheKey);
      if (cachedCompanies != null) {
        return cachedCompanies;
      }

      final response = await _supabase
          .from('products')
          .select('company')
          .eq('is_available', true)
          .order('company');

      final companies = <String>{};
      for (final item in response) {
        final company = item['company'] as String?;
        if (company != null && company.isNotEmpty) {
          companies.add(company);
        }
      }

      final companiesList = companies.toList();
      _addToCache(cacheKey, companiesList);
      return companiesList;
    } catch (e) {
      debugPrint('خطأ في جلب الشركات: $e');
      throw Exception('فشل في جلب قائمة الشركات: $e');
    }
  }

  /// جلب المنتجات مع الترتيب والتصفية
  Future<List<ProductModel>> getSortedProducts({
    required String sortBy,
    required bool ascending,
    String? categoryId,
    String? company,
    int? limit,
  }) async {
    if (!_isInitialized) await _initialize();

    try {
      final cacheKey =
          'sorted_products_${sortBy}_${ascending}_${categoryId}_${company}_$limit';
      final cachedProducts = _getFromCache<List<ProductModel>>(cacheKey);
      if (cachedProducts != null) {
        return cachedProducts;
      }

      var query = _supabase
          .from('products')
          .select(
            'id, name, description, price, category_id, company_id, created_at, view_count, sales_count, is_featured, stock_quantity, image_urls, specifications, average_rating, review_count',
          )
          .eq('is_available', true);

      if (categoryId != null) {
        query = query.eq('category_id', categoryId);
      }

      if (company != null && company.isNotEmpty) {
        query = query.eq('company', company);
      }

      var orderedQuery = query.order(sortBy, ascending: ascending);

      if (limit != null) {
        orderedQuery = orderedQuery.limit(limit);
      }

      final response = await orderedQuery.timeout(const Duration(seconds: 10));
      final products =
          response
              .map<ProductModel>((json) => ProductModel.fromJson(json))
              .toList();

      _addToCache(cacheKey, products);
      return products;
    } catch (e) {
      debugPrint('خطأ في جلب المنتجات المرتبة: $e');
      throw Exception('فشل في جلب المنتجات: $e');
    }
  }

  /// جلب المنتجات حسب الشركة
  Future<List<ProductModel>> getProductsByCompany(String company) async {
    if (!_isInitialized) await _initialize();

    try {
      final cacheKey = 'products_by_company_$company';
      final cachedProducts = _getFromCache<List<ProductModel>>(cacheKey);
      if (cachedProducts != null) {
        return cachedProducts;
      }

      final response = await _supabase
          .from('products')
          .select(
            'id, name, description, price, category_id, company_id, created_at, view_count, sales_count, is_featured, stock_quantity, image_urls, specifications, average_rating, review_count',
          )
          .eq('company_id', company)
          .eq('is_available', true)
          .order('name')
          .timeout(const Duration(seconds: 10));

      final products =
          response
              .map<ProductModel>((json) => ProductModel.fromJson(json))
              .toList();

      _addToCache(cacheKey, products);
      return products;
    } catch (e) {
      debugPrint('خطأ في جلب منتجات الشركة: $e');
      throw Exception('فشل في جلب منتجات الشركة: $e');
    }
  }

  /// البحث في المنتجات (محسن)
  Future<List<Map<String, dynamic>>> searchProductsExtended(
    String keyword,
  ) async {
    if (!_isInitialized) await _initialize();

    if (keyword.isEmpty) {
      return [];
    }

    try {
      final cacheKey = 'search_extended_$keyword';
      final cachedResults = _getFromCache<List<Map<String, dynamic>>>(cacheKey);
      if (cachedResults != null) {
        return cachedResults;
      }

      final response = await _supabase
          .from('products')
          .select()
          .or(
            'name.ilike.%$keyword%,description.ilike.%$keyword%,company.ilike.%$keyword%',
          )
          .eq('is_available', true)
          .order('name')
          .limit(50)
          .timeout(const Duration(seconds: 10));

      final results = List<Map<String, dynamic>>.from(response);
      _addToCache(cacheKey, results);
      return results;
    } catch (e) {
      debugPrint('خطأ في البحث: $e');
      throw Exception('فشل في البحث: $e');
    }
  }

  /// جلب المنتجات الجديدة
  Future<List<ProductModel>> getNewProducts({
    int page = 0,
    int pageSize = 20,
  }) async {
    if (!_isInitialized) await _initialize();

    try {
      final cacheKey = 'new_products_${page}_$pageSize';
      final cachedProducts = _getFromCache<List<ProductModel>>(cacheKey);
      if (cachedProducts != null) {
        return cachedProducts;
      }

      final int startRange = page * pageSize;
      final int endRange = startRange + pageSize - 1;

      final response = await _supabase
          .from('products')
          .select(
            'id, sku, name, description, brand, price, discount_price, original_price, category_id, company_id, created_at, view_count, sales_count, is_featured, is_best_selling, is_available, is_on_sale, is_new, new_until, stock_quantity, min_stock_level, weight, dimensions, image_urls, specifications, average_rating, review_count, meta_title, meta_description, tags',
          )
          .eq('is_available', true)
          .eq('is_new', true)
          .gte('new_until', DateTime.now().toIso8601String())
          .order('created_at', ascending: false)
          .range(startRange, endRange)
          .timeout(const Duration(seconds: 10));

      final products =
          response
              .map<ProductModel>((json) => ProductModel.fromJson(json))
              .toList();

      _addToCache(cacheKey, products);
      return products;
    } catch (e) {
      debugPrint('خطأ في جلب المنتجات الجديدة: $e');
      throw Exception('فشل جلب المنتجات الجديدة: $e');
    }
  }

  /// عدد المنتجات الجديدة
  Future<int> getNewProductsCount() async {
    if (!_isInitialized) await _initialize();

    try {
      final cacheKey = 'new_products_count';
      final cachedCount = _getFromCache<int>(cacheKey);
      if (cachedCount != null) {
        return cachedCount;
      }

      final response = await _supabase
          .from('products')
          .select('id')
          .eq('is_available', true)
          .eq('is_new', true)
          .gte('new_until', DateTime.now().toIso8601String())
          .timeout(const Duration(seconds: 5));

      final count = response.length;
      _addToCache(cacheKey, count);
      return count;
    } catch (e) {
      debugPrint('خطأ في جلب عدد المنتجات الجديدة: $e');
      return 0;
    }
  }

  /// مسح التخزين المؤقت
  void clearCache() {
    final cacheSize = _cache.length;
    _cache.clear();
    debugPrint('تم مسح الذاكرة المؤقتة ($cacheSize عنصر)');
  }

  /// الحصول على إحصائيات الذاكرة المؤقتة
  Map<String, dynamic> getCacheStats() {
    int totalSize = 0;
    int totalAccessCount = 0;
    DateTime? oldestTimestamp;
    DateTime? newestTimestamp;

    for (final entry in _cache.values) {
      totalSize += entry['size'] as int? ?? 0;
      totalAccessCount += entry['accessCount'] as int? ?? 0;

      final timestamp = entry['timestamp'] as DateTime;
      if (oldestTimestamp == null || timestamp.isBefore(oldestTimestamp)) {
        oldestTimestamp = timestamp;
      }
      if (newestTimestamp == null || timestamp.isAfter(newestTimestamp)) {
        newestTimestamp = timestamp;
      }
    }

    return {
      'itemCount': _cache.length,
      'maxSize': _maxCacheSize,
      'totalSize': totalSize,
      'totalAccessCount': totalAccessCount,
      'oldestItem': oldestTimestamp?.toIso8601String(),
      'newestItem': newestTimestamp?.toIso8601String(),
      'hitRate':
          totalAccessCount > 0
              ? (totalAccessCount / _cache.length).toStringAsFixed(2)
              : '0.00',
    };
  }

  /// طباعة إحصائيات الذاكرة المؤقتة
  void printCacheStats() {
    final stats = getCacheStats();
    debugPrint('📊 إحصائيات الذاكرة المؤقتة:');
    debugPrint('   العناصر: ${stats['itemCount']}/${stats['maxSize']}');
    debugPrint('   الحجم التقديري: ${stats['totalSize']} بايت');
    debugPrint('   إجمالي الوصول: ${stats['totalAccessCount']}');
    debugPrint('   معدل النجاح: ${stats['hitRate']}');
  }
}
