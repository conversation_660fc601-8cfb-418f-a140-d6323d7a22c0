import 'package:flutter/material.dart';
import 'package:motorcycle_parts_shop/core/constants/egypt_governorates.dart';
import 'package:motorcycle_parts_shop/core/services/auth_supabase_service.dart';
import 'package:motorcycle_parts_shop/core/theme/app_theme.dart';
import 'package:motorcycle_parts_shop/core/widgets/common_form_fields.dart';
import 'package:motorcycle_parts_shop/screens/home/<USER>';
import 'package:provider/provider.dart';

class CompleteGoogleProfileScreen extends StatefulWidget {
  final String email;
  final String name;
  final String userId;

  const CompleteGoogleProfileScreen({
    super.key,
    required this.email,
    required this.name,
    required this.userId,
  });

  @override
  State<CompleteGoogleProfileScreen> createState() =>
      _CompleteGoogleProfileScreenState();
}

class _CompleteGoogleProfileScreenState
    extends State<CompleteGoogleProfileScreen> {
  final _formKey = GlobalKey<FormState>();
  final _addressController = TextEditingController();
  final _phoneController = TextEditingController();

  DateTime? _selectedBirthDate;
  String? _selectedGovernorate;
  bool _isLoading = false;
  List<String> _governorates = [];
  bool _governoratesLoading = true;

  @override
  void initState() {
    super.initState();
    _loadGovernorates();
  }

  Future<void> _loadGovernorates() async {
    try {
      final governorates = await EgyptGovernorates.getGovernoratesList();
      if (mounted) {
        setState(() {
          _governorates = governorates;
          _governoratesLoading = false;
        });
      }
    } catch (e) {
      debugPrint('خطأ في تحميل المحافظات: $e');
      if (mounted) {
        setState(() {
          _governoratesLoading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _addressController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  Future<void> _completeProfile() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() => _isLoading = true);

    try {
      final authService = Provider.of<AuthSupabaseService>(
        context,
        listen: false,
      );

      // تحديث الملف الشخصي بالبيانات الإضافية
      final profileData = {
        'id': widget.userId,
        'email': widget.email,
        'name': widget.name,
        'profile_type': 'customer',
        'phone': _phoneController.text.trim(),
        'address': _addressController.text.trim(),
        'governorate': _selectedGovernorate,
        'birth_date': _selectedBirthDate?.toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
        'raw_user_meta_data': {
          'provider': 'google',
          'name': widget.name,
          'email': widget.email,
          'phone': _phoneController.text.trim(),
          'address': _addressController.text.trim(),
          'governorate': _selectedGovernorate,
          'birth_date': _selectedBirthDate?.toIso8601String(),
        },
      };

      // تحديث الملف الشخصي في قاعدة البيانات
      await authService.client
          .from('profiles')
          .update(profileData)
          .eq('id', widget.userId);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إكمال الملف الشخصي بنجاح'),
            backgroundColor: Colors.green,
          ),
        );

        // الانتقال للشاشة الرئيسية
        Navigator.of(context).pushAndRemoveUntil(
          MaterialPageRoute(builder: (context) => const HomeScreen()),
          (route) => false,
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في إكمال الملف الشخصي: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text('إكمال الملف الشخصي'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24.0),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // رسالة ترحيبية
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 10,
                        offset: const Offset(0, 5),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      Icon(
                        Icons.person_add,
                        size: 60,
                        color: AppTheme.primaryColor,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'مرحباً ${widget.name}!',
                        style: Theme.of(
                          context,
                        ).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: AppTheme.primaryColor,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'يرجى إكمال بياناتك لإنهاء إنشاء الحساب',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[600],
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 32),

                // البيانات المطلوبة
                Container(
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 10,
                        offset: const Offset(0, 5),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      _buildGovernorateDropdown(),
                      const SizedBox(height: 20),
                      _buildAddressField(),
                      const SizedBox(height: 20),
                      _buildBirthDateField(),
                      const SizedBox(height: 20),
                      _buildPhoneField(),
                    ],
                  ),
                ),
                const SizedBox(height: 32),

                // زر إكمال التسجيل
                Container(
                  height: 56,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16),
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        AppTheme.primaryColor,
                        AppTheme.primaryColor.withOpacity(0.8),
                      ],
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: AppTheme.primaryColor.withOpacity(0.4),
                        blurRadius: 15,
                        offset: const Offset(0, 8),
                      ),
                    ],
                  ),
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _completeProfile,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.transparent,
                      shadowColor: Colors.transparent,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                    ),
                    child:
                        _isLoading
                            ? const CircularProgressIndicator(
                              color: Colors.white,
                            )
                            : const Text(
                              'إكمال التسجيل',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildGovernorateDropdown() {
    return DropdownButtonFormField<String>(
      value: _selectedGovernorate,
      decoration: InputDecoration(
        labelText: 'المحافظة *',
        prefixIcon: Icon(Icons.apartment, color: AppTheme.primaryColor),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey[300]!),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: AppTheme.primaryColor, width: 2),
        ),
      ),
      items:
          _governoratesLoading
              ? [DropdownMenuItem(value: null, child: Text('جاري التحميل...'))]
              : _governorates
                  .map(
                    (governorate) => DropdownMenuItem(
                      value: governorate,
                      child: Text(governorate),
                    ),
                  )
                  .toList(),
      onChanged: (value) => setState(() => _selectedGovernorate = value),
      validator: (value) => value == null ? 'الرجاء اختيار المحافظة' : null,
    );
  }

  Widget _buildAddressField() {
    return TextFormField(
      controller: _addressController,
      decoration: InputDecoration(
        labelText: 'العنوان التفصيلي *',
        prefixIcon: Icon(Icons.home_outlined, color: AppTheme.primaryColor),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey[300]!),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: AppTheme.primaryColor, width: 2),
        ),
      ),
      maxLines: 2,
      validator:
          (value) => value?.isEmpty ?? true ? 'الرجاء إدخال العنوان' : null,
    );
  }

  Widget _buildBirthDateField() {
    return InkWell(
      onTap: () async {
        final DateTime? pickedDate = await showDatePicker(
          context: context,
          initialDate: _selectedBirthDate ?? DateTime(2000),
          firstDate: DateTime(1920),
          lastDate: DateTime.now(),
          locale: const Locale('ar'),
        );
        if (pickedDate != null) {
          setState(() => _selectedBirthDate = pickedDate);
        }
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey[300]!),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Icon(Icons.calendar_today, color: AppTheme.primaryColor),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                _selectedBirthDate != null
                    ? '${_selectedBirthDate!.day}/${_selectedBirthDate!.month}/${_selectedBirthDate!.year}'
                    : 'تاريخ الميلاد (اختياري)',
                style: TextStyle(
                  color:
                      _selectedBirthDate != null
                          ? Colors.black
                          : Colors.grey[600],
                  fontSize: 16,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPhoneField() {
    return CommonFormFields.buildPhoneField(
      controller: _phoneController,
      onFieldSubmitted: (_) => _completeProfile(),
    );
  }
}
