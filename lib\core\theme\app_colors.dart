import 'package:flutter/material.dart';

class AppColors {
  // Primary colors
  static const Color primaryColor = Color(0xFF2563EB); // More vibrant blue
  static const Color secondaryColor = Color(0xFFFF6B35); // Warmer orange
  static const Color accentColor = Color(0xFF0EA5E9); // Modern light blue
  static const Color backgroundColor = Color(0xFFF9FAFB); // Improved light gray
  
  // Status colors
  static const Color errorColor = Color(0xFFDC2626); // Improved red
  static const Color successColor = Color(0xFF16A34A); // Improved green
  static const Color warningColor = Color(0xFFF59E0B); // Improved yellow
  
  // Additional colors for gradients and effects
  static const Color primaryLightColor = Color(0xFFDBEAFE);
  static const Color secondaryLightColor = Color(0xFFFFEDE8);
  static const Color surfaceColor = Color(0xFFFFFFFF);
  static const Color shadowColor = Color(0x1A000000);
  static const Color shimmerBaseColor = Color(0xFFE2E8F0);
  static const Color shimmerHighlightColor = Color(0xFFF8FAFC);

  // Text colors
  static const Color textPrimaryColor = Color(0xFF1F2937); // Improved black
  static const Color textSecondaryColor = Color(0xFF6B7280); // Improved gray
  static const Color textLightColor = Color(0xFFFFFFFF); // White

  // Background colors
  static const Color cardColor = Color(0xFFFFFFFF); // White
  static const Color dividerColor = Color(0xFFE5E7EB); // Improved light gray
}