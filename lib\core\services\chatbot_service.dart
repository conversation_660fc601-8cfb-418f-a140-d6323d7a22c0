import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:google_generative_ai/google_generative_ai.dart';
import 'package:motorcycle_parts_shop/core/services/auth_supabase_service.dart';
import 'package:speech_to_text/speech_to_text.dart';

class ChatbotService extends ChangeNotifier {
  final AuthSupabaseService _supabaseService;
  final SpeechToText _speechToText = SpeechToText();
  late GenerativeModel _geminiModel;
  bool _isInitialized = false;
  bool _isListening = false;
  bool _isGeminiInitialized = false;

  ChatbotService(this._supabaseService) {
    _initializeGemini();
  }

  /// تهيئة Gemini AI
  Future<void> _initializeGemini() async {
    try {
      final apiKey = dotenv.env['GEMINI_API_KEY'];
      if (apiKey == null || apiKey.isEmpty) {
        debugPrint('Gemini API Key not found in environment variables');
        return;
      }

      _geminiModel = GenerativeModel(
        model: 'gemini-1.5-flash',
        apiKey: apiKey,
        generationConfig: GenerationConfig(
          temperature: 0.7,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 1024,
        ),
        systemInstruction: Content.system(_getSystemPrompt()),
      );

      _isGeminiInitialized = true;
      debugPrint('Gemini AI initialized successfully');
    } catch (e) {
      debugPrint('Failed to initialize Gemini AI: $e');
      _isGeminiInitialized = false;
    }
  }

  /// إنشاء النص التوجيهي للنظام
  String _getSystemPrompt() {
    return '''
أنت مساعد ذكي لمتجر قطع غيار الدراجات النارية. اسمك "مساعد المتجر الذكي".

مهامك:
1. الإجابة على أسئلة العملاء حول المنتجات والخدمات
2. مساعدة العملاء في العثور على قطع الغيار المناسبة
3. تقديم معلومات عن الطلبات والشحن
4. تقديم الدعم الفني والاستشارات

قواعد مهمة:
- أجب باللغة العربية دائماً
- كن مهذباً ومفيداً
- إذا لم تعرف الإجابة، اطلب من العميل التواصل مع خدمة العملاء
- استخدم المعلومات من قاعدة البيانات عند توفرها
- قدم اقتراحات مفيدة للعملاء

أسلوب الرد:
- استخدم عبارات ترحيبية ودودة
- قدم إجابات واضحة ومفصلة
- اقترح منتجات أو خدمات ذات صلة عند الإمكان
''';
  }

  Future<String> getChatbotResponse(String message) async {
    try {
      // التحقق من الاتصال بالإنترنت
      if (!await _checkConnectivity()) {
        return 'يبدو أنك غير متصل بالإنترنت. يرجى التحقق من اتصالك والمحاولة مرة أخرى.';
      }

      // التحقق من تهيئة Gemini
      if (!_isGeminiInitialized) {
        await _initializeGemini();
        if (!_isGeminiInitialized) {
          return 'عذراً، خدمة المساعد الذكي غير متاحة حالياً. يرجى المحاولة لاحقاً.';
        }
      }

      // جمع معلومات السياق من قاعدة البيانات
      final contextData = await _getContextData(message);

      // إنشاء الرسالة مع السياق
      final enhancedMessage = _buildEnhancedMessage(message, contextData);

      // إرسال الرسالة إلى Gemini
      final content = [Content.text(enhancedMessage)];
      final response = await _geminiModel.generateContent(content);

      if (response.text != null && response.text!.isNotEmpty) {
        return response.text!;
      } else {
        return 'عذراً، لم أتمكن من فهم طلبك. هل يمكنك إعادة صياغته؟';
      }
    } catch (e) {
      debugPrint('Chatbot error: $e');
      return 'عذراً، حدث خطأ أثناء معالجة طلبك. يرجى المحاولة مرة أخرى.';
    }
  }

  /// جمع البيانات السياقية من قاعدة البيانات
  Future<Map<String, dynamic>> _getContextData(String message) async {
    final contextData = <String, dynamic>{};

    try {
      // تحليل الرسالة لتحديد نوع الاستفسار
      final messageType = _analyzeMessageType(message);

      switch (messageType) {
        case 'product_inquiry':
          contextData['products'] = await _getProductsData(message);
          contextData['categories'] = await _getCategoriesData();
          break;
        case 'order_inquiry':
          if (_supabaseService.isAuthenticated) {
            contextData['orders'] = await _getUserOrdersData();
          }
          break;
        case 'general_inquiry':
          contextData['featured_products'] = await _getFeaturedProducts();
          contextData['categories'] = await _getCategoriesData();
          break;
      }

      // إضافة معلومات عامة عن المتجر
      contextData['store_info'] = await _getStoreInfo();
    } catch (e) {
      debugPrint('Error getting context data: $e');
    }

    return contextData;
  }

  /// تحليل نوع الرسالة
  String _analyzeMessageType(String message) {
    final lowerMessage = message.toLowerCase();

    // كلمات مفتاحية للمنتجات
    final productKeywords = ['منتج', 'قطعة', 'غيار', 'سعر', 'متوفر', 'مواصفات'];

    // كلمات مفتاحية للطلبات
    final orderKeywords = ['طلب', 'شحن', 'توصيل', 'فاتورة', 'دفع'];

    if (productKeywords.any((keyword) => lowerMessage.contains(keyword))) {
      return 'product_inquiry';
    } else if (orderKeywords.any((keyword) => lowerMessage.contains(keyword))) {
      return 'order_inquiry';
    } else {
      return 'general_inquiry';
    }
  }

  /// بناء الرسالة المحسنة مع السياق
  String _buildEnhancedMessage(
    String originalMessage,
    Map<String, dynamic> contextData,
  ) {
    final buffer = StringBuffer();

    buffer.writeln('رسالة العميل: $originalMessage');
    buffer.writeln('\nمعلومات السياق:');

    // إضافة معلومات المنتجات
    if (contextData['products'] != null) {
      final products = contextData['products'] as List;
      if (products.isNotEmpty) {
        buffer.writeln('\nالمنتجات ذات الصلة:');
        for (final product in products.take(5)) {
          buffer.writeln('- ${product['name']}: ${product['price']} جنيه');
          if (product['description'] != null) {
            buffer.writeln('  الوصف: ${product['description']}');
          }
          buffer.writeln('  متوفر: ${product['quantity'] > 0 ? 'نعم' : 'لا'}');
        }
      }
    }

    // إضافة معلومات الفئات
    if (contextData['categories'] != null) {
      final categories = contextData['categories'] as List;
      if (categories.isNotEmpty) {
        buffer.writeln('\nالفئات المتاحة:');
        for (final category in categories) {
          buffer.writeln('- ${category['name']}');
        }
      }
    }

    // إضافة معلومات الطلبات
    if (contextData['orders'] != null) {
      final orders = contextData['orders'] as List;
      if (orders.isNotEmpty) {
        buffer.writeln('\nطلبات العميل الأخيرة:');
        for (final order in orders.take(3)) {
          buffer.writeln('- طلب رقم: ${order['id']}');
          buffer.writeln('  الحالة: ${order['status']}');
          buffer.writeln('  التاريخ: ${order['created_at']}');
        }
      }
    }

    // إضافة معلومات المتجر
    if (contextData['store_info'] != null) {
      final storeInfo = contextData['store_info'] as Map<String, dynamic>;
      buffer.writeln('\nمعلومات المتجر:');
      buffer.writeln(
        '- اسم المتجر: ${storeInfo['name'] ?? 'متجر قطع غيار الدراجات النارية'}',
      );
      buffer.writeln(
        '- ساعات العمل: ${storeInfo['hours'] ?? 'من 9 صباحاً إلى 9 مساءً'}',
      );
      buffer.writeln('- رقم الهاتف: ${storeInfo['phone'] ?? '01234567890'}');
    }

    buffer.writeln(
      '\nيرجى الإجابة على استفسار العميل بناءً على المعلومات المتاحة أعلاه.',
    );

    return buffer.toString();
  }

  /// جلب بيانات المنتجات ذات الصلة
  Future<List<Map<String, dynamic>>> _getProductsData(String message) async {
    try {
      // البحث في المنتجات بناءً على الكلمات المفتاحية
      final searchTerms = _extractSearchTerms(message);

      if (searchTerms.isEmpty) {
        // إذا لم توجد كلمات بحث، أرجع المنتجات المميزة
        return await _getFeaturedProducts();
      }

      final query = _supabaseService.client
          .from('products')
          .select('''
            id, name, description, price, quantity, brand,
            category_id, is_featured, rating, reviews_count
          ''')
          .or(
            searchTerms
                .map(
                  (term) =>
                      'name.ilike.%$term%,description.ilike.%$term%,brand.ilike.%$term%',
                )
                .join(','),
          )
          .eq('is_active', true)
          .limit(10);

      final response = await query;
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      debugPrint('Error getting products data: $e');
      return [];
    }
  }

  /// استخراج كلمات البحث من الرسالة
  List<String> _extractSearchTerms(String message) {
    // إزالة كلمات الوصل والحروف الشائعة
    final stopWords = [
      'في',
      'من',
      'إلى',
      'على',
      'عن',
      'مع',
      'هل',
      'ما',
      'كيف',
      'أين',
    ];

    final words =
        message
            .split(' ')
            .where((word) => word.length > 2 && !stopWords.contains(word))
            .toList();

    return words;
  }

  /// جلب بيانات الفئات
  Future<List<Map<String, dynamic>>> _getCategoriesData() async {
    try {
      final response = await _supabaseService.client
          .from('categories')
          .select('id, name, description')
          .eq('is_active', true)
          .order('name');

      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      debugPrint('Error getting categories data: $e');
      return [];
    }
  }

  /// جلب المنتجات المميزة
  Future<List<Map<String, dynamic>>> _getFeaturedProducts() async {
    try {
      final response = await _supabaseService.client
          .from('products')
          .select('''
            id, name, description, price, quantity, brand,
            category_id, rating, reviews_count
          ''')
          .eq('is_featured', true)
          .eq('is_active', true)
          .limit(5);

      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      debugPrint('Error getting featured products: $e');
      return [];
    }
  }

  /// جلب طلبات المستخدم
  Future<List<Map<String, dynamic>>> _getUserOrdersData() async {
    try {
      final userId = _supabaseService.currentUser?.id;
      if (userId == null) return [];

      final response = await _supabaseService.client
          .from('orders')
          .select('id, status, total_amount, created_at')
          .eq('user_id', userId)
          .order('created_at', ascending: false)
          .limit(5);

      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      debugPrint('Error getting user orders: $e');
      return [];
    }
  }

  /// جلب معلومات المتجر
  Future<Map<String, dynamic>> _getStoreInfo() async {
    try {
      final response = await _supabaseService.client
          .from('admin_settings')
          .select('setting_key, setting_value')
          .inFilter('setting_key', [
            'store_name',
            'store_hours',
            'store_phone',
          ]);

      final settings = <String, dynamic>{};
      for (final setting in response) {
        final key = setting['setting_key'].toString().replaceAll('store_', '');
        settings[key] = setting['setting_value'];
      }

      return settings;
    } catch (e) {
      debugPrint('Error getting store info: $e');
      return {
        'name': 'متجر قطع غيار الدراجات النارية',
        'hours': 'من 9 صباحاً إلى 9 مساءً',
        'phone': '01234567890',
      };
    }
  }

  Future<void> saveChatHistory(List<Map<String, dynamic>> messages) async {
    try {
      // التحقق من تسجيل دخول المستخدم
      if (!_supabaseService.isAuthenticated) {
        debugPrint('لا يمكن حفظ المحادثة: المستخدم غير مسجل الدخول');
        return;
      }

      // التحقق من الاتصال بالإنترنت
      if (!await _checkConnectivity()) {
        debugPrint('لا يمكن حفظ المحادثة: لا يوجد اتصال بالإنترنت');
        return;
      }

      // الحصول على آخر محادثة محفوظة
      final userId = _supabaseService.currentUser?.id;
      if (userId == null) {
        debugPrint('لا يمكن حفظ المحادثة: معرف المستخدم غير موجود');
        return;
      }

      final existingHistory =
          await _supabaseService.client
              .from('chat_history')
              .select()
              .eq('user_id', userId)
              .order('created_at', ascending: false)
              .limit(1)
              .maybeSingle();

      // تحديد الرسائل الجديدة فقط للحفظ
      final lastSavedMessageCount =
          existingHistory != null
              ? (existingHistory['messages'] as List).length
              : 0;

      if (messages.length > lastSavedMessageCount) {
        // حفظ المحادثة الكاملة المحدثة
        await _supabaseService.client.from('chat_history').upsert({
          'user_id': userId, // userId تم التحقق منه مسبقًا
          'messages': messages,
          'created_at': DateTime.now().toIso8601String(),
        });
      }
    } catch (e) {
      debugPrint('Failed to save chat history: $e');
    }
  }

  Future<List<Map<String, dynamic>>> getChatHistory() async {
    try {
      // التحقق من تسجيل دخول المستخدم
      if (!_supabaseService.isAuthenticated) {
        return [];
      }

      // التحقق من الاتصال بالإنترنت
      if (!await _checkConnectivity()) {
        return [];
      }

      final userId = _supabaseService.currentUser?.id;
      if (userId == null) {
        return [];
      }

      final history =
          await _supabaseService.client
              .from('chat_history')
              .select()
              .eq('user_id', userId)
              .order('created_at', ascending: false)
              .limit(1)
              .maybeSingle();

      if (history != null && history['messages'] != null) {
        return List<Map<String, dynamic>>.from(history['messages']);
      }
      return [];
    } catch (e) {
      debugPrint('Failed to get chat history: $e');
      return [];
    }
  }

  Future<String> handleProductInquiry(String productName) async {
    try {
      // التحقق من الاتصال بالإنترنت
      if (!await _checkConnectivity()) {
        return 'يبدو أنك غير متصل بالإنترنت. يرجى التحقق من اتصالك والمحاولة مرة أخرى.';
      }

      final response = await _supabaseService.client.rpc(
        'get_product_info',
        params: {'product_name': productName},
      );
      return response as String;
    } catch (e) {
      debugPrint('Product inquiry error: $e');
      return 'عذرًا، لا يمكن العثور على معلومات المنتج حاليًا. يرجى المحاولة لاحقًا.';
    }
  }

  Future<String> handleSalesInquiry() async {
    try {
      // التحقق من الاتصال بالإنترنت
      if (!await _checkConnectivity()) {
        return 'يبدو أنك غير متصل بالإنترنت. يرجى التحقق من اتصالك والمحاولة مرة أخرى.';
      }

      final response = await _supabaseService.client.rpc('get_sales_info');
      return response as String;
    } catch (e) {
      debugPrint('Sales inquiry error: $e');
      return 'عذرًا، لا يمكن العثور على معلومات المبيعات حاليًا. يرجى المحاولة لاحقًا.';
    }
  }

  Future<void> sendOrderNotification(String orderId) async {
    try {
      // التحقق من الاتصال بالإنترنت
      if (!await _checkConnectivity()) {
        throw Exception('لا يوجد اتصال بالإنترنت');
      }

      await _supabaseService.client.rpc(
        'send_order_notification',
        params: {'order_id': orderId},
      );
    } catch (e) {
      debugPrint('Failed to send order notification: $e');
      throw Exception('فشل في إرسال إشعار حالة الطلب');
    }
  }

  // Speech recognition functions
  Future<void> initSpeech() async {
    try {
      _isInitialized = await _speechToText.initialize();
      notifyListeners();
    } catch (e) {
      debugPrint('Failed to initialize speech recognition: $e');
      _isInitialized = false;
      notifyListeners();
    }
  }

  Future<String> listenToVoiceCommand() async {
    if (!_isInitialized) {
      await initSpeech(); // Ensure initialization before listening
      if (!_isInitialized) return 'فشل تهيئة التعرف الصوتي';
    }

    String recognizedText = '';
    _isListening = true;
    notifyListeners();

    try {
      await _speechToText.listen(
        onResult: (result) {
          recognizedText = result.recognizedWords;
        },
        localeId: 'ar_SA', // تعيين اللغة العربية للتعرف الصوتي
      );

      // انتظار انتهاء التعرف الصوتي
      await Future.delayed(const Duration(seconds: 5));
      stopListening();

      return recognizedText;
    } catch (e) {
      debugPrint('Voice recognition error: $e');
      stopListening();
      return '';
    }
  }

  void stopListening() {
    if (_isListening) {
      _speechToText.stop();
      _isListening = false;
      notifyListeners();
    }
  }

  // التحقق من الاتصال بالإنترنت
  Future<bool> _checkConnectivity() async {
    try {
      // محاولة بسيطة للتحقق من الاتصال بالإنترنت عن طريق استعلام بسيط
      await _supabaseService.client
          .from('_dummy_table_for_connectivity_check')
          .select()
          .limit(1)
          .maybeSingle()
          .timeout(const Duration(seconds: 5));
      return true;
    } catch (e) {
      return false;
    }
  }

  // Optional: Getter to check if speech is initialized
  bool get isSpeechInitialized => _isInitialized;

  // Getter to check if speech is currently listening
  bool get isListening => _isListening;
}
