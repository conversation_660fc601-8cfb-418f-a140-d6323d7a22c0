import 'package:flutter/material.dart';
import 'package:motorcycle_parts_shop/core/services/analytics_service.dart';
import 'package:motorcycle_parts_shop/core/services/auth_supabase_service.dart';
import 'package:motorcycle_parts_shop/core/services/navigation_service.dart';
import 'package:motorcycle_parts_shop/core/theme/app_theme.dart';
import 'package:motorcycle_parts_shop/core/utils/responsive_helper.dart';
import 'package:motorcycle_parts_shop/models/user_model.dart';
import 'package:motorcycle_parts_shop/screens/address/address_book_screen.dart';
import 'package:motorcycle_parts_shop/screens/orders/order_history_screen.dart';
import 'package:motorcycle_parts_shop/screens/profile/help_support_screen.dart';
import 'package:motorcycle_parts_shop/screens/settings/settings_screen.dart';
import 'package:provider/provider.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  bool _isLoading = false;
  final NavigationService _navigationService = NavigationService();
  late final AnalyticsService _analyticsService;
  Map<String, dynamic> _userStats = {};

  @override
  void initState() {
    super.initState();
    _analyticsService = Provider.of<AnalyticsService>(context, listen: false);
    _loadUserStats();
  }

  Future<void> _loadUserStats() async {
    final authService = Provider.of<AuthSupabaseService>(
      context,
      listen: false,
    );
    final user = authService.currentUser;
    if (user != null) {
      setState(() => _isLoading = true);
      try {
        final stats = await _analyticsService.getUserMetrics(user.id);
        if (!mounted) return;
        setState(() {
          _userStats = stats;
          _isLoading = false;
        });
      } catch (e) {
        if (!mounted) return;
        setState(() => _isLoading = false);
        debugPrint('خطأ في تحميل إحصائيات المستخدم: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final authService = Provider.of<AuthSupabaseService>(context);
    final UserModel? user = authService.currentUser;

    if (user == null) {
      return const Center(child: Text('يرجى تسجيل الدخول لعرض الملف الشخصي'));
    }

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : CustomScrollView(
                slivers: [
                  // شريط التطبيق المتطور
                  SliverAppBar(
                    expandedHeight: ResponsiveHelper.getAppBarHeight(context),
                    pinned: true,
                    elevation: 0,
                    backgroundColor: AppTheme.primaryColor,
                    flexibleSpace: FlexibleSpaceBar(
                      background: Container(
                        decoration: BoxDecoration(
                          gradient: AppTheme.primaryGradient,
                          borderRadius: const BorderRadius.only(
                            bottomLeft: Radius.circular(32),
                            bottomRight: Radius.circular(32),
                          ),
                        ),
                        child: SafeArea(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const SizedBox(height: 60),

                              // صورة الملف الشخصي المحسنة
                              Container(
                                padding: const EdgeInsets.all(4),
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  border: Border.all(
                                    color: AppTheme.textLightColor.withOpacity(
                                      0.3,
                                    ),
                                    width: 3,
                                  ),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withOpacity(0.2),
                                      blurRadius: 20,
                                      offset: const Offset(0, 10),
                                    ),
                                  ],
                                ),
                                child: CircleAvatar(
                                  radius: 60,
                                  backgroundColor: AppTheme.textLightColor
                                      .withOpacity(0.2),
                                  child: Icon(
                                    Icons.person_rounded,
                                    size: 60,
                                    color: AppTheme.textLightColor,
                                  ),
                                ),
                              ),

                              const SizedBox(height: 20),

                              // اسم المستخدم
                              Text(
                                user.name,
                                style: AppTheme.heroTitle.copyWith(
                                  color: AppTheme.textLightColor,
                                  fontSize: 24,
                                  fontWeight: FontWeight.w800,
                                ),
                              ),

                              const SizedBox(height: 8),

                              // البريد الإلكتروني
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 8,
                                ),
                                decoration: BoxDecoration(
                                  color: AppTheme.textLightColor.withOpacity(
                                    0.2,
                                  ),
                                  borderRadius: BorderRadius.circular(20),
                                ),
                                child: Text(
                                  user.email,
                                  style: AppTheme.cardSubtitle.copyWith(
                                    color: AppTheme.textLightColor.withOpacity(
                                      0.9,
                                    ),
                                    fontSize: 14,
                                  ),
                                ),
                              ),

                              const SizedBox(height: 20),

                              // زر تعديل الملف الشخصي
                              Container(
                                decoration: BoxDecoration(
                                  color: AppTheme.textLightColor.withOpacity(
                                    0.2,
                                  ),
                                  borderRadius: BorderRadius.circular(16),
                                  border: Border.all(
                                    color: AppTheme.textLightColor.withOpacity(
                                      0.3,
                                    ),
                                    width: 1,
                                  ),
                                ),
                                child: Material(
                                  color: Colors.transparent,
                                  child: InkWell(
                                    onTap: () async {
                                      await _navigationService.navigateTo(
                                        '/edit-profile',
                                        arguments: user,
                                      );
                                      setState(() {});
                                    },
                                    borderRadius: BorderRadius.circular(16),
                                    child: Padding(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 24,
                                        vertical: 12,
                                      ),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Icon(
                                            Icons.edit_rounded,
                                            color: AppTheme.textLightColor,
                                            size: 20,
                                          ),
                                          const SizedBox(width: 8),
                                          Text(
                                            'تعديل الملف الشخصي',
                                            style: AppTheme.buttonText.copyWith(
                                              fontSize: 14,
                                              fontWeight: FontWeight.w600,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),

                  // المحتوى الرئيسي
                  SliverToBoxAdapter(
                    child: Padding(
                      padding: EdgeInsets.all(
                        ResponsiveHelper.getPadding(
                          context,
                          mobile: 16,
                          tablet: 18,
                          desktop: 20,
                        ),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SizedBox(height: 24),
                          const Divider(),
                          const SizedBox(height: 16),

                          // User Statistics Section
                          _buildSectionHeader('الإحصائيات'),
                          _buildStatisticsGrid(),
                          const SizedBox(height: 24),
                          const Divider(),
                          const SizedBox(height: 16),

                          // Personal statistics
                          _buildStatisticsSection(context, user),
                          const SizedBox(height: 16),
                          const Divider(),
                          const SizedBox(height: 16),

                          // Profile options
                          _buildProfileOption(
                            context,
                            icon: Icons.location_on,
                            title: 'إدارة العناوين',
                            subtitle: 'إضافة وتعديل عناوين التوصيل',
                            onTap: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder:
                                      (context) => const AddressBookScreen(),
                                ),
                              );
                            },
                          ),
                          _buildProfileOption(
                            context,
                            icon: Icons.shopping_bag,
                            title: 'سجل الطلبات',
                            subtitle:
                                'عرض طلباتك السابقة وتتبع الطلبات الحالية',
                            onTap: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder:
                                      (context) => const OrderHistoryScreen(),
                                ),
                              );
                            },
                          ),
                          _buildProfileOption(
                            context,
                            icon: Icons.settings,
                            title: 'الإعدادات',
                            subtitle: 'تغيير اللغة، الإشعارات، والمظهر',
                            onTap: () {
                              Navigator.of(context).push(
                                MaterialPageRoute(
                                  builder: (context) => const SettingsScreen(),
                                ),
                              );
                            },
                          ),
                          _buildProfileOption(
                            context,
                            icon: Icons.help,
                            title: 'المساعدة والدعم',
                            subtitle: 'الأسئلة الشائعة وطرق التواصل',
                            onTap: () {
                              Navigator.of(context).push(
                                MaterialPageRoute(
                                  builder:
                                      (context) => const HelpSupportScreen(),
                                ),
                              );
                            },
                          ),
                          const SizedBox(height: 16),
                          const Divider(),
                          const SizedBox(height: 16),
                          // Logout button
                          _buildProfileOption(
                            context,
                            icon: Icons.logout,
                            title: 'تسجيل الخروج',
                            subtitle: 'تسجيل الخروج من حسابك',
                            textColor: AppTheme.errorColor,
                            onTap: () async {
                              setState(() {
                                _isLoading = true;
                              });

                              await authService.signOut();

                              if (!mounted) return;

                              // Navigate back to login screen
                              Navigator.of(context).pushNamedAndRemoveUntil(
                                '/welcome',
                                (route) => false,
                              );
                            },
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
    );
  }

  Widget _buildStatisticsSection(BuildContext context, UserModel user) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader('إحصائياتي'),
        const SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _buildStatItem(
              context,
              Icons.shopping_bag,
              'الطلبات',
              user.orderCount?.toString() ?? '0',
            ),
            _buildStatItem(
              context,
              Icons.favorite,
              'المفضلة',
              user.wishlistCount?.toString() ?? '0',
            ),
            _buildStatItem(
              context,
              Icons.star,
              'التقييمات',
              user.averageRating?.toStringAsFixed(1) ?? '0.0',
            ),
          ],
        ),
        const SizedBox(height: 16),
        if (user.totalSpent != null)
          _buildStatItem(
            context,
            Icons.monetization_on,
            'إجمالي المشتريات',
            '${user.totalSpent!.toStringAsFixed(2)} ج.م',
          ),
        const SizedBox(height: 8),
      ],
    );
  }

  Widget _buildStatItem(
    BuildContext context,
    IconData icon,
    String title,
    String value,
  ) {
    return Column(
      children: [
        CircleAvatar(
          backgroundColor: AppTheme.primaryColor.withOpacity(0.1),
          child: Icon(icon, color: AppTheme.primaryColor),
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryColor,
          ),
        ),
        Text(
          title,
          style: Theme.of(
            context,
          ).textTheme.bodySmall?.copyWith(color: AppTheme.textSecondaryColor),
        ),
      ],
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: AppTheme.primaryColor,
        ),
      ),
    );
  }

  Widget _buildStatisticsGrid() {
    return ResponsiveBuilder(
      builder: (context, constraints) {
        final crossAxisCount = ResponsiveHelper.getGridColumns(
          context,
          maxColumns: 2,
        );
        final spacing = ResponsiveHelper.getPadding(
          context,
          mobile: 12,
          tablet: 14,
          desktop: 16,
        );
        final aspectRatio = ResponsiveHelper.getAspectRatio(
          context,
          mobile: 1.3,
          tablet: 1.4,
          desktop: 1.5,
        );

        return GridView.count(
          crossAxisCount: crossAxisCount,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          mainAxisSpacing: spacing,
          crossAxisSpacing: spacing,
          childAspectRatio: aspectRatio,
          children: [
            _buildStatCard(
              'عدد الطلبات',
              _userStats['total_orders']?.toString() ?? '0',
              Icons.shopping_bag,
            ),
            _buildStatCard(
              'متوسط قيمة الطلب',
              '${_userStats['average_order_value']?.toStringAsFixed(2) ?? '0'} ج.م',
              Icons.attach_money,
            ),
            _buildStatCard(
              'درجة التفاعل',
              _userStats['engagement_score']?.toStringAsFixed(1) ?? '0',
              Icons.star,
            ),
            _buildStatCard(
              'أيام النشاط',
              _userStats['active_days_last_month']?.toString() ?? '0',
              Icons.calendar_today,
            ),
          ],
        );
      },
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: 12,
          vertical: 8,
        ), // تقليل الهامش العمودي
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize:
              MainAxisSize.min, // تعديل لضمان أن العمود يأخذ أقل مساحة ممكنة
          crossAxisAlignment: CrossAxisAlignment.center, // ضمان توسيط المحتوى
          children: [
            Icon(
              icon,
              color: AppTheme.primaryColor,
              size: 22,
            ), // تقليل حجم الأيقونة
            const SizedBox(height: 4), // تقليل المسافة
            FittedBox(
              // إضافة FittedBox لضمان ملاءمة النص
              fit: BoxFit.scaleDown,
              child: Text(
                title,
                style: const TextStyle(
                  fontSize: 12, // تقليل حجم الخط
                  color: AppTheme.textSecondaryColor,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: 2), // تقليل المسافة
            FittedBox(
              // إضافة FittedBox لضمان ملاءمة النص
              fit: BoxFit.scaleDown,
              child: Text(
                value,
                style: const TextStyle(
                  fontSize: 16, // تقليل حجم الخط
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileOption(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    Color? textColor,
  }) {
    return ListTile(
      leading: CircleAvatar(
        backgroundColor: AppTheme.primaryColor.withOpacity(0.1),
        child: Icon(icon, color: textColor ?? AppTheme.primaryColor),
      ),
      title: Text(title, style: TextStyle(color: textColor)),
      subtitle: Text(subtitle),
      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: onTap,
    );
  }
}
