-- ===================================================================
-- إعداد قاعدة البيانات الأساسي والإضافات المطلوبة
-- متجر قطع غيار الدراجات النارية - Supabase Database
-- ===================================================================

-- تفعيل الإضافات المطلوبة
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE SCHEMA IF NOT EXISTS extensions;
ALTER EXTENSION pg_trgm SET SCHEMA extensions;
ALTER DATABASE postgres SET search_path = public, extensions;
-- تفعي<PERSON> امتداد pg_cron للمهام المجدولة (إذا كان متاحاً)
-- CREATE EXTENSION IF NOT EXISTS pg_cron;

-- ===================================================================
-- الدوال المشتركة الأساسية
-- ===================================================================

-- دالة تحديث الطوابع الزمنية
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$;

-- دالة إنشاء رقم طلب فريد
CREATE OR REPLACE FUNCTION generate_order_number()
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    new_number TEXT;
    counter INTEGER;
BEGIN
    -- التحقق من وجود جدول orders
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'orders') THEN
        -- الحصول على العداد الحالي لليوم
        SELECT COALESCE(MAX(CAST(SUBSTRING(order_number FROM 9) AS INTEGER)), 0) + 1
        INTO counter
        FROM orders
        WHERE order_number LIKE TO_CHAR(NOW(), 'YYYYMMDD') || '%';
    ELSE
        -- إذا لم يكن الجدول موجود، ابدأ من 1
        counter := 1;
    END IF;

    -- إنشاء رقم الطلب الجديد
    new_number := TO_CHAR(NOW(), 'YYYYMMDD') || LPAD(counter::TEXT, 4, '0');

    RETURN new_number;
END;
$$;

-- دالة تنظيف البيانات القديمة
CREATE OR REPLACE FUNCTION cleanup_old_data()
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    -- حذف سجلات التحليلات القديمة (أكثر من سنة) - إذا كانت الجداول موجودة
    BEGIN
        DELETE FROM user_behavior_analytics WHERE created_at < NOW() - INTERVAL '1 year';
    EXCEPTION WHEN undefined_table THEN NULL;
    END;

    BEGIN
        DELETE FROM product_views WHERE created_at < NOW() - INTERVAL '1 year';
    EXCEPTION WHEN undefined_table THEN NULL;
    END;

    BEGIN
        DELETE FROM smart_search_log WHERE created_at < NOW() - INTERVAL '1 year';
    EXCEPTION WHEN undefined_table THEN NULL;
    END;

    -- حذف الإشعارات المقروءة القديمة (أكثر من 6 أشهر)
    BEGIN
        DELETE FROM notifications WHERE is_read = true AND created_at < NOW() - INTERVAL '6 months';
    EXCEPTION WHEN undefined_table THEN NULL;
    END;

    -- حذف سجلات النسخ الاحتياطي القديمة (أكثر من 3 أشهر)
    BEGIN
        DELETE FROM backup_logs WHERE started_at < NOW() - INTERVAL '3 months';
    EXCEPTION WHEN undefined_table THEN NULL;
    END;

    -- تحديث حالة المنتجات الجديدة
    BEGIN
        PERFORM update_new_products_status();
    EXCEPTION WHEN undefined_function THEN NULL;
    END;

    -- تحديث الإحصائيات
    ANALYZE;

    RAISE NOTICE 'تم تنظيف البيانات القديمة بنجاح';
END;
$$;

-- دالة إعداد المهام المجدولة للمنتجات الجديدة
CREATE OR REPLACE FUNCTION setup_new_products_scheduler()
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    v_cleanup_interval INTEGER;
    v_cron_schedule TEXT;
    v_job_exists BOOLEAN := false;
BEGIN
    -- التحقق من وجود امتداد pg_cron
    IF NOT EXISTS (SELECT 1 FROM pg_extension WHERE extname = 'pg_cron') THEN
        RAISE NOTICE 'امتداد pg_cron غير متاح - سيتم استخدام التنظيف اليدوي';
        RETURN false;
    END IF;

    -- الحصول على فترة التنظيف من الإعدادات
    BEGIN
        SELECT setting_value INTO v_cleanup_interval
        FROM new_product_settings
        WHERE setting_name = 'cleanup_interval_hours' AND is_active = true;
    EXCEPTION
        WHEN OTHERS THEN
            v_cleanup_interval := NULL;
    END;

    IF v_cleanup_interval IS NULL THEN
        v_cleanup_interval := 24; -- افتراضي كل 24 ساعة
    END IF;

    -- تحديد جدولة cron حسب الفترة
    CASE
        WHEN v_cleanup_interval <= 1 THEN v_cron_schedule := '0 * * * *'; -- كل ساعة
        WHEN v_cleanup_interval <= 6 THEN v_cron_schedule := '0 */6 * * *'; -- كل 6 ساعات
        WHEN v_cleanup_interval <= 12 THEN v_cron_schedule := '0 */12 * * *'; -- كل 12 ساعة
        ELSE v_cron_schedule := '0 2 * * *'; -- يومياً في الساعة 2 صباحاً
    END CASE;

    -- التحقق من وجود المهمة
    SELECT EXISTS (
        SELECT 1 FROM cron.job
        WHERE jobname = 'cleanup_new_products'
    ) INTO v_job_exists;

    -- حذف المهمة الموجودة إذا كانت موجودة
    IF v_job_exists THEN
        PERFORM cron.unschedule('cleanup_new_products');
    END IF;

    -- إضافة المهمة الجديدة
    PERFORM cron.schedule(
        'cleanup_new_products',
        v_cron_schedule,
        'SELECT update_new_products_status();'
    );

    -- تسجيل في السجل
    BEGIN
        INSERT INTO system_logs (
            log_type, message, details
        ) VALUES (
            'scheduler_setup',
            'تم إعداد جدولة تنظيف المنتجات الجديدة',
            jsonb_build_object(
                'interval_hours', v_cleanup_interval,
                'cron_schedule', v_cron_schedule,
                'setup_time', NOW()
            )
        );
    EXCEPTION
        WHEN OTHERS THEN
            -- تجاهل الخطأ إذا لم يكن جدول system_logs موجود
            NULL;
    END;

    RAISE NOTICE 'تم إعداد جدولة تنظيف المنتجات الجديدة: %', v_cron_schedule;
    RETURN true;

EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'خطأ في إعداد الجدولة: %', SQLERRM;
        RETURN false;
END;
$$;

-- ملاحظة: دوال ومحفزات المنتجات الجديدة سيتم إنشاؤها في ملف setup_new_products_system.sql
-- بعد إنشاء الجداول المطلوبة

-- ===================================================================
-- إعدادات الأداء والتحسين
-- ===================================================================

-- تحسين إعدادات PostgreSQL للأداء
-- هذه الإعدادات يجب تطبيقها على مستوى قاعدة البيانات

-- تحسين إعدادات الذاكرة
-- shared_buffers = 256MB
-- effective_cache_size = 1GB
-- work_mem = 4MB
-- maintenance_work_mem = 64MB

-- تحسين إعدادات الكتابة
-- wal_buffers = 16MB
-- checkpoint_completion_target = 0.9
-- checkpoint_timeout = 10min

-- تحسين إعدادات الاستعلامات
-- random_page_cost = 1.1
-- effective_io_concurrency = 200

-- ===================================================================
-- رسالة نجاح الإعداد
-- ===================================================================

DO $$
BEGIN
    RAISE NOTICE '✅ تم إعداد قاعدة البيانات الأساسي بنجاح!';
    RAISE NOTICE '📦 تم تفعيل الإضافات المطلوبة';
    RAISE NOTICE '⚡ تم إنشاء الدوال المشتركة';
    RAISE NOTICE '🚀 قاعدة البيانات جاهزة للخطوة التالية';
END $$;
