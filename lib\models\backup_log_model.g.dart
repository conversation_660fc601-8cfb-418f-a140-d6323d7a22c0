
// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'backup_log_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BackupLogModel _$BackupLogModelFromJson(Map<String, dynamic> json) =>
    BackupLogModel(
      id: json['id'] as String,
      backupName: json['backupName'] as String,
      backupPath: json['backupPath'] as String,
      backupSize: json['backupSize'] as String?,
      status: json['status'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      errorMessage: json['errorMessage'] as String?,
    );

Map<String, dynamic> _$BackupLogModelToJson(BackupLogModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'backupName': instance.backupName,
      'backupPath': instance.backupPath,
      'backupSize': instance.backupSize,
      'status': instance.status,
      'createdAt': instance.createdAt.toIso8601String(),
      'errorMessage': instance.errorMessage,
    };
