import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:motorcycle_parts_shop/core/theme/app_theme.dart';
import 'package:motorcycle_parts_shop/models/product_model.dart';
import 'package:motorcycle_parts_shop/screens/product/product_details_screen.dart';

/// بطاقة عرض المنتج
/// تستخدم لعرض معلومات المنتج بشكل موجز وجذاب في القوائم والشبكات
class ProductCard extends StatelessWidget {
  /// نموذج المنتج
  final ProductModel product;

  /// دالة يتم استدعاؤها عند النقر على البطاقة
  final VoidCallback? onTap;

  /// هل يتم عرض شارة الخصم؟
  final bool showDiscountBadge;

  /// هل يتم عرض شارة "جديد"؟
  final bool showNewBadge;

  /// هل يتم عرض شارة "الأكثر مبيعًا"؟
  final bool showBestSellerBadge;

  /// هل يتم عرض زر الإضافة إلى المفضلة؟
  final bool showFavoriteButton;

  /// هل المنتج في المفضلة؟
  final bool isFavorite;

  /// دالة يتم استدعاؤها عند النقر على زر المفضلة
  final VoidCallback? onFavoriteToggle;

  /// ارتفاع البطاقة
  final double? height;

  /// عرض البطاقة
  final double? width;

  /// هل يتم عرض تقييم المنتج؟
  final bool showRating;

  const ProductCard({
    super.key,
    required this.product,
    this.onTap,
    this.showDiscountBadge = true,
    this.showNewBadge = false,
    this.showBestSellerBadge = true,
    this.showFavoriteButton = true,
    this.isFavorite = false,
    this.onFavoriteToggle,
    this.height,
    this.width,
    this.showRating = true,
  });

  @override
  Widget build(BuildContext context) {
    // حساب نسبة الخصم إذا كان هناك خصم
    final hasDiscount =
        product.discountPrice != null && product.discountPrice! < product.price;
    final discountPercentage =
        hasDiscount
            ? ((product.price - product.discountPrice!) / product.price * 100)
                .round()
            : 0;

    // التحقق مما إذا كان المنتج جديدًا (أقل من 14 يومًا)
    final isNew = DateTime.now().difference(product.createdAt).inDays < 14;

    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap:
              onTap ??
              () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder:
                        (context) => ProductDetailsScreen(product: product),
                  ),
                );
              },
          borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
          child: Container(
            height: height,
            width: width,
            decoration: BoxDecoration(
              gradient: AppTheme.cardGradient,
              borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
              boxShadow: AppTheme.cardShadow,
              border: Border.all(
                color: AppTheme.primaryColor.withOpacity(0.08),
                width: 1,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // صورة المنتج مع الشارات
                Stack(
                  children: [
                    // صورة المنتج
                    ClipRRect(
                      borderRadius: const BorderRadius.vertical(
                        top: Radius.circular(12),
                      ),
                      child: AspectRatio(
                        aspectRatio: 1,
                        child:
                            product.images.isNotEmpty
                                ? Hero(
                                  tag: 'product_image_${product.id}',
                                  child: CachedNetworkImage(
                                    imageUrl: product.images.first,
                                    fit: BoxFit.cover,
                                    errorWidget:
                                        (context, url, error) => const Center(
                                          child: Icon(
                                            Icons.image_not_supported,
                                            color: Colors.grey,
                                            size: 40,
                                          ),
                                        ),
                                    placeholder:
                                        (context, url) => const Center(
                                          child: CircularProgressIndicator(),
                                        ),
                                  ),
                                )
                                : const Center(
                                  child: Icon(
                                    Icons.image_not_supported,
                                    color: Colors.grey,
                                    size: 40,
                                  ),
                                ),
                      ),
                    ),

                    // شارات المنتج (الخصم، جديد، الأكثر مبيعًا)
                    Positioned(
                      top: 8,
                      left: 8,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // شارة الخصم المتطورة
                          if (showDiscountBadge && hasDiscount)
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 10,
                                vertical: 6,
                              ),
                              decoration: BoxDecoration(
                                gradient: AppTheme.secondaryGradient,
                                borderRadius: BorderRadius.circular(
                                  AppTheme.radiusSmall,
                                ),
                                boxShadow: [
                                  BoxShadow(
                                    color: AppTheme.secondaryColor.withOpacity(
                                      0.4,
                                    ),
                                    blurRadius: 6,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    Icons.local_offer_rounded,
                                    color: AppTheme.textLightColor,
                                    size: 12,
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    '$discountPercentage%',
                                    style: AppTheme.buttonText.copyWith(
                                      fontSize: 11,
                                      fontWeight: FontWeight.w700,
                                    ),
                                  ),
                                ],
                              ),
                            ),

                          // شارة "جديد"
                          if (showNewBadge && isNew)
                            Container(
                              margin: const EdgeInsets.only(top: 4),
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.green,
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: const Text(
                                'جديد',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),

                          // شارة "الأكثر مبيعًا"
                          if (showBestSellerBadge && product.isBestSelling)
                            Container(
                              margin: const EdgeInsets.only(top: 4),
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.orange,
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: const Text(
                                'الأكثر مبيعًا',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),

                    // زر المفضلة المتطور
                    if (showFavoriteButton)
                      Positioned(
                        top: 8,
                        right: 8,
                        child: AnimatedContainer(
                          duration: const Duration(milliseconds: 300),
                          decoration: BoxDecoration(
                            gradient:
                                isFavorite
                                    ? AppTheme.secondaryGradient
                                    : LinearGradient(
                                      colors: [
                                        Colors.white.withOpacity(0.9),
                                        Colors.white.withOpacity(0.7),
                                      ],
                                    ),
                            shape: BoxShape.circle,
                            boxShadow: [
                              BoxShadow(
                                color:
                                    isFavorite
                                        ? AppTheme.secondaryColor.withOpacity(
                                          0.3,
                                        )
                                        : Colors.black.withOpacity(0.1),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Material(
                            color: Colors.transparent,
                            child: InkWell(
                              onTap: onFavoriteToggle,
                              borderRadius: BorderRadius.circular(20),
                              child: Container(
                                width: 40,
                                height: 40,
                                alignment: Alignment.center,
                                child: AnimatedSwitcher(
                                  duration: const Duration(milliseconds: 300),
                                  child: Icon(
                                    isFavorite
                                        ? Icons.favorite_rounded
                                        : Icons.favorite_border_rounded,
                                    key: ValueKey(isFavorite),
                                    color:
                                        isFavorite
                                            ? AppTheme.textLightColor
                                            : AppTheme.textSecondaryColor,
                                    size: 20,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),

                    // شارة عدم التوفر
                    if (!product.isAvailable)
                      Positioned.fill(
                        child: Container(
                          decoration: BoxDecoration(
                            color: Colors.black.withOpacity(0.5),
                            borderRadius: const BorderRadius.vertical(
                              top: Radius.circular(12),
                            ),
                          ),
                          child: const Center(
                            child: Text(
                              'غير متوفر',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                      ),
                  ],
                ),

                // معلومات المنتج
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        // اسم المنتج
                        Text(
                          product.name,
                          style: Theme.of(context).textTheme.bodyMedium
                              ?.copyWith(fontWeight: FontWeight.bold),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),

                        const SizedBox(height: 4),

                        // العلامة التجارية
                        Text(
                          product.brand,
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(color: Colors.grey[600]),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),

                        const Spacer(),

                        // السعر المحسن
                        if (hasDiscount) ...[
                          Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 4,
                                ),
                                decoration: BoxDecoration(
                                  gradient: AppTheme.primaryGradient,
                                  borderRadius: BorderRadius.circular(6),
                                ),
                                child: Text(
                                  '${product.discountPrice!.toStringAsFixed(0)} ج.م',
                                  style: AppTheme.priceText.copyWith(
                                    color: AppTheme.textLightColor,
                                    fontSize: 14,
                                    fontWeight: FontWeight.w700,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Text(
                                '${product.price.toStringAsFixed(0)} ج.م',
                                style: AppTheme.discountText.copyWith(
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                        ] else ...[
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              gradient: AppTheme.primaryGradient,
                              borderRadius: BorderRadius.circular(6),
                            ),
                            child: Text(
                              '${product.price.toStringAsFixed(0)} ج.م',
                              style: AppTheme.priceText.copyWith(
                                color: AppTheme.textLightColor,
                                fontSize: 14,
                                fontWeight: FontWeight.w700,
                              ),
                            ),
                          ),
                        ],

                        const SizedBox(height: 8),

                        // التقييم المحسن
                        if (showRating)
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: AppTheme.backgroundColor,
                              borderRadius: BorderRadius.circular(6),
                              border: Border.all(
                                color: AppTheme.primaryColor.withOpacity(0.1),
                                width: 1,
                              ),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.star_rounded,
                                  color: AppTheme.warningColor,
                                  size: 16,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  product.rating.toStringAsFixed(1),
                                  style: AppTheme.cardTitle.copyWith(
                                    fontSize: 12,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  '(${product.reviewsCount})',
                                  style: AppTheme.cardSubtitle.copyWith(
                                    fontSize: 10,
                                    color: AppTheme.textTertiaryColor,
                                  ),
                                ),
                              ],
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
