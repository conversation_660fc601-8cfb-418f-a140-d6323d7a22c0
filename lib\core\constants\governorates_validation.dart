import 'governorates_constants.dart';

/// دالة للتحقق من صحة بيانات المحافظات
void main() {
  print('🔍 بدء التحقق من صحة بيانات المحافظات...');
  
  final allGovernorates = GovernoratesConstants.getAllGovernorates();
  final errors = <String>[];
  
  // التحقق من عدد المحافظات
  if (allGovernorates.length != 27) {
    errors.add('❌ عدد المحافظات غير صحيح: ${allGovernorates.length} بدلاً من 27');
  } else {
    print('✅ عدد المحافظات صحيح: 27 محافظة');
  }
  
  // التحقق من وجود مراكز لكل محافظة
  int totalCenters = 0;
  int governoratesWithoutCenters = 0;
  
  for (final gov in allGovernorates) {
    if (gov.centers.isEmpty) {
      errors.add('❌ المحافظة "${gov.name}" لا تحتوي على مراكز');
      governoratesWithoutCenters++;
    }
    totalCenters += gov.centers.length;
  }
  
  if (governoratesWithoutCenters == 0) {
    print('✅ جميع المحافظات تحتوي على مراكز');
  }
  
  print('📊 إجمالي المراكز: $totalCenters');
  
  // التحقق من الأكواد المكررة
  final ids = allGovernorates.map((gov) => gov.id).toList();
  final uniqueIds = ids.toSet();
  if (ids.length != uniqueIds.length) {
    errors.add('❌ يوجد أكواد محافظات مكررة');
  } else {
    print('✅ جميع أكواد المحافظات فريدة');
  }
  
  // التحقق من الأسماء المكررة
  final names = allGovernorates.map((gov) => gov.name).toList();
  final uniqueNames = names.toSet();
  if (names.length != uniqueNames.length) {
    errors.add('❌ يوجد أسماء محافظات مكررة');
  } else {
    print('✅ جميع أسماء المحافظات فريدة');
  }
  
  // اختبار الدوال المساعدة
  print('\n🧪 اختبار الدوال المساعدة...');
  
  // اختبار البحث بالاسم
  final cairo = GovernoratesConstants.findGovernorateByName('القاهرة');
  if (cairo != null && cairo.centers.isNotEmpty) {
    print('✅ دالة البحث بالاسم تعمل: القاهرة لديها ${cairo.centers.length} مركز');
  } else {
    errors.add('❌ دالة البحث بالاسم لا تعمل');
  }
  
  // اختبار البحث بالكود
  final alex = GovernoratesConstants.findGovernorateById('ALX');
  if (alex != null && alex.name == 'الإسكندرية') {
    print('✅ دالة البحث بالكود تعمل: ALX = الإسكندرية');
  } else {
    errors.add('❌ دالة البحث بالكود لا تعمل');
  }
  
  // اختبار الحصول على المراكز
  final cairoCenters = GovernoratesConstants.getCentersByGovernorateName('القاهرة');
  if (cairoCenters.isNotEmpty) {
    print('✅ دالة الحصول على المراكز تعمل: القاهرة لديها ${cairoCenters.length} مركز');
    print('   أول 3 مراكز: ${cairoCenters.take(3).join(', ')}');
  } else {
    errors.add('❌ دالة الحصول على المراكز لا تعمل');
  }
  
  // اختبار التحقق من صحة المركز
  final isValidCenter = GovernoratesConstants.isValidCenterInGovernorate('مصر الجديدة', 'CAI');
  if (isValidCenter) {
    print('✅ دالة التحقق من صحة المركز تعمل');
  } else {
    errors.add('❌ دالة التحقق من صحة المركز لا تعمل');
  }
  
  // عرض النتائج
  print('\n📋 نتائج التحقق:');
  if (errors.isEmpty) {
    print('🎉 جميع البيانات صحيحة!');
  } else {
    print('⚠️ تم العثور على ${errors.length} خطأ:');
    for (final error in errors) {
      print(error);
    }
  }
  
  // إحصائيات المناطق
  final regions = GovernoratesConstants.getAllRegions();
  print('\n📍 المناطق الجغرافية (${regions.length}):');
  for (final region in regions) {
    final govs = GovernoratesConstants.getGovernoratesByRegion(region);
    final totalRegionCenters = govs.fold<int>(0, (sum, gov) => sum + gov.centers.length);
    print('   - $region: ${govs.length} محافظات، $totalRegionCenters مركز');
  }
  
  // عرض تفاصيل بعض المحافظات
  print('\n🏛️ تفاصيل بعض المحافظات:');
  final mainGovernorates = ['القاهرة', 'الإسكندرية', 'الجيزة', 'الدقهلية'];
  for (final govName in mainGovernorates) {
    final gov = GovernoratesConstants.findGovernorateByName(govName);
    if (gov != null) {
      print('   - ${gov.name} (${gov.id}): ${gov.centers.length} مركز في منطقة ${gov.region}');
    }
  }
  
  print('\n🎯 ملخص نهائي:');
  print('   📊 المحافظات: ${allGovernorates.length}');
  print('   🏘️ المراكز: $totalCenters');
  print('   🗺️ المناطق: ${regions.length}');
  print('   ✅ الحالة: ${errors.isEmpty ? 'جميع البيانات صحيحة' : '${errors.length} أخطاء موجودة'}');
  
  if (errors.isEmpty) {
    print('\n🚀 النظام جاهز للاستخدام!');
  }
}
