import 'package:flutter/material.dart';
import 'package:motorcycle_parts_shop/core/theme/app_theme.dart';

/// مكونات مشتركة لحقول النماذج
class CommonFormFields {
  /// حقل إدخال البريد الإلكتروني
  static Widget buildEmailField({
    required TextEditingController controller,
    required void Function(String)? onFieldSubmitted,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      keyboardType: TextInputType.emailAddress,
      textDirection: TextDirection.ltr,
      decoration: InputDecoration(
        labelText: 'البريد الإلكتروني',
        prefixIcon: Icon(Icons.email_outlined, color: AppTheme.primaryColor),
        border: const OutlineInputBorder(),
      ),
      textInputAction: TextInputAction.next,
      onFieldSubmitted: onFieldSubmitted,
      validator:
          validator ??
          (value) {
            if (value?.isEmpty ?? true) return 'الرجاء إدخال البريد الإلكتروني';
            if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value!)) {
              return 'الرجاء إدخال بريد إلكتروني صحيح';
            }
            return null;
          },
    );
  }

  /// حقل إدخال كلمة المرور
  static Widget buildPasswordField({
    required TextEditingController controller,
    required bool isPasswordVisible,
    required void Function() onVisibilityToggle,
    required void Function(String)? onFieldSubmitted,
    String? Function(String?)? validator,
    String labelText = 'كلمة المرور',
  }) {
    return TextFormField(
      controller: controller,
      obscureText: !isPasswordVisible,
      textDirection: TextDirection.ltr,
      decoration: InputDecoration(
        labelText: labelText,
        prefixIcon: Icon(Icons.lock_outline, color: AppTheme.primaryColor),
        suffixIcon: IconButton(
          icon: Icon(
            isPasswordVisible ? Icons.visibility_off : Icons.visibility,
            color: AppTheme.primaryColor,
          ),
          onPressed: onVisibilityToggle,
        ),
        border: const OutlineInputBorder(),
      ),
      textInputAction: TextInputAction.next,
      onFieldSubmitted: onFieldSubmitted,
      validator:
          validator ??
          (value) {
            if (value?.isEmpty ?? true) return 'الرجاء إدخال كلمة المرور';
            if (value!.length < 6) {
              return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
            }
            return null;
          },
    );
  }

  /// حقل إدخال رقم الهاتف
  static Widget buildPhoneField({
    required TextEditingController controller,
    required void Function(String)? onFieldSubmitted,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      keyboardType: TextInputType.phone,
      textDirection: TextDirection.ltr,
      decoration: InputDecoration(
        labelText: 'رقم الهاتف',
        hintText: '01XXXXXXXXX',
        prefixIcon: Icon(Icons.phone_outlined, color: AppTheme.primaryColor),
        border: const OutlineInputBorder(),
      ),
      textInputAction: TextInputAction.done,
      onFieldSubmitted: onFieldSubmitted,
      validator:
          validator ??
          (value) {
            if (value?.isEmpty ?? true) return 'الرجاء إدخال رقم الهاتف';
            final cleanPhone = value!.replaceAll(RegExp(r'[\s-]'), '');
            if (!RegExp(r'^\+?[0-9]{8,15}$').hasMatch(cleanPhone)) {
              return 'يرجى إدخال رقم هاتف صحيح';
            }
            return null;
          },
    );
  }

  /// زر التحميل
  static Widget buildLoadingButton({
    required bool isLoading,
    required VoidCallback onPressed,
    required String text,
    Color? backgroundColor,
  }) {
    return ElevatedButton(
      onPressed: isLoading ? null : onPressed,
      style: ElevatedButton.styleFrom(
        padding: const EdgeInsets.symmetric(vertical: 16),
        backgroundColor: backgroundColor ?? AppTheme.primaryColor,
      ),
      child:
          isLoading
              ? const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: Colors.white,
                ),
              )
              : Text(text, style: const TextStyle(fontSize: 16)),
    );
  }
}
