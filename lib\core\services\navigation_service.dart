import 'package:flutter/material.dart';

/// خدمة التنقل بين الشاشات في التطبيق
class NavigationService extends ChangeNotifier {
  static final NavigationService _instance = NavigationService._internal();
  final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

  // نمط Singleton للتأكد من وجود نسخة واحدة فقط من الخدمة
  factory NavigationService() {
    return _instance;
  }

  NavigationService._internal();

  /// الانتقال إلى شاشة جديدة
  Future<dynamic> navigateTo(String routeName, {Object? arguments}) {
    notifyListeners();
    return navigatorKey.currentState!.pushNamed(
      routeName,
      arguments: arguments,
    );
  }

  /// الانتقال إلى شاشة جديدة واستبدال الشاشة الحالية
  Future<dynamic> navigateToReplace(String routeName, {Object? arguments}) {
    notifyListeners();
    return navigatorKey.currentState!.pushReplacementNamed(
      routeName,
      arguments: arguments,
    );
  }

  /// الانتقال إلى شاشة جديدة وإزالة جميع الشاشات السابقة
  Future<dynamic> navigateToAndRemoveUntil(
    String routeName, {
    Object? arguments,
  }) {
    notifyListeners();
    return navigatorKey.currentState!.pushNamedAndRemoveUntil(
      routeName,
      (Route<dynamic> route) => false,
      arguments: arguments,
    );
  }

  /// الرجوع إلى الشاشة السابقة
  void goBack() {
    notifyListeners();
    return navigatorKey.currentState!.pop();
  }
}
