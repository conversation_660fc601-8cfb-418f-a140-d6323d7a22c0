import 'dart:async';
import 'package:flutter/material.dart';
import 'package:motorcycle_parts_shop/core/services/auth_supabase_service.dart';
import 'package:motorcycle_parts_shop/core/theme/app_theme.dart';
import 'package:motorcycle_parts_shop/screens/auth/login_screen.dart';
import 'package:motorcycle_parts_shop/screens/home/<USER>';
import 'package:pin_code_fields/pin_code_fields.dart';
import 'package:provider/provider.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class VerifyEmailScreen extends StatefulWidget {
  final String email;
  final String password;
  final String name;
  final String? address;
  final DateTime? birthDate;
  final String? phone;
  final String? governorate;
  final String? center;

  const VerifyEmailScreen({
    super.key,
    required this.email,
    required this.password,
    required this.name,
    this.address,
    this.birthDate,
    this.phone,
    this.governorate,
    this.center,
  });

  @override
  State<VerifyEmailScreen> createState() => _VerifyEmailScreenState();
}

class _VerifyEmailScreenState extends State<VerifyEmailScreen> {
  bool _isResending = false;
  final bool _resendSuccess = false;
  bool _isEmailVerified = false;
  String _otpError = '';
  int _resendCountdown = 0;
  bool _canResendOTP = true;
  late final SupabaseClient _supabaseClient;
  Timer? _resendTimer;

  @override
  void initState() {
    super.initState();
    _supabaseClient =
        Provider.of<AuthSupabaseService>(context, listen: false).client;
    _checkEmailVerificationStatus();
    _startInitialCountdown();
    Future.delayed(Duration.zero, () {
      _startPeriodicVerificationCheck();
    });
  }

  Future<void> _checkEmailVerificationStatus() async {
    final session = _supabaseClient.auth.currentSession;
    if (session != null && session.user.emailConfirmedAt != null) {
      setState(() {
        _isEmailVerified = true;
      });
    }
  }

  void _startPeriodicVerificationCheck() {
    const checkInterval = Duration(seconds: 5);
    Timer.periodic(checkInterval, (timer) {
      if (!_isEmailVerified) {
        _checkEmailVerificationStatus();
      } else {
        timer.cancel();
      }
    });
  }

  void _startInitialCountdown() {
    setState(() => _resendCountdown = 60);
    _startResendTimer();
  }

  /// إعادة إرسال رمز التحقق
  Future<void> _resendOTP() async {
    setState(() {
      _isResending = true;
      _otpError = '';
    });

    try {
      final authService = Provider.of<AuthSupabaseService>(
        context,
        listen: false,
      );

      final success = await authService.resendOTP(widget.email);

      if (!mounted) return;

      if (success) {
        // إعادة تشغيل العد التنازلي
        _startResendTimer();

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إرسال رمز تحقق جديد إلى بريدك الإلكتروني'),
            backgroundColor: AppTheme.successColor,
            behavior: SnackBarBehavior.floating,
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              authService.error ??
                  'فشل في إرسال رمز التحقق. يرجى المحاولة مرة أخرى',
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } catch (e) {
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ: $e'),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isResending = false;
        });
      }
    }
  }

  /// بدء العد التنازلي لإعادة الإرسال
  void _startResendTimer() {
    setState(() {
      _canResendOTP = false;
      _resendCountdown = 60;
    });

    _resendTimer?.cancel();
    _resendTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_resendCountdown > 0) {
        setState(() {
          _resendCountdown--;
        });
      } else {
        setState(() {
          _canResendOTP = true;
        });
        timer.cancel();
      }
    });
  }

  Future<void> _verifyOtp(String otp) async {
    if (otp.length != 6) {
      setState(() {
        _otpError = 'يرجى إدخال رمز مكون من 6 أرقام';
      });
      return;
    }

    setState(() {
      _otpError = '';
    });

    try {
      final authService = Provider.of<AuthSupabaseService>(
        context,
        listen: false,
      );
      final verified = await authService.verifyEmailOtp(widget.email, otp);

      if (!mounted) return;

      if (verified) {
        await _handleSuccessfulVerification(authService);
      } else {
        setState(() {
          _otpError = authService.error ?? 'رمز التحقق غير صحيح';
        });
        if (authService.error?.contains('انتهت صلاحية') ?? false) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('انتهت صلاحية الرمز، يرجى إعادة إرسال رمز جديد'),
              backgroundColor: Colors.red,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      }
    } catch (e) {
      setState(() {
        _otpError = 'حدث خطأ أثناء التحقق: $e';
      });
    }
  }

  Future<void> _handleSuccessfulVerification(
    AuthSupabaseService authService,
  ) async {
    try {
      // تحديث الملف الشخصي بالبيانات المجمعة
      final success = await authService.updateUserProfile(
        name: widget.name,
        phone: widget.phone?.isEmpty ?? true ? null : widget.phone,
        address: widget.address?.isEmpty ?? true ? null : widget.address,
        birthDate: widget.birthDate,
        governorate:
            widget.governorate?.isEmpty ?? true ? null : widget.governorate,
        center: widget.center?.isEmpty ?? true ? null : widget.center,
      );

      if (!mounted) return;

      if (success) {
        setState(() {
          _isEmailVerified = true;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم التحقق من البريد الإلكتروني وتحديث الحساب بنجاح'),
            backgroundColor: AppTheme.successColor,
            behavior: SnackBarBehavior.floating,
          ),
        );

        // محاولة تسجيل الدخول تلقائيًا
        try {
          final loginSuccess = await authService.signInWithEmail(
            widget.email,
            widget.password,
          );

          if (loginSuccess) {
            // الانتقال إلى الشاشة الرئيسية
            Future.delayed(const Duration(seconds: 2), () {
              if (mounted) {
                Navigator.of(context).pushAndRemoveUntil(
                  MaterialPageRoute(builder: (context) => const HomeScreen()),
                  (route) => false,
                );
              }
            });
          } else {
            // إذا فشل تسجيل الدخول، انتقل إلى شاشة تسجيل الدخول
            _navigateToLogin();
          }
        } catch (loginError) {
          debugPrint('خطأ في تسجيل الدخول التلقائي: $loginError');
          _navigateToLogin();
        }
      } else {
        setState(() {
          _otpError = authService.error ?? 'فشل تحديث الحساب';
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل تحديث الحساب: ${authService.error}'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } catch (e) {
      if (!mounted) return;

      setState(() {
        _otpError = 'حدث خطأ أثناء تحديث الحساب: $e';
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ أثناء تحديث الحساب: $e'),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  void _navigateToLogin() {
    Navigator.of(context).pushAndRemoveUntil(
      MaterialPageRoute(builder: (context) => const LoginScreen()),
      (route) => false,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppTheme.primaryColor.withOpacity(0.8),
              AppTheme.primaryColor.withOpacity(0.6),
              Colors.white,
            ],
            stops: [0.0, 0.3, 1.0],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.symmetric(
              horizontal: 24.0,
              vertical: 40.0,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                const SizedBox(height: 40),
                // Enhanced Header Section
                Column(
                  children: [
                    Container(
                      height: 120,
                      width: 120,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [Colors.white, Colors.white.withOpacity(0.9)],
                        ),
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 30,
                            spreadRadius: 5,
                            offset: Offset(0, 10),
                          ),
                          BoxShadow(
                            color: AppTheme.primaryColor.withOpacity(0.3),
                            blurRadius: 20,
                            spreadRadius: 2,
                          ),
                        ],
                      ),
                      child: Icon(
                        Icons.mark_email_read,
                        size: 60,
                        color: AppTheme.primaryColor,
                      ),
                    ),
                    const SizedBox(height: 24),
                    Text(
                      'تأكيد البريد الإلكتروني',
                      style: Theme.of(
                        context,
                      ).textTheme.headlineLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                        fontSize: 28,
                        shadows: [
                          Shadow(
                            color: Colors.black.withOpacity(0.3),
                            offset: Offset(0, 2),
                            blurRadius: 4,
                          ),
                        ],
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'أدخل الرمز المرسل إلى بريدك الإلكتروني',
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: Colors.white.withOpacity(0.9),
                        fontSize: 16,
                        shadows: [
                          Shadow(
                            color: Colors.black.withOpacity(0.2),
                            offset: Offset(0, 1),
                            blurRadius: 2,
                          ),
                        ],
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
                const SizedBox(height: 40),
                // Main Content Container
                Container(
                  padding: const EdgeInsets.all(24.0),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 20,
                        spreadRadius: 5,
                        offset: Offset(0, 10),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      // Status Icon
                      AnimatedSwitcher(
                        duration: const Duration(milliseconds: 500),
                        transitionBuilder: (
                          Widget child,
                          Animation<double> animation,
                        ) {
                          return ScaleTransition(
                            scale: animation,
                            child: FadeTransition(
                              opacity: animation,
                              child: child,
                            ),
                          );
                        },
                        child:
                            _isEmailVerified
                                ? Container(
                                  key: const ValueKey('verified'),
                                  width: 80,
                                  height: 80,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: Colors.green,
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.green.withOpacity(0.3),
                                        blurRadius: 15,
                                        spreadRadius: 3,
                                        offset: const Offset(0, 5),
                                      ),
                                    ],
                                  ),
                                  child: const Icon(
                                    Icons.check_circle,
                                    size: 50,
                                    color: Colors.white,
                                  ),
                                )
                                : Container(
                                  key: const ValueKey('unverified'),
                                  width: 80,
                                  height: 80,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: AppTheme.primaryColor,
                                    boxShadow: [
                                      BoxShadow(
                                        color: AppTheme.primaryColor
                                            .withOpacity(0.3),
                                        blurRadius: 15,
                                        spreadRadius: 3,
                                        offset: const Offset(0, 5),
                                      ),
                                    ],
                                  ),
                                  child: const Icon(
                                    Icons.email_outlined,
                                    size: 40,
                                    color: Colors.white,
                                  ),
                                ),
                      ),
                      const SizedBox(height: 24),
                      Text(
                        _isEmailVerified
                            ? 'تم تأكيد البريد!'
                            : 'أدخل رمز التحقق',
                        style: Theme.of(
                          context,
                        ).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: AppTheme.primaryColor,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 12),
                      Text(
                        _isEmailVerified
                            ? 'تم تأكيد بريدك الإلكتروني بنجاح'
                            : 'تم إرسال رمز التحقق إلى\n${widget.email}',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[600],
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 32),
                      if (!_isEmailVerified) ...[
                        // حقل إدخال رمز التحقق محسن
                        Container(
                          margin: const EdgeInsets.symmetric(horizontal: 20),
                          padding: const EdgeInsets.all(20),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(20),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.1),
                                blurRadius: 15,
                                spreadRadius: 2,
                                offset: const Offset(0, 5),
                              ),
                            ],
                          ),
                          child: Directionality(
                            textDirection: TextDirection.ltr,
                            child: PinCodeTextField(
                              appContext: context,
                              length: 6,
                              obscureText: false,
                              onChanged: (value) {
                                setState(() {
                                  _otpError = '';
                                });
                              },
                              onCompleted: (value) async {
                                await _verifyOtp(value);
                              },
                              textStyle: const TextStyle(
                                fontSize: 22,
                                fontWeight: FontWeight.bold,
                                color: AppTheme.primaryColor,
                              ),
                              pinTheme: PinTheme(
                                shape: PinCodeFieldShape.box,
                                borderRadius: BorderRadius.circular(15),
                                fieldHeight: 65,
                                fieldWidth: 50,
                                activeFillColor: AppTheme.primaryColor
                                    .withOpacity(0.1),
                                inactiveFillColor: Colors.grey[50],
                                selectedFillColor: AppTheme.primaryColor
                                    .withOpacity(0.2),
                                activeColor: AppTheme.primaryColor,
                                inactiveColor: Colors.grey[300],
                                selectedColor: AppTheme.primaryColor,
                                borderWidth: 2,
                                disabledColor: Colors.grey.withOpacity(0.3),
                              ),
                              keyboardType: TextInputType.number,
                              animationType: AnimationType.fade,
                              animationDuration: const Duration(
                                milliseconds: 300,
                              ),
                              enableActiveFill: true,
                              boxShadows: [
                                BoxShadow(
                                  offset: const Offset(0, 2),
                                  color: AppTheme.primaryColor.withOpacity(0.1),
                                  blurRadius: 8,
                                ),
                              ],
                            ),
                          ),
                        ),
                        if (_otpError.isNotEmpty) ...[
                          const SizedBox(height: 12),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 10,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.red.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(color: Colors.red.shade300),
                            ),
                            child: Row(
                              children: [
                                const Icon(
                                  Icons.error_outline,
                                  color: Colors.red,
                                  size: 20,
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    _otpError,
                                    style: const TextStyle(
                                      color: Colors.red,
                                      fontWeight: FontWeight.w500,
                                    ),
                                    textAlign: TextAlign.right,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                        const SizedBox(height: 24),
                        // زر إعادة إرسال الرمز محسن
                        Container(
                          margin: const EdgeInsets.symmetric(horizontal: 30),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(15),
                            gradient:
                                _isResending || !_canResendOTP
                                    ? LinearGradient(
                                      colors: [
                                        Colors.grey[400]!,
                                        Colors.grey[500]!,
                                      ],
                                      begin: Alignment.topLeft,
                                      end: Alignment.bottomRight,
                                    )
                                    : const LinearGradient(
                                      colors: [
                                        Color(0xFF6366F1),
                                        Color(0xFF8B5CF6),
                                      ],
                                      begin: Alignment.topLeft,
                                      end: Alignment.bottomRight,
                                    ),
                            boxShadow: [
                              BoxShadow(
                                color:
                                    (_isResending || !_canResendOTP)
                                        ? Colors.grey.withOpacity(0.3)
                                        : const Color(
                                          0xFF6366F1,
                                        ).withOpacity(0.4),
                                blurRadius: 12,
                                spreadRadius: 2,
                                offset: const Offset(0, 6),
                              ),
                            ],
                          ),
                          child: ElevatedButton(
                            onPressed:
                                _isResending || !_canResendOTP
                                    ? null
                                    : _resendOTP,
                            style: ElevatedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 18),
                              backgroundColor: Colors.transparent,
                              shadowColor: Colors.transparent,
                              elevation: 0,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(15),
                              ),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                if (_isResending)
                                  const Padding(
                                    padding: EdgeInsets.only(left: 12.0),
                                    child: SizedBox(
                                      width: 18,
                                      height: 18,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2.5,
                                        valueColor:
                                            AlwaysStoppedAnimation<Color>(
                                              Colors.white,
                                            ),
                                      ),
                                    ),
                                  )
                                else
                                  const Padding(
                                    padding: EdgeInsets.only(left: 8.0),
                                    child: Icon(
                                      Icons.refresh,
                                      color: Colors.white,
                                      size: 20,
                                    ),
                                  ),
                                Text(
                                  _isResending
                                      ? 'جاري إعادة الإرسال...'
                                      : !_canResendOTP
                                      ? 'انتظر $_resendCountdown ثانية'
                                      : 'إعادة إرسال الرمز',
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.white,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(height: 16),
                      ],
                      // زر العودة إلى تسجيل الدخول محسن
                      Container(
                        margin: const EdgeInsets.symmetric(horizontal: 40),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: AppTheme.primaryColor.withOpacity(0.3),
                            width: 1.5,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.05),
                              blurRadius: 8,
                              spreadRadius: 1,
                              offset: const Offset(0, 3),
                            ),
                          ],
                        ),
                        child: TextButton(
                          onPressed: () {
                            Navigator.of(context).pushReplacement(
                              MaterialPageRoute(
                                builder: (context) => const LoginScreen(),
                              ),
                            );
                          },
                          style: TextButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.arrow_back,
                                color: AppTheme.primaryColor,
                                size: 20,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'العودة إلى تسجيل الدخول',
                                style: TextStyle(
                                  color: AppTheme.primaryColor,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      if (_resendSuccess && !_isEmailVerified) ...[
                        const SizedBox(height: 24),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 12,
                          ),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                AppTheme.successColor.withOpacity(0.1),
                                AppTheme.successColor.withOpacity(0.05),
                              ],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            ),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: AppTheme.successColor.withOpacity(0.5),
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: AppTheme.successColor.withOpacity(0.1),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: AppTheme.successColor.withOpacity(0.2),
                                  shape: BoxShape.circle,
                                ),
                                child: const Icon(
                                  Icons.check_circle,
                                  color: AppTheme.successColor,
                                  size: 24,
                                ),
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Text(
                                  'تم إرسال رمز التحقق بنجاح',
                                  style: TextStyle(
                                    color: AppTheme.successColor,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _resendTimer?.cancel();
    super.dispose();
  }
}
